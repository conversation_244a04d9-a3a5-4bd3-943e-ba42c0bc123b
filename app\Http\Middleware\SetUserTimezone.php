<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SetUserTimezone
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();
        if (isset($user) && isset($request->user()->company)) {

            // Set app timezone to user's timezone
            $timezone = $user->company->country->timezone;
            config(['app.timezone' => $timezone]);
            date_default_timezone_set($timezone);
        } else {
            // Default to UTC for unauthenticated requests
            date_default_timezone_set('UTC');
        }

        return $next($request);
    }
}
