<?php

namespace App\Rules;

use App\Traits\V1\PhoneHelper;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\DB;

class UniquePhoneWithCountryCodeRule implements Rule
{
    use PhoneHelper;

    protected ?int $exceptId;
    protected string $countryCode;


    public function __construct(string $countryCode, ?int $exceptId = null)
    {
        $this->countryCode = $countryCode;
        $this->exceptId = $exceptId;
    }

    public function passes($attribute, $value): bool
    {
        $fullPhone = $this->formatPhoneWithCountryCode($this->countryCode, $value);

        $query = DB::table('employees')
            ->where('phone', $fullPhone)
            ->whereNull('deleted_at');

        if ($this->exceptId) {
            $query->where('id', '!=', $this->exceptId);
        }

        return !$query->exists();
    }

    public function message(): string
    {
        return __('validation.unique_phone');
    }
}
