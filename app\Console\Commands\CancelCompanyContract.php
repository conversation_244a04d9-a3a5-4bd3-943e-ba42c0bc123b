<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CancelCompanyContract extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:cancel-company-contract';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'cancel company contract based on contract end date';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::table('companies')
            ->where('contract_end_date', Carbon::now()->format('Y-m-d'))
            ->update([
                'status' => 'suspended',
                'next_bill_date' => null,
            ]);
    }
}
