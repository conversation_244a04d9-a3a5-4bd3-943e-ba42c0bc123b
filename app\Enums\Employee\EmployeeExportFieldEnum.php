<?php

namespace App\Enums\Employee;

use App\Traits\EnumToArray;

enum EmployeeExportFieldEnum: string
{
    use EnumToArray;

    // Fields should be ordered as it is in the front end

    // Essential fields (always included)
    case EMPLOYEE_CODE = 'employee_code';
    case EMPLOYEE_NAME_AR = 'employee_name_ar';
    case EMPLOYEE_NAME_EN = 'employee_name_en';
    case JOB_TITLE = 'job_title';
    case DEPARTMENT = 'department';
    case LOCATION = 'location';
    case PRIMARY_PHONE = 'primary_phone';

    // Basic details
    case NATIONALITY = 'nationality';
    case NATIONAL_ID = 'national_id';
    case PASSPORT_NUMBER = 'passport_number';
    case GENDER = 'gender';
    case PLACE_OF_BIRTH = 'place_of_birth';
    case ADDRESS = 'address';
    case MILITARY_STATUS = 'military_status';
    case DATE_OF_BIRTH = 'date_of_birth';
    case RELIGION = 'religion';
    case MARITAL_STATUS = 'marital_status';
    case NUMBER_OF_CHILDREN = 'number_of_children';
    case YEARS_OF_EXPERIENCE = 'years_of_experience';

    // Contact details
    case SECONDARY_PHONE = 'secondary_phone';
    case WORK_EMAIL = 'work_email';
    case PERSONAL_EMAIL = 'personal_email';
    case EMERGENCY_CONTACT_NAME = 'emergency_contact_name';
    case EMERGENCY_CONTACT_PHONE = 'emergency_contact_phone';
    case EMERGENCY_CONTACT_RELATION = 'emergency_contact_relation';

    // Education details
    case DEGREE_TYPE = 'degree_type';
    case DEGREE_NAME = 'degree_name';
    case INSTITUTION_NAME = 'institution_name';
    case GRADUATION_YEAR = 'graduation_year';

    // Employment details
    case HIRING_DATE = 'hiring_date';
    case EMPLOYMENT_TYPE = 'employment_type';
    case LATEST_CONTRACT_START_DATE = 'latest_contract_start_date';
    case LATEST_CONTRACT_DURATION = 'latest_contract_duration';
    case LATEST_CONTRACT_END_DATE = 'latest_contract_end_date';

    /*// Salary details
    case NET_SALARY = 'net_salary';
    case GROSS_SALARY = 'gross_salary';
    case SOCIAL_INSURANCE_SALARY = 'social_insurance_salary';
    case APPLIED_ALLOWANCES = 'applied_allowances';


    // Social insurance data
    case SOCIAL_INSURANCE_STATUS = 'social_insurance_status';
    case SOCIAL_INSURANCE_NUMBER = 'social_insurance_number';
    case SOCIAL_INSURANCE_START_DATE = 'social_insurance_start_date';
    case SOCIAL_INSURANCE_END_DATE = 'social_insurance_end_date';
    case SOCIAL_INSURANCE_OFFICE = 'social_insurance_office';

    // Medical insurance data
    case MEDICAL_INSURANCE_STATUS = 'medical_insurance_status';
    case MEDICAL_INSURANCE_NUMBER = 'medical_insurance_number';
    case MEDICAL_INSURANCE_PROVIDER = 'medical_insurance_provider';
    case MEDICAL_INSURANCE_START_DATE = 'medical_insurance_start_date';
    case MEDICAL_INSURANCE_END_DATE = 'medical_insurance_end_date';*/

    public static function getCategory(self $value): EmployeeExportCategoryEnum
    {
        return match ($value) {
            self::EMPLOYEE_CODE, self::EMPLOYEE_NAME_AR, self::EMPLOYEE_NAME_EN,
            self::JOB_TITLE, self::DEPARTMENT, self::LOCATION, self::PRIMARY_PHONE
                => EmployeeExportCategoryEnum::ESSENTIAL,

            self::NATIONALITY, self::NATIONAL_ID, self::PASSPORT_NUMBER, self::GENDER,
            self::PLACE_OF_BIRTH, self::ADDRESS, self::MILITARY_STATUS, self::DATE_OF_BIRTH,
            self::RELIGION, self::MARITAL_STATUS, self::NUMBER_OF_CHILDREN,
            self::YEARS_OF_EXPERIENCE
                => EmployeeExportCategoryEnum::BASIC,

            self::SECONDARY_PHONE, self::WORK_EMAIL, self::PERSONAL_EMAIL,
            self::EMERGENCY_CONTACT_NAME, self::EMERGENCY_CONTACT_PHONE, self::EMERGENCY_CONTACT_RELATION
                => EmployeeExportCategoryEnum::CONTACT,

            self::DEGREE_TYPE, self::DEGREE_NAME, self::INSTITUTION_NAME, self::GRADUATION_YEAR
                => EmployeeExportCategoryEnum::EDUCATION,

            self::HIRING_DATE, self::EMPLOYMENT_TYPE, self::LATEST_CONTRACT_START_DATE, self::LATEST_CONTRACT_DURATION, self::LATEST_CONTRACT_END_DATE
                => EmployeeExportCategoryEnum::EMPLOYMENT,

            /*self::NET_SALARY, self::GROSS_SALARY, self::SOCIAL_INSURANCE_SALARY, self::APPLIED_ALLOWANCES
                => EmployeeExportCategoryEnum::SALARY,

            self::SOCIAL_INSURANCE_STATUS, self::SOCIAL_INSURANCE_NUMBER, self::SOCIAL_INSURANCE_START_DATE,
            self::SOCIAL_INSURANCE_END_DATE, self::SOCIAL_INSURANCE_OFFICE
                => EmployeeExportCategoryEnum::SOCIAL_INSURANCE,

            self::MEDICAL_INSURANCE_STATUS, self::MEDICAL_INSURANCE_NUMBER, self::MEDICAL_INSURANCE_PROVIDER,
            self::MEDICAL_INSURANCE_START_DATE,self::MEDICAL_INSURANCE_END_DATE
                => EmployeeExportCategoryEnum::MEDICAL_INSURANCE,*/
        };
    }
    public static function getCategoryForBulkAdd(self $value): EmployeeExportCategoryEnum
    {
        return match ($value) {
            self::EMPLOYEE_NAME_AR, self::EMPLOYEE_NAME_EN, self::EMPLOYEE_CODE, self::GENDER,
            self::PRIMARY_PHONE, self::HIRING_DATE, self::JOB_TITLE, self::LOCATION
                => EmployeeExportCategoryEnum::ESSENTIAL,

            self::NATIONALITY, self::NATIONAL_ID, self::PASSPORT_NUMBER,
            self::PLACE_OF_BIRTH, self::ADDRESS, self::MILITARY_STATUS, self::DATE_OF_BIRTH,
            self::RELIGION, self::MARITAL_STATUS, self::NUMBER_OF_CHILDREN,
            self::YEARS_OF_EXPERIENCE
                => EmployeeExportCategoryEnum::BASIC,

            self::SECONDARY_PHONE, self::WORK_EMAIL, self::PERSONAL_EMAIL,
            self::EMERGENCY_CONTACT_NAME, self::EMERGENCY_CONTACT_PHONE, self::EMERGENCY_CONTACT_RELATION
                => EmployeeExportCategoryEnum::CONTACT,

            self::DEGREE_TYPE, self::DEGREE_NAME, self::INSTITUTION_NAME, self::GRADUATION_YEAR
                => EmployeeExportCategoryEnum::EDUCATION,

            self::EMPLOYMENT_TYPE, self::LATEST_CONTRACT_START_DATE, self::LATEST_CONTRACT_DURATION, self::LATEST_CONTRACT_END_DATE
                => EmployeeExportCategoryEnum::EMPLOYMENT,

            /*self::NET_SALARY, self::GROSS_SALARY, self::SOCIAL_INSURANCE_SALARY, self::APPLIED_ALLOWANCES
                => EmployeeExportCategoryEnum::SALARY,

            self::SOCIAL_INSURANCE_STATUS, self::SOCIAL_INSURANCE_NUMBER, self::SOCIAL_INSURANCE_START_DATE,
            self::SOCIAL_INSURANCE_END_DATE, self::SOCIAL_INSURANCE_OFFICE
                => EmployeeExportCategoryEnum::SOCIAL_INSURANCE,

            self::MEDICAL_INSURANCE_STATUS, self::MEDICAL_INSURANCE_NUMBER, self::MEDICAL_INSURANCE_PROVIDER,
            self::MEDICAL_INSURANCE_START_DATE,self::MEDICAL_INSURANCE_END_DATE
                => EmployeeExportCategoryEnum::MEDICAL_INSURANCE,*/
        };
    }
    public static function getCategoryForBulkEdit(self $value): EmployeeExportCategoryEnum
    {
        return match ($value) {
            self::EMPLOYEE_CODE, self::EMPLOYEE_NAME_AR, self::EMPLOYEE_NAME_EN,
            self::PRIMARY_PHONE
                => EmployeeExportCategoryEnum::ESSENTIAL,

            self::NATIONALITY, self::NATIONAL_ID, self::PASSPORT_NUMBER,  self::GENDER,
            self::PLACE_OF_BIRTH, self::ADDRESS, self::MILITARY_STATUS, self::DATE_OF_BIRTH,
            self::RELIGION, self::MARITAL_STATUS, self::NUMBER_OF_CHILDREN,
            self::YEARS_OF_EXPERIENCE
                => EmployeeExportCategoryEnum::BASIC,

            self::SECONDARY_PHONE, self::WORK_EMAIL, self::PERSONAL_EMAIL,
            self::EMERGENCY_CONTACT_NAME, self::EMERGENCY_CONTACT_PHONE, self::EMERGENCY_CONTACT_RELATION
                => EmployeeExportCategoryEnum::CONTACT,

            self::HIRING_DATE, self::EMPLOYMENT_TYPE
                => EmployeeExportCategoryEnum::EMPLOYMENT,

            /*self::NET_SALARY, self::GROSS_SALARY, self::SOCIAL_INSURANCE_SALARY, self::APPLIED_ALLOWANCES
                => EmployeeExportCategoryEnum::SALARY,

            self::SOCIAL_INSURANCE_STATUS, self::SOCIAL_INSURANCE_NUMBER, self::SOCIAL_INSURANCE_START_DATE,
            self::SOCIAL_INSURANCE_END_DATE, self::SOCIAL_INSURANCE_OFFICE
                => EmployeeExportCategoryEnum::SOCIAL_INSURANCE,

            self::MEDICAL_INSURANCE_STATUS, self::MEDICAL_INSURANCE_NUMBER, self::MEDICAL_INSURANCE_PROVIDER,
            self::MEDICAL_INSURANCE_START_DATE,self::MEDICAL_INSURANCE_END_DATE
                => EmployeeExportCategoryEnum::MEDICAL_INSURANCE,*/
        };
    }
    public static function getLabel(self $value): string
    {
        return trans('employeeexport.' . $value->value);
    }

    public static function isPhoneField(self $value): bool
    {
        return in_array($value, [
            self::PRIMARY_PHONE,
            self::SECONDARY_PHONE,
            self::EMERGENCY_CONTACT_PHONE,
        ]);
    }

    public static function getPhoneCountryCodeField(self $phoneField): string
    {
        return match ($phoneField) {
            self::PRIMARY_PHONE => 'primary_phone_country_code',
            self::SECONDARY_PHONE => 'secondary_phone_country_code',
            self::EMERGENCY_CONTACT_PHONE => 'emergency_contact_phone_country_code',
            default => throw new \InvalidArgumentException('Not a phone field: ' . $phoneField->value),
        };
    }

    public static function getFieldsForCategory(EmployeeExportCategoryEnum $category): array
    {
        return match ($category) {
            EmployeeExportCategoryEnum::ESSENTIAL => [
                self::EMPLOYEE_CODE->value,
                self::EMPLOYEE_NAME_AR->value,
                self::EMPLOYEE_NAME_EN->value,
                self::JOB_TITLE->value,
                self::DEPARTMENT->value,
                self::LOCATION->value,
                self::PRIMARY_PHONE->value,
            ],
            EmployeeExportCategoryEnum::BASIC => [
                self::NATIONALITY->value,
                self::NATIONAL_ID->value,
                self::PASSPORT_NUMBER->value,
                self::GENDER->value,
                self::PLACE_OF_BIRTH->value,
                self::ADDRESS->value,
                self::MILITARY_STATUS->value,
                self::DATE_OF_BIRTH->value,
                self::RELIGION->value,
                self::MARITAL_STATUS->value,
                self::NUMBER_OF_CHILDREN->value,
                self::YEARS_OF_EXPERIENCE->value,
            ],
            EmployeeExportCategoryEnum::CONTACT => [
                self::SECONDARY_PHONE->value,
                self::WORK_EMAIL->value,
                self::PERSONAL_EMAIL->value,
                self::EMERGENCY_CONTACT_NAME->value,
                self::EMERGENCY_CONTACT_PHONE->value,
                self::EMERGENCY_CONTACT_RELATION->value,
            ],
            EmployeeExportCategoryEnum::EDUCATION => [
                self::DEGREE_TYPE->value,
                self::DEGREE_NAME->value,
                self::INSTITUTION_NAME->value,
                self::GRADUATION_YEAR->value,
            ],
            EmployeeExportCategoryEnum::EMPLOYMENT => [
                self::HIRING_DATE->value,
                self::EMPLOYMENT_TYPE->value,
                self::LATEST_CONTRACT_START_DATE->value,
                self::LATEST_CONTRACT_DURATION->value,
                self::LATEST_CONTRACT_END_DATE->value,
            ],
            /*EmployeeExportCategoryEnum::SALARY => [
                self::GROSS_SALARY->value,
                self::NET_SALARY->value,
                self::SOCIAL_INSURANCE_SALARY->value,
                self::APPLIED_ALLOWANCES->value,
            ],
            EmployeeExportCategoryEnum::SOCIAL_INSURANCE => [
                self::SOCIAL_INSURANCE_STATUS->value,
                self::SOCIAL_INSURANCE_NUMBER->value,
                self::SOCIAL_INSURANCE_START_DATE->value,
                self::SOCIAL_INSURANCE_END_DATE->value,
                self::SOCIAL_INSURANCE_OFFICE->value,
            ],
            EmployeeExportCategoryEnum::MEDICAL_INSURANCE => [
                self::MEDICAL_INSURANCE_STATUS->value,
                self::MEDICAL_INSURANCE_NUMBER->value,
                self::MEDICAL_INSURANCE_START_DATE->value,
                self::MEDICAL_INSURANCE_END_DATE->value,
                self::MEDICAL_INSURANCE_PROVIDER->value,
            ],*/
        };
    }
    public static function getFieldsForCategoryForBulkAdd(EmployeeExportCategoryEnum $category): array
    {
        return match ($category) {
            EmployeeExportCategoryEnum::ESSENTIAL => [
                self::EMPLOYEE_NAME_AR->value,
                self::EMPLOYEE_NAME_EN->value,
                self::EMPLOYEE_CODE->value,
                self::GENDER->value,
                self::PRIMARY_PHONE->value,
                self::HIRING_DATE->value,
                self::JOB_TITLE->value,
                self::LOCATION->value,
            ],
            EmployeeExportCategoryEnum::BASIC => [
                self::NATIONALITY->value,
                self::NATIONAL_ID->value,
                self::PASSPORT_NUMBER->value,
                self::PLACE_OF_BIRTH->value,
                self::ADDRESS->value,
                self::MILITARY_STATUS->value,
                self::DATE_OF_BIRTH->value,
                self::RELIGION->value,
                self::MARITAL_STATUS->value,
                self::NUMBER_OF_CHILDREN->value,
                self::YEARS_OF_EXPERIENCE->value,
            ],
            EmployeeExportCategoryEnum::CONTACT => [
                self::SECONDARY_PHONE->value,
                self::WORK_EMAIL->value,
                self::PERSONAL_EMAIL->value,
                self::EMERGENCY_CONTACT_NAME->value,
                self::EMERGENCY_CONTACT_PHONE->value,
                self::EMERGENCY_CONTACT_RELATION->value,
            ],
            EmployeeExportCategoryEnum::EDUCATION => [
                self::DEGREE_TYPE->value,
                self::DEGREE_NAME->value,
                self::INSTITUTION_NAME->value,
                self::GRADUATION_YEAR->value,
            ],
            EmployeeExportCategoryEnum::EMPLOYMENT => [
                self::EMPLOYMENT_TYPE->value,
                self::LATEST_CONTRACT_START_DATE->value,
                self::LATEST_CONTRACT_DURATION->value,
                self::LATEST_CONTRACT_END_DATE->value,
            ],
            default => null,
        };
    }
    public static function getFieldsForCategoryForBulkEdit(EmployeeExportCategoryEnum $category): array
    {
        return match ($category) {
            EmployeeExportCategoryEnum::ESSENTIAL => [
                self::EMPLOYEE_CODE->value,
                self::EMPLOYEE_NAME_AR->value,
                self::EMPLOYEE_NAME_EN->value,
                self::PRIMARY_PHONE->value,
            ],
            EmployeeExportCategoryEnum::BASIC => [
                self::NATIONALITY->value,
                self::NATIONAL_ID->value,
                self::PASSPORT_NUMBER->value,
                self::GENDER->value,
                self::PLACE_OF_BIRTH->value,
                self::ADDRESS->value,
                self::MILITARY_STATUS->value,
                self::DATE_OF_BIRTH->value,
                self::RELIGION->value,
                self::MARITAL_STATUS->value,
                self::NUMBER_OF_CHILDREN->value,
                self::YEARS_OF_EXPERIENCE->value,
            ],
            EmployeeExportCategoryEnum::CONTACT => [
                self::SECONDARY_PHONE->value,
                self::WORK_EMAIL->value,
                self::PERSONAL_EMAIL->value,
                self::EMERGENCY_CONTACT_NAME->value,
                self::EMERGENCY_CONTACT_PHONE->value,
                self::EMERGENCY_CONTACT_RELATION->value,
            ],
            EmployeeExportCategoryEnum::EMPLOYMENT => [
                self::HIRING_DATE->value,
                self::EMPLOYMENT_TYPE->value,
            ],
            default => [],
        };
    }
}
