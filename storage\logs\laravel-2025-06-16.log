[2025-06-16 11:51:34] local.ERROR: Symfony\Component\HttpClient\Exception\InvalidArgumentException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php:580
Stack trace:
#0 C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php(178): Symfony\Component\HttpClient\CurlHttpClient::resolveUrl(Array, NULL, Array)
#1 C:\Herd\www\API\vendor\symfony\http-client\CurlHttpClient.php(100): Symfony\Component\HttpClient\CurlHttpClient::prepareRequest('GET', Array, Array, Array)
#2 C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php(135): Symfony\Component\HttpClient\CurlHttpClient->request('GET', '/client/feature...', Array)
#3 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#4 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#5 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#6 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#7 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#8 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#9 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#10 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#11 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#12 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#13 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#14 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#59 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#60 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#61 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#62 {main}

Next Symfony\Component\HttpClient\Psr18RequestException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php:140
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#4 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#6 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#8 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#9 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#10 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#11 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#59 {main}

Next Unleash\Client\Exception\HttpResponseException: Got invalid response code when getting features and no default bootstrap provided: unknown response status code in C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php:548
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#4 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#6 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#8 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#9 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#10 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#11 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#58 {main}  
[2025-06-16 11:51:36] local.INFO: PayslipService initialized with Browsershot {"supported_languages":["en","ar"],"browsershot_enabled":true} 
[2025-06-16 11:51:36] local.INFO: Starting payslip generation {"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"} 
[2025-06-16 11:51:36] local.INFO: Payslip generation language {"requested_language":"ar","final_language":"ar","data_keys":["company_id","payroll_id","month","year","employee_ids","print_type","print_lang"]} 
[2025-06-16 11:51:36] local.INFO: Language configuration debug {"language":"ar","langConfig":{"name":"Arabic","direction":"rtl","font":"arabic"},"direction":"rtl","full_config":{"fonts":{"arabic":{"regular":{"name":"Amiri","file":"Amiri-Regular.ttf"},"bold":{"name":"Amiri","file":"Amiri-Bold.ttf","weight":700}},"latin":{"regular":{"name":"Arial","fallback":["Helvetica","sans-serif"]},"bold":{"name":"Arial-Bold","fallback":["Helvetica-Bold","sans-serif"]}}},"default_direction":"ltr","default_language":"en","supported_languages":{"en":{"name":"English","direction":"ltr","font":"latin"},"ar":{"name":"Arabic","direction":"rtl","font":"arabic"}},"storage":{"fonts_path":"C:\\Herd\\www\\API\\storage\\fonts/Amiri/static"},"browsershot_options":{"format":"A4","margins":[20,20,20,20],"timeout":60,"wait_until_network_idle":true,"show_background":true,"temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","chrome_args":["--no-sandbox","--disable-setuid-sandbox","--disable-dev-shm-usage","--disable-gpu","--no-first-run","--disable-default-apps","--disable-features=TranslateUI","--disable-ipc-flooding-protection","--font-render-hinting=none","--disable-font-subpixel-positioning","--disable-web-security","--allow-running-insecure-content","--disable-extensions","--disable-plugins","--disable-images","--run-all-compositor-stages-before-draw","--disable-background-timer-throttling","--disable-renderer-backgrounding","--disable-backgrounding-occluded-windows","--disable-features=VizDisplayCompositor"]}}} 
[2025-06-16 11:51:36] local.INFO: Forcing temporary directory {"path":"C:\\Herd\\www\\API\\storage\\app/temp","path_exists":true,"is_writable":true} 
[2025-06-16 11:51:36] local.ERROR: Payslip generation failed {"error":"mkdir(): Permission denied","data":{"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"}} 
[2025-06-16 11:51:36] local.ERROR: Unexpected error during payslip generation {"error":"mkdir(): Permission denied","trace":"#0 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'mkdir(): Permis...', 'C:\\\\Herd\\\\www\\\\API...', 48)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'mkdir(): Permis...', 'C:\\\\Herd\\\\www\\\\API...', 48)
#2 C:\\Herd\\www\\API\\vendor\\spatie\\temporary-directory\\src\\TemporaryDirectory.php(48): mkdir('C:\\\\Windows\\\\2528...', 511, true)
#3 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(1047): Spatie\\TemporaryDirectory\\TemporaryDirectory->create()
#4 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(1230): Spatie\\Browsershot\\Browsershot->createTemporaryHtmlFile()
#5 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(892): Spatie\\Browsershot\\Browsershot->getFinalContentsUrl()
#6 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(708): Spatie\\Browsershot\\Browsershot->createPdfCommand()
#7 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(346): Spatie\\Browsershot\\Browsershot->pdf()
#8 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(232): App\\Services\\V1\\Payroll\\PayslipService->generateEmployeePDF(Array, 'Diwan', 'ar')
#9 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(133): App\\Services\\V1\\Payroll\\PayslipService->generatePDFsAndCreateZip(Object(Illuminate\\Support\\Collection), Array, 'Diwan', 'ar')
#10 C:\\Herd\\www\\API\\app\\Http\\Controllers\\Payroll\\PayslipController.php(42): App\\Services\\V1\\Payroll\\PayslipService->generateAndStorePayslips(Array)
#11 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Payroll\\PayslipController->store(Object(App\\Http\\Requests\\V1\\StorePayslipRequest))
#12 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#13 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Payroll\\PayslipController), 'store')
#14 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Herd\\www\\API\\app\\Http\\Middleware\\RequestResponseLogger.php(72): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequestResponseLogger->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Herd\\www\\API\\app\\Http\\Middleware\\CheckUserCanAccessSystem.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserCanAccessSystem->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Herd\\www\\API\\app\\Http\\Middleware\\handleAppConfigs.php(70): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\handleAppConfigs->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Herd\\www\\API\\app\\Http\\Middleware\\ThrottleRequests.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Herd\\www\\API\\app\\Http\\Middleware\\Localization.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Herd\\www\\API\\app\\Http\\Middleware\\SetUserTimezone.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user-api')
#31 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Herd\\www\\API\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\Herd\\www\\API\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Herd\\\\www\\\\API...')
#59 {main}","data":{"payroll_id":668,"month":3,"year":2025,"print_lang":"ar"},"user_id":27835} 
[2025-06-16 11:53:10] local.ERROR: Symfony\Component\HttpClient\Exception\InvalidArgumentException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php:580
Stack trace:
#0 C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php(178): Symfony\Component\HttpClient\CurlHttpClient::resolveUrl(Array, NULL, Array)
#1 C:\Herd\www\API\vendor\symfony\http-client\CurlHttpClient.php(100): Symfony\Component\HttpClient\CurlHttpClient::prepareRequest('GET', Array, Array, Array)
#2 C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php(135): Symfony\Component\HttpClient\CurlHttpClient->request('GET', '/client/feature...', Array)
#3 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#4 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#5 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#6 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#7 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#8 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#9 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#10 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#11 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#12 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#13 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#14 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#59 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#60 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#61 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#62 {main}

Next Symfony\Component\HttpClient\Psr18RequestException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php:140
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#4 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#6 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#8 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#9 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#10 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#11 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#59 {main}

Next Unleash\Client\Exception\HttpResponseException: Got invalid response code when getting features and no default bootstrap provided: unknown response status code in C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php:548
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#4 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#6 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#8 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#9 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#10 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#11 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#58 {main}  
[2025-06-16 11:53:11] local.INFO: PayslipService initialized with Browsershot {"supported_languages":["en","ar"],"browsershot_enabled":true} 
[2025-06-16 11:53:11] local.INFO: Starting payslip generation {"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"} 
[2025-06-16 11:53:12] local.INFO: Payslip generation language {"requested_language":"ar","final_language":"ar","data_keys":["company_id","payroll_id","month","year","employee_ids","print_type","print_lang"]} 
[2025-06-16 11:53:12] local.INFO: Language configuration debug {"language":"ar","langConfig":{"name":"Arabic","direction":"rtl","font":"arabic"},"direction":"rtl","full_config":{"fonts":{"arabic":{"regular":{"name":"Amiri","file":"Amiri-Regular.ttf"},"bold":{"name":"Amiri","file":"Amiri-Bold.ttf","weight":700}},"latin":{"regular":{"name":"Arial","fallback":["Helvetica","sans-serif"]},"bold":{"name":"Arial-Bold","fallback":["Helvetica-Bold","sans-serif"]}}},"default_direction":"ltr","default_language":"en","supported_languages":{"en":{"name":"English","direction":"ltr","font":"latin"},"ar":{"name":"Arabic","direction":"rtl","font":"arabic"}},"storage":{"fonts_path":"C:\\Herd\\www\\API\\storage\\fonts/Amiri/static"},"browsershot_options":{"format":"A4","margins":[20,20,20,20],"timeout":60,"wait_until_network_idle":true,"show_background":true,"temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","chrome_args":["--no-sandbox","--disable-setuid-sandbox","--disable-dev-shm-usage","--disable-gpu","--no-first-run","--disable-default-apps","--disable-features=TranslateUI","--disable-ipc-flooding-protection","--font-render-hinting=none","--disable-font-subpixel-positioning","--disable-web-security","--allow-running-insecure-content","--disable-extensions","--disable-plugins","--disable-images","--run-all-compositor-stages-before-draw","--disable-background-timer-throttling","--disable-renderer-backgrounding","--disable-backgrounding-occluded-windows","--disable-features=VizDisplayCompositor"]}}} 
[2025-06-16 11:53:12] local.ERROR: Payslip generation failed {"error":"An URL is not allow to start with file:// or file:/","data":{"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"}} 
[2025-06-16 11:53:12] local.ERROR: Unexpected error during payslip generation {"error":"An URL is not allow to start with file:// or file:/","trace":"#0 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(277): Spatie\\Browsershot\\Exceptions\\FileUrlNotAllowed::make()
#1 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(82): Spatie\\Browsershot\\Browsershot->setUrl('file:///C:\\\\Herd...')
#2 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(315): Spatie\\Browsershot\\Browsershot::url('file:///C:\\\\Herd...')
#3 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(232): App\\Services\\V1\\Payroll\\PayslipService->generateEmployeePDF(Array, 'Diwan', 'ar')
#4 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(133): App\\Services\\V1\\Payroll\\PayslipService->generatePDFsAndCreateZip(Object(Illuminate\\Support\\Collection), Array, 'Diwan', 'ar')
#5 C:\\Herd\\www\\API\\app\\Http\\Controllers\\Payroll\\PayslipController.php(42): App\\Services\\V1\\Payroll\\PayslipService->generateAndStorePayslips(Array)
#6 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Payroll\\PayslipController->store(Object(App\\Http\\Requests\\V1\\StorePayslipRequest))
#7 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#8 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Payroll\\PayslipController), 'store')
#9 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#10 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#11 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Herd\\www\\API\\app\\Http\\Middleware\\RequestResponseLogger.php(72): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequestResponseLogger->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Herd\\www\\API\\app\\Http\\Middleware\\CheckUserCanAccessSystem.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserCanAccessSystem->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Herd\\www\\API\\app\\Http\\Middleware\\handleAppConfigs.php(70): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\handleAppConfigs->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Herd\\www\\API\\app\\Http\\Middleware\\ThrottleRequests.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Herd\\www\\API\\app\\Http\\Middleware\\Localization.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Herd\\www\\API\\app\\Http\\Middleware\\SetUserTimezone.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user-api')
#26 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Herd\\www\\API\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\Herd\\www\\API\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Herd\\\\www\\\\API...')
#54 {main}","data":{"payroll_id":668,"month":3,"year":2025,"print_lang":"ar"},"user_id":27835} 
[2025-06-16 11:54:56] local.ERROR: Symfony\Component\HttpClient\Exception\InvalidArgumentException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php:580
Stack trace:
#0 C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php(178): Symfony\Component\HttpClient\CurlHttpClient::resolveUrl(Array, NULL, Array)
#1 C:\Herd\www\API\vendor\symfony\http-client\CurlHttpClient.php(100): Symfony\Component\HttpClient\CurlHttpClient::prepareRequest('GET', Array, Array, Array)
#2 C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php(135): Symfony\Component\HttpClient\CurlHttpClient->request('GET', '/client/feature...', Array)
#3 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#4 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#5 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#6 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#7 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#8 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#9 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#10 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#11 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#12 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#13 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#14 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#59 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#60 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#61 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#62 {main}

Next Symfony\Component\HttpClient\Psr18RequestException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php:140
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#4 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#6 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#8 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#9 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#10 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#11 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#59 {main}

Next Unleash\Client\Exception\HttpResponseException: Got invalid response code when getting features and no default bootstrap provided: unknown response status code in C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php:548
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#4 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#6 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#8 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#9 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#10 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#11 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#58 {main}  
[2025-06-16 11:54:57] local.INFO: PayslipService initialized with Browsershot {"supported_languages":["en","ar"],"browsershot_enabled":true} 
[2025-06-16 11:54:57] local.INFO: Starting payslip generation {"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"} 
[2025-06-16 11:54:57] local.INFO: Payslip generation language {"requested_language":"ar","final_language":"ar","data_keys":["company_id","payroll_id","month","year","employee_ids","print_type","print_lang"]} 
[2025-06-16 11:54:57] local.INFO: Language configuration debug {"language":"ar","langConfig":{"name":"Arabic","direction":"rtl","font":"arabic"},"direction":"rtl","full_config":{"fonts":{"arabic":{"regular":{"name":"Amiri","file":"Amiri-Regular.ttf"},"bold":{"name":"Amiri","file":"Amiri-Bold.ttf","weight":700}},"latin":{"regular":{"name":"Arial","fallback":["Helvetica","sans-serif"]},"bold":{"name":"Arial-Bold","fallback":["Helvetica-Bold","sans-serif"]}}},"default_direction":"ltr","default_language":"en","supported_languages":{"en":{"name":"English","direction":"ltr","font":"latin"},"ar":{"name":"Arabic","direction":"rtl","font":"arabic"}},"storage":{"fonts_path":"C:\\Herd\\www\\API\\storage\\fonts/Amiri/static"},"browsershot_options":{"format":"A4","margins":[20,20,20,20],"timeout":60,"wait_until_network_idle":true,"show_background":true,"temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","chrome_args":["--no-sandbox","--disable-setuid-sandbox","--disable-dev-shm-usage","--disable-gpu","--no-first-run","--disable-default-apps","--disable-features=TranslateUI","--disable-ipc-flooding-protection","--font-render-hinting=none","--disable-font-subpixel-positioning","--disable-web-security","--allow-running-insecure-content","--disable-extensions","--disable-plugins","--disable-images","--run-all-compositor-stages-before-draw","--disable-background-timer-throttling","--disable-renderer-backgrounding","--disable-backgrounding-occluded-windows","--disable-features=VizDisplayCompositor"]}}} 
[2025-06-16 11:54:58] local.ERROR: Payslip generation failed {"error":"An URL is not allow to start with file:// or file:/","data":{"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"}} 
[2025-06-16 11:54:58] local.ERROR: Unexpected error during payslip generation {"error":"An URL is not allow to start with file:// or file:/","trace":"#0 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(277): Spatie\\Browsershot\\Exceptions\\FileUrlNotAllowed::make()
#1 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(82): Spatie\\Browsershot\\Browsershot->setUrl('file:///C:\\\\Herd...')
#2 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(315): Spatie\\Browsershot\\Browsershot::url('file:///C:\\\\Herd...')
#3 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(232): App\\Services\\V1\\Payroll\\PayslipService->generateEmployeePDF(Array, 'Diwan', 'ar')
#4 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(133): App\\Services\\V1\\Payroll\\PayslipService->generatePDFsAndCreateZip(Object(Illuminate\\Support\\Collection), Array, 'Diwan', 'ar')
#5 C:\\Herd\\www\\API\\app\\Http\\Controllers\\Payroll\\PayslipController.php(42): App\\Services\\V1\\Payroll\\PayslipService->generateAndStorePayslips(Array)
#6 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Payroll\\PayslipController->store(Object(App\\Http\\Requests\\V1\\StorePayslipRequest))
#7 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#8 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Payroll\\PayslipController), 'store')
#9 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#10 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#11 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Herd\\www\\API\\app\\Http\\Middleware\\RequestResponseLogger.php(72): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequestResponseLogger->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Herd\\www\\API\\app\\Http\\Middleware\\CheckUserCanAccessSystem.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserCanAccessSystem->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Herd\\www\\API\\app\\Http\\Middleware\\handleAppConfigs.php(70): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\handleAppConfigs->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Herd\\www\\API\\app\\Http\\Middleware\\ThrottleRequests.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Herd\\www\\API\\app\\Http\\Middleware\\Localization.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Herd\\www\\API\\app\\Http\\Middleware\\SetUserTimezone.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user-api')
#26 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Herd\\www\\API\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\Herd\\www\\API\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Herd\\\\www\\\\API...')
#54 {main}","data":{"payroll_id":668,"month":3,"year":2025,"print_lang":"ar"},"user_id":27835} 
[2025-06-16 11:57:03] local.ERROR: Symfony\Component\HttpClient\Exception\InvalidArgumentException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php:580
Stack trace:
#0 C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php(178): Symfony\Component\HttpClient\CurlHttpClient::resolveUrl(Array, NULL, Array)
#1 C:\Herd\www\API\vendor\symfony\http-client\CurlHttpClient.php(100): Symfony\Component\HttpClient\CurlHttpClient::prepareRequest('GET', Array, Array, Array)
#2 C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php(135): Symfony\Component\HttpClient\CurlHttpClient->request('GET', '/client/feature...', Array)
#3 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#4 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#5 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#6 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#7 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#8 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#9 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#10 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#11 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#12 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#13 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#14 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#59 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#60 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#61 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#62 {main}

Next Symfony\Component\HttpClient\Psr18RequestException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php:140
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#4 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#6 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#8 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#9 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#10 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#11 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#59 {main}

Next Unleash\Client\Exception\HttpResponseException: Got invalid response code when getting features and no default bootstrap provided: unknown response status code in C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php:548
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#4 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#6 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#8 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#9 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#10 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#11 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#58 {main}  
[2025-06-16 11:57:04] local.INFO: PayslipService initialized with Browsershot {"supported_languages":["en","ar"],"browsershot_enabled":true} 
[2025-06-16 11:57:05] local.INFO: Starting payslip generation {"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"} 
[2025-06-16 11:57:05] local.INFO: Payslip generation language {"requested_language":"ar","final_language":"ar","data_keys":["company_id","payroll_id","month","year","employee_ids","print_type","print_lang"]} 
[2025-06-16 11:57:05] local.INFO: Language configuration debug {"language":"ar","langConfig":{"name":"Arabic","direction":"rtl","font":"arabic"},"direction":"rtl","full_config":{"fonts":{"arabic":{"regular":{"name":"Amiri","file":"Amiri-Regular.ttf"},"bold":{"name":"Amiri","file":"Amiri-Bold.ttf","weight":700}},"latin":{"regular":{"name":"Arial","fallback":["Helvetica","sans-serif"]},"bold":{"name":"Arial-Bold","fallback":["Helvetica-Bold","sans-serif"]}}},"default_direction":"ltr","default_language":"en","supported_languages":{"en":{"name":"English","direction":"ltr","font":"latin"},"ar":{"name":"Arabic","direction":"rtl","font":"arabic"}},"storage":{"fonts_path":"C:\\Herd\\www\\API\\storage\\fonts/Amiri/static"},"browsershot_options":{"format":"A4","margins":[20,20,20,20],"timeout":60,"wait_until_network_idle":true,"show_background":true,"temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","chrome_args":["--no-sandbox","--disable-setuid-sandbox","--disable-dev-shm-usage","--disable-gpu","--no-first-run","--disable-default-apps","--disable-features=TranslateUI","--disable-ipc-flooding-protection","--font-render-hinting=none","--disable-font-subpixel-positioning","--disable-web-security","--allow-running-insecure-content","--disable-extensions","--disable-plugins","--disable-images","--run-all-compositor-stages-before-draw","--disable-background-timer-throttling","--disable-renderer-backgrounding","--disable-backgrounding-occluded-windows","--disable-features=VizDisplayCompositor"]}}} 
[2025-06-16 11:57:05] local.ERROR: Payslip generation failed {"error":"mkdir(): Permission denied","data":{"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"}} 
[2025-06-16 11:57:05] local.ERROR: Unexpected error during payslip generation {"error":"mkdir(): Permission denied","trace":"#0 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'mkdir(): Permis...', 'C:\\\\Herd\\\\www\\\\API...', 48)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'mkdir(): Permis...', 'C:\\\\Herd\\\\www\\\\API...', 48)
#2 C:\\Herd\\www\\API\\vendor\\spatie\\temporary-directory\\src\\TemporaryDirectory.php(48): mkdir('C:\\\\Windows\\\\1847...', 511, true)
#3 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(1047): Spatie\\TemporaryDirectory\\TemporaryDirectory->create()
#4 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(1230): Spatie\\Browsershot\\Browsershot->createTemporaryHtmlFile()
#5 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(892): Spatie\\Browsershot\\Browsershot->getFinalContentsUrl()
#6 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(708): Spatie\\Browsershot\\Browsershot->createPdfCommand()
#7 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(344): Spatie\\Browsershot\\Browsershot->pdf()
#8 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(232): App\\Services\\V1\\Payroll\\PayslipService->generateEmployeePDF(Array, 'Diwan', 'ar')
#9 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(133): App\\Services\\V1\\Payroll\\PayslipService->generatePDFsAndCreateZip(Object(Illuminate\\Support\\Collection), Array, 'Diwan', 'ar')
#10 C:\\Herd\\www\\API\\app\\Http\\Controllers\\Payroll\\PayslipController.php(42): App\\Services\\V1\\Payroll\\PayslipService->generateAndStorePayslips(Array)
#11 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Payroll\\PayslipController->store(Object(App\\Http\\Requests\\V1\\StorePayslipRequest))
#12 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#13 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Payroll\\PayslipController), 'store')
#14 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Herd\\www\\API\\app\\Http\\Middleware\\RequestResponseLogger.php(72): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequestResponseLogger->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Herd\\www\\API\\app\\Http\\Middleware\\CheckUserCanAccessSystem.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserCanAccessSystem->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Herd\\www\\API\\app\\Http\\Middleware\\handleAppConfigs.php(70): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\handleAppConfigs->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Herd\\www\\API\\app\\Http\\Middleware\\ThrottleRequests.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Herd\\www\\API\\app\\Http\\Middleware\\Localization.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Herd\\www\\API\\app\\Http\\Middleware\\SetUserTimezone.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user-api')
#31 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Herd\\www\\API\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\Herd\\www\\API\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Herd\\\\www\\\\API...')
#59 {main}","data":{"payroll_id":668,"month":3,"year":2025,"print_lang":"ar"},"user_id":27835} 
[2025-06-16 11:57:23] local.ERROR: Symfony\Component\HttpClient\Exception\InvalidArgumentException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php:580
Stack trace:
#0 C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php(178): Symfony\Component\HttpClient\CurlHttpClient::resolveUrl(Array, NULL, Array)
#1 C:\Herd\www\API\vendor\symfony\http-client\CurlHttpClient.php(100): Symfony\Component\HttpClient\CurlHttpClient::prepareRequest('GET', Array, Array, Array)
#2 C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php(135): Symfony\Component\HttpClient\CurlHttpClient->request('GET', '/client/feature...', Array)
#3 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#4 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#5 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#6 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#7 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#8 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#9 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#10 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#11 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#12 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#13 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#14 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#59 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#60 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#61 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#62 {main}

Next Symfony\Component\HttpClient\Psr18RequestException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php:140
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#4 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#6 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#8 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#9 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#10 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#11 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#59 {main}

Next Unleash\Client\Exception\HttpResponseException: Got invalid response code when getting features and no default bootstrap provided: unknown response status code in C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php:548
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#4 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#6 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#8 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#9 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#10 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#11 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#58 {main}  
[2025-06-16 11:57:24] local.INFO: PayslipService initialized with Browsershot {"supported_languages":["en","ar"],"browsershot_enabled":true} 
[2025-06-16 11:57:24] local.INFO: Starting payslip generation {"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"} 
[2025-06-16 11:57:24] local.INFO: Payslip generation language {"requested_language":"ar","final_language":"ar","data_keys":["company_id","payroll_id","month","year","employee_ids","print_type","print_lang"]} 
[2025-06-16 11:57:24] local.INFO: Language configuration debug {"language":"ar","langConfig":{"name":"Arabic","direction":"rtl","font":"arabic"},"direction":"rtl","full_config":{"fonts":{"arabic":{"regular":{"name":"Amiri","file":"Amiri-Regular.ttf"},"bold":{"name":"Amiri","file":"Amiri-Bold.ttf","weight":700}},"latin":{"regular":{"name":"Arial","fallback":["Helvetica","sans-serif"]},"bold":{"name":"Arial-Bold","fallback":["Helvetica-Bold","sans-serif"]}}},"default_direction":"ltr","default_language":"en","supported_languages":{"en":{"name":"English","direction":"ltr","font":"latin"},"ar":{"name":"Arabic","direction":"rtl","font":"arabic"}},"storage":{"fonts_path":"C:\\Herd\\www\\API\\storage\\fonts/Amiri/static"},"browsershot_options":{"format":"A4","margins":[20,20,20,20],"timeout":60,"wait_until_network_idle":true,"show_background":true,"temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","chrome_args":["--no-sandbox","--disable-setuid-sandbox","--disable-dev-shm-usage","--disable-gpu","--no-first-run","--disable-default-apps","--disable-features=TranslateUI","--disable-ipc-flooding-protection","--font-render-hinting=none","--disable-font-subpixel-positioning","--disable-web-security","--allow-running-insecure-content","--disable-extensions","--disable-plugins","--disable-images","--run-all-compositor-stages-before-draw","--disable-background-timer-throttling","--disable-renderer-backgrounding","--disable-backgrounding-occluded-windows","--disable-features=VizDisplayCompositor"]}}} 
[2025-06-16 11:57:24] local.ERROR: Payslip generation failed {"error":"mkdir(): Permission denied","data":{"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"}} 
[2025-06-16 11:57:24] local.ERROR: Unexpected error during payslip generation {"error":"mkdir(): Permission denied","trace":"#0 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'mkdir(): Permis...', 'C:\\\\Herd\\\\www\\\\API...', 48)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'mkdir(): Permis...', 'C:\\\\Herd\\\\www\\\\API...', 48)
#2 C:\\Herd\\www\\API\\vendor\\spatie\\temporary-directory\\src\\TemporaryDirectory.php(48): mkdir('C:\\\\Windows\\\\1380...', 511, true)
#3 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(1047): Spatie\\TemporaryDirectory\\TemporaryDirectory->create()
#4 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(1230): Spatie\\Browsershot\\Browsershot->createTemporaryHtmlFile()
#5 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(892): Spatie\\Browsershot\\Browsershot->getFinalContentsUrl()
#6 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(708): Spatie\\Browsershot\\Browsershot->createPdfCommand()
#7 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(344): Spatie\\Browsershot\\Browsershot->pdf()
#8 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(232): App\\Services\\V1\\Payroll\\PayslipService->generateEmployeePDF(Array, 'Diwan', 'ar')
#9 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(133): App\\Services\\V1\\Payroll\\PayslipService->generatePDFsAndCreateZip(Object(Illuminate\\Support\\Collection), Array, 'Diwan', 'ar')
#10 C:\\Herd\\www\\API\\app\\Http\\Controllers\\Payroll\\PayslipController.php(42): App\\Services\\V1\\Payroll\\PayslipService->generateAndStorePayslips(Array)
#11 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Payroll\\PayslipController->store(Object(App\\Http\\Requests\\V1\\StorePayslipRequest))
#12 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#13 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Payroll\\PayslipController), 'store')
#14 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Herd\\www\\API\\app\\Http\\Middleware\\RequestResponseLogger.php(72): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequestResponseLogger->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Herd\\www\\API\\app\\Http\\Middleware\\CheckUserCanAccessSystem.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserCanAccessSystem->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Herd\\www\\API\\app\\Http\\Middleware\\handleAppConfigs.php(70): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\handleAppConfigs->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Herd\\www\\API\\app\\Http\\Middleware\\ThrottleRequests.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Herd\\www\\API\\app\\Http\\Middleware\\Localization.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Herd\\www\\API\\app\\Http\\Middleware\\SetUserTimezone.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user-api')
#31 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Herd\\www\\API\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\Herd\\www\\API\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Herd\\\\www\\\\API...')
#59 {main}","data":{"payroll_id":668,"month":3,"year":2025,"print_lang":"ar"},"user_id":27835} 
[2025-06-16 12:00:21] local.ERROR: Symfony\Component\HttpClient\Exception\InvalidArgumentException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php:580
Stack trace:
#0 C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php(178): Symfony\Component\HttpClient\CurlHttpClient::resolveUrl(Array, NULL, Array)
#1 C:\Herd\www\API\vendor\symfony\http-client\CurlHttpClient.php(100): Symfony\Component\HttpClient\CurlHttpClient::prepareRequest('GET', Array, Array, Array)
#2 C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php(135): Symfony\Component\HttpClient\CurlHttpClient->request('GET', '/client/feature...', Array)
#3 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#4 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#5 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#6 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#7 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#8 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#9 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#10 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#11 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#12 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#13 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#14 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#59 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#60 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#61 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#62 {main}

Next Symfony\Component\HttpClient\Psr18RequestException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php:140
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#4 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#6 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#8 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#9 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#10 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#11 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#59 {main}

Next Unleash\Client\Exception\HttpResponseException: Got invalid response code when getting features and no default bootstrap provided: unknown response status code in C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php:548
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#4 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#6 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#8 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#9 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#10 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#11 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#58 {main}  
[2025-06-16 12:00:22] local.INFO: PayslipService initialized with Browsershot {"supported_languages":["en","ar"],"browsershot_enabled":true} 
[2025-06-16 12:00:22] local.INFO: Starting payslip generation {"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"} 
[2025-06-16 12:00:22] local.INFO: Payslip generation language {"requested_language":"ar","final_language":"ar","data_keys":["company_id","payroll_id","month","year","employee_ids","print_type","print_lang"]} 
[2025-06-16 12:00:22] local.INFO: Language configuration debug {"language":"ar","langConfig":{"name":"Arabic","direction":"rtl","font":"arabic"},"direction":"rtl","full_config":{"fonts":{"arabic":{"regular":{"name":"Amiri","file":"Amiri-Regular.ttf"},"bold":{"name":"Amiri","file":"Amiri-Bold.ttf","weight":700}},"latin":{"regular":{"name":"Arial","fallback":["Helvetica","sans-serif"]},"bold":{"name":"Arial-Bold","fallback":["Helvetica-Bold","sans-serif"]}}},"default_direction":"ltr","default_language":"en","supported_languages":{"en":{"name":"English","direction":"ltr","font":"latin"},"ar":{"name":"Arabic","direction":"rtl","font":"arabic"}},"storage":{"fonts_path":"C:\\Herd\\www\\API\\storage\\fonts/Amiri/static"},"browsershot_options":{"format":"A4","margins":[20,20,20,20],"timeout":60,"wait_until_network_idle":true,"show_background":true,"temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","chrome_args":["--no-sandbox","--disable-setuid-sandbox","--disable-dev-shm-usage","--disable-gpu","--no-first-run","--disable-default-apps","--disable-features=TranslateUI","--disable-ipc-flooding-protection","--font-render-hinting=none","--disable-font-subpixel-positioning","--disable-web-security","--allow-running-insecure-content","--disable-extensions","--disable-plugins","--disable-images","--run-all-compositor-stages-before-draw","--disable-background-timer-throttling","--disable-renderer-backgrounding","--disable-backgrounding-occluded-windows","--disable-features=VizDisplayCompositor"]}}} 
[2025-06-16 12:00:22] local.ERROR: Browsershot PDF generation failed with temp directory setup {"error":"mkdir(): Permission denied","temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","temp_dir_exists":true,"temp_dir_writable":true,"chrome_data_dir":"C:\\Herd\\www\\API\\storage\\app/temp/chrome-data","chrome_data_dir_exists":true,"browsershot_temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp/browsershot","browsershot_temp_dir_exists":true} 
[2025-06-16 12:00:22] local.ERROR: Payslip generation failed {"error":"mkdir(): Permission denied","data":{"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"}} 
[2025-06-16 12:00:22] local.ERROR: Unexpected error during payslip generation {"error":"mkdir(): Permission denied","trace":"#0 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'mkdir(): Permis...', 'C:\\\\Herd\\\\www\\\\API...', 48)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'mkdir(): Permis...', 'C:\\\\Herd\\\\www\\\\API...', 48)
#2 C:\\Herd\\www\\API\\vendor\\spatie\\temporary-directory\\src\\TemporaryDirectory.php(48): mkdir('C:\\\\Windows\\\\7049...', 511, true)
#3 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(1047): Spatie\\TemporaryDirectory\\TemporaryDirectory->create()
#4 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(1230): Spatie\\Browsershot\\Browsershot->createTemporaryHtmlFile()
#5 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(892): Spatie\\Browsershot\\Browsershot->getFinalContentsUrl()
#6 C:\\Herd\\www\\API\\vendor\\spatie\\browsershot\\src\\Browsershot.php(708): Spatie\\Browsershot\\Browsershot->createPdfCommand()
#7 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(359): Spatie\\Browsershot\\Browsershot->pdf()
#8 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(232): App\\Services\\V1\\Payroll\\PayslipService->generateEmployeePDF(Array, 'Diwan', 'ar')
#9 C:\\Herd\\www\\API\\app\\Services\\V1\\Payroll\\PayslipService.php(133): App\\Services\\V1\\Payroll\\PayslipService->generatePDFsAndCreateZip(Object(Illuminate\\Support\\Collection), Array, 'Diwan', 'ar')
#10 C:\\Herd\\www\\API\\app\\Http\\Controllers\\Payroll\\PayslipController.php(42): App\\Services\\V1\\Payroll\\PayslipService->generateAndStorePayslips(Array)
#11 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Payroll\\PayslipController->store(Object(App\\Http\\Requests\\V1\\StorePayslipRequest))
#12 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#13 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Payroll\\PayslipController), 'store')
#14 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Herd\\www\\API\\app\\Http\\Middleware\\RequestResponseLogger.php(72): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RequestResponseLogger->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Herd\\www\\API\\app\\Http\\Middleware\\CheckUserCanAccessSystem.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserCanAccessSystem->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Herd\\www\\API\\app\\Http\\Middleware\\handleAppConfigs.php(70): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\handleAppConfigs->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Herd\\www\\API\\app\\Http\\Middleware\\ThrottleRequests.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Herd\\www\\API\\app\\Http\\Middleware\\Localization.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Herd\\www\\API\\app\\Http\\Middleware\\SetUserTimezone.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user-api')
#31 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Herd\\www\\API\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\Herd\\www\\API\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\Herd\\www\\API\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Herd\\\\www\\\\API...')
#59 {main}","data":{"payroll_id":668,"month":3,"year":2025,"print_lang":"ar"},"user_id":27835} 
[2025-06-16 12:06:46] local.ERROR: Symfony\Component\HttpClient\Exception\InvalidArgumentException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php:580
Stack trace:
#0 C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php(178): Symfony\Component\HttpClient\CurlHttpClient::resolveUrl(Array, NULL, Array)
#1 C:\Herd\www\API\vendor\symfony\http-client\CurlHttpClient.php(100): Symfony\Component\HttpClient\CurlHttpClient::prepareRequest('GET', Array, Array, Array)
#2 C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php(135): Symfony\Component\HttpClient\CurlHttpClient->request('GET', '/client/feature...', Array)
#3 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#4 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#5 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#6 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#7 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#8 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#9 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#10 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#11 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#12 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#13 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#14 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#59 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#60 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#61 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#62 {main}

Next Symfony\Component\HttpClient\Psr18RequestException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php:140
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#4 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#6 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#8 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#9 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#10 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#11 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#59 {main}

Next Unleash\Client\Exception\HttpResponseException: Got invalid response code when getting features and no default bootstrap provided: unknown response status code in C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php:548
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#4 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#6 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#8 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#9 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#10 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#11 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#58 {main}  
[2025-06-16 12:06:47] local.INFO: PayslipService initialized with Browsershot {"supported_languages":["en","ar"],"browsershot_enabled":true} 
[2025-06-16 12:06:47] local.INFO: Starting payslip generation {"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"} 
[2025-06-16 12:06:47] local.INFO: Payslip generation language {"requested_language":"ar","final_language":"ar","data_keys":["company_id","payroll_id","month","year","employee_ids","print_type","print_lang"]} 
[2025-06-16 12:06:47] local.INFO: Language configuration debug {"language":"ar","langConfig":{"name":"Arabic","direction":"rtl","font":"arabic"},"direction":"rtl","full_config":{"fonts":{"arabic":{"regular":{"name":"Amiri","file":"Amiri-Regular.ttf"},"bold":{"name":"Amiri","file":"Amiri-Bold.ttf","weight":700}},"latin":{"regular":{"name":"Arial","fallback":["Helvetica","sans-serif"]},"bold":{"name":"Arial-Bold","fallback":["Helvetica-Bold","sans-serif"]}}},"default_direction":"ltr","default_language":"en","supported_languages":{"en":{"name":"English","direction":"ltr","font":"latin"},"ar":{"name":"Arabic","direction":"rtl","font":"arabic"}},"storage":{"fonts_path":"C:\\Herd\\www\\API\\storage\\fonts/Amiri/static"},"browsershot_options":{"format":"A4","margins":[20,20,20,20],"timeout":60,"wait_until_network_idle":true,"show_background":true,"temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","chrome_args":["--no-sandbox","--disable-setuid-sandbox","--disable-dev-shm-usage","--disable-gpu","--no-first-run","--disable-default-apps","--disable-features=TranslateUI","--disable-ipc-flooding-protection","--font-render-hinting=none","--disable-font-subpixel-positioning","--disable-web-security","--allow-running-insecure-content","--disable-extensions","--disable-plugins","--disable-images","--run-all-compositor-stages-before-draw","--disable-background-timer-throttling","--disable-renderer-backgrounding","--disable-backgrounding-occluded-windows","--disable-features=VizDisplayCompositor"]}}} 
[2025-06-16 12:06:47] local.ERROR: Browsershot PDF generation failed with custom temp path {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","temp_dir_exists":true,"temp_dir_writable":true,"chrome_data_dir":"C:\\Herd\\www\\API\\storage\\app/temp/chrome-data","chrome_data_dir_exists":true} 
[2025-06-16 12:06:47] local.ERROR: Payslip generation failed {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","data":{"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"}} 
[2025-06-16 12:06:47] local.ERROR: Payslip generation runtime error {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","data":{"payroll_id":668,"month":3,"year":2025,"print_lang":"ar"},"user_id":27835} 
[2025-06-16 12:10:03] local.ERROR: Symfony\Component\HttpClient\Exception\InvalidArgumentException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php:580
Stack trace:
#0 C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php(178): Symfony\Component\HttpClient\CurlHttpClient::resolveUrl(Array, NULL, Array)
#1 C:\Herd\www\API\vendor\symfony\http-client\CurlHttpClient.php(100): Symfony\Component\HttpClient\CurlHttpClient::prepareRequest('GET', Array, Array, Array)
#2 C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php(135): Symfony\Component\HttpClient\CurlHttpClient->request('GET', '/client/feature...', Array)
#3 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#4 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#5 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#6 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#7 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#8 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#9 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#10 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#11 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#12 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#13 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#14 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#59 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#60 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#61 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#62 {main}

Next Symfony\Component\HttpClient\Psr18RequestException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php:140
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#4 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#6 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#8 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#9 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#10 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#11 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#59 {main}

Next Unleash\Client\Exception\HttpResponseException: Got invalid response code when getting features and no default bootstrap provided: unknown response status code in C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php:548
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#4 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#6 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#8 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#9 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#10 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#11 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#58 {main}  
[2025-06-16 12:10:04] local.INFO: PayslipService initialized with Browsershot {"supported_languages":["en","ar"],"browsershot_enabled":true} 
[2025-06-16 12:10:04] local.INFO: Starting payslip generation {"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"} 
[2025-06-16 12:10:04] local.INFO: Payslip generation language {"requested_language":"ar","final_language":"ar","data_keys":["company_id","payroll_id","month","year","employee_ids","print_type","print_lang"]} 
[2025-06-16 12:10:04] local.INFO: Language configuration debug {"language":"ar","langConfig":{"name":"Arabic","direction":"rtl","font":"arabic"},"direction":"rtl","full_config":{"fonts":{"arabic":{"regular":{"name":"Amiri","file":"Amiri-Regular.ttf"},"bold":{"name":"Amiri","file":"Amiri-Bold.ttf","weight":700}},"latin":{"regular":{"name":"Arial","fallback":["Helvetica","sans-serif"]},"bold":{"name":"Arial-Bold","fallback":["Helvetica-Bold","sans-serif"]}}},"default_direction":"ltr","default_language":"en","supported_languages":{"en":{"name":"English","direction":"ltr","font":"latin"},"ar":{"name":"Arabic","direction":"rtl","font":"arabic"}},"storage":{"fonts_path":"C:\\Herd\\www\\API\\storage\\fonts/Amiri/static"},"browsershot_options":{"format":"A4","margins":[20,20,20,20],"timeout":60,"wait_until_network_idle":true,"show_background":true,"temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","chrome_args":["--no-sandbox","--disable-setuid-sandbox","--disable-dev-shm-usage","--disable-gpu","--no-first-run","--disable-default-apps","--disable-features=TranslateUI","--disable-ipc-flooding-protection","--font-render-hinting=none","--disable-font-subpixel-positioning","--disable-web-security","--allow-running-insecure-content","--disable-extensions","--disable-plugins","--disable-images","--run-all-compositor-stages-before-draw","--disable-background-timer-throttling","--disable-renderer-backgrounding","--disable-backgrounding-occluded-windows","--disable-features=VizDisplayCompositor"]}}} 
[2025-06-16 12:10:04] local.INFO: Custom temp directory setup {"custom_temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","sys_get_temp_dir_before":"C:\\Windows"} 
[2025-06-16 12:10:04] local.ERROR: Browsershot PDF generation failed with custom temp path {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","temp_dir_exists":true,"temp_dir_writable":true,"chrome_data_dir":"C:\\Herd\\www\\API\\storage\\app/temp/chrome-data","chrome_data_dir_exists":true} 
[2025-06-16 12:10:04] local.ERROR: Payslip generation failed {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","data":{"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"}} 
[2025-06-16 12:10:04] local.ERROR: Payslip generation runtime error {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","data":{"payroll_id":668,"month":3,"year":2025,"print_lang":"ar"},"user_id":27835} 
[2025-06-16 12:16:00] local.ERROR: Symfony\Component\HttpClient\Exception\InvalidArgumentException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php:580
Stack trace:
#0 C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php(178): Symfony\Component\HttpClient\CurlHttpClient::resolveUrl(Array, NULL, Array)
#1 C:\Herd\www\API\vendor\symfony\http-client\CurlHttpClient.php(100): Symfony\Component\HttpClient\CurlHttpClient::prepareRequest('GET', Array, Array, Array)
#2 C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php(135): Symfony\Component\HttpClient\CurlHttpClient->request('GET', '/client/feature...', Array)
#3 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#4 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#5 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#6 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#7 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#8 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#9 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#10 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#11 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#12 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#13 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#14 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#59 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#60 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#61 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#62 {main}

Next Symfony\Component\HttpClient\Psr18RequestException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php:140
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#4 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#6 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#8 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#9 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#10 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#11 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#59 {main}

Next Unleash\Client\Exception\HttpResponseException: Got invalid response code when getting features and no default bootstrap provided: unknown response status code in C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php:548
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#4 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#6 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#8 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#9 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#10 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#11 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#58 {main}  
[2025-06-16 12:16:01] local.INFO: PayslipService initialized with Browsershot {"supported_languages":["en","ar"],"browsershot_enabled":true} 
[2025-06-16 12:16:01] local.INFO: Starting payslip generation {"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"} 
[2025-06-16 12:16:01] local.INFO: Payslip generation language {"requested_language":"ar","final_language":"ar","data_keys":["company_id","payroll_id","month","year","employee_ids","print_type","print_lang"]} 
[2025-06-16 12:16:01] local.INFO: Language configuration debug {"language":"ar","langConfig":{"name":"Arabic","direction":"rtl","font":"arabic"},"direction":"rtl","full_config":{"fonts":{"arabic":{"regular":{"name":"Amiri","file":"Amiri-Regular.ttf"},"bold":{"name":"Amiri","file":"Amiri-Bold.ttf","weight":700}},"latin":{"regular":{"name":"Arial","fallback":["Helvetica","sans-serif"]},"bold":{"name":"Arial-Bold","fallback":["Helvetica-Bold","sans-serif"]}}},"default_direction":"ltr","default_language":"en","supported_languages":{"en":{"name":"English","direction":"ltr","font":"latin"},"ar":{"name":"Arabic","direction":"rtl","font":"arabic"}},"storage":{"fonts_path":"C:\\Herd\\www\\API\\storage\\fonts/Amiri/static"},"browsershot_options":{"format":"A4","margins":[20,20,20,20],"timeout":60,"wait_until_network_idle":true,"show_background":true,"temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","chrome_args":["--no-sandbox","--disable-setuid-sandbox","--disable-dev-shm-usage","--disable-gpu","--no-first-run","--disable-default-apps","--disable-features=TranslateUI","--disable-ipc-flooding-protection","--font-render-hinting=none","--disable-font-subpixel-positioning","--disable-web-security","--allow-running-insecure-content","--disable-extensions","--disable-plugins","--disable-images","--run-all-compositor-stages-before-draw","--disable-background-timer-throttling","--disable-renderer-backgrounding","--disable-backgrounding-occluded-windows","--disable-features=VizDisplayCompositor"]}}} 
[2025-06-16 12:16:01] local.INFO: Selected temp directory {"temp_dir":"C:\\Herd\\www\\API\\temp"} 
[2025-06-16 12:16:01] local.INFO: Custom temp directory setup {"custom_temp_dir":"C:\\Herd\\www\\API\\temp","sys_get_temp_dir_before":"C:\\Windows"} 
[2025-06-16 12:16:01] local.ERROR: Browsershot PDF generation failed with custom temp path {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","temp_dir":"C:\\Herd\\www\\API\\temp","temp_dir_exists":true,"temp_dir_writable":true,"chrome_data_dir":"C:\\Herd\\www\\API\\temp/chrome-data","chrome_data_dir_exists":true} 
[2025-06-16 12:16:01] local.INFO: Attempting fallback PDF generation method  
[2025-06-16 12:16:01] local.ERROR: Fallback PDF generation also failed {"fallback_error":"mkdir(): Permission denied"} 
[2025-06-16 12:16:01] local.ERROR: Payslip generation failed {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","data":{"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"}} 
[2025-06-16 12:16:01] local.ERROR: Payslip generation runtime error {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","data":{"payroll_id":668,"month":3,"year":2025,"print_lang":"ar"},"user_id":27835} 
[2025-06-16 12:16:07] local.ERROR: Symfony\Component\HttpClient\Exception\InvalidArgumentException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php:580
Stack trace:
#0 C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php(178): Symfony\Component\HttpClient\CurlHttpClient::resolveUrl(Array, NULL, Array)
#1 C:\Herd\www\API\vendor\symfony\http-client\CurlHttpClient.php(100): Symfony\Component\HttpClient\CurlHttpClient::prepareRequest('GET', Array, Array, Array)
#2 C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php(135): Symfony\Component\HttpClient\CurlHttpClient->request('GET', '/client/feature...', Array)
#3 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#4 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#5 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#6 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#7 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#8 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#9 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#10 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#11 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#12 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#13 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#14 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#59 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#60 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#61 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#62 {main}

Next Symfony\Component\HttpClient\Psr18RequestException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php:140
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#4 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#6 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#8 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#9 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#10 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#11 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#59 {main}

Next Unleash\Client\Exception\HttpResponseException: Got invalid response code when getting features and no default bootstrap provided: unknown response status code in C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php:548
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#4 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#6 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#8 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#9 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#10 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#11 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#58 {main}  
[2025-06-16 12:16:07] local.INFO: PayslipService initialized with Browsershot {"supported_languages":["en","ar"],"browsershot_enabled":true} 
[2025-06-16 12:16:08] local.INFO: Starting payslip generation {"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"} 
[2025-06-16 12:16:08] local.INFO: Payslip generation language {"requested_language":"ar","final_language":"ar","data_keys":["company_id","payroll_id","month","year","employee_ids","print_type","print_lang"]} 
[2025-06-16 12:16:08] local.INFO: Language configuration debug {"language":"ar","langConfig":{"name":"Arabic","direction":"rtl","font":"arabic"},"direction":"rtl","full_config":{"fonts":{"arabic":{"regular":{"name":"Amiri","file":"Amiri-Regular.ttf"},"bold":{"name":"Amiri","file":"Amiri-Bold.ttf","weight":700}},"latin":{"regular":{"name":"Arial","fallback":["Helvetica","sans-serif"]},"bold":{"name":"Arial-Bold","fallback":["Helvetica-Bold","sans-serif"]}}},"default_direction":"ltr","default_language":"en","supported_languages":{"en":{"name":"English","direction":"ltr","font":"latin"},"ar":{"name":"Arabic","direction":"rtl","font":"arabic"}},"storage":{"fonts_path":"C:\\Herd\\www\\API\\storage\\fonts/Amiri/static"},"browsershot_options":{"format":"A4","margins":[20,20,20,20],"timeout":60,"wait_until_network_idle":true,"show_background":true,"temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","chrome_args":["--no-sandbox","--disable-setuid-sandbox","--disable-dev-shm-usage","--disable-gpu","--no-first-run","--disable-default-apps","--disable-features=TranslateUI","--disable-ipc-flooding-protection","--font-render-hinting=none","--disable-font-subpixel-positioning","--disable-web-security","--allow-running-insecure-content","--disable-extensions","--disable-plugins","--disable-images","--run-all-compositor-stages-before-draw","--disable-background-timer-throttling","--disable-renderer-backgrounding","--disable-backgrounding-occluded-windows","--disable-features=VizDisplayCompositor"]}}} 
[2025-06-16 12:16:08] local.INFO: Selected temp directory {"temp_dir":"C:\\Herd\\www\\API\\temp"} 
[2025-06-16 12:16:08] local.INFO: Custom temp directory setup {"custom_temp_dir":"C:\\Herd\\www\\API\\temp","sys_get_temp_dir_before":"C:\\Windows"} 
[2025-06-16 12:16:08] local.ERROR: Browsershot PDF generation failed with custom temp path {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","temp_dir":"C:\\Herd\\www\\API\\temp","temp_dir_exists":true,"temp_dir_writable":true,"chrome_data_dir":"C:\\Herd\\www\\API\\temp/chrome-data","chrome_data_dir_exists":true} 
[2025-06-16 12:16:08] local.INFO: Attempting fallback PDF generation method  
[2025-06-16 12:16:08] local.ERROR: Fallback PDF generation also failed {"fallback_error":"mkdir(): Permission denied"} 
[2025-06-16 12:16:08] local.ERROR: Payslip generation failed {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","data":{"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"}} 
[2025-06-16 12:16:08] local.ERROR: Payslip generation runtime error {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","data":{"payroll_id":668,"month":3,"year":2025,"print_lang":"ar"},"user_id":27835} 
[2025-06-16 12:19:18] local.ERROR: Symfony\Component\HttpClient\Exception\InvalidArgumentException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php:580
Stack trace:
#0 C:\Herd\www\API\vendor\symfony\http-client\HttpClientTrait.php(178): Symfony\Component\HttpClient\CurlHttpClient::resolveUrl(Array, NULL, Array)
#1 C:\Herd\www\API\vendor\symfony\http-client\CurlHttpClient.php(100): Symfony\Component\HttpClient\CurlHttpClient::prepareRequest('GET', Array, Array, Array)
#2 C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php(135): Symfony\Component\HttpClient\CurlHttpClient->request('GET', '/client/feature...', Array)
#3 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#4 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#5 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#6 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#7 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#8 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#9 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#10 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#11 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#12 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#13 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#14 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#59 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#60 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#61 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#62 {main}

Next Symfony\Component\HttpClient\Psr18RequestException: Invalid URL: scheme is missing in "/client/features". Did you forget to add "http(s)://"? in C:\Herd\www\API\vendor\symfony\http-client\Psr18Client.php:140
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(525): Symfony\Component\HttpClient\Psr18Client->sendRequest(Object(Nyholm\Psr7\Request))
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#4 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#6 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#8 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#9 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#10 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#11 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#17 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#56 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#58 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#59 {main}

Next Unleash\Client\Exception\HttpResponseException: Got invalid response code when getting features and no default bootstrap provided: unknown response status code in C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php:548
Stack trace:
#0 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(120): Unleash\Client\Repository\DefaultUnleashRepository->fetchFeatures()
#1 C:\Herd\www\API\vendor\unleash\client\src\Repository\DefaultUnleashRepository.php(102): Unleash\Client\Repository\DefaultUnleashRepository->getFeatures()
#2 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(126): Unleash\Client\Repository\DefaultUnleashRepository->findFeature('new-work-types')
#3 C:\Herd\www\API\vendor\unleash\client\src\DefaultUnleash.php(49): Unleash\Client\DefaultUnleash->findFeature('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#4 C:\Herd\www\API\app\FeatureToggles\Unleash.php(45): Unleash\Client\DefaultUnleash->isEnabled('new-work-types', Object(Unleash\Client\Configuration\UnleashContext))
#5 C:\Herd\www\API\app\Traits\V2\WorkTypesTrait.php(48): App\FeatureToggles\Unleash->getNewWorkTypesFeatureFlag(20)
#6 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\GetUserPermissionsService.php(98): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->getTypesToAppearInSchedule(20)
#7 C:\Herd\www\API\app\Services\CompanySetup\BusinessServices\VerifyMobileUserService.php(112): App\Services\CompanySetup\BusinessServices\GetUserPermissionsService->perform(Array, Object(stdClass))
#8 C:\Herd\www\API\app\Http\Controllers\CompanySetup\LoginRegisterController.php(111): App\Services\CompanySetup\BusinessServices\VerifyMobileUserService->perform(Array, Object(stdClass))
#9 [internal function]: App\Http\Controllers\CompanySetup\LoginRegisterController->verifyMobile(Array, Object(stdClass))
#10 C:\Herd\www\API\app\Http\Controllers\Controller.php(41): call_user_func_array(Array, Array)
#11 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Controller->InternalDispatcher(Object(Illuminate\Http\Request))
#12 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('InternalDispatc...', Array)
#13 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\CompanySetup\LoginRegisterController), 'InternalDispatc...')
#14 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#15 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#16 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#17 C:\Herd\www\API\app\Http\Middleware\CheckFileExtentions.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckFileExtentions->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\Herd\www\API\app\Http\Middleware\ThrottleRequests.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#21 C:\Herd\www\API\app\Http\Middleware\Localization.php(31): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#22 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\Localization->handle(Object(Illuminate\Http\Request), Object(Closure))
#23 C:\Herd\www\API\app\Http\Middleware\SetUserTimezone.php(29): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetUserTimezone->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(122): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\Http\Request), Object(Closure), 'api', Object(Closure))
#29 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle(Object(Illuminate\Http\Request), Object(Closure), 'api')
#30 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#32 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#33 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#34 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#35 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#36 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#37 C:\Herd\www\API\app\Http\Middleware\SetLocale.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\SetLocale->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#55 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#56 C:\Herd\www\API\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#57 C:\Herd\www\API\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\Herd\\www\\API...')
#58 {main}  
[2025-06-16 12:19:19] local.INFO: PayslipService initialized with Browsershot {"supported_languages":["en","ar"],"browsershot_enabled":true} 
[2025-06-16 12:19:19] local.INFO: Starting payslip generation {"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"} 
[2025-06-16 12:19:19] local.INFO: Payslip generation language {"requested_language":"ar","final_language":"ar","data_keys":["company_id","payroll_id","month","year","employee_ids","print_type","print_lang"]} 
[2025-06-16 12:19:19] local.INFO: Language configuration debug {"language":"ar","langConfig":{"name":"Arabic","direction":"rtl","font":"arabic"},"direction":"rtl","full_config":{"fonts":{"arabic":{"regular":{"name":"Amiri","file":"Amiri-Regular.ttf"},"bold":{"name":"Amiri","file":"Amiri-Bold.ttf","weight":700}},"latin":{"regular":{"name":"Arial","fallback":["Helvetica","sans-serif"]},"bold":{"name":"Arial-Bold","fallback":["Helvetica-Bold","sans-serif"]}}},"default_direction":"ltr","default_language":"en","supported_languages":{"en":{"name":"English","direction":"ltr","font":"latin"},"ar":{"name":"Arabic","direction":"rtl","font":"arabic"}},"storage":{"fonts_path":"C:\\Herd\\www\\API\\storage\\fonts/Amiri/static"},"browsershot_options":{"format":"A4","margins":[20,20,20,20],"timeout":60,"wait_until_network_idle":true,"show_background":true,"temp_dir":"C:\\Herd\\www\\API\\storage\\app/temp","chrome_args":["--no-sandbox","--disable-setuid-sandbox","--disable-dev-shm-usage","--disable-gpu","--no-first-run","--disable-default-apps","--disable-features=TranslateUI","--disable-ipc-flooding-protection","--font-render-hinting=none","--disable-font-subpixel-positioning","--disable-web-security","--allow-running-insecure-content","--disable-extensions","--disable-plugins","--disable-images","--run-all-compositor-stages-before-draw","--disable-background-timer-throttling","--disable-renderer-backgrounding","--disable-backgrounding-occluded-windows","--disable-features=VizDisplayCompositor"]}}} 
[2025-06-16 12:19:19] local.INFO: Selected temp directory {"temp_dir":"C:\\temp\\laravel_pdf"} 
[2025-06-16 12:19:19] local.INFO: Custom temp directory setup {"custom_temp_dir":"C:\\temp\\laravel_pdf","sys_get_temp_dir_before":"C:\\Windows","php_os_family":"Windows"} 
[2025-06-16 12:19:19] local.ERROR: Browsershot PDF generation failed with custom temp path {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","temp_dir":"C:\\temp\\laravel_pdf","temp_dir_exists":true,"temp_dir_writable":true,"chrome_data_dir":"C:\\temp\\laravel_pdf/chrome-data","chrome_data_dir_exists":true} 
[2025-06-16 12:19:19] local.INFO: Attempting fallback PDF generation method  
[2025-06-16 12:19:19] local.WARNING: Fallback method 1 failed {"error":"mkdir(): Permission denied"} 
[2025-06-16 12:19:19] local.ERROR: All fallback methods failed {"method1_error":"mkdir(): Permission denied","method2_error":"mkdir(): Permission denied"} 
[2025-06-16 12:19:19] local.INFO: Trying file-based PDF generation  
[2025-06-16 12:19:19] local.ERROR: File-based method also failed {"error":"The given URL `file://C:\\temp\\laravel_pdf/payslip_68500bc7db8f1.html` is not a valid URL"} 
[2025-06-16 12:19:19] local.ERROR: Fallback PDF generation also failed {"fallback_error":"PDF generation failed with all methods. Last error: The given URL `file://C:\\temp\\laravel_pdf/payslip_68500bc7db8f1.html` is not a valid URL"} 
[2025-06-16 12:19:19] local.ERROR: Payslip generation failed {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","data":{"company_id":20,"payroll_id":668,"month":3,"year":2025,"employee_ids":null,"print_type":null,"print_lang":"ar"}} 
[2025-06-16 12:19:19] local.ERROR: Payslip generation runtime error {"error":"A temporary file could not be opened to write the process output: fopen(C:\\Windows\\sf_proc_00.out.lock): Failed to open stream: Permission denied","data":{"payroll_id":668,"month":3,"year":2025,"print_lang":"ar"},"user_id":27835} 
