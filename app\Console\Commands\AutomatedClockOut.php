<?php

namespace App\Console\Commands;

use App\Services\TimeTracking\CrudServices\CicoCrudService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AutomatedClockOut extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'automated:clock:out';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    public function __construct(private CicoCrudService $cicoCrudService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        DB::beginTransaction();
        try {
            $this->cicoCrudService->automateClockOut();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
