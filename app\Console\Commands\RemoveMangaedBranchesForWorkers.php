<?php

namespace App\Console\Commands;

use App\Models\User;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RemoveMangaedBranchesForWorkers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:mangaed-branches-for-workers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        try {
            DB::beginTransaction();

            $usersWithOnePermission = User::all()->filter(function ($user) {
                $permissions = $user->getAllPermissions();

                return count($permissions) === 1;
            });

            foreach ($usersWithOnePermission as $user) {
                if ($user->employee->branches->count() === 0) {
                    continue;
                }

                $user->employee->branches()->detach();
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            //// dd($e);
            \Sentry\captureException($e);
            Log::info($e);
        }
    }
}
