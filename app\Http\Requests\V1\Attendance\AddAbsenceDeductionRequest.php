<?php

namespace App\Http\Requests\V1\Attendance;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;

class AddAbsenceDeductionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'policy_name' => 'required|string',
            'absent_deduction_days' => 'required|numeric|min:0',
            'absent_deduction_days_without_permission' => 'required|numeric|min:0',
            'title_ids' => 'required|array',
            'title_ids.*' => [
                'required',
                'integer',
                (new Exists('titles', 'id'))->where(function ($query) {
                    $query->where('company_id', auth()->user()->company_id)
                        ->whereNull('deleted_at');
                }),
            ],
        ];

        return $rules;
    }
}
