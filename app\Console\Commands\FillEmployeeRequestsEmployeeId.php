<?php

namespace App\Console\Commands;

use App\Models\EmployeeRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FillEmployeeRequestsEmployeeId extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fill-employee-requests-employee-id';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $employeeRequests = EmployeeRequest::with('requestable')->get();
            echo ' Employee Requests:'.count($employeeRequests)."\n";

            foreach ($employeeRequests as $request) {
                // echo "Cloning Employee Request ID: " . $request->id . " with request type " .$request->requestable_type . "\n";
                if (! $request->requestable) {
                    echo "Employee Requests Requestable not found\n";
                    $request->delete();

                    continue;
                }
                if (! $request->employee_id) {
                    // echo "Employee Requests Employee ID not filled\n";
                    $this->addEmployeeIdToEmployeeRequests($request);
                }
                if (! $request->date) {
                    // echo "Employee Requests Date not filled\n";
                    $this->addDateToEmployeeRequests($request);
                }
            }

            echo "Employee Requests Employee ID filled successfully\n";
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            // dd($e);
            $this->error($e->getMessage());
        }
    }

    public function addEmployeeIdToEmployeeRequests($employeeRequest
    ) {
        $employeeRequest->employee_id = $employeeRequest->requestable->employee_id;
        $employeeRequest->save();
    }

    public function addDateToEmployeeRequests($employeeRequest
    ) {
        if ($employeeRequest->requestable_type == 'employee_leave_request') {
            $employeeRequest->date = $employeeRequest->requestable->created_at;
            $employeeRequest->save();
        } else {
            $employeeRequest->date = $employeeRequest->requestable->date;
            $employeeRequest->save();
        }

    }
}
