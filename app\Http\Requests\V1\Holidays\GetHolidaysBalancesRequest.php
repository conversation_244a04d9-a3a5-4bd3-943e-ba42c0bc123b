<?php

namespace App\Http\Requests\V1\Holidays;

use Illuminate\Foundation\Http\FormRequest;

class GetHolidaysBalancesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'branch_ids' => [
                'array',
            ],
            'branch_ids.*' => [
                'integer',
                'exists:branches,id',
            ],
            'department_ids' => [
                'array',
            ],
            'department_ids.*' => [
                'integer',
                'exists:departments,id',
            ],
            'title_ids' => [
                'array',
            ],
            'title_ids.*' => [
                'integer',
                'exists:titles,id',
            ],
            'status' => [
                'in:pending,resolved',
            ],
            'search_value' => [
                'min:1',
                'max:30',
            ],
            'holiday_ids' => [
                'array',
            ],
            'holiday_ids.*' => [
                'integer',
                'exists:public_holidays,id',
            ],
            'is_custom' => [
                'boolean',
                'nullable',
            ],
            'page' => [
                'integer',
                'min:1',
            ],
            'page_size' => [
                'integer',
                'min:0',
            ],
            'sort_by' => [
                'nullable',
                'array',
            ],
            'sort_by.*' => [
                'array',
            ],
            'sort_by.*.field' => [
                'string',
            ],
            'sort_by.*.order' => [
                'boolean',
            ],
        ];
    }
}
