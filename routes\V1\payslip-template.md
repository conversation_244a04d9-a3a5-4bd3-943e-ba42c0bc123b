Please update the payslip blade template structure and style with the following:
1- Header: 
    - row one (company_name, bluworks logo), justify-between
2- line break
3- section one:
    - row one (payslip month)
    - row two (employee name)
    - row three (code, title)
4- line break
5- section two (iterative): 
    - row one (category name (bold), amount_keyword (semi bold)), justify-between
    - dashed line break 
    - row two (iterative) (component name  (bold), real amount), justify-between
    - final row (total keyword, total amount  (bold)), justify-between, with background gray, opacity 60%
6- section three
    - row one (title (taxes & social insurance) (bold), amount (semi bold))
    - row two (taxes keyword, -amount)
    - row three (social insurance keyword, -amount)
    - final row (total keyword, total amount  (bold)), justify-between, with background gray, opacity 60%

6- section four:
    - row one (net pay keyword, salary), justify-between, with background gray, opacity 30%
7- section five:
    - row one (employee signture: box for signature)
