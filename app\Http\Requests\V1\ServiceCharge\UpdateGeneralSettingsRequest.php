<?php

namespace App\Http\Requests\V1\ServiceCharge;

use App\Enums\ServiceCharge\ServiceChargeApplicationEnum;
use App\Rules\TitleIdRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateGeneralSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'is_taxable' => [
                'required',
                'boolean',
            ],
            'application_policy' => [
                'required',
                'string',
                new Enum(ServiceChargeApplicationEnum::class),
            ],
            'title_ids' => [
                'required',
                'array',
            ],
            'title_ids.*' => [
                'integer',
                new TitleIdRule,
            ],
        ];

    }
}
