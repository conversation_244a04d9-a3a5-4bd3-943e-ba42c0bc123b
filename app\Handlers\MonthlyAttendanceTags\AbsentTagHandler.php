<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Models\Timecard;
use Illuminate\Support\Carbon;

class AbsentTagHandler implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        // $absentTag = $employeeAttendance->entityTags->where('tag', 'absent')->first();
        return [
            'name' => $employeeAttendance->name ?? $employeeAttendance->shift?->name ?? $employeeAttendance->timecardType->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($employeeAttendance->from)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        return isset($tags[$employeeId]['tags']['absent']) ? $tags[$employeeId]['tags']['absent']['count'] + 1 : 1;
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return $employeeAttendance instanceof Timecard && is_null($employeeAttendance->attendance) && $employeeAttendance->entityTags->pluck('tag')->contains($tag);
    }

    public function handleTagUnit(): string
    {
        return 'days';
    }
}
