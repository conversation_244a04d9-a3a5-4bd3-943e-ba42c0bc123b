<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Employee;
use App\Models\EmployeeLeaveRequest;
use App\Repositories\Repository;
use App\Services\LeaveManagement\BusinessServices\BalanceAggregatorService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixLeaveBalances extends Command
{
    public function __construct(
        private BalanceAggregatorService $balanceAggregatorService
    ) {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:leaves:balances';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
        DB::beginTransaction();
        try {
            $leaves = EmployeeLeaveRequest::where('status', 'approved')->get();
            foreach ($leaves as $leave) {

                $balanceEntity = $employeeLeaveBalanceRepository->getBalanceOfLeave($leave);
                $dummyOutput = new \stdClass;
                $employee = Employee::find($leave->employee_id);
                if (isset($employee) && isset($balanceEntity)) {
                    $this->balanceAggregatorService->perform(['id' => $balanceEntity->id,
                        'employee_id' => $leave->employee_id], $dummyOutput);
                    $balanceEntity->balance = max(0, $balanceEntity->balance);
                    $balanceEntity->save();
                }
            }
            DB::commit();
        } catch (Exception $e) {

            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
            // dd($e);
        }
    }
}
