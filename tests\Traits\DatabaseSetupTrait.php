<?php

namespace Tests\Traits;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

trait DatabaseSetupTrait
{
    /**
     * Tables that were modified during tests
     */
    protected $modifiedTables = [];

    /**
     * Disable foreign key constraints
     */
    public function disableForeignKeyConstraints()
    {
        Schema::disableForeignKeyConstraints();
    }

    /**
     * Enable foreign key constraints
     */
    public function enableForeignKeyConstraints()
    {
        Schema::enableForeignKeyConstraints();
    }

    /**
     * Track that a table was modified
     */
    public function trackTable($tableName)
    {
        if (!in_array($tableName, $this->modifiedTables)) {
            $this->modifiedTables[] = $tableName;
        }
    }

    /**
     * Truncate a specific table
     */
    public function truncateTable($table)
    {
        if (Schema::hasTable($table)) {
            DB::table($table)->truncate();
            $this->trackTable($table);
        }
    }

    /**
     * Set up the database before each test
     */
    public function setUpDatabase()
    {
        $this->disableForeignKeyConstraints();
        $this->modifiedTables = [];
    }

    /**
     * Clean up the database after test
     */
    public function tearDownDatabase()
    {
        // Only cleanup tables that were actually modified
        $this->cleanUpModifiedTables();
        $this->enableForeignKeyConstraints();
    }
    
    /**
     * Clean up only the tables that were modified during the test
     */
    protected function cleanUpModifiedTables()
    {
        $this->disableForeignKeyConstraints();
        
        // Delete records from join tables first
        $joinTables = array_filter($this->modifiedTables, function($table) {
            return strpos($table, '_') !== false; // Simple heuristic for identifying join tables
        });
        
        foreach ($joinTables as $table) {
            DB::table($table)->delete();
        }
        
        // Then delete from main tables
        $mainTables = array_filter($this->modifiedTables, function($table) use ($joinTables) {
            return !in_array($table, $joinTables);
        });
        
        foreach ($mainTables as $table) {
            DB::table($table)->delete();
        }
        
        $this->enableForeignKeyConstraints();
    }
} 