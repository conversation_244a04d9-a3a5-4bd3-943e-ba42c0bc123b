<?php

namespace App\CsvEntity;

use Maat<PERSON>bsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class EmployeeLeavesCsvEntity implements WithColumnFormatting, WithCustomValueBinder, WithHeadingRow, WithHeadings
{
    public function headings(): array
    {
        return [
            'Code',
            'Employee First Name',
            'Employee Second Name',
            'Date of Leave',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A0:A10' => NumberFormat::FORMAT_TEXT,
            'B0:B10' => NumberFormat::FORMAT_TEXT,
            'C0:C10' => NumberFormat::FORMAT_TEXT,
            'D0:D10' => NumberFormat::FORMAT_TEXT,
        ];
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
    }

    public function mapCsvInput($inputArray): array
    {
        $mappedLeave['employee_number'] = $inputArray['code'];
        $mappedLeave['first_name'] = $inputArray['employee_first_name'];
        $mappedLeave['second_name'] = $inputArray['employee_second_name'];
        $mappedLeave['date'] = $inputArray['date_of_leave'];

        return $mappedLeave;
    }
}
