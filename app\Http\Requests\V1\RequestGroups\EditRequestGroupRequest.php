<?php

namespace App\Http\Requests\V1\RequestGroups;

use App\Rules\IsArabic;
use App\Rules\IsEnglish;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;

class EditRequestGroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name_en' => ['string', new IsEnglish],
            'name_ar' => ['string', new IsArabic],
            'title_ids' => 'array',
            'title_ids.*' => [
                'integer',
                (new Exists('titles', 'id'))->where(function ($query) {
                    $query->where('company_id', auth()->user()->company_id)
                        ->whereNull('deleted_at');
                }),
            ],
        ];

    }
}
