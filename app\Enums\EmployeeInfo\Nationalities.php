<?php

namespace App\Enums\EmployeeInfo;

enum Nationalities: string
{
    case AFGHAN = 'afghan';
    case ALBANIAN = 'albanian';
    case ALGERIAN = 'algerian';
    case AMERICAN = 'american';
    case ANDORRAN = 'andorran';
    case ANGOLAN = 'angolan';
    case ANTIGUAN_BARBUDA = 'antiguan_barbuda';
    case ARGENTINE = 'argentine';
    case ARMENIAN = 'armenian';
    case AUSTRALIAN = 'australian';
    case AUSTRIAN = 'austrian';
    case AZERBAIJANI = 'azerbaijani';
    case BAHAMIAN = 'bahamian';
    case BAHRAINI = 'bahraini';
    case BANGLADESHI = 'bangladeshi';
    case BARBADIAN = 'barbadian';
    case BELARUSIAN = 'belarusian';
    case BELGIAN = 'belgian';
    case BELIZIAN = 'belizian';
    case BENINESE = 'beninese';
    case BHUTANESE = 'bhutanese';
    case BOLIVIAN = 'bolivian';
    case BOSNIAN = 'bosnian';
    case BOTSWANAN = 'botswanan';
    case BRAZILIAN = 'brazilian';
    case BRITISH = 'british';
    case BRUNEIAN = 'bruneian';
    case BULGARIAN = 'bulgarian';
    case BURKINABE = 'burkinabe';
    case BURUNDIAN = 'burundian';
    case CABO_VERDEAN = 'cabo_verdean';
    case CAMBODIAN = 'cambodian';
    case CAMEROONIAN = 'cameroonian';
    case CANADIAN = 'canadian';
    case CENTRAL_AFRICAN = 'central_african';
    case CHADIAN = 'chadian';
    case CHILEAN = 'chilean';
    case CHINESE = 'chinese';
    case COLOMBIAN = 'colombian';
    case COMORIAN = 'comorian';
    case CONGOLESE = 'congolese';
    case COSTA_RICAN = 'costa_rican';
    case CROATIAN = 'croatian';
    case CUBAN = 'cuban';
    case CYPRIOT = 'cypriot';
    case CZECH = 'czech';
    case DANISH = 'danish';
    case DJIBOUTIAN = 'djiboutian';
    case DOMINICAN = 'dominican';
    case DUTCH = 'dutch';
    case EAST_TIMORESE = 'east_timorese';
    case ECUADOREAN = 'ecuadorean';
    case EGYPTIAN = 'egyptian';
    case ERITREAN = 'eritrean';
    case ESTONIAN = 'estonian';
    case ETHIOPIAN = 'ethiopian';
    case FIJIAN = 'fijian';
    case FINNISH = 'finnish';
    case FRENCH = 'french';
    case GABONESE = 'gabonese';
    case GAMBIAN = 'gambian';
    case GEORGIAN = 'georgian';
    case GERMAN = 'german';
    case GHANAIAN = 'ghanaian';
    case GREEK = 'greek';
    case GRENADIAN = 'grenadian';
    case GUATEMALAN = 'guatemalan';
    case GUINEAN = 'guinean';
    case GUYANESE = 'guyanese';
    case HAITIAN = 'haitian';
    case HONDURAN = 'honduran';
    case HUNGARIAN = 'hungarian';
    case ICELANDER = 'icelander';
    case INDIAN = 'indian';
    case INDONESIAN = 'indonesian';
    case IRANIAN = 'iranian';
    case IRAQI = 'iraqi';
    case IRISH = 'irish';
    case ISRAELI = 'israeli';
    case ITALIAN = 'italian';
    case JAMAICAN = 'jamaican';
    case JAPANESE = 'japanese';
    case JORDANIAN = 'jordanian';
    case KAZAKH = 'kazakh';
    case KENYAN = 'kenyan';
    case KIRIBATI = 'kiribati';
    case KOREAN = 'korean';
    case KUWAITI = 'kuwaiti';
    case KYRGYZ = 'kyrgyz';
    case LAOTIAN = 'laotian';
    case LATVIAN = 'latvian';
    case LEBANESE = 'lebanese';
    case LESOTHAN = 'lesothan';
    case LIBERIAN = 'liberian';
    case LIBYAN = 'libyan';
    case LIECHTENSTEIN = 'liechtenstein';
    case LITHUANIAN = 'lithuanian';
    case LUXEMBOURGISH = 'luxembourgish';
    case MACEDONIAN = 'macedonian';
    case MALAGASY = 'malagasy';
    case MALAWIAN = 'malawian';
    case MALAYSIAN = 'malaysian';
    case MALDIVIAN = 'maldivian';
    case MALIAN = 'malian';
    case MALTA = 'malta';
    case MAURITIAN = 'mauritian';
    case MEXICAN = 'mexican';
    case MOLDOVAN = 'moldovan';
    case MONGOLIAN = 'mongolian';
    case MOROCCAN = 'moroccan';
    case MOZAMBICAN = 'mozambican';
    case NAMIBIAN = 'namibian';
    case NEPALESE = 'nepalese';
    case NEW_ZEALAND = 'new_zealand';
    case NIGERIAN = 'nigerian';
    case NORTH_KOREAN = 'north_korean';
    case NORWEGIAN = 'norwegian';
    case OMANI = 'omani';
    case PAKISTANI = 'pakistani';
    case PANAMANIAN = 'panamanian';
    case PERUVIAN = 'peruvian';
    case PHILIPPINE = 'philippine';
    case POLISH = 'polish';
    case PORTUGUESE = 'portuguese';
    case QATARI = 'qatari';
    case ROMANIAN = 'romanian';
    case RUSSIAN = 'russian';
    case RWANDAN = 'rwandan';
    case SAUDI = 'saudi';
    case SENEGALISE = 'senegalise';
    case SINGAPOREAN = 'singaporean';
    case SOUTH_AFRICAN = 'south_african';
    case SOUTH_KOREAN = 'south_korean';
    case SPANISH = 'spanish';
    case SRI_LANKAN = 'sri_lankan';
    case SWEDISH = 'swedish';
    case SWISS = 'swiss';
    case SYRIAN = 'syrian';
    case TAIWANESE = 'taiwanese';
    case TAJIK = 'tajik';
    case TANZANIAN = 'tanzanian';
    case THAI = 'thai';
    case TUNISIAN = 'tunisian';
    case TURKISH = 'turkish';
    case UKRAINIAN = 'ukrainian';
    case URUGUAYAN = 'uruguayan';
    case VIETNAMESE = 'vietnamese';
    case YEMENI = 'yemeni';
    case ZIMBABWEAN = 'zimbabwean';

    public static function all(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    public static function getTranslatedValues(): array
    {
        return array_map(fn ($case) => trans('employeeexport.' . $case->value), self::cases());
    }

    public static function getLabel(self $value): string
    {
        return trans('employeeexport.' . $value->value);
    }

    public static function getFormattedValue(?string $nationality): string
    {
        if (empty($nationality)) {
            return '';
        }

        foreach (self::cases() as $case) {
            if ($case->value === strtolower($nationality)) {
                return self::getLabel($case);
            }
        }

        return $nationality;
    }

    public static function getLegacyValues(): array
    {
        return [
            'أفغاني - Afghan',
            'ألباني - Albanian',
            'جزائري - Algerian',
            'أمريكي - American',
            'أندوري - Andorran',
            'أنغولي - Angolan',
            'أنتيغواني وباربودي - Antiguan and Barbudan',
            'أرجنتيني - Argentine',
            'أرميني - Armenian',
            'أسترالي - Australian',
            'نمساوي - Austrian',
            'أذربيجاني - Azerbaijani',
            'باهامي - Bahamian',
            'بحريني - Bahraini',
            'بنغالي - Bangladeshi',
            'بربادوسي - Barbadian',
            'بيلاروسي - Belarusian',
            'بلجيكي - Belgian',
            'بليزي - Belizian',
            'بنيني - Beninese',
            'بوتاني - Bhutanese',
            'بوليفي - Bolivian',
            'بوسني - Bosnian',
            'بوتسواني - Botswanan',
            'برازيلي - Brazilian',
            'بريطاني - British',
            'برونايي - Bruneian',
            'بلغاري - Bulgarian',
            'بوركيني - Burkinabe',
            'بوروندي - Burundian',
            'كابو فيردي - Cabo Verdean',
            'كامبودي - Cambodian',
            'كاميروني - Cameroonian',
            'كندي - Canadian',
            'إفريقي وسط - Central African',
            'تشادي - Chadian',
            'تشيلي - Chilean',
            'صيني - Chinese',
            'كولومبي - Colombian',
            'جزر القمر - Comorian',
            'كونغولي - Congolese',
            'كوستاريكي - Costa Rican',
            'كرواتي - Croatian',
            'كوبي - Cuban',
            'قبرصي - Cypriot',
            'تشيكي - Czech',
            'دنماركي - Danish',
            'جيبوتي - Djiboutian',
            'دومينيكاني - Dominican',
            'هولندي - Dutch',
            'تيموري شرقي - East Timorese',
            'إكوادوري - Ecuadorean',
            'مصري - Egyptian',
            'إريتري - Eritrean',
            'إستوني - Estonian',
            'إثيوبي - Ethiopian',
            'فيجي - Fijian',
            'فنلندي - Finnish',
            'فرنسي - French',
            'غابوني - Gabonese',
            'غامبي - Gambian',
            'جورجي - Georgian',
            'ألماني - German',
            'غاني - Ghanaian',
            'يوناني - Greek',
            'غرينادي - Grenadian',
            'غواتيمالي - Guatemalan',
            'غيني - Guinean',
            'غياني - Guyanese',
            'هايتي - Haitian',
            'هندوراسي - Honduran',
            'هنغاري - Hungarian',
            'آيسلندي - Icelander',
            'هندي - Indian',
            'إندونيسي - Indonesian',
            'إيراني - Iranian',
            'عراقي - Iraqi',
            'أيرلندي - Irish',
            'إسرائيلي - Israeli',
            'إيطالي - Italian',
            'جامايكي - Jamaican',
            'ياباني - Japanese',
            'أردني - Jordanian',
            'كازاخي - Kazakh',
            'كيني - Kenyan',
            'كيريباتي - Kiribati',
            'كوري - Korean',
            'كويتي - Kuwaiti',
            'قيرغيزستاني - Kyrgyz',
            'لاوسي - Laotian',
            'لاتفي - Latvian',
            'لبناني - Lebanese',
            'ليسوتوي - Lesothan',
            'ليبيري - Liberian',
            'ليبي - Libyan',
            'ليختنشتايني - Liechtenstein',
            'ليتواني - Lithuanian',
            'لوكسمبورغي - Luxembourgish',
            'مقدوني - Macedonian',
            'ملاجي - Malagasy',
            'ملوي - Malawian',
            'ماليزي - Malaysian',
            'مالديفي - Maldivian',
            'مالي - Malian',
            'مالطا - Malta',
            'موريشي - Mauritian',
            'مكسيكي - Mexican',
            'مولدافي - Moldovan',
            'منغولي - Mongolian',
            'مغربي - Moroccan',
            'موزمبيقي - Mozambican',
            'ناميبي - Namibian',
            'نيبالي - Nepalese',
            'نيوزيلندي - New Zealand',
            'نيجيري - Nigerian',
            'كوري شمالي - North Korean',
            'نرويجي - Norwegian',
            'عماني - Omani',
            'باكستاني - Pakistani',
            'بنمي - Panamanian',
            'بيروفي - Peruvian',
            'فلبيني - Philippine',
            'بولندي - Polish',
            'برتغالي - Portuguese',
            'قطري - Qatari',
            'روماني - Romanian',
            'روسي - Russian',
            'رواندي - Rwandan',
            'سعودي - Saudi',
            'سنغالي - Senegalise',
            'سنغافوري - Singaporean',
            'جنوب إفريقي - South African',
            'كوريا الجنوبية - South Korean',
            'إسباني - Spanish',
            'سريلانكي - Sri Lankan',
            'سويدي - Swedish',
            'سويسري - Swiss',
            'سوري - Syrian',
            'تايواني - Taiwanese',
            'طاجيكي - Tajik',
            'تنزاني - Tanzanian',
            'تايلاندي - Thai',
            'تونسي - Tunisian',
            'تركي - Turkish',
            'أوكراني - Ukrainian',
            'أوروغوياني - Uruguayan',
            'فيتنامي - Vietnamese',
            'يمني - Yemeni',
            'زيمبابوي - Zimbabwean',
        ];
    }

}
