<?php

namespace App\Http\Resources\V1\WorkFlows;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ScopeResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {

        return [
            'id' => $this->id,
            'key' => $this->key,
            'name' => ['en' => $this->name_en, 'ar' => $this->name_ar],
            'description' => ['en' => $this->description_en, 'ar' => $this->description_ar],

        ];
    }
}
