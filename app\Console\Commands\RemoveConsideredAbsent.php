<?php

namespace App\Console\Commands;

use App\Models\Employee;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RemoveConsideredAbsent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:considered-absent {company_id} {from}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $employees = Employee::withWhereHas('attendance.entityTags', function ($q) {
                $q->where('tag', 'considered_absent')->whereDate('created_at', '>=', $this->argument('from'));
            })->where('company_id', $this->argument('company_id'))->get();

            foreach ($employees as $employee) {
                $employee->attendance->each(function ($attendance) {
                    $attendance->entityTags->each(function ($tag) {
                        if ($tag->tag == 'considered_absent') {
                            $tag->delete();
                        }
                    });
                });
            }

            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::error($e);
            DB::rollBack();
        }
    }
}
