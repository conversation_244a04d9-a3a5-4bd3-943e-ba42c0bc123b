<?php

namespace App\Console\Commands;

use App\Models\EmployeeLeaveBalance;
use App\Models\EmployeeLeaveRequest;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddDayAsLeaveForCompany extends Command
{
    protected $signature = 'app:add-leave-to-company 
        {day : Date in YYYY-MM-DD format} 
        {leave_type_id : Company leave type ID}';

    protected $description = 'Adds a day as leave for all employees with active balances';

    public function handle()
    {
        $day = $this->argument('day');
        $leaveTypeId = $this->argument('leave_type_id');

        // Validate date format
        try {
            $date = Carbon::parse($day);
        } catch (\Exception $e) {
            $this->error('Invalid date format. Please use YYYY-MM-DD.');

            return 1;
        }

        $this->info("Processing leave on {$day}...");

        // Get eligible employees with their employee relationship
        $employeesBalances = EmployeeLeaveBalance::where('company_leave_type_id', $leaveTypeId)
            ->whereDate('start', '<=', $day)
            ->whereDate('end', '>=', $day)
            ->with('employee')
            ->get();

        if ($employeesBalances->isEmpty()) {
            $this->warn('No employees found with active leave balances for this date.');

            return 0;
        }

        $this->info("Found {$employeesBalances->count()} employees to process.");

        DB::beginTransaction();

        try {
            foreach ($employeesBalances as $balance) {
                // Update leave balance
                $newBalance = $balance->balance - 8;
                $balance->update(['balance' => $newBalance]);

                // Create leave request
                EmployeeLeaveRequest::create([
                    'company_leave_type_id' => $balance->company_leave_type_id,
                    'company_leave_type_policy_id' => $balance->company_leave_type_policy_id,
                    'employee_id' => $balance->employee->id,
                    'status' => 'approved',
                    'from' => $date->copy()->startOfDay(),
                    'to' => $date->copy()->endOfDay(),
                    'net_quantity' => 8,
                    'type' => 'prorated',
                    'branch_id' => $balance->employee->branch_id,
                ]);

                $this->info("Processed employee {$balance->employee->id} - New balance: {$newBalance}");
            }

            DB::commit();
            $this->info("\nSuccessfully processed all employees. Transaction committed.");
            $this->info("Total affected employees: {$employeesBalances->count()}");

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("\nError processing employees: ".$e->getMessage());
            $this->error('All changes have been rolled back.');

            return 1;
        }

        return 0;
    }
}
