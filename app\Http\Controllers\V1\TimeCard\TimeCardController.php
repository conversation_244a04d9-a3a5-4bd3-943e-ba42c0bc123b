<?php

namespace App\Http\Controllers\V1\TimeCard;

use App\DomainData\TimecardDto;
use App\Factories\V1\NextSlotFactory;
use App\Http\Controllers\NewController;
use App\Http\Requests\DateRangeRequest;
use App\Http\Requests\GetWorkerAppAttendanceByFilterRequest;
use App\Http\Requests\V1\Attendance\AssignShiftToOffShiftRequest;
use App\Http\Requests\V1\Attendance\GetNextSlotUpdatedRequest;
use App\Http\Resources\TimeCard\WorkerNextSlotResource;
use App\Http\Resources\V1\WorkerApp\WorkerAttendanceCollection;
use App\Http\Resources\V1\WorkerApp\WorkerAttendanceResource;
use App\Http\Resources\WorkerScheduleCollection;
use App\Models\EmptyModel;
use App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService;
use App\Services\Schedule\CrudServices\RestEmployeeCrudService;
use App\Services\TimeTracking\BusinessServices\GetCicosFilterService;
use App\Services\TimeTracking\BusinessServices\GetTimecardsFilterService;
use App\Services\TimeTracking\CrudServices\CicoCrudService;
use App\Services\TimeTracking\CrudServices\TimecardCrudService;
use App\Services\V1\Attendance\TimeCardsService;
use App\Traits\V1\LogTrait;
use App\Traits\V2\WorkTypesTrait;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TimeCardController extends NewController
{
    use LogTrait, TimecardDto, WorkTypesTrait;

    public function __construct(
        private TimecardCrudService $timeCardCrudService,
        private RestEmployeeCrudService $restEmployeeCrudService,
        private CicoCrudService $cicoCrudService,
        private GetTimecardsFilterService $getTimecardsFilterService,
        private EmployeeLeaveRequestCrudService $employeeLeaveRequestCrudService,
        private GetCicosFilterService $getCicosFilterService,
        private TimeCardsService $timeCardsService
    ) {}

    public function getNextSlot()
    {
        $branchId = config('globals.branchId');
        $employeeId = auth()->user()->employee_id;
        $timeCard = $this->timeCardCrudService->getTimeCardForWorker($employeeId, $branchId);
        $restDay = $this->getRestDays($employeeId);
        $lastUnverifiedCico = $this->cicoCrudService->getLastUnverifiedCi($employeeId);
        $unverifiedClockInDate = $lastUnverifiedCico?->date;
        $unverifiedClockInId = $lastUnverifiedCico?->id;

        return $this->determineWorkerNextSlot($timeCard, $restDay, $unverifiedClockInDate, $unverifiedClockInId);
    }

    public function getWorkerSchedule(DateRangeRequest $request)
    {
        $branchId = config('globals.branchId');
        $employeeId = auth()->user()->employee_id;
        $from = $request->input('from');
        $to = $request->input('to');
        $timeCards = $this->timeCardCrudService->getWorkerSchedule($employeeId, $branchId, $from, $to);
        $restDays = $this->employeeLeaveRequestCrudService->getEmployeeApprovedLeaves($employeeId, $from, $to);
        $combinedCollection = $timeCards->merge($restDays);

        return new WorkerScheduleCollection($combinedCollection->sortBy('from'));
    }

    public function getWorkerAttendance(GetWorkerAppAttendanceByFilterRequest $request)
    {
        $data = $this->getData($request);
        $unVerified = $this->cicoCrudService->getUnverifiedClockInsInDateRangeForEmployee($data);
        $verified = $this->getTimecardsFilterService->getVerifiedAttendance($data);
        $combinedCollection = $verified->merge($unVerified);
        $tag = $request->input('tag');
        if (isset($tag)) {
            $combinedCollection = $combinedCollection->filter(function ($item) {
                return new WorkerAttendanceResource($item);
            });
        }

        return new WorkerAttendanceCollection($combinedCollection->sortBy('from'));
    }

    /**
     * @return JsonResponse
     */
    private function determineWorkerNextSlot($timeCard, $restDay, $unverifiedClockInDate, $unverifiedClockInId)
    {
        if ($timeCard) {
            return $this->processTimeCardSlot($timeCard, $restDay, $unverifiedClockInDate, $unverifiedClockInId);
        } elseif ($restDay) {
            return $this->processRestDaySlot($restDay, $unverifiedClockInDate, $unverifiedClockInId);
        } elseif ($unverifiedClockInId) {
            return $this->processUnverifiedSlot($unverifiedClockInDate, $unverifiedClockInId);
        }

        return getResponseStructure(['data' => null],
            HttpStatusCodeUtil::OK, 'there is no next slot');
    }

    /**
     * @return JsonResponse
     */
    private function processTimeCardSlot($timeCard, $restDay, $unverifiedClockInDate, $unverifiedClockInId)
    {
        if ($this->isFutureTimeCardWithAttendance($timeCard) && $this->isRestDayBeforeTimeCard($restDay, $timeCard)) {
            return $this->processRestDaySlot($restDay, $unverifiedClockInDate, $unverifiedClockInId);
        } else {
            $timeCard->unverified_ci_date = $unverifiedClockInDate;
            $timeCard->unverified_ci_id = $unverifiedClockInId;
            $timeCard->type = 'timecard';

            $lastVerifiedClockOut = $this->cicoCrudService->getLastVerifiedClockOut($timeCard->employee_id) ?? null;
            if ($lastVerifiedClockOut) {
                $timeCard->last_verified_co_date = Carbon::parse($lastVerifiedClockOut->date);
            } else {
                $timeCard->last_verified_co_date = null;
            }

            return getResponseStructure(['data' => new WorkerNextSlotResource($timeCard)],
                HttpStatusCodeUtil::OK, 'Available slots');
        }
    }

    /**
     * @return bool
     */
    private function isFutureTimeCardWithAttendance($timeCard)
    {
        return $timeCard->from > Carbon::now()->toDateTimeString() && ! isset($timeCard->attendance);
    }

    /**
     * @return bool
     */
    private function isRestDayBeforeTimeCard($restDay, $timeCard)
    {
        return isset($restDay) && $restDay->from <= $timeCard->from;
    }

    /**
     * @return JsonResponse
     */
    private function processRestDaySlot($restDay, $unverifiedClockInDate, $unverifiedClockInId)
    {
        $restDay->name = $restDay->companyLeaveType->name;
        $restDay->type = 'leave';
        $restDay->unverified_ci_date = $unverifiedClockInDate;
        $restDay->unverified_ci_id = $unverifiedClockInId;

        return getResponseStructure(['data' => new WorkerNextSlotResource($restDay)],
            HttpStatusCodeUtil::OK, 'Available slots');
    }

    /**
     * @return JsonResponse
     */
    private function processUnverifiedSlot($unverifiedClockInDate, $unverifiedClockInId)
    {
        $model = new EmptyModel;
        $model->unverified_ci_date = $unverifiedClockInDate;
        $model->unverified_ci_id = $unverifiedClockInId;

        return getResponseStructure(['data' => new WorkerNextSlotResource($model)],
            HttpStatusCodeUtil::OK, 'Available slots');
    }

    private function getRestDays($employeeId)
    {
        return $this->employeeLeaveRequestCrudService->getRestDayForWorker($employeeId);
    }

    public function getData(GetWorkerAppAttendanceByFilterRequest $request): array
    {
        $data['branch_id'] = config('globals.branchId');
        $data['employee_id'] = auth()->user()->employee_id;
        $data['start_date'] = $request->input('from');
        $data['end_date'] = $request->input('to');
        $data['page_size'] = config('globals.MAX_PAGE_SIZE');
        $data['order_by'] = 'from';
        $data['order_by_type'] = 'asc';

        return $data;
    }

    public function assignOffShiftTimecard(AssignShiftToOffShiftRequest $request)
    {
        $data = $request->validated();

        DB::beginTransaction();
        try {
            $this->timeCardsService->assignOffShiftTimecard($data);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);

            return getResponseStructure(['message' => $e->getMessage()], HttpStatusCodeUtil::UNPROCESSABLE_ENTITY, 'Unprocessable Entity');
        }

        return getResponseStructure(['message' => 'Shift assigned successfully'], HttpStatusCodeUtil::OK, 'Shift assigned successfully');

    }

    public function getNextSlotUpdated(GetNextSlotUpdatedRequest $request)
    {
        $employee = auth()->user()->employee;
        $data = $request->validated();
        $nextSlotFactory = new NextSlotFactory($employee);
        $response = $nextSlotFactory->createNextSlot($data['lat'], $data['long']);

        $logData['response'] = $response;
        $logData['request_param'] = $data;
        $this->logBasedOnRoute($request, $logData);

        return getResponseStructure(['data' => $response],
            HttpStatusCodeUtil::OK, 'Next Slot Updated');
    }

    public function workTypeNextSlot(GetNextSlotUpdatedRequest $request)
    {
        $data = $request->validated();

        $employee = auth()->user()->employee;
        $employee->load('title.workTypePolicy');
        //        echo 'in tests employee '.$employee->user->employee->id.' workType '.$employee->title->workTypePolicy->id.PHP_EOL;
        $user = $this->getUserWorkType($employee);
        $user->buildNextSlot($data['lat'], $data['long']);
        $response = $user->getNextSlot();

        $logData['response'] = $response;
        $logData['request_param'] = $data;
        $this->logBasedOnRoute($request, $logData);

        return getResponseStructure(['data' => $response],
            HttpStatusCodeUtil::OK, 'Next Slot Updated');
    }
}
