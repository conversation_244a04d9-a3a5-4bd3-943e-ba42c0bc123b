<?php

namespace App\Repositories\V1;

use App\Models\Employee;
use App\Repositories\BaseRepository;
use App\Traits\QueriesHelper;
use App\Util\EmployeeUtil;
use App\Util\FilesUtil;
use App\Util\LoansUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class EmployeeRepository extends BaseRepository
{
    use QueriesHelper;

    public function model(): string
    {
        return Employee::class;
    }

    public function list($data)
    {

        $obj = $this->model
            ->join('employees_info', 'employees_info.employee_id', '=', 'employees.id')
            ->select([
                'employees.id as id',
                'employees.name_en',
                'employees.name_ar',
                'employees.national_id',
                'employees.title_id',
                'employees.phone',
                'employees.employee_number',
                'employees.revoke_date',
                'employees_info.termination_date as termination_date',
                'employees_info.birth_date as date_of_birth',
                'employees_info.number_of_years_of_experience as years_of_experience',
                'employees_info.notes as notes',
                'employees_info.training_certification_status as training_certification_status',
                'employees.branch_id',
                'employees.first_name_ar',
                'employees.second_name_ar',
                'employees.third_name_ar',
                'employees.fourth_name_ar',
                'employees.fifth_name_ar',
                'employees.first_name_en',
                'employees.second_name_en',
                'employees.third_name_en',
                'employees.fourth_name_en',
                'employees.fifth_name_en',
                'employees.status',
                'employees.is_trackable',
            ])
            ->with('title:id,name_en,name_ar,color,department_id,sub_department_id', 'title.department:id,name_ar,name_en',
                'branch:id,name_en,name_ar', 'profilePicture', 'user', 'faceIdImage', 'title.subDepartment:id,name_ar,name_en')
            ->when(isset($data['search_value']), function ($q) use ($data) {
                $this->appendEmployeeTextSearchQuery($q, $data['search_value']);
            })
            ->when(isset($data['is_unregistered_face_id']) && $data['is_unregistered_face_id'], function ($q) {
                $q->whereHas('user', function ($q) {
                    $q->whereNull('face_id');
                });
            })
            ->when(isset($data['statuses']), function ($q) use ($data) {
                $q->whereIn('status', $data['statuses'])
                    ->when(in_array('terminated', $data['statuses']) && isset($data['termination_from_date']), function ($q) use ($data) {
                        $q->whereHas('employeeInfo', function ($q) use ($data) {
                            $q->whereBetween('termination_date', [$data['termination_from_date'], $data['termination_to_date']]);
                        });
                    });
            })
            ->when(isset($data['is_central']), function ($q) use ($data) {
                $q->whereHas('title.department', function ($q) use ($data) {
                    $q->where('is_central', $data['is_central']);
                });
            })
            ->when(isset($data['missing_employee_info']) && $data['missing_employee_info'], function ($q) {
                $q->whereHas('employeeInfo', function ($q) {
                    $q->whereNull('join_date');
                });
            })
            ->when(isset($data['role_ids']) && count($data['role_ids']) > 0, function ($q) use ($data) {
                $q->whereHas('user.customRoles', function ($q) use ($data) {
                    $q->whereIn('spatie_roles.id', $data['role_ids']);
                });
            })
            ->when(isset($data['is_trackable']), function ($q) use ($data) {
                $q->where('is_trackable', $data['is_trackable']);
            });

        $this->appendScopeQuery($obj, $data);

        $obj->orderBy('title_id');
        if (! isset($data['page_size'])) {
            return $obj->get();
        }

        return $this->getFilterPaginated($obj, $data['page_size']);
    }

    public function listForBulkEdit($data)
    {
        $obj = $this->model
            ->select([
                'employees.id as id',
                'employees.name_en',
                'employees.name_ar',
                'employees.phone',
                'employees.employee_number',
            ])
            ->when(isset($data['search_value']), function ($q) use ($data) {
                $this->appendEmployeeTextSearchQuery($q, $data['search_value']);
            })
            ->when(isset($data['statuses']), function ($q) use ($data) {
                $q->whereIn('status', $data['statuses'])
                    ->when(in_array('terminated', $data['statuses']) && isset($data['termination_from_date']), function ($q) use ($data) {
                        $q->whereHas('employeeInfo', function ($q) use ($data) {
                            $q->whereBetween('termination_date', [$data['termination_from_date'], $data['termination_to_date']]);
                        });
                    });
            })
            ->when(isset($data['is_central']), function ($q) use ($data) {
                $q->whereHas('title.department', function ($q) use ($data) {
                    $q->where('is_central', $data['is_central']);
                });
            })
            ->when(isset($data['missing_employee_info']) && $data['missing_employee_info'], function ($q) {
                $q->whereHas('employeeInfo', function ($q) {
                    $q->whereNull('join_date');
                });
            })
            ->when(isset($data['role_ids']) && count($data['role_ids']) > 0, function ($q) use ($data) {
                $q->whereHas('user.customRoles', function ($q) use ($data) {
                    $q->whereIn('spatie_roles.id', $data['role_ids']);
                });
            })
            ->when(isset($data['is_trackable']), function ($q) use ($data) {
                $q->where('is_trackable', $data['is_trackable']);
            });

        $this->appendScopeQuery($obj, $data);

        $obj->orderBy('title_id');
        return $obj->get();
    }

    public function getEmployeeNames($data)
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');

        return $this->model
            ->select('id', 'name'.$lang, 'title_id', 'branch_id', 'employee_number', 'status', 'is_trackable')
            ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->when(isset($data['is_central']), function ($q) use ($data) {
                $q->whereHas('title.department', function ($q) use ($data) {
                    $q->where('is_central', $data['is_central']);
                });
            })
            ->with('title:id,name'.$lang.',color')
            ->get();
    }

    public function getFilterPaginated($obj, $pageSize)
    {
        return $obj->paginate($pageSize);
    }

    public function getEmployeeInfo($id)
    {
        $unleash = app(\App\FeatureToggles\Unleash::class);
        $isEnhancedProfileEnabled = $unleash->isEnhancedEmployeeProfileEnabled();

        $query = $this->model
            ->select([
                'employees.id as id',
                'employees.name_en',
                'employees.name_ar',
                'employees.phone',
                'employees.employee_number',
                'employees.revoke_date',
                'employees.status',
                'employees.national_id',
                'employees.title_id',
                'employees.branch_id',
                'employees.status',
                'employees.is_trackable',
                'employees.company_id',
            ])
            ->with('title.department', 'branch', 'profilePicture')
            ->with('employeeChanges', function ($q) use ($id) {
                $q->where('change_type', 'branch')->where('employee_id', $id)
                    ->where('status', 'completed')
                    ->with('fromBranch:id,name_en,name_ar')->orderBy('as_of_date', 'asc');
            })
            ->with([
                'employeeInfo',
                'terminationRequest',
                'managedDepartments',
                'managedSubDepartments',
                'branches',
            ]);

        if ($isEnhancedProfileEnabled) {
            $query->with([
                'emergencyContacts',
                'education',
                'contracts' => function ($query) {
                    $query->orderBy('contract_start_date', 'desc');
                }
            ]);
        }

        return $query->find($id);
    }

    public function getEmployeeSalaryInfo($id)
    {
        return $this->model
            ->select('employees.id as id')
            ->with('employeeSalary.bank', 'employeeInsurance')
            ->find($id);

    }

    public function getEmployeeRoles($id)
    {
        return $this->model
            ->select('employees.id as id')
            ->with('user.roles:id,name_en,name_ar,is_system_role', 'user.roles.permissions:id,name_en,name_ar,permission_group_id')
            ->find($id);
    }

    public function getCountEmployeesOnTitles($titleIds)
    {
        return $this->model
            ->whereIn('title_id', $titleIds)
            ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->count();
    }

    public function getEmployeesExistingRequiredCategories()
    {
        $obj = $this->model
            ->select('id')
            ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->withWhereHas('employeeFiles', function ($q) {
                $q->select('id', 'employee_id', 'employee_file_category_id')
                    ->where('status', '!=', FilesUtil::File_STATUS['EXPIRED'])
                    ->withWhereHas('employeeFileCategory', function ($q) {
                        $q->where('is_document_required', 1);
                    });
            });
        $this->appendScopeQuery($obj);

        return $obj->get();
    }

    public function getActiveEmployees($data)
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');

        return $this->model
            ->when(isset($data['search_value']), function ($q) use ($data) {
                $q->where(function ($query) use ($data) {
                    $query->where('name_en', 'LIKE', '%'.$data['search_value'].'%')
                        ->orWhere('name_ar', 'LIKE', '%'.$data['search_value'].'%')
                        ->orWhere('employee_number', 'LIKE', '%'.$data['search_value'].'%');
                });
            })
            ->when(isset($data['title_ids']), function ($q) use ($data) {
                $q->whereIn('title_id', $data['title_ids']);
            })
            ->when(isset($data['department_ids']), function ($q) use ($data) {
                $q->whereHas('title', function ($q) use ($data) {
                    $q->whereIn('department_id', $data['department_ids']);
                });
            })
            ->when(isset($data['branch_ids']), function ($q) use ($data) {
                $q->whereIn('branch_id', $data['branch_ids']);
            })
            ->select('id', 'first_name'.$lang, 'second_name'.$lang, 'employee_number', 'branch_id')
            ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->get();
    }

    public function getTrackableAndWithTerminatedEmployees($data)
    {
        return $this->model
            ->where('is_trackable', 1)
            ->when(isset($data['employee_id']), function ($q) use ($data) {
                $q->where('id', $data['employee_id']);
            })
            ->when(isset($data['search_by']), function ($q) use ($data) {
                $q->where('name_ar', 'like', '%'.$data['search_by'].'%')
                    ->orWhere('name_en', 'like', '%'.$data['search_by'].'%')
                    ->orWhere('name', 'like', '%'.$data['search_by'].'%')
                    ->orWhere('employee_number', 'like', '%'.$data['search_by'].'%');
            })
            ->when(isset($data['start_date']), function ($q) use ($data) {
                $q->whereHas('employeeInfo', function ($query) use ($data) {
                    $query->where(function ($subQuery) use ($data) {
                        $subQuery->whereNull('termination_date')
                            ->orWhere('termination_date', '>=', $data['start_date']);
                    });
                });
            })
            ->where(function ($query) use ($data) {
                $this->appendScopeQuery($query, $data);
            })
            ->with('title', 'employeeInfo:id,employee_id,join_date,termination_date', 'profilePicture')
            ->get();
    }

    public function getTrackableActiveEmployeesWithNoAttendanceOnDate($date, $theDayBeforeDate = null, $publicHolidayId = null)
    {
        return $this->model
            ->with('user')
            ->with(['publicHolidayAbsences' => function ($q) use ($theDayBeforeDate, $publicHolidayId) {
                $q
                    ->where('public_holiday_id', $publicHolidayId)
                    ->where('to', $theDayBeforeDate)
                    ->orderBy('id');
            }])
            ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->whereHas('employeeInfo', function ($q) use ($date) {
                $q->where(function ($q) use ($date) {
                    $q->whereNull('termination_date')
                        ->orWhereDate('termination_date', '>=', $date);
                });
            })
            ->where('is_trackable', 1)
            ->whereDoesntHave('attendance', function ($q) use ($date) {
                $q->where('date', $date);
            })
            ->whereDoesntHave('publicHolidayAbsences', function ($q) use ($date) {
                $q->where('to', $date);
            })
            ->get();
    }

    public function getNonTerminatedEmployees($titleIds = null)
    {
        return $this->model
            ->when(isset($titleIds), function ($q) use ($titleIds) {
                $q->whereIn('title_id', $titleIds);
            })
            ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->with('employeeInfo', 'title.companyLeaveTypePolicies', 'company.maternityLeaveType', 'user', 'employeeLeaveBalances')
            ->get();
    }

    public function getEmployeesWithSalaryInfo()
    {
        return $this->model
            ->with('employeeSalary:id,employee_id,net_salary,social_insurance_salary')
            ->get();
    }

    public function getEmployeesByTitleIds($titleIds)
    {
        return $this->model
            ->whereIn('title_id', $titleIds)
            ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->select('id', 'branch_id', 'company_id')
            ->with('company')->get();
    }

    public function getEmployeesByTitleId($titleId, $employeeId = null)
    {
        return $this->model
            ->where('title_id', $titleId)
            ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->when($employeeId, function ($q) use ($employeeId) {
                $q->where('id', $employeeId);
            })
            // select with title (id, work_type_policy_id) , with workTypePolicy
            ->with('title:id,work_type_policy_id', 'title.workTypePolicy:id,work_days_type,work_days_count,apply_any_location,any_location_type,rest_days_type')
            ->select('id', 'branch_id', 'title_id', 'company_id')->get();
    }

    public function getEmployeesInfo($ids)
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');

        return $this->model
            ->select(
                'id',
                'first_name'.$lang,
                'second_name'.$lang,
                'employee_number',
                'title_id',
                'branch_id'
            )
            ->whereIn('employees.id', $ids)
            ->with(['title:id,name_en,name_ar,color', 'branch:id,name_en,name_ar'])
            ->get()->toArray();
    }

    public function getEmployeesWithTheirApprovers($companyId, $type)
    {
        return $this->model
            ->withoutGlobalScopes()
            ->where('company_id', $companyId)
            ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->with('title.requestGroup.requestWorkflows', function ($q) {
                $q->whereNull('deleted_at')
                    ->with('role.users.employee', function ($q) {
                        $q->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                            ->with('branches', function ($q) {
                                $q->select('branch_employee.id', 'branch_id');
                            });
                    })
//                        ->orderBy('request_group_id')
                    ->orderBy('order');
            })
            ->get();
    }

    public function getEmployeesWithDisbursedLoansForPayroll($employeeIds, $payrollStartDate, $payrollEndDate, $notPaidInstallments = true)
    {

        return $this->model
            ->whereIn('id', $employeeIds)
            ->withWhereHas('loans', function ($q) use ($payrollStartDate, $payrollEndDate, $notPaidInstallments) {
                $q->where('status', LoansUtil::DISBURSED)
                    ->withWhereHas('installments', function ($q) use ($payrollStartDate, $payrollEndDate, $notPaidInstallments) {
                        $q->select('id', 'amount', 'loan_id', 'date')
                            ->when($notPaidInstallments, function ($q) {
                                $q->where('status', 0);
                            })
                            ->whereBetween('date', [$payrollStartDate, $payrollEndDate]);
                    });
            })
            ->get();
    }

    public function getEmployeesWithForPayroll($employeeIds, $payrollStartDate, $payrollEndDate)
    {
        return $this->model
            ->whereIn('id', $employeeIds)
            ->withWhereHas('loans', function ($q) use ($payrollStartDate, $payrollEndDate) {
                $q->where('status', LoansUtil::DISBURSED)
                    ->withWhereHas('installments', function ($q) use ($payrollStartDate, $payrollEndDate) {
                        $q->select('id', 'amount', 'loan_id')
                            ->where('status', 0)
                            ->whereBetween('date', [$payrollStartDate, $payrollEndDate]);
                    });
            })
            ->get();
    }

    public function getEmployeesWithDisbursedSalaryAdvancesForPayroll($employeeIds, $payrollStartDate, $payrollEndDate)
    {
        return $this->model
            ->whereIn('id', $employeeIds)
            ->withWhereHas('salaryAdvances', function ($q) use ($payrollStartDate, $payrollEndDate) {
                $q
                    ->select('id', 'employee_id', 'amount', 'status', 'disbursement_date')
                    ->where('status', LoansUtil::DISBURSED)
                    ->whereBetween('disbursement_date', [$payrollStartDate, $payrollEndDate]);
            })
            ->get();
    }

    public function getEmployeesWithPaidBalanceCompensation($employeeIds, $payrollStartDate, $payrollEndDate)
    {
        return $this->model
            ->select('id', 'branch_id')
            ->whereIn('id', $employeeIds)
            ->withWhereHas('repaidLeaveBalances', function ($q) use ($payrollStartDate, $payrollEndDate) {
                $q
                    ->select('id', 'employee_id', 'balance')
                    ->where('is_repaid', 0)
                    ->whereBetween('date', [$payrollStartDate, $payrollEndDate]);
            })
            ->get();
    }

    public function getEmployeesWithPaidBalanceCompensationGrouped($employeeIds, $payrollStartDate, $payrollEndDate)
    {
        return $this->model
            ->select('employees.id as id', 'branch_id', DB::raw('SUM(repaid_leave_balances.balance) as total_balance'))
            ->whereIn('employees.id', $employeeIds)
            ->join('repaid_leave_balances', 'repaid_leave_balances.employee_id', '=', 'employees.id')
            ->where('repaid_leave_balances.is_repaid', 0)
            ->whereBetween('repaid_leave_balances.date', [$payrollStartDate, $payrollEndDate])
            ->groupBy('employees.id')
            ->get();
    }

    public function getEmployeesProfilePictures($employeeIds)
    {
        return $this->model
            ->whereIn('id', $employeeIds)
            ->with('profilePicture')
            ->get();
    }

    public function getEmployeesWithBalances($employeeId)
    {
        return $this->model
            ->where('id', $employeeId)
            ->withWhereHas('employeeLeaveBalances', function ($q) {
                $q->where('start', '<=', Carbon::now()->toDateTimeString())
                    ->where('end', '>=', Carbon::now()->toDateTimeString());
            })->get();
    }

    public function getActiveEmployeesIdsByBranches($branchIds, $payrollEndDate, $payrollStartDate, $titleIds)
    {
        return $this->model
            ->select('id')
            ->when(! empty($branchIds), function ($query) use ($branchIds) {
                $query->whereIn('branch_id', $branchIds);
            })
            ->whereIn('title_id', $titleIds)
            ->where('is_trackable', 1)
            ->withWhereHas('employeeInfo', function ($q) use ($payrollEndDate, $payrollStartDate) {
                $q->whereDate('join_date', '<=', $payrollEndDate)
                    ->where(function ($q) use ($payrollStartDate) {
                        $q->whereNull('termination_date')
                            ->orWhereDate('termination_date', '>=', $payrollStartDate);
                    });
            })
            ->get();
    }

    public function getEmployeesWithPublicHolidayCompensation($employeeIds, $start, $end)
    {

        return $this->model
            ->select('employees.id',
                'employees.title_id',
                'attendances.date',
                'attendances.employee_id as attendance_employee_id',
                'titles.public_holidays_policy_id',
                'public_holidays_policies.compensation_method',
                'public_holidays_policies.compensation_pay_rate',
                'public_holidays_policies.compensation_holidays_rate',
                'cicos.branch_id as cico_branch_id',
                'public_holidays.name_ar',
                'public_holidays_attendance_payouts.compensation_payed_out'
            )
            ->join('attendances', function ($q) use ($start, $end) {
                $q->on('employees.id', '=', 'attendances.employee_id')
                    ->where('attendances.date', '>=', $start)
                    ->where('attendances.date', '<=', $end);
            })
            ->join('public_holidays', function ($q) {
                $q->on('attendances.date', '>=', 'public_holidays.start')
                    ->on('attendances.date', '<=', 'public_holidays.end');
            })
            ->leftJoin('public_holidays_attendance_payouts', function ($q) {
                $q->on('employees.id', '=', 'public_holidays_attendance_payouts.employee_id')
                    ->on('public_holidays_attendance_payouts.public_holiday_id', '=', 'public_holidays.id');
            })
            ->join('cicos', 'cicos.id', '=', 'attendances.co_id')
            ->join('entity_tags', function ($q) {
                $q->on('attendances.id', '=', 'entity_tags.entity_id')
                    ->where('entity_tags.entity_type', 'attendance')
                    ->where('entity_tags.tag', config('globals.ATTENDANCE_TAGS.PUBLIC_HOLIDAY'));
            })
            ->join('titles', 'employees.title_id', '=', 'titles.id')
            ->join('public_holidays_policies', 'titles.public_holidays_policy_id', '=', 'public_holidays_policies.id')
            ->whereIn('employees.id', $employeeIds)
            ->get();
    }

    public function getSalaryForEmployees($employeeIds)
    {
        return $this->model
            ->select('id', 'branch_id')
            ->whereIn('employees.id', $employeeIds)
            ->withWhereHas('employeeSalary', function ($q) {  // convert to joins ?
                $q->select('employee_salaries.basic_salary', 'employee_salaries.employee_id', 'employee_salaries.net_salary', 'employee_salaries.gross_salary');
            })
            ->with(['employeeChanges' => function ($q) {
                $q->select('id', 'employee_id', 'from_value', 'to_value', 'as_of_date')
                    ->where('change_type', 'branch')
                    ->where('status', 'completed');
            },
                'employeeInfo:id,employee_id,join_date,termination_date'])
            ->get();
    }

    public function getAllEmployees()
    {
        return $this->selectColumns(['id', 'name_ar', 'name_en'])->keyBy('id')->toArray();
    }
}
