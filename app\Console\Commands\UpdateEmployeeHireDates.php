<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Models\EmployeeHireHistory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateEmployeeHireDates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-employee-hire-dates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the previous hire date and join date for employees, handling multiple histories';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $employeeIds = EmployeeHireHistory::distinct('employee_id')->pluck('employee_id');
        foreach ($employeeIds as $employeeId) {
            $this->updateHireDates($employeeId);
        }

        return 0;
    }

    private function updateHireDates($employeeId)
    {
        DB::transaction(function () use ($employeeId) {
            $employee = Employee::with('employeeInfo')->findOrFail($employeeId);

            $hireHistories = EmployeeHireHistory::where('employee_id', $employeeId)
                ->orderBy('action_date', 'desc')
                ->get();

            $lastRehire = null;
            $lastTermination = null;

            foreach ($hireHistories as $history) {
                if ($history->action_type === 'rehired') {
                    if ($lastTermination) {
                        $employeeInfo = $employee->employeeInfo;

                        EmployeeHireHistory::create([
                            'employee_id' => $employeeId,
                            'company_id' => $history->company_id,
                            'rehire_date' => $history->action_date,
                            'termination_date' => $lastTermination->action_date,
                            'previous_hire_date' => $employeeInfo->join_date,
                            'action_type' => 'combined',
                            'action_date' => now(),
                        ]);
                        $employeeInfo->join_date = $history->action_date;

                        $employeeInfo->save();

                        // Log the information before setting lastTermination to null
                        $this->info("Processed employee ID {$employeeId}: Rehired on {$history->action_date}, Terminated on {$lastTermination->action_date}");

                        $lastTermination->delete();
                        $history->delete();

                        $lastTermination = null;
                    } else {
                        $lastRehire = $history;
                    }
                } elseif ($history->action_type === 'terminated') {
                    if ($lastRehire) {
                        $employeeInfo = $employee->employeeInfo;

                        EmployeeHireHistory::create([
                            'employee_id' => $employeeId,
                            'company_id' => $lastRehire->company_id,
                            'rehire_date' => $lastRehire->action_date,
                            'termination_date' => $history->action_date,
                            'previous_hire_date' => $employeeInfo->join_date,
                            'action_type' => 'combined',
                            'action_date' => now(),
                        ]);
                        $employeeInfo->join_date = $lastRehire->action_date;

                        $employeeInfo->save();

                        $lastRehire->delete();
                        $history->delete();

                        $lastRehire = null;
                    } else {
                        $lastTermination = $history;
                    }
                }
            }

            // Handle unprocessed records (in case of mismatched rehire/termination)
            if ($lastRehire || $lastTermination) {
                $unprocessedAction = $lastRehire ? 'rehired' : 'terminated';
                $unprocessedDate = $lastRehire ? $lastRehire->action_date : $lastTermination->action_date;
                $this->info("Unprocessed row for Employee ID {$employeeId}, Action: {$unprocessedAction}, Date: {$unprocessedDate}");
            }
        });
    }
}
