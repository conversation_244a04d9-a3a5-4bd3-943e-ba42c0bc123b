<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Title;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AddTitleWorkingHours extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:title-working-hours {company_id} {working_hours}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add title working hours to titles table';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $companyId = $this->argument('company_id');
        $workingHours = $this->argument('working_hours');

        DB::beginTransaction();
        try {
            $titles = Title::where('company_id', $companyId)->get();
            foreach ($titles as $title) {
                $title->working_hours = $workingHours;
                $title->save();
            }
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::error($e);
            DB::rollBack();
        }
    }
}
