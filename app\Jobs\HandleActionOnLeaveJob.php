<?php

namespace App\Jobs;

use App\FeatureToggles\Unleash;
use App\Repositories\Repository;
use App\Repositories\V1\Attendance\TimecardRepository;
use App\Services\V1\Holidays\PublicHolidaysAttendanceService;
use App\Services\V1\LeaveManagement\EmployeeLeavesService;
use App\Services\V1\LeaveManagement\LeavesSplitterService;
use App\Traits\V1\NotificationRedirection;
use App\Util\HolidaysUtil;
use Illuminate\Support\Facades\Log;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class HandleActionOnLeaveJob implements ShouldQueue
{
    use NotificationRedirection, Queueable, SerializesModels, InteractsWithQueue, Dispatchable;

    public $tries = 250;

    public $timeout = 120;

    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }


    public function handle()
    {
        try {

            $employeeLeavesService = app(EmployeeLeavesService::class);

            if ($this->data['final_status'] == 'approved') {
                Log::info('request: '.json_encode($this->data['request']->withoutRelations() ?? []));
                Log::info('in handle the request is ' . $this->data['request']->id);
                Log::info('leave balance is ' . $this->data['request']->net_quantity);
                $this->splitLeavesBasedOnRestdaysAndHolidays($this->data['request']);
                $employeeLeavesService->handleApprovedPartialLeaveRequest($this->data['request']);
            }

            Log::info('second balance is ' . $this->data['request']->net_quantity);
            $this->redirectNotificationsAfterRequestFinalized($this->data['request'], $this->data['final_status']);
            Log::info('Request is ' . $this->data['final_status']);

            if (! isset($this->data['request']->partial_leave_type)) {
                $this->deleteTimecardsIfExists();
            }
        } catch (\Exception $e) {
            Log::info('error in leave job');
            Log::info($e);
        }
    }

    public function deleteTimecardsIfExists()
    {
        if ($this->data['final_status'] == 'approved' && count($this->data['timecards'])) {
            $timecardRepository = new TimecardRepository;
            $timecardRepository->delete($this->data['timecards']);
        }
    }

    public function splitLeavesBasedOnRestdaysAndHolidays($leaveObj)
    {
        Log::info('splitLeavesBasedOnRestdaysAndHolidays');
        $leavesSplitterService = app(LeavesSplitterService::class);
        Log::info('leave splitter service: '.json_encode($leavesSplitterService));
        $leaves = $leavesSplitterService->splitLeaveBasedOnHolidaysAndRestdays($leaveObj);
        foreach ($leaves as $leave) {
            $this->updateLeaveBalance($leave);
        }
    }

    public function updateLeaveBalance($leaveObj)
    {
        $leaveObj->load('employee.title.PublicHolidaysPolicy');

        $this->handlePublicHolidayCompensation($leaveObj);
        $employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');

        $leaveBalance = $employeeLeaveBalanceRepository->leaveBalanceForEmployee(
            $leaveObj->employee_id, $leaveObj->from, $leaveObj->company_leave_type_policy_id
        );

        if ($leaveBalance) {
            $this->updateRelatedBalances($leaveObj, $leaveBalance, $employeeLeaveBalanceRepository);
            $leaveBalance->balance = max(0, $leaveBalance->balance - $leaveObj->net_quantity);
            $leaveBalance->save();
        }
    }

    private function handlePublicHolidayCompensation($leaveObj)
    {
        if (isset($leaveObj->employee->title->PublicHolidaysPolicy) &&
            $leaveObj->employee->title->PublicHolidaysPolicy->compensation_method != HolidaysUtil::POLICIES['EXTRA_WORK_DAY']
        ) {
            Log::info('HolidayBalanceToBeUpdated');
            $publicHolidaysAttendanceService = new PublicHolidaysAttendanceService;
            $publicHolidaysAttendanceService->updateHolidayBalanceAfterLeaveApproved($leaveObj);
        }
    }

    private function updateRelatedBalances($leaveObj, $leaveBalance, $employeeLeaveBalanceRepository)
    {
        $unleash = app(Unleash::class);
        Log::info('updateRelatedBalances');
        if ($unleash->isLeaveBalanceChangesFlagEnabled()) {
            Log::info('updateRelatedBalances');
            $this->adjustDebitBalance($leaveObj, $leaveBalance);
            $this->adjustTransferredBalance($leaveObj, $leaveBalance);
            $this->adjustParentLeaveBalance($leaveObj, $employeeLeaveBalanceRepository);
            $leaveObj->save();
        }
    }

    private function adjustDebitBalance($leaveObj, $leaveBalance)
    {
        if ($leaveObj->net_quantity > $leaveBalance->balance) {
            $debitBalance = $leaveObj->net_quantity - $leaveBalance->balance;
            $leaveBalance->debit_balance += $debitBalance;
            $leaveObj->debit_net_quantity = $debitBalance;
        }
    }

    private function adjustTransferredBalance($leaveObj, $leaveBalance)
    {
        if ($leaveBalance->used_transferred_balance < $leaveBalance->transferred_balance) {
            $leaveBalance->used_transferred_balance = min($leaveBalance->transferred_balance, $leaveBalance->used_transferred_balance + $leaveObj->net_quantity
            );
        }
    }

    private function adjustParentLeaveBalance($leaveObj, $employeeLeaveBalanceRepository)
    {
        $parentLeaveType = $leaveObj->companyLeaveType->parentCompanyLeaveType;
        Log::info('parentLeaveType exists'.isset($parentLeaveType));
        if ($parentLeaveType) {
            $parentLeaveBalance = $employeeLeaveBalanceRepository->leaveBalanceForEmployee(
                $leaveObj->employee_id, $leaveObj->from, $parentLeaveType->companyLeaveTypePolicy->id
            );
            Log::info('parentLeaveType balance exists '.isset($parentLeaveBalance).' policy '.$parentLeaveType->companyLeaveTypePolicy->id);
            if ($parentLeaveBalance && $parentLeaveBalance->balance > 0) {
                Log::info('parentLeaveType balance '.$parentLeaveBalance && $parentLeaveBalance->balance);
                if ($leaveObj->net_quantity > $parentLeaveBalance->balance) {
                    $debitBalance = $leaveObj->net_quantity - $parentLeaveBalance->balance;
                    $parentLeaveBalance->debit_balance += $debitBalance;
                    $leaveObj->debit_net_quantity = $debitBalance;
                }
                $parentLeaveBalance->balance = max(0, $parentLeaveBalance->balance - $leaveObj->net_quantity);
                $parentLeaveBalance->save();
            }
        }
    }
}
