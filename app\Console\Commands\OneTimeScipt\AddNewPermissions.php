<?php

namespace App\Console\Commands\OneTimeScipt;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log as FacadesLog;

class AddNewPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:add-new-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Adding new permissions');
            DB::beginTransaction();
            $permissions = [
                [
                    'name' => 'view_employees',
                    'guard_name' => 'user-api',
                    'description_ar' => 'عرض الموظفين',
                    'description_en' => 'View Employees',
                    'name_ar' => 'عرض الموظفين',
                    'name_en' => 'View Employees',
                    'permission_group_id' => 3,
                ],
                [
                    'name' => 'calculate_payroll',
                    'guard_name' => 'user-api',
                    'description_ar' => 'حساب المرتبات',
                    'description_en' => 'Calculate payroll',
                    'name_ar' => 'حساب المرتبات',
                    'name_en' => 'Calculate payroll',
                    'permission_group_id' => 11,
                ],
            ];

            foreach ($permissions as $permissionData) {
                $p = Permission::findOrCreate($permissionData['name'], $permissionData['guard_name']);
            }
            DB::commit();
            $rolesWithFinalizePayroll = Role::whereHas('permissions', function ($query) {
                $query->where('name', 'finalize_payroll');
            })->get();
            $this->info('Roles count: '.count($rolesWithFinalizePayroll));

            foreach ($rolesWithFinalizePayroll as $role) {
                $role->givePermissionTo('calculate_payroll');
            }

            $rolesWithAddEmployee = Role::whereHas('permissions', function ($query) {
                $query->where('name', 'add_employee');
            })->get();
            $this->info('Roles count: '.count($rolesWithAddEmployee));
            foreach ($rolesWithAddEmployee as $role) {
                $role->givePermissionTo('view_employees');
            }

            $this->info('New permissions added successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error adding new permissions');
            FacadesLog::error($e->getMessage());
        }
    }
}
