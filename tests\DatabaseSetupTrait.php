<?php

namespace Tests;

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

trait DatabaseSetupTrait
{
    /**
     * Clean up the database after each test
     */
    public function tearDown(): void
    {
        $this->cleanupDatabase();
        parent::tearDown();
    }

    /**
     * Setup database for testing by disabling foreign key checks
     */
    protected function setupDatabase(): void
    {
        // Determine which database connection we're using
        $connection = DB::connection()->getDriverName();

        // Disable foreign key checks based on connection type
        if ($connection === 'mysql') {
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        }
    }

    /**
     * Re-enable foreign key constraints
     */
    protected function restoreForeignKeys(): void
    {
        // Determine which database connection we're using
        $connection = DB::connection()->getDriverName();

        // Re-enable foreign key checks based on connection type
        if ($connection === 'mysql') {
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        } elseif ($connection === 'sqlite') {
            DB::statement('PRAGMA foreign_keys = ON;');
        } elseif ($connection === 'pgsql') {
            // For PostgreSQL
            Schema::enableForeignKeyConstraints();
        }
    }

    /**
     * Clean up the database by truncating all tables
     * Override this in child classes if you need specific cleanup logic
     */
    protected function cleanupDatabase(): void
    {
        $connection = DB::connection()->getDriverName();

        // Disable foreign key checks
        $this->setupDatabase();

        try {
            // Get all tables for the current connection
            if ($connection === 'mysql') {
                $tables = DB::select('SHOW TABLES');
                foreach ($tables as $table) {
                    $tableName = array_values((array)$table)[0];
                    // Skip migration and certain system tables
                    if (!in_array($tableName, ['migrations', 'sqlite_sequence'])) {
                        DB::table($tableName)->truncate();
                    }
                }
            } elseif ($connection === 'sqlite') {
                $tables = DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';");
                foreach ($tables as $table) {
                    if (!in_array($table->name, ['migrations', 'sqlite_sequence'])) {
                        DB::table($table->name)->truncate();
                    }
                }
            } elseif ($connection === 'pgsql') {
                // For PostgreSQL
                $tables = DB::select("SELECT tablename FROM pg_catalog.pg_tables WHERE schemaname != 'pg_catalog' AND schemaname != 'information_schema';");
                foreach ($tables as $table) {
                    if (!in_array($table->tablename, ['migrations'])) {
                        DB::statement("TRUNCATE TABLE \"" . $table->tablename . "\" CASCADE;");
                    }
                }
            }
        } finally {
            // Always re-enable foreign key constraints
            $this->restoreForeignKeys();
        }
    }

    /**
     * Run migrations on the current database connection
     */
    protected function runMigrations(): void
    {
        Artisan::call('migrate:fresh');
        echo "Migrations completed successfully.\n";
    }
} 