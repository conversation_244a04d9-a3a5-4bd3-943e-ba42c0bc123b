<?php

namespace App\Http\Requests\V1\Files;

use App\Rules\IsArabic;
use App\Rules\IsEnglish;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;

class BulkUploadEmployeeFilesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {

        return [
            'employee_id' => [
                'integer',
                (new Exists('employees', 'id'))->where(function ($query) {
                    $query->where('company_id', auth()->user()->company_id)
                        ->whereNull('deleted_at');
                }),
            ],

            'files.*.employee_file_category_id' => [
                'integer',
                (new Exists('employee_file_categories', 'id'))->where(function ($query) {
                    $query->where('company_id', auth()->user()->company_id)
                        ->whereNull('deleted_at');
                }),
            ],
            'files.*.expiry_date' => 'date_format:Y-m-d',
            'files.*.attachments.*' => 'required|mimes:pdf,doc,docx,png,jpg,jpeg|max:'.config('globals.MAX_FILE_SIZE_KB'),
            'files.*.name_ar' => ['required_without:files.*.employee_file_category_id', 'string', new IsArabic],
            'files.*.name_en' => ['required_without:files.*.employee_file_category_id', 'string', new IsEnglish],
        ];
    }
}
