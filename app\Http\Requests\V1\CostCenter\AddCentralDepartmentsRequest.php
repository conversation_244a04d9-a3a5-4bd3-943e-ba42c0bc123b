<?php

namespace App\Http\Requests\V1\CostCenter;

use App\Rules\DepartmentIdRule;
use Illuminate\Foundation\Http\FormRequest;

class AddCentralDepartmentsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'department_ids' => [
                'required',
                'array',
            ],
            'department_ids.*' => [
                'required',
                new DepartmentIdRule,
            ],
        ];

    }
}
