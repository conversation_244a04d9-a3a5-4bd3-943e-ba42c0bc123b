<?php

namespace App\Traits;

use App\Repositories\TerminationRequestRepository;

trait RequestsHelper
{
    use WorkflowTrait;

    protected function getTerminationRequestRepository(): TerminationRequestRepository
    {
        // Lazily resolve the repository instance
        return app(TerminationRequestRepository::class);
    }

    public function isActionable($employeeApproves, $status): bool
    {
        if ($this->isAuthorizedToForceApprove() && ($status == 'pending')) {
            return true;
        }
        foreach ($employeeApproves as $approver) {
            if (in_array($approver->pivot->role_id, config('globals.user_role_ids')) && $approver->pivot->status == 'pending') {
                if ($approver->pivot->operator == 'or') {
                    return true;
                } elseif ($approver->pivot->operator == 'then') {
                    foreach ($employeeApproves as $otherApprover) {
                        if ($otherApprover->pivot->order < $approver->pivot->order &&
                        ($otherApprover->pivot->status != 'approved' && $otherApprover->pivot->status != 'skipped')) {
                            return false;
                        }
                    }

                    return true;
                }
            }
        }

        return false;
    }

    public function isFinalApprover($employeeApproves): bool
    {
        $roleIds = config('globals.user_role_ids');

        $otherPendingApprover = false;

        foreach ($employeeApproves as $approver) {
            if (in_array($approver->pivot->role_id, $roleIds)) {
                if ($approver->pivot->operator == 'or') {
                    return true;
                } else {
                    if ($otherPendingApprover) {
                        return false;
                    } else {
                        return true;
                    }
                }
            } elseif ($approver->pivot->status == 'pending') {
                $otherPendingApprover = true;
            }
        }

        return false;
    }

    public function canCancel($employeeApproves, $status): bool
    {
        if ($this->isAuthorizedToForceApprove() && ($status == 'approved')) {
            return true;
        }
        foreach ($employeeApproves as $approver) {
            if (in_array($approver->pivot->role_id, config('globals.user_role_ids')) && $approver->pivot->status == 'approved') {
                return true;
            }
        }

        return false;
    }

    public function canCancelTermination($employee): bool
    {
        $userRoles = auth()->user()->roles;
        $user = auth()->user();

        $hasPermissionToLiteTerminate = $user->hasPermissionTo('lite_terminate', 'user-api');
        //        $hasPermissionToLiteTerminate = $userRoles->contains(function ($role) {
        //            return $role->hasPermissionTo('lite_terminate', 'user-api');
        //        });

        $isNotTerminated = $employee->status != 'terminated';

        $terminationRequest = $this->getTerminationRequestRepository()->getTerminationRequest($employee->id);

        $cancelAction = $hasPermissionToLiteTerminate && $isNotTerminated && isset($terminationRequest)
            && $terminationRequest->status == 'pending' && $terminationRequest->workflow_id != null;

        return $cancelAction;
    }

    public function checkUserInApprovalCycle($employeeApproves): bool
    {
        foreach ($employeeApproves as $approver) {
            if (in_array($approver->pivot->role_id, auth()->user()->roles->pluck('id')->toArray())) {
                return true;
            }

        }

        return false;
    }
}
