<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use App\Models\Cico;
use App\Models\Company;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixTCAndAttendanceConflicts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-tc-and-attendance-conflicts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $companies = Company::whereNull('deleted_at')->get()->pluck('id');
            foreach ($companies as $companyId) {
                Log::info('Company id: '.$companyId);

                $timecards = $this->getWrongTimecards($companyId);

                Log::info('Timecards count: '.count($timecards));
                foreach ($timecards as $timecard) {
                    Log::info('Timecard id: '.$timecard->timecard_id);
                    if (isset($timecard->attendance_id)) {
                        $timecardDate = Carbon::parse($timecard->timecard_from)->format('Y-m-d');
                        $attendance = Attendance::find($timecard->attendance_id);
                        if (! $attendance) {
                            Log::info('Attendance not found for timecard id: '.$timecard->timecard_id);

                            continue;
                        }
                        $attendance->update(['date' => $timecardDate]);

                        $clockInDate = Carbon::parse($timecard->ci_date)->subDay()->format('Y-m-d H:i:s');
                        $clockIn = Cico::find($timecard->clock_in_id);
                        if (! $clockIn) {
                            Log::info('ClockIn not found for timecard id: '.$timecard->timecard_id);

                            continue;
                        }
                        $clockIn->update(['date' => $clockInDate]);

                    }

                }

                $timecards = $this->getWrongTimecards($companyId);
                Log::info('Timecards count: '.count($timecards));
                Log::info('---------------------------------');
            }

            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();
        }
    }

    public function getWrongTimecards($companyId)
    {
        return DB::table('timecards as t')
            ->selectRaw('t.id as timecard_id, t.employee_id, a.company_id, t.from as timecard_from, t.to as timecard_to,
                     a.date as attendance_date, ci.date as ci_date, co.date as co_date,
                     a.id as attendance_id, ci.id as clock_in_id')
            ->join('attendances as a', 'a.slotable_id', '=', 't.id')
            ->join('cicos as ci', 'ci.id', '=', 'a.ci_id')
            ->join('cicos as co', 'co.id', '=', 'a.co_id')
            ->where('a.company_id', $companyId)
            ->where('a.slotable_type', 'time_card')
        // ->whereDate('t.from', '>=', '2024-02-21')
        // ->whereDate('t.from', '<=', '2024-03-26')
            ->whereRaw('ci.date > co.date')
            ->whereNull('t.deleted_at')
            ->whereNull('t.deleted_at')
            ->whereNull('ci.deleted_at')
            ->whereNull('co.deleted_at')
            ->orderByDesc('t.from')
            ->get();
    }
}
