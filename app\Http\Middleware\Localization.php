<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\App;

class Localization
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response|RedirectResponse)  $next
     * @return Response|RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $locale = 'en';

        $url = $request->url();
        if (! str_contains($url, '/admin') || str_contains($url, '/admin/company')) {
            $locale = request()->header('Accept-Language') === 'en' ? $locale : 'ar';
        }

        config(['globals.lang' => $locale]);
        App::setLocale($locale);

        return $next($request);
    }
}
