<?php

namespace App\Console\Commands;

use App\Models\Branch;
use App\Models\SalaryComponentPolicy;
use App\Models\Title;
use Illuminate\Console\Command;

class AssignSalaryPoliciesTitlesAndBranches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:assign-salary-policies-titles-and-branches';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $salaryComponentPolicies = SalaryComponentPolicy::all();

        foreach ($salaryComponentPolicies as $policy) {
            $companyTitles = Title::where('company_id', $policy->company_id)->pluck('id')->toArray();
            $companyBranches = Branch::where('company_id', $policy->company_id)->pluck('id')->toArray();

            if (is_null($policy->title_id)) {
                $policy->titles()->sync($companyTitles);
                $this->info("Assigned all titles of company ID {$policy->company_id} to policy ID: {$policy->id}");
            } else {
                $policy->titles()->sync([$policy->title_id]);
                $this->info("Assigned specific title ID {$policy->title_id} to policy ID: {$policy->id}");
            }

            if (is_null($policy->branch_id)) {
                $policy->branches()->sync($companyBranches);
                $this->info("Assigned all branches of company ID {$policy->company_id} to policy ID: {$policy->id}");
            } else {
                $policy->branches()->sync([$policy->branch_id]);
                $this->info("Assigned specific branch ID {$policy->branch_id} to policy ID: {$policy->id}");
            }
        }

        $this->info('Assignment of titles and branches to salary component policies completed.');

    }
}
