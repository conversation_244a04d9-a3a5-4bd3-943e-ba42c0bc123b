<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\Payroll;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class ConvertDynamicCompanyToPayrollDay extends Command
{
    /**
     * Run with:
     *   php artisan app:convert-to-closing-day  <company_id>  <day>
     *
     * Example:
     *   php artisan app:convert-to-closing-day 7 25
     *
     * — company_id : target company
     * — day        : fixed monthly closing day (1-31)
     */
    protected $signature = 'app:convert-to-closing-day {company_id} {day}';

    protected $description = 'Disable dynamic payroll closing and move a company '
        .'to a fixed monthly closing day, realigning any '
        .'drafted payrolls and rest-day leave balances.';

    /* ──────────────────────────────────────────────────────────────── */
    public function handle(): int
    {
        /* 1️⃣  Validate CLI arguments -------------------------------- */
        $companyId = (int) $this->argument('company_id');
        $closingDay = (int) $this->argument('day');

        if ($closingDay < 1 || $closingDay > 31) {
            $this->error('The closing day must be between 1 and 31.');

            return self::FAILURE;
        }

        $company = \App\Models\Company::find($companyId);
        if (! $company) {
            $this->error("Company [{$companyId}] not found.");

            return self::FAILURE;
        }

        /* 2️⃣  One atomic transaction for everything ---------------- */
        DB::transaction(function () use ($company, $closingDay) {

            /* a. Switch off “dynamic” and save the static day -------- */
            DB::table('system_settings')
                ->where('company_id', $company->id)
                ->where('key', 'apply_dynamic_closing_day')
                ->delete();

            DB::table('system_settings')->updateOrInsert(
                ['company_id' => $company->id, 'key' => 'payroll_monthly_closing_day'],
                [
                    'value' => $closingDay,
                    'as_of_date' => now()->toDateString(),
                    'updated_at' => now(),
                    'created_at' => now(),   // ignored on update
                    'deleted_at' => null,
                ]
            );

            /* b. Realign every drafted payroll ---------------------- */
            Payroll::where('company_id', $company->id)
                ->where('status', 'draft')
                ->orderByDesc('end')
                ->get()
                ->each(function (Payroll $payroll) use ($closingDay) {

                    [$start, $end] = $this->window(
                        $payroll->start, $payroll->end, $closingDay
                    );

                    $payroll->update([
                        'start' => $start->toDateString(),
                        'end' => $end->toDateString(),
                    ]);
                });

            /* c. Shift rest-day leave balances (query-builder) ------ */
            if ($company->rest_day_leave_id) {
                DB::table('employee_leave_balances')
                    ->where('company_leave_type_id', $company->rest_day_leave_id)
                    ->orderByDesc('end')
                    ->chunkById(1000, function ($chunk) use ($closingDay) {

                        foreach ($chunk as $row) {
                            [$start, $end] = $this->window(
                                $row->start, $row->end, $closingDay
                            );

                            DB::table('employee_leave_balances')
                                ->where('id', $row->id)
                                ->update([
                                    'start' => $start->format('Y-m-d 00:00:00'),
                                    'end' => $end->format('Y-m-d 23:59:59'),
                                ]);
                        }
                    });
            }
        });

        $this->info("✅  {$company->name} now closes payroll on day {$closingDay} of each month.");

        return self::SUCCESS;
    }

    /* ──────────────────────────────────────────────────────────────── */
    /**
     * Re-compute a payroll/leave window so that its **end** date falls on
     * the chosen closing day inside the same calendar month as $oldEnd.
     *
     * Returns [$newStart, $newEnd] (Carbon instances).
     */
    private function window($oldStart, $oldEnd, int $day): array
    {
        $oldEnd = $oldEnd instanceof Carbon ? $oldEnd : Carbon::parse($oldEnd);

        // 1. first day of the month that contains $oldEnd
        $monthStart = $oldEnd->copy()->startOfMonth();

        // 2. clamp the closing day into that month (Feb 30 → Feb 28/29)
        $newEnd = $monthStart->copy()->addDays(
            min($day, $monthStart->daysInMonth) - 1
        );

        // 3. contiguous period → start = (end − 1 month) + 1 day
        $newStart = $newEnd->copy()->subMonth()->addDay();

        return [$newStart, $newEnd];
    }
}
