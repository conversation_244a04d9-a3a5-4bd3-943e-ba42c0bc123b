<?php

namespace App\Http\Requests\Penalties;

use Illuminate\Foundation\Http\FormRequest;

class PenaltiesDateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'penalty_date_from' => 'required|date_format:Y-m-d',
            'penalty_date_to' => 'required|date_format:Y-m-d',
        ];
    }
}
