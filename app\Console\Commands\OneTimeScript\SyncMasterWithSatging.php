<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Branch;
use App\Models\CompanyLeaveType;
use App\Models\Department;
use App\Models\Employee;
use App\Models\RequestGroup;
use App\Models\RequestWorkflow;
use App\Models\Role;
use App\Models\RoleHasPermission;
use App\Models\SubDepartment;
use App\Models\Title;
use App\Services\CompanySetup\TitlesService;
use App\Services\EmployeeChange\TransferEmployeeAttendancesAndTimecardsService;
use App\Services\EmployeeChange\TransferEmployeeLeaveRequestsService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class SyncMasterWithSatging extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:production_staging:data {file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'sync some data on production with data on staging.';

    private $titlesStgToProdMap = [];

    private $rolesStgToProdMap = [];

    private $departmentStgToProdMap = [];

    private $branchStgToProdMap = [];

    private $subDepartmentsExistsArr = [];

    // private $roles
    /**
     * Execute the console command.
     */
    public function __construct(
        private TransferEmployeeLeaveRequestsService $transferEmployeeLeaveRequestsService,
        private TransferEmployeeAttendancesAndTimecardsService $transferEmployeeAttendancesAndTimecardsService,
        private TitlesService $titlesService)
    {
        parent::__construct();
    }

    public function handle(

    ) {
        $s3Path = $this->argument('file');
        $fileContents = Storage::disk('s3')->get($s3Path);

        $tempDir = tempnam(sys_get_temp_dir(), 'RolesTitleCompany.xlsx');
        file_put_contents($tempDir, $fileContents);

        $data = Excel::toArray([], $tempDir);

        $sheet = $data[0];

        $columnsMap = [
            'employee_id' => 0,
            'title_id' => 1,
            'role_id' => 2,
            'branch_id' => 3,
            'managed_branch_ids' => 4,
            'managed_department_ids' => 5,
            'managed_sub_department_ids' => 6,
            'department_id' => 7,
            'sub_department_id' => 8,
        ];
        $employeeIds = [];
        $titleIds = [];
        $roleIds = [];
        $branchIds = [];

        $departmentIds = [];
        $subDepartmentIds = [];
        $count = 0;
        for ($i = 1; $i < count($sheet); $i++) {
            $employeeId = $sheet[$i][$columnsMap['employee_id']];
            if (isset($employeeId) && $employeeId != '') {
                $employeeIds[] = $employeeId;
            } else {
                echo 'employee id not found at row '.$i + 1 .PHP_EOL;

                continue;
            }
            $titleId = $sheet[$i][$columnsMap['title_id']];
            if (isset($titleId)) {
                $titleIds[] = $titleId;
            }
            $roleId = $sheet[$i][$columnsMap['role_id']];
            if (isset($roleId)) {
                $roleIds[] = $roleId;
            }
            $branchId = $sheet[$i][$columnsMap['branch_id']];
            if (isset($branchId)) {
                $branchIds[] = $branchId;
            }
            $managedBranchesRow = $sheet[$i][$columnsMap['managed_branch_ids']];
            if (isset($managedBranchesRow)) {
                $managedBranchesRow = str_replace(', ', ',', $managedBranchesRow);
                $managedBranchesRow = explode(',', $managedBranchesRow);
                $branchIds = array_merge($branchIds, $managedBranchesRow);
            }
            $managedDepartmentsRow = $sheet[$i][$columnsMap['managed_department_ids']];
            if (isset($managedDepartmentsRow)) {
                $managedDepartmentsRow = str_replace(', ', ',', $managedDepartmentsRow);
                $managedDepartmentsRow = explode(',', $managedDepartmentsRow);
                $departmentIds = array_merge($departmentIds, $managedDepartmentsRow);
            }

            $managedSubDepartmentsRow = $sheet[$i][$columnsMap['managed_sub_department_ids']];
            if (isset($managedSubDepartmentsRow)) {
                $managedSubDepartmentsRow = str_replace(', ', ',', $managedSubDepartmentsRow);
                $managedSubDepartmentsRow = explode(',', $managedSubDepartmentsRow);
                $subDepartmentIds = array_merge($subDepartmentIds, $managedSubDepartmentsRow);
            }

            $departmentId = $sheet[$i][$columnsMap['department_id']];
            if (isset($departmentId)) {
                $departmentIds[] = $departmentId;
            }
            $subDepartmentId = $sheet[$i][$columnsMap['sub_department_id']];
            if (isset($subDepartmentId)) {
                $subDepartmentIds[] = $subDepartmentId;
            }
            $count += 1;

            // if($count > 10)
            //     break;
        }
        echo ' parsed successfully '.$count.' rows'.PHP_EOL;
        // echo ('branch ids in excel sheet '. implode(' ', $branchIds) . PHP_EOL);

        $stg_employeesMap = DB::connection('staging_database')->table('employees')->whereIn('id', $employeeIds)->get()->keyBy('id');

        echo 'retrieved data from staging '.PHP_EOL;
        echo 'num employees retrieved '.count($stg_employeesMap).PHP_EOL;

        DB::beginTransaction();
        try {

            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            $this->syncAllBranches();
            $this->syncAllDepartments();
            $this->syncAllSubDepartments();
            $this->syncAllRoles();
            $this->syncAllTitles();
            $this->syncUUIDofLeaves();
            $this->syncAllRequestGroupsAndRequestWorkFlows();
            $countTesting = 0;
            $withError = 0;
            $bar = $this->output->createProgressBar(10);
            // $bar->start();
            for ($i = 1; $i < count($sheet); $i++) {

                $title = $branch = $role = $subDepartment = null;

                $employeeId = $sheet[$i][$columnsMap['employee_id']];
                $prod_employee = Employee::find($employeeId); // all employees exists already on production
                if (! isset($employeeId) || ! isset($stg_employeesMap[$employeeId]) || ! isset($prod_employee)) {
                    continue;
                }

                $oldProdTitle = $prod_employee->title;
                // $prod_employee = $this->syncAndGetEmployee($stg_employeesMap[$employeeId]);

                $titleId = $sheet[$i][$columnsMap['title_id']];
                $departmentId = $sheet[$i][$columnsMap['department_id']];
                $branchId = $sheet[$i][$columnsMap['branch_id']];
                $roleId = $sheet[$i][$columnsMap['role_id']];
                $branchesString = str_replace(', ', ',', $sheet[$i][$columnsMap['managed_branch_ids']]);
                $managedBranchesIds = explode(',', $branchesString);
                $managedDepartmentsIds = explode(',', $sheet[$i][$columnsMap['managed_department_ids']]);
                $managedSubDepartmentsIds = explode(',', $sheet[$i][$columnsMap['managed_sub_department_ids']]);
                $subDepartmentId = $sheet[$i][$columnsMap['sub_department_id']];

                if (isset($titleId)) {
                    $titleId = $this->titlesStgToProdMap[$titleId] ?? $titleId;
                    $title = Title::withTrashed()->find($titleId);
                    if ($title->company_id != $prod_employee->company_id) {
                        echo 'title company id is different from employee company id at row : '.$i + 1;
                        $withError = 1;
                    }
                    $prod_employee->title_id = $title->id;
                }

                if (isset($roleId)) {
                    $roleId = $this->rolesStgToProdMap[$roleId] ?? $roleId;
                    $role = Role::withTrashed()->find($roleId);
                    if (isset($title) && $title->role_id != $oldProdTitle->role_id) {
                        $prod_employee->user->removeRole($oldProdTitle->role);
                    }
                    if ($role->company_id != $prod_employee->company_id) {
                        echo 'role company id is different from employee company id at row : '.$i + 1;
                        $withError = 1;
                    }
                    $prod_employee->user->assignRole($role);
                }

                if (isset($branchId)) {
                    $branchId = $this->branchStgToProdMap[$branchId] ?? $branchId;
                    $branch = Branch::withTrashed()->find($branchId);
                    if ($prod_employee->branch_id != $branch->id) {
                        $dummyOutput = new \stdClass;
                        config(['globals.user' => $prod_employee->user]);
                        $this->transferEmployeeLeaveRequestsService->transfer(['employee_id' => $prod_employee->id], $dummyOutput);
                        $this->transferEmployeeAttendancesAndTimecardsService->transfer(['employee_id' => $prod_employee->id], $dummyOutput);
                        config(['globals.user' => null]);
                    }
                    if ($branch->company_id != $prod_employee->company_id) {
                        echo 'branch company id is different from employee company id at row : '.$i + 1;
                        $withError = 1;
                    }
                    $prod_employee->branch_id = $branch->id;
                }

                $prod_employee->save();

                $syncedManagedBranches = $syncedManagedDepartments = $syncedManagedSubDepartments = [];
                foreach ($managedBranchesIds as $branchId) {
                    if ($branchId == '') {
                        continue;
                    }
                    if ($branchId > 1000) {
                        //dd("branch id parsed wrongly : ", $branchId);
                        break;
                    }
                    $branchId = $this->branchStgToProdMap[$branchId] ?? $branchId;
                    $branch = Branch::withTrashed()->find($branchId);
                    if ($branch->company_id != $prod_employee->company_id) {
                        echo 'managed branch company id is different from employee company id at row : '.$i + 1 .' branch id is '.$branch->id;
                        $withError = 1;
                    }
                    $branchId = $this->branchStgToProdMap[$branchId] ?? $branchId;
                    $syncedManagedBranches[] = $branchId;
                }
                if (count($syncedManagedBranches) > 0) {
                    $prod_employee->branches()->sync($syncedManagedBranches);
                }

                foreach ($managedDepartmentsIds as $departmentId) {
                    if ($departmentId == '') {
                        continue;
                    }

                    $departmentId = $this->departmentStgToProdMap[$departmentId] ?? $departmentId;
                    $department = Department::withTrashed()->find($departmentId);
                    if ($department->company_id != $prod_employee->company_id) {
                        echo 'managed department company id is different from employee company id at row : '.$i + 1 .' department id is '.$department->id;
                        $withError = 1;
                    }
                    $syncedManagedDepartments[] = $department->id;
                }
                if (count($syncedManagedDepartments) > 0) {
                    $prod_employee->managedDepartments()->sync($syncedManagedDepartments);
                }

                foreach ($managedSubDepartmentsIds as $subDepartmentId) {
                    if ($subDepartmentId == '') {
                        continue;
                    }
                    $syncedManagedSubDepartments[] = $subDepartmentId; // sub department id on production is same as staging
                }
                if (count($syncedManagedSubDepartments) > 0) {
                    $prod_employee->managedSubDepartments()->sync($syncedManagedSubDepartments);
                }

                $countTesting += 1;
                echo 'synced employee : '.$employeeId.' at row : '.$i + 1 .PHP_EOL;

                // if ($countTesting > 10) {
                //     break;
                // }

                // $bar->advance();
            }

            // $bar->finish();
            echo 'synced '.$countTesting.' employees '.PHP_EOL;
            if ($withError == 1) {
                //dd('error in data');
            }

            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
            DB::commit();
        } catch (Exception $e) {
            //// dd($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
        unlink($tempDir);

    }

    public function syncAllRequestGroupsAndRequestWorkFlows()
    {
        $stg_requestGroups = DB::connection('staging_database')->table('request_groups')->get();
        $stg_requestWorkFlows = DB::connection('staging_database')->table('request_workflows')->get();

        foreach ($stg_requestGroups as $stg_requestGroup) {
            $prod_requestGroup = RequestGroup::create((array) $stg_requestGroup);
            echo 'added request group with id : '.$prod_requestGroup->id.' staging id was : '.$stg_requestGroup->id.PHP_EOL;
        }

        foreach ($stg_requestWorkFlows as $stg_requestWorkFlow) {
            $prod_requestWorkflow = $stg_requestWorkFlow;
            $prod_requestWorkflow->role_id = $this->rolesStgToProdMap[$stg_requestWorkFlow->role_id] ?? $stg_requestWorkFlow->role_id;
            $prod_requestWorkflow = RequestWorkflow::create((array) $prod_requestWorkflow);
            echo 'added request workflow with id : '.$prod_requestWorkflow->id.' staging id was : '.$stg_requestWorkFlow->id.PHP_EOL;
        }
    }

    public function syncAllDepartments()
    {
        $stg_departments = DB::connection('staging_database')->table('departments')->get();
        foreach ($stg_departments as $stg_department) {

            $prod_department = Department::where('company_id', $stg_department->company_id)->where('id', $stg_department->id)
                ->where(function ($q) use ($stg_department) {
                    $q->where('name', $stg_department->name)
                        ->orWhere('name_ar', $stg_department->name_ar)
                        ->orWhere('name_en', $stg_department->name_en);
                })->withTrashed()->first();

            if (! isset($prod_department)) {
                $prod_department = Department::create((array) $stg_department);
                echo 'created department '.$prod_department->name_en.' with id: '.$prod_department->id.' | id on staging is: '.$stg_department->id.PHP_EOL;
            } else {
                $prod_department->update((array) $stg_department);
            }
            $this->departmentStgToProdMap[$stg_department->id] = $prod_department->id;
        }
    }

    public function syncAllBranches()
    {
        $stg_branches = DB::connection('staging_database')->table('branches')->get();
        foreach ($stg_branches as $stg_branch) {

            $prod_branch = Branch::where('company_id', $stg_branch->company_id)->where('id', $stg_branch->id)
                ->where(function ($q) use ($stg_branch) {
                    $q->where('name', $stg_branch->name)
                        ->orWhere('name_ar', $stg_branch->name_ar)
                        ->orWhere('name_en', $stg_branch->name_en);
                })->withTrashed()->first();

            if (! isset($prod_branch)) {
                $prod_branch = Branch::create((array) $stg_branch);
                echo 'created branch '.$prod_branch->name_en.' with id: '.$prod_branch->id.' | id on staging is: '.$stg_branch->id.PHP_EOL;
            } else {
                $prod_branch->update((array) $stg_branch);
            }
            $this->branchStgToProdMap[$stg_branch->id] = $prod_branch->id;
        }
    }

    public function syncAllSubDepartments()
    {
        $stg_subDepartments = DB::connection('staging_database')->table('sub_departments')->get();
        foreach ($stg_subDepartments as $stg_subDepartment) {

            $subDepartmentArr = (array) $stg_subDepartment;
            $subDepartmentArr['department_id'] = $this->departmentStgToProdMap[$stg_subDepartment->department_id] ?? $stg_subDepartment->department_id;
            $prod_subDepartment = SubDepartment::create($subDepartmentArr);
            echo 'created sub department '.$prod_subDepartment->name_en.' with id: '.$prod_subDepartment->id.' | id on staging is: '.$stg_subDepartment->id.PHP_EOL;
        }

    }

    public function syncAllRoles()
    {
        $stg_roles = DB::connection('staging_database')->table('spatie_roles')->get();
        foreach ($stg_roles as $stg_role) {

            $prod_role = Role::where('company_id', $stg_role->company_id)->where('id', $stg_role->id)
                ->where(function ($q) use ($stg_role) {
                    $q->where('name', $stg_role->name)
                        ->orWhere('name_ar', $stg_role->name_ar)
                        ->orWhere('name_en', $stg_role->name_en);
                })->withTrashed()->first();

            if (! isset($prod_role)) {
                // echo()
                // echo('role not found ' . $stg_role->id . ' company id: '. $stg_role->company_id . ' config user  employee_id '. (config('globals.user')?->employee_id) . PHP_EOL);
                $prod_role = Role::create((array) $stg_role);
                echo 'created role '.$prod_role->name_en.' with id: '.$prod_role->id.' | id on staging is: '.$stg_role->id.PHP_EOL;
            } else {
                $prod_role->update((array) $stg_role);
            }
            $stg_rolePermissionIds = DB::connection('staging_database')->table('role_has_permissions')->where('role_id', $stg_role->id)->pluck('permission_id')->toArray();

            $prod_role->syncPermissions([]);
            foreach ($stg_rolePermissionIds as $permissionId) {
                RoleHasPermission::create(['role_id' => $prod_role->id, 'permission_id' => $permissionId]);
            }
            $stgScopeIds = DB::connection('staging_database')->table('role_scope')->where('role_id', $stg_role->id)->pluck('scope_id')->toArray();
            $prod_role->scopes()->sync($stgScopeIds);

            $this->rolesStgToProdMap[$stg_role->id] = $prod_role->id;
        }
    }

    public function syncAllTitles()
    {
        $stg_titles = DB::connection('staging_database')->table('titles')->get();
        foreach ($stg_titles as $stg_title) {

            $prod_title = Title::where('company_id', $stg_title->company_id)->where('id', $stg_title->id)
                ->where(function ($q) use ($stg_title) {
                    $q->where('name', $stg_title->name)
                        ->orWhere('name_ar', $stg_title->name_ar)
                        ->orWhere('name_en', $stg_title->name_en);
                })->withTrashed()->first();

            $stgTitleArr = (array) $stg_title;
            $stgTitleArr['role_id'] = $this->rolesStgToProdMap[$stg_title->role_id] ?? $stg_title->role_id;
            $stgTitleArr['department_id'] = $this->departmentStgToProdMap[$stg_title->department_id] ?? $stg_title->department_id;
            if (! isset($prod_title)) {
                $prod_title = Title::create($stgTitleArr);
                echo 'created title '.$prod_title->name_en.' with id: '.$prod_title->id.' | id on staging is: '.$stg_title->id.PHP_EOL;
            } else {
                $prod_title->update($stgTitleArr);
            }

            $this->titlesStgToProdMap[$stg_title->id] = $prod_title->id;
        }
    }

    public function syncUUIDofLeaves()
    {
        $stg_leaves = DB::connection('staging_database')->table('company_leave_types')->whereNull('deleted_at')->get();
        foreach ($stg_leaves as $stg_leave) {
            $prod_leave = CompanyLeaveType::where('company_id', $stg_leave->company_id)->where('id', $stg_leave->id)
                ->where(function ($q) use ($stg_leave) {
                    $q->where('name', $stg_leave->name)
                        ->orWhere('name_ar', $stg_leave->name_ar)
                        ->orWhere('name_en', $stg_leave->name_en);
                })->first();

            if (isset($prod_leave)) {
                $prod_leave->update(['uuid' => $stg_leave->uuid]);
            } else {
                echo 'leave not found : '.$stg_leave->id.' company id: '.$stg_leave->company_id.PHP_EOL;
                //dd('leave not found');
            }
        }
    }
}
