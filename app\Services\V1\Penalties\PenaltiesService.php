<?php

namespace App\Services\V1\Penalties;

use App\Exceptions\UnprocessableException;
use App\Jobs\V1\PenaltyApprovedPushNotificationJob;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\Repository;
use App\Repositories\V1\PenaltyRepositories\PenaltyGroupRepository;
use App\Repositories\V1\PenaltyRepositories\PenaltyPolicyRepository;
use App\Repositories\V1\PenaltyRepositories\PenaltyRepository;
use App\Services\BaseService;
use App\Services\TimeTracking\BusinessServices\ApplyRequestCycleRulesService;
use App\Services\TimeTracking\BusinessServices\AssignApprovalRequestToEmployeesService;
use App\Traits\DataPreparation;
use App\Traits\QueriesHelper;
use App\Traits\V1\EmployeeRequestsTrait;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Traits\WorkflowTrait;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Messaging\Messages;
use App\Jobs\AssignApprovalsJob;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;
use Workflow\WorkflowStub;

class PenaltiesService extends BaseService
{
    use DataPreparation, EmployeeRequestsTrait, PayrollHelper, QueriesHelper, WorkflowTrait, PrepareAssignRequestCycleDataTrait;

    private Repository $employeeRepository;

    private Repository $requestCycleRepository;

    private Repository $approvalCycleRepository;

    private Repository $activityLogRepository;

    private $payrollStartDate;

    private $payrollEndDate;

    public function __construct(
        PenaltyRepository $penaltyRepository,
        private PenaltyGroupRepository $penaltyGroupsRepository,
        private AssignApprovalRequestToEmployeesService $assignApprovalRequestToEmployeesService,
        private SystemSettingRepository $systemSettingRepository,
        private PenaltyPolicyRepository $penaltyPolicyRepository,
        private ApplyRequestCycleRulesService $applyRequestCycleRulesService,
        private PayrollsRepository $payrollsRepository)
    {
        parent::__construct($penaltyRepository);
        $this->employeeRepository = Repository::getRepository('Employee');
        $this->requestCycleRepository = Repository::getRepository('RequestCycle');
        $this->approvalCycleRepository = Repository::getRepository('ApprovalCycle');
        $this->activityLogRepository = Repository::getRepository('ActivityLog');
    }

    public function checkPayrollPeriod(string $penaltyDate)
    {
        $monthlyClosingDay = $this->getMonthlyClosingDay($penaltyDate);
        if (! isset($monthlyClosingDay)) {
            throw new UnprocessableException('Monthly closing day not set');
        }

        $penaltyPayrollMonth = $this->payrollsRepository->payrollCoversDate($penaltyDate);

        if (isset($penaltyPayrollMonth) && $penaltyPayrollMonth->status == 'finalized') {
            throw new UnprocessableException('Penalty date is in a finalized payroll period');
        }

        if (isset($penaltyPayrollMonth)) {
            $this->payrollStartDate = $penaltyPayrollMonth->start;
            $this->payrollEndDate = $penaltyPayrollMonth->end;
        }
    }

    public function updateDataBasedOnPenaltyGroup($penalty, $data)
    {

        $penaltyGroupId = $data['penalty_group_id'] ?? null;
        $oldPenaltyGroupId = $penalty->penalty_group_id;

        if ($penaltyGroupId != $oldPenaltyGroupId) {
            if (isset($data['penalty_group_id'])) {
                $employeePenaltyGroupIds = $this->penaltyGroupsRepository->getPenaltyGroupsApplicableToEmployee($penalty->employee_id)->pluck('id')->toArray();

                if (! in_array($data['penalty_group_id'], $employeePenaltyGroupIds)) {
                    throw new UnprocessableException(trans('messages.penalty_group_not_applicable_to_employee'));
                }

                $payrollMonthlyClosingDate = $this->getMonthlyClosingDay();
                $currentEnd = Carbon::now()->days($payrollMonthlyClosingDate)->endOfDay()->toDateTimeString();
                $currentStart = $this->getPayrollMonthStartBasedOnMonthEnd($currentEnd);

                $data['recurrence'] =
                $this->repository->getEmployeeRecurrenceOfPenaltyGroup($data['penalty_group_id'],
                    $penalty->employee_id, $currentStart, $currentEnd)->recurrence + 1;
            } else {
                $data['recurrence'] = null;
                $data['penalty_group_id'] = null;
            }
        }

        return $data;
    }

    public function add($data)
    {
        $this->checkPayrollPeriod($data['date']);

        if (isset($data['penalty_group_id'])) {
            $employeePenaltyGroupIds = $this->penaltyGroupsRepository->getPenaltyGroupsApplicableToEmployee($data['employee_id'])->pluck('id')->toArray();

            if (! in_array($data['penalty_group_id'], $employeePenaltyGroupIds)) {
                throw new UnprocessableException(trans('messages.penalty_group_not_applicable_to_employee'));
            }
        }

        // $cycle = $this->requestCycleRepository->getRelatedCycle(
        //     config('globals.REQUEST_CYCLE_TYPES.PENALTY')
        // );

        // if (!isset($cycle) || sizeof($cycle) == 0) {
        //     throw new UnprocessableException(trans('messages.penalties_has_no_cycle'));
        // }

        $employee = $this->employeeRepository->getById($data['employee_id']);

        $terminationDate = $employee->employeeInfo?->termination_date;
        if (isset($terminationDate) && $data['date'] >= $terminationDate) {
            throw new UnprocessableException(trans('messages.penalty_after_termination_date'));
        }

        $payrollMonthlyClosingDate = $this->getMonthlyClosingDay();
        $currentEnd = Carbon::now()->days($payrollMonthlyClosingDate)->endOfDay()->toDateTimeString();
        $currentStart = $this->getPayrollMonthStartBasedOnMonthEnd($currentEnd);

        $data['submitted_by'] = auth()->user()->employee_id;
        $data['branch_id'] = $employee->branch_id;
        if (isset($data['penalty_group_id'])) {
            $data['recurrence'] =
            $this->repository->getEmployeeRecurrenceOfPenaltyGroup($data['penalty_group_id'],
                $data['employee_id'], $currentStart, $currentEnd)->recurrence + 1;
            $penaltyGroup = $this->penaltyGroupsRepository->find($data['penalty_group_id']);
            $data['unit'] = $penaltyGroup?->unit ?? 'days';
        }

        $penalty = $this->repository->add($data);

        $requesterRoleIds = auth()->user()->roles->pluck('id')->toArray();

        AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('penalty', $penalty, $requesterRoleIds));

        return $penalty;
    }

    public function update($id, $data)
    {

        $penalty = $this->repository->findOrFail($id);
        $this->checkPayrollPeriod($penalty->date);

        $terminationDate = $penalty->employee->employeeInfo?->termination_date;
        if (isset($terminationDate) && $data['date'] >= $terminationDate) {
            throw new UnprocessableException(trans('messages.penalty_after_termination_date'));
        }

        $approverTookAction = $this->approvalCycleRepository->approverTookAction($penalty->id, 'penalty');

        if ($approverTookAction) {
            throw new UnprocessableException(trans('messages.cant_edit_approver_took_action'));
        }

        $data = $this->updateDataBasedOnPenaltyGroup($penalty, $data);

        $penalty->update($data);

        return $penalty;
    }

    public function cancel($id)
    {

        $penalty = $this->repository->findOrFail($id);
        $this->checkPayrollPeriod($penalty->date);

        $this->actionOnPenalty($penalty, 'cancel');
    }

    public function approvePenalty($id, $data)
    {
        $penalty = $this->repository->findOrFail($id);
        $this->checkPayrollPeriod($penalty->date);
        $data = $this->updateDataBasedOnPenaltyGroup($penalty, $data);

        if (isset($data['penalty_group_id']) && isset($penalty->recurrence)) {

            $corrospondingPolicy = $this->penaltyPolicyRepository
                ->getAmountRangeForPenaltyGroupAndRecurrence($data['penalty_group_id'], $penalty->recurrence);

            if (! isset($corrospondingPolicy)) {
                $corrospondingPolicy = $this->penaltyPolicyRepository
                    ->getAmountRangeForPenaltyGroupWithGreatestSequence($data['penalty_group_id']);
            }

            if (isset($corrospondingPolicy) &&
                ($data['amount'] < $corrospondingPolicy->minimum_amount || $data['amount'] > $corrospondingPolicy->maximum_amount)) {
                throw new UnprocessableException(trans('messages.penalty_amount_not_in_range'));
            }

        }

        $penalty->update($data);
        $penalty->refresh();
        if ($penalty->status != config('globals.REQUEST_STATUSES.PENDING')) {
            throw new UnprocessableException(trans('messages.workflow_is_completed'));
        }

        $this->actionOnPenalty($penalty, 'approve');

        // $preparedRequestData = $this->prepareRequestCycleData($penalty, config('globals.REQUEST_STATUSES.APPROVED'));

        // $approvalOutput = new \stdClass();
        // $this->applyRequestCycleRulesService->perform($preparedRequestData, $approvalOutput);

        // if (isset($approvalOutput->Error)) {
        //     throw new UnprocessableException(getTranslatedErrorMessage($approvalOutput->Error));
        // }

        // if ($penalty->status == config('globals.REQUEST_STATUSES.PENDING')) //  action has been taken but not final status
        // {
        //     $penalty->status = config('globals.REQUEST_STATUSES.IN_PROCESS');
        // }
        // else if($penalty->status == config('globals.REQUEST_STATUSES.APPROVED'))
        // {
        //     dispatch(new PenaltyApprovedPushNotificationJob($penalty))->afterResponse();  //  done
        // }
    }

    public function rejectPenalty($id)
    {
        $penalty = $this->repository->findOrFail($id);
        $this->checkPayrollPeriod($penalty->date);
        
        if ($penalty->status != config('globals.REQUEST_STATUSES.PENDING')) {
            throw new UnprocessableException(trans('messages.workflow_is_completed'));
        }

        $this->actionOnPenalty($penalty, 'reject');
        // $preparedRequestData = $this->prepareRequestCycleData($penalty, config('globals.REQUEST_STATUSES.REJECTED'));

        // $approvalOutput = new \stdClass();
        // $this->applyRequestCycleRulesService->perform($preparedRequestData, $approvalOutput);

        // if (isset($approvalOutput->Error)) {
        //     throw new UnprocessableException(getTranslatedErrorMessage($approvalOutput->Error));
        // }

        // if ($penalty->status == config('globals.REQUEST_STATUSES.PENDING')) //  action has been taken but not final status
        // {
        //     $penalty->status = config('globals.REQUEST_STATUSES.IN_PROCESS');
        // }

    }

    public function actionOnPenalty($penalty, $actionType): void
    {
        try {
            $roleIds = config('globals.user')->roles->pluck('id')->toArray();
            $userCanTakeAnAction = $this->canTakeAnAction($roleIds, $penalty->id, config('globals.REQUEST_WORKFLOW_TYPES.PENALTY'), $actionType);
            if (! $userCanTakeAnAction) {
                throw new UnprocessableException(trans('messages.can_not_take_this_action'));
            }

            $this->doAnAction($actionType);

            if ($this->checkRequestIsCompleted($penalty->employeeRequest)) {
                $finalStatus = $this->getFinalStatus($penalty->employeeRequest, $actionType);
                $this->updateEntity($penalty, $finalStatus);
                $this->updateRequest($penalty->employeeRequest, $finalStatus);
            }
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException(trans('messages.can_not_take_this_action'));
        }

    }

    public function canApprove($employeeApproves)
    {
        if ($this->isAuthorizedToForceApprove()) {
            return true;
        }

        $emploeeId = auth()->user()->employee_id;

        foreach ($employeeApproves as $employeeApproval) {
            $employeeIds = array_column($employeeApproval->employees, 'id');
            if (in_array($emploeeId, $employeeIds) and $employeeApproval->pivot->operator == 'or') {
                if ($employeeApproval->pivot->status == config('globals.REQUEST_STATUSES.PENDING')) {
                    return true;
                } else { // if the status is approved or rejected then don't show approve or reject button
                    return false;
                }

            }
        }

        foreach ($employeeApproves as $employeeApproval) {
            $employeeIds = array_column($employeeApproval->employees, 'id');
            if (in_array($emploeeId, $employeeIds)) {
                if ($employeeApproval->pivot->status == config('globals.REQUEST_STATUSES.PENDING')) {
                    return true;
                } else { // if the status is approved or rejected then don't show approve or reject button
                    return false;
                }
            }

            if (! in_array($emploeeId, $employeeIds) && $employeeApproval->pivot->status == config('globals.REQUEST_STATUSES.PENDING')) {
                return false;
            }
        }

        return false;
    }

    public function getPenalty($id)
    {
        $penalty = $this->repository->findOrFail($id);

        $penalty = $this->repository->getPenaltiesByFilters(['id' => $id])->first(); // to get the relations

        $approvalCylce = $this->getApprovalCycle($penalty, $penalty->employee);

        $penalty->employeeApproves = $approvalCylce;

        $penalty->can_approve = $this->canApprove($approvalCylce);

        return $penalty;
    }

    public function getWorkerPenalties($data)
    {
        $employeeId = auth()->user()->employee_id;
        $penalties = $this->repository->getWorkerPenalties($employeeId, $data['penalty_date_from'], $data['penalty_date_to']);

        return $penalties;
    }

    public function getPenaltiesByFilter($data)
    {

        $penalties = $this->repository->getPenaltiesByFilters($data);

        foreach ($penalties as $penalty) {
            $approvalCylce = $this->getApprovalCycle($penalty, $penalty->employee);
            $penalty->can_approve = $this->canApprove($approvalCylce);
        }

        return $penalties;
    }

    public function getPendingPenaltiesCount()
    {
        $count = $this->repository->getPendingPenaltiesCount();

        return $count;
    }

    public function getRequestData($penalty)
    {
        return [
            'requestable_id' => $penalty->id,
            'requestable_type' => config('globals.REQUEST_WORKFLOW_TYPES.PENALTY'),
            'role_ids' => auth()->user()->roles->pluck('id')->toArray(),
        ];
    }

    public function getPenaltyEditHistory($id)
    {
        $penalty = $this->repository->findOrFail($id);

        $approvals = $penalty->employeeApproves;

        $approvalIds = $approvals->map(function ($approval) {
            return $approval->pivot->id;
        })->toArray();

        $approvalLogs = $this->activityLogRepository->approveOrCancelLogs($approvalIds, $penalty->id, 'penalty');

        return $approvalLogs;
    }
}
