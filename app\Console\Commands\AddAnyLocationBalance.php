<?php

namespace App\Console\Commands;

use App\Services\V1\Attendance\AddAnyLocationBalanceService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddAnyLocationBalance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:add-any-location-balance';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $this->info('Starting to add any location balance');

            AddAnyLocationBalanceService::addAnyLocationBalance();

            DB::commit();
            $this->info('Any location balance added successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Failed to add any location balance');
            $this->error($e->getMessage());
        }
    }
}
