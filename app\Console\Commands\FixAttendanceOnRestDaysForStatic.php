<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use App\Models\Employee;
use App\Models\EmployeeLeaveRequest;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixAttendanceOnRestDaysForStatic extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-attendance-on-rest-days-for-static';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix attendances on rest days for static work-type employees';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $fromDate = '2025-04-01';
        $toDate = '2025-04-30';

        $this->info("Starting fix of attendance for static employees in companies from {$fromDate} to {$toDate}...");

        try {
            DB::transaction(function () use ($fromDate, $toDate) {
                // Fetch employees with non-dynamic onsite policies
                $employees = Employee::with('title.workTypePolicy')
                    ->where('status', '!=', 'terminated')
                    ->whereHas('title.workTypePolicy', function ($q) {
                        $q->where('work_days_type', '!=', 'dynamic_onsite');
                    })
                    ->get();

                foreach ($employees as $employee) {
                    $policy = $employee->title->workTypePolicy;
                    $restDayCodes = array_map('trim', explode(',', $policy->rest_days)); // e.g. ['sat','fri']

                    // Fetch attendances in range
                    $attendances = Attendance::where('employee_id', $employee->id)
                        ->whereDate('date', '>=', $fromDate)
                        ->whereDate('date', '<=', $toDate)
                        ->get();

                    foreach ($attendances as $attendance) {
                        // If marked late but also on-time, remove on_time tag
                        $tags = $attendance->entityTags->pluck('tag')->toArray();
                        if (in_array('late', $tags) && in_array('on_time', $tags)) {
                            // Remove only the on_time EntityTag record
                            $attendance->entityTags()
                                ->where('tag', 'on_time')
                                ->delete();
                            $this->info("Removed 'on_time' EntityTag for attendance #{$attendance->id}");
                        }

                        // Determine the attendance day code (e.g. 'sat', 'mon')
                        $dayCode = strtolower((new Carbon($attendance->date))->format('D'));

                        // If attendance falls on a rest day
                        if (in_array($dayCode, $restDayCodes)) {
                            // Delete any rest-day leave requests for this date
                            $requests = EmployeeLeaveRequest::where('employee_id', $employee->id)
                                ->whereDate('from', $attendance->date)
                                ->get();

                            foreach ($requests as $req) {
                                $req->delete();
                                $this->info("Deleted rest-day leave request #{$req->id} for {$attendance->date}");
                            }

                            // Update associated timecard via slotable relation
                            $timecard = $attendance->slotable;
                            if ($timecard) {
                                $timecard->name = 'unscheduled timecard';
                                $timecard->from = $attendance->clockIn->date ?? $timecard->from;
                                $timecard->to = $attendance->clockOut->date ?? Carbon::parse($attendance->clockIn->date)->addHours($attendance->employee->title->working_hours)->toDateTimeString();
                                $timecard->save();

                                $this->info("Updated timecard #{$timecard->id} to unscheduled for attendance #{$attendance->id}");
                            }
                        }
                    }
                }
            });

            $this->info('Attendance fix complete.');

            return 0;

        } catch (\Throwable $e) {
            DB::rollBack();
            $this->error('❌ Error: ' . $e->getMessage());

            return 1;
        }
    }
}
