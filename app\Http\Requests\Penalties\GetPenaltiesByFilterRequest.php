<?php

namespace App\Http\Requests\Penalties;

use App\Rules\ScopeSubDepartmentIdsRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;

class GetPenaltiesByFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules['branch_ids'] = ['array'];
        $rules['branch_ids.*'] = ['required', 'integer', (new Exists('branches', 'id'))->where(function ($query) {
            $query->where('company_id', auth()->user()->company_id) // company rule doesn't apply here
                ->whereNull('deleted_at');
        }), ];
        $rules['title_ids'] = ['array'];
        $rules['title_ids.*'] = ['required', 'integer', (new Exists('titles', 'id'))->where(function ($query) {
            $query->where('company_id', auth()->user()->company_id) // company rule doesn't apply here
                ->whereNull('deleted_at');
        }), ];
        $rules['department_ids'] = ['array'];
        $rules['department_ids.*'] = ['required', 'integer', (new Exists('departments', 'id'))->where(function ($query) {
            $query->where('company_id', auth()->user()->company_id) // company rule doesn't apply here
                ->whereNull('deleted_at');
        }), ];
        $rules['page'] = ['integer', 'min:1'];
        $rules['page_size'] = ['integer', 'min:0']; // 0 means no pagination
        $rules['search_value'] = ['min:1', 'max:30'];
        $rules['penalty_date_from'] = 'date_format:Y-m-d';
        $rules['penalty_date_to'] = ['date_format:Y-m-d', 'after_or_equal:penalty_date_from'];
        $rules['statuses'] = ['array'];
        $rules['statuses.*'] = ['string', 'in:approved,rejected,pending,in_process,cancelled'];
        $rules['types'] = ['array'];
        $rules['types.*'] = ['required', 'integer', (new Exists('penalty_groups', 'id'))->where(function ($query) {
            $query->where('company_id', auth()->user()->company_id) // company rule doesn't apply here
                ->whereNull('deleted_at');
        })];
        $rules['sub_department_ids'] = ['array'];
        $rules['sub_department_ids.*'] = ['integer', new ScopeSubDepartmentIdsRule];

        return $rules;
    }
}
