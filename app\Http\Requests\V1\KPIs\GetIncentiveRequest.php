<?php

namespace App\Http\Requests\V1\KPIs;

use Illuminate\Foundation\Http\FormRequest;

class GetIncentiveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'page' => ['integer', 'min:1'],
            'page_size' => ['integer', 'min:0'],
            'search_value' => ['min:1', 'max:30'],
            'payout_frequency' => ['string', 'in:monthly,quarterly,semi_annually,annually'],
            'month' => ['string'],
            'year' => ['string'],
            'branch_ids' => ['array'],
            'branch_ids.*' => ['integer'],
            'title_ids' => ['array'],
            'title_ids.*' => ['integer'],
            'department_ids' => ['array'],
            'department_ids.*' => ['integer'],
            'status' => ['string', 'in:finalized,drafted,not_finalized'],
        ];
    }
}
