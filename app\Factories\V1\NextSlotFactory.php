<?php

namespace App\Factories\V1;

use App\Facades\DistanceFacade;
use App\FeatureToggles\Unleash;
use App\Models\Employee;
use App\Repositories\Repository;
use App\Repositories\V1\Attendance\CicoRepository;
use App\Repositories\V1\Attendance\TimecardRepository;
use App\Repositories\V1\BranchRepository;
use App\Repositories\V1\Holidays\PublicHolidaysRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use App\Traits\CICOHelper;
use App\Util\AttendanceUtil;
use Carbon\Carbon;

class NextSlotFactory
{
    use CICOHelper;

    private $branchRepository;

    private $employee;

    private $user;

    private $closestBranch;

    private $locationBranch;

    private $hasAnyLocationPermission;

    private $hasAnyBranchPermission;

    private $cicoRepository;

    private $lastClockInWithoutOut;

    private $nextSlotResponse;

    private $employeeLeaveRequestRepository;

    private $timecardRepository;

    private $attendanceSettingRepository;

    private $publicHolidayRepository;

    private $employeeJustDidCico;

    private $allowedMinutesToClockInBefore;

    private $closestTimecard;

    private $now;

    private $unleash;

    private $leave;

    private $publicHoliday;

    private $clockOutDeadline;

    private $clockInLocation;

    private $clockOutLocation;

    private $isInCiLocation;

    private $isInCoLocation;

    private $closestBranchIsTimecardBranch;

    private $correctCustomClockIn;

    private $correctCustomClockOut;

    private $timecardHasClockIn;

    private $slotBranch;

    private $clockOutDeadlineMinutes;

    public function __construct(Employee $employee)
    {
        $this->branchRepository = new BranchRepository;
        $this->employee = $employee;
        $this->user = $this->employee->user;
        $this->employeeLeaveRequestRepository = new EmployeeLeaveRequestRepository;
        $this->cicoRepository = new CicoRepository;
        $this->timecardRepository = new TimecardRepository;
        $this->publicHolidayRepository = new PublicHolidaysRepository;
        $this->attendanceSettingRepository = Repository::getRepository('AttendanceSetting');
        $this->now = Carbon::now();
        $this->unleash = app(Unleash::class);
        $this->nextSlotResponse = [
            'id' => null,
            'name' => null,
            'color' => null,
            'from' => null,
            'to' => null,
            'verified_ci_date' => null,
            'verified_ci_id' => null,
            'slot_branch' => null,
            'location_branch' => null,
            'type' => null,
            'unverified_ci_date' => null,
            'unverified_ci_id' => null,
            'clockInable' => true,
            'status' => null,
            'case' => null,
        ];
        $this->clockInLocation = null;
        $this->clockOutLocation = null;
        $this->slotBranch = null;
        $this->isInCiLocation = false;
        $this->isInCoLocation = false;
    }

    public function createNextSlot($latitude, $longitude)
    {
        $this->prepareData($latitude, $longitude);
        $this->fillSlotBranch(); // this is set if there's a close timecard and this timecard has an assigned branch (not a custom location )
        $this->fillLocationBranch($latitude, $longitude); // this is set if my current location is in a company branch
        $this->fillClockInable(); // this determines if mobile should show clock in/out button  whatever it's verified or unverified or any status
        $this->fillStatus(); // this determines the status (verified / off-shift / (off-branch or unverified)
        $this->fillCase(); // if timecard is off-shift check the reason of off-shift  (early to timecard or late)
        $this->fillLastClockInData(); // check If I did a clock in without a clock out  => return it to clock out for it
        $this->fillSlotData(); // return either timecard or leave if there's no timecard

        return $this->nextSlotResponse;
    }

    public function prepareData($latitude, $longitude): void
    {
        $cicoInTimeRange = $this->cicoRepository->cicoForEmployeeInTimeRange($this->employee->id, AttendanceUtil::MINIMUM_CICO_TIME_DIFFERENCE);
        $this->employeeJustDidCico = isset($cicoInTimeRange);
        $this->fillClosestBranch($latitude, $longitude);
        $this->hasAnyLocationPermission = $this->user->hasPermissionTo('any_location', 'user-api');
        $this->hasAnyBranchPermission = $this->user->hasPermissionTo('any_branch', 'user-api');
        $this->lastClockInWithoutOut = $this->cicoRepository->getLastClockInWithoutOutForEmployeeInTheLastDay($this->employee->id);
        $this->allowedMinutesToClockInBefore = $this->attendanceSettingRepository->findAttendanceSetting(AttendanceUtil::ALLOWED_MINUTES_TO_CHECK_IN_BEFORE)->value ?? AttendanceUtil::DEFAULT_ALLOWED_MINUTES_TO_CHECK_IN_BEFORE;
        $this->clockOutDeadlineMinutes = $this->attendanceSettingRepository->findAttendanceSetting(AttendanceUtil::CLOCK_OUT_DEADLINE)->value ?? AttendanceUtil::DEFAULT_CLOCK_OUT_DEADLINE_MINS;
        $this->closestTimecard = $this->timecardRepository->getClosestTimecardForEmployee($this->employee->id, $this->allowedMinutesToClockInBefore, $this->clockOutDeadlineMinutes);
        $this->leave = $this->employeeLeaveRequestRepository->approvedLeaveExistsAroundDateForEmployee($this->employee->id, $this->now);
        $this->publicHoliday = $this->publicHolidayRepository->publicHolidayOnDate($this->now->toDateString());
        $this->fillCicoLocationsFlags($latitude, $longitude);

        $this->timecardHasClockIn = isset($this->closestTimecard->attendance->ci_id);
        $this->correctCustomClockIn = ! $this->timecardHasClockIn && $this->isInCiLocation; // timecard has no clock in yet __ and I'm on the custom clock in location
        $this->correctCustomClockOut = $this->timecardHasClockIn && $this->isInCoLocation; // timecard has clock in __ and I'm on the custom clock out location
    }

    public function fillCicoLocationsFlags($latitude, $longitude): void
    {
        $companyRadius = config('globals.company')->radius ?? config('globals.DEFAULT_COMPANY_RADIUS');

        if (isset($this->closestTimecard) && (isset($this->closestTimecard->required_ci_lat) || isset($this->closestTimecard->required_ci_branch_id))) {

            $requiredCiLat = $this->closestTimecard->required_ci_lat ?? trim(explode(',', $this->closestTimecard?->clockInBranch?->location)[0]);
            $requiredCiLong = $this->closestTimecard->required_ci_long ?? trim(explode(',', $this->closestTimecard?->clockInBranch?->location)[1]);

            $this->clockInLocation = [
                'lat' => trim($requiredCiLat),
                'long' => $requiredCiLong,
            ];

            $distance = DistanceFacade::getDistance($latitude, $longitude, $requiredCiLat, $requiredCiLong);

            $this->isInCiLocation = isset($this->closestTimecard->required_ci_lat) && isset($this->closestTimecard->required_ci_long) ?
                                            $distance <= $companyRadius : $distance <= $this->closestTimecard?->clockInBranch?->radius;
        }

        if (isset($this->closestTimecard) && (isset($this->closestTimecard->required_co_lat) || isset($this->closestTimecard->required_co_branch_id))) {
            $requiredCoLat = $this->closestTimecard->required_co_lat ?? explode(',', $this->closestTimecard?->clockOutBranch?->location)[0];
            $requiredCoLong = $this->closestTimecard->required_co_long ?? explode(',', $this->closestTimecard?->clockOutBranch?->location)[1];
            $this->clockOutLocation = [
                'lat' => $requiredCoLat,
                'long' => $requiredCoLong,
            ];

            $distance = DistanceFacade::getDistance($latitude, $longitude, $requiredCoLat, $requiredCoLong);

            $this->isInCoLocation = isset($this->closestTimecard->required_co_lat) && isset($this->closestTimecard->required_co_long)
                                            ? $distance <= $companyRadius : $distance <= $this->closestTimecard?->clockOutBranch?->radius;
        }
    }

    public function fillClosestBranch($latitude, $longitude)
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');
        $branches = $this->branchRepository->selectColumns(['id', 'location', 'radius', 'name'.$lang]);
        $minDistance = PHP_FLOAT_MAX;
        $closestBranch = null;
        foreach ($branches as $branch) {
            $location = explode(',', $branch->location);
            $branchLatitude = trim($location[0]);
            $branchLongitude = trim($location[1]);
            $distance = DistanceFacade::getDistance($latitude, $longitude, $branchLatitude, $branchLongitude);
            if ($distance < $minDistance && $distance < $branch->radius) {
                $minDistance = $distance;
                $closestBranch = $branch;
            }
        }

        $this->closestBranch = $closestBranch;
    }

    private function fillClockInable(): void // you are in a company branch or has any location or in a custom location
    {

        $this->nextSlotResponse['clockInable'] =
            ! $this->employeeJustDidCico &&
            (
                $this->hasAnyLocationPermission
                || (isset($this->closestBranch))
                || $this->correctCustomClockIn
                || $this->correctCustomClockOut
            );
    }

    private function fillStatus(): void
    {
        if ($this->checkVerified()) {
            $status = 'verified';
        } elseif ($this->checkOffShift()) {
            $status = 'off_shift';
        } else {
            $status = $this->unleash->getUnscheduledShiftsFeatureFlag() ? 'off_branch' : 'unverified';
        }

        $this->nextSlotResponse['status'] = $status;
    }

    private function fillCase(): void
    {
        $dateTime = $this->now->toDateTimeString();
        if ($this->nextSlotResponse['status'] == 'off_shift' && isset($this->closestTimecard) && ! $this->canClockInBasedOnAttendancePolicy()) { // if I'm here then there's a timecard and I'm too early for it or too late
            $this->nextSlotResponse['case'] = $dateTime < $this->closestTimecard->from ? 'early' : 'late';
        }
    }

    private function checkVerified(): bool
    {
        return $this->hasAnyLocationPermission
            ||
            ($this->hasAnyBranchPermission && isset($this->closestBranch))
            ||
            $this->canClockOnTimecard();
        //            ||
        //            (isset($this->lastClockInWithoutOut) && $this->lastClockInWithoutOut->status == 'verified');
    }

    private function checkOffShift(): bool // this should be checked if checkVerified is false .
    {
        return $this->unleash->getUnscheduledShiftsFeatureFlag() &&
            (
                (
                    isset($this->closestBranch) && $this->closestBranch->id == $this->employee->branch_id && ! isset($this->closestTimecard)
                )
                ||
                $this->isInCiLocation // for off-shift clock in  location is same as clock out so no need to check clock in location or clockout location , just check on clock in location is enough
            );
    }

    private function canClockOnTimecard(): bool
    {
        if (! isset($this->closestTimecard)) {
            return false;
        }

        if (isset($this->lastClockInWithoutOut) && $this->lastClockInWithoutOut->status == 'unverified') {
            return false;
        }

        if (! $this->correctCustomClockIn && ! $this->correctCustomClockOut) {
            return false;
        }

        if ($this->timecardHasClockIn) { // if I clocked in then I can clockout after minimum 30 minutes of clock in
            return true;
        }

        return $this->canClockInBasedOnAttendancePolicy(); // I can clock in only in the allowed minutes to clock in before or after timecard start
    }

    private function canClockInBasedOnAttendancePolicy()
    {
        $timecardFrom = Carbon::parse($this->closestTimecard->from);
        $timeDifferenceInMinutes = $timecardFrom->diffInMinutes($this->now);

        if ($this->now->lessThanOrEqualTo($timecardFrom)) {
            return $timeDifferenceInMinutes <= $this->allowedMinutesToClockInBefore;
        } else {
            return $timeDifferenceInMinutes <= $this->getMaximumAllowedLate($this->employee->title_id);
        }
    }

    private function fillLocationBranch($latitude, $longitude): void
    {
        if (isset($this->slotBranch)) {
            if ($this->isBranchClose($this->slotBranch, $latitude, $longitude)) {
                $this->nextSlotResponse['location_branch'] = [
                    'id' => $this->slotBranch->id,
                    'name' => $this->slotBranch->name,
                ];

                return;
            }
        }

        $this->nextSlotResponse['location_branch'] =
            isset($this->closestBranch) ? [
                'id' => $this->closestBranch->id,
                'name' => $this->closestBranch->name,
            ] : null;
    }

    private function fillSlotBranch(): void
    {
        if (isset($this->closestTimecard)) {
            if ($this->timecardHasClockIn && isset($this->closestTimecard?->required_co_branch_id)) {
                $this->nextSlotResponse['slot_branch'] = [
                    'id' => $this->closestTimecard->clockOutBranch->id,
                    'name' => $this->closestTimecard->clockOutBranch->name,
                ];

                $this->slotBranch = $this->closestTimecard->clockOutBranch;
            } elseif (! $this->timecardHasClockIn && isset($this->closestTimecard?->required_ci_branch_id)) {
                $this->nextSlotResponse['slot_branch'] = [
                    'id' => $this->closestTimecard->clockInBranch->id,
                    'name' => $this->closestTimecard->clockInBranch->name,
                ];

                $this->slotBranch = $this->closestTimecard->clockInBranch;
            }
        }
    }

    public function fillLastClockInData(): void
    {

        if ($this->timecardHasClockIn) {
            $this->nextSlotResponse['verified_ci_date'] = $this->closestTimecard->attendance?->clockIn?->date;
            $this->nextSlotResponse['verified_ci_id'] = $this->closestTimecard->attendance->ci_id;
        } elseif (isset($this->lastClockInWithoutOut)) {
            if ($this->lastClockInWithoutOut->status == 'unverified') {
                $this->nextSlotResponse['unverified_ci_date'] = $this->lastClockInWithoutOut->date;
                $this->nextSlotResponse['unverified_ci_id'] = $this->lastClockInWithoutOut->id;
            } else {
                $this->nextSlotResponse['verified_ci_date'] = $this->lastClockInWithoutOut->date;
                $this->nextSlotResponse['verified_ci_id'] = $this->lastClockInWithoutOut->id;
            }
        }

    }

    public function fillSlotData()
    {
        if (isset($this->lastClockInWithoutOut)) {
            if ($this->lastClockInWithoutOut->status == 'verified') {
                $this->nextSlotResponse['id'] = $this->lastClockInWithoutOut?->attendance?->slotable_id;
                $this->nextSlotResponse['name'] = $this->lastClockInWithoutOut?->attendance?->slotable?->name ?? $this->lastClockInWithoutOut?->attendance?->slotable?->shift?->name;
                $this->nextSlotResponse['color'] = $this->lastClockInWithoutOut?->attendance?->slotable?->shift?->colorhex;
                $this->nextSlotResponse['from'] = $this->lastClockInWithoutOut?->attendance?->slotable?->from;
                $this->nextSlotResponse['to'] = $this->lastClockInWithoutOut?->attendance?->slotable?->to;
                $this->nextSlotResponse['type'] = 'timecard';
            }
        } elseif (isset($this->closestTimecard)) {
            $this->nextSlotResponse['id'] = $this->closestTimecard->id;
            $this->nextSlotResponse['name'] = $this->closestTimecard->name ?? $this->closestTimecard?->shift?->name;
            $this->nextSlotResponse['color'] = $this->closestTimecard?->shift?->colorhex;
            $this->nextSlotResponse['from'] = $this->closestTimecard->from;
            $this->nextSlotResponse['to'] = $this->closestTimecard->to;
            $this->nextSlotResponse['type'] = 'timecard';
        } elseif (isset($this->publicHoliday)) {
            $this->nextSlotResponse['id'] = $this->publicHoliday->id;
            $this->nextSlotResponse['name'] = $this->publicHoliday->name;
            $this->nextSlotResponse['from'] = $this->publicHoliday->start;
            $this->nextSlotResponse['to'] = $this->publicHoliday->end;
            $this->nextSlotResponse['type'] = 'public_holiday';
        } elseif (isset($this->leave)) {
            $this->nextSlotResponse['id'] = $this->leave->id;
            $this->nextSlotResponse['name'] = $this->leave->companyLeaveType->name;
            $this->nextSlotResponse['from'] = $this->leave->from;
            $this->nextSlotResponse['to'] = $this->leave->to;
            $this->nextSlotResponse['type'] = 'leave';
        }
    }

    public function isBranchClose($branch, $lat, $long): bool
    {
        $location = explode(',', $branch->location);
        $branchLatitude = trim($location[0]);
        $branchLongitude = trim($location[1]);
        $distance = DistanceFacade::getDistance($lat, $long, $branchLatitude, $branchLongitude);

        return $distance <= $branch->radius;
    }
}
