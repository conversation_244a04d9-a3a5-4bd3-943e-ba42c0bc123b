<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Company;
use App\Models\CompanyDefaultLeaveType;
use App\Models\SystemSetting;
use App\Models\Title;
use App\Repositories\NewCompanyLeaveTypePolicyRepository;
use App\Repositories\NewCompanyLeaveTypeRepository;
use App\Repositories\V1\Leaves\CompanyDefaultLeaveTypesRepository;
use App\Traits\GenerateUuid;
use App\Util\DefaultLeaveTypesUtil;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AddDefaultHolidayLeaveType extends Command
{
    use GenerateUuid;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:default:leave:types';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $companies = Company::all();
            $repository = new NewCompanyLeaveTypeRepository;
            $companyDefaultLeaveTypesRepository = new CompanyDefaultLeaveTypesRepository;
            $newCompanyLeaveTypePolicyRepository = new NewCompanyLeaveTypePolicyRepository;
            foreach ($companies as $company) {
                $numAdded = 0;
                $publicHolidaySetting = SystemSetting::where('company_id', $company->id)->where('key', 'public_holidays_policy')->first();
                if (isset($publicHolidaySetting) && $publicHolidaySetting->value == 1) {
                    $companyHasPublicHolidays = CompanyDefaultLeaveType::where('company_id', $company->id)->where('key', DefaultLeaveTypesUtil::PUBLIC_HOLIDAY)->exists();

                    if (! $companyHasPublicHolidays) {
                        $publicHolidayLeaveType = [
                            'name' => 'Public Holiday Compensation',
                            'name_en' => 'Public Holiday Compensation',
                            'name_ar' => 'بدل العطلة الرسمية',
                            'is_primary' => 1,
                            'uuid' => $this->Uuid7(),
                            'balance_period' => config('globals.BALANCE_PERIODS.CALENDAR_YEAR'),
                            'gender' => 'all',
                            'company_id' => $company->id,
                        ];
                        $leaveType = $repository->add($publicHolidayLeaveType);

                        $publicHolidayPolicy = [
                            'company_leave_type_id' => $leaveType->id,
                            'base_balance' => 0,
                            'unit' => 'days',
                            'is_probation_allowed' => 1,
                            'company_id' => $company->id,
                        ];
                        $leaveTypePolicy = $newCompanyLeaveTypePolicyRepository->add($publicHolidayPolicy);

                        $companyTitleIds = Title::where('company_id', $company->id)->pluck('id')->toArray();
                        $leaveTypePolicy->titles()->attach($companyTitleIds);

                        $companyDefaultLeaveType = [
                            'key' => DefaultLeaveTypesUtil::PUBLIC_HOLIDAY,
                            'company_leave_type_id' => $leaveType->id,
                            'company_id' => $company->id,
                        ];
                        $companyDefaultLeaveTypesRepository->add($companyDefaultLeaveType);
                        $numAdded++;
                    }
                    echo 'Company ID: '.$company->id.' Added leave type : '.$numAdded."\n";
                }
            }
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
