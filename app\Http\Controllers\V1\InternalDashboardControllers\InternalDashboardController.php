<?php

namespace App\Http\Controllers\V1\InternalDashboardControllers;

use App\Exceptions\UnprocessableException;
use App\Exports\AreasBranchesExport;
use App\Exports\DeptTitlesExport;
use App\Http\Controllers\NewController;
use App\Http\Requests\CreateCompanyCustmerSuccessRequest;
use App\Http\Requests\CreateDemoRequest;
use App\Http\Requests\GetAttendanceDetailsRequest;
use App\Http\Requests\ImportAreasBranchesRequest;
use App\Http\Requests\ImportDeptTitlesRequest;
use App\Http\Requests\ListCompaniesRequest;
use App\Http\Requests\StoreAttendanceRecordsRequest;
use App\Http\Resources\DemoAccountResource;
use App\Http\Resources\GetCountriesResource;
use App\Http\Resources\ListCompaniesResource;
use App\Http\Resources\ListIndustriesResource;
use App\Http\Resources\V1\CustomerSuccessAttendanceResource;
use App\Http\Resources\V1\GetBranchesInternalResource;
use App\Http\Resources\V1\GetCompanyLeaveTypesInternalResource;
use App\Http\Resources\V1\GetDepartmentsInternalResource;
use App\Http\Resources\V1\GetPayrollDatesInternalResource;
use App\Http\Resources\V1\GetTitlesInternalResource;
use App\Jobs\InternalDashboard\CreateDemoCompany;
use App\Services\V1\InternalDashboard\CustomerSuccess\CustomerSuccessAttendanceService;
use App\Services\V1\InternalDashboard\CustomerSuccess\CustomerSuccessCompanyService;
use App\Services\V1\InternalDashboard\DemoAccount\DemoAccountService;
use App\Util\HttpStatusCodeUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class InternalDashboardController extends NewController
{
    public function __construct(
        protected DemoAccountService               $demoAccountService,
        protected CustomerSuccessCompanyService    $customerSuccessCompanyService,
        protected CustomerSuccessAttendanceService $customerSuccessAttendanceService
    )
    {
    }

    public function createCompanyCustomerSuccess(CreateCompanyCustmerSuccessRequest $request)
    {
        $request->validated();
        DB::beginTransaction();
        try {
            $this->customerSuccessCompanyService->createCompany($request);
            DB::commit();

            return getResponseStructure(['data' => []],
                HttpStatusCodeUtil::OK, 'Company Successfully Created');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);
            throw $e;
        }
    }

    public function addDemoAccount(CreateDemoRequest $request)
    {
        try {
            CreateDemoCompany::dispatch(
                $this->demoAccountService,
                $this->customerSuccessCompanyService,
                $request->validated()
            );

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'created successfully'
            );

        } catch (\Exception $e) {
            Log::error('Error in addDemoAccount: ' . $e->getMessage());
            throw new UnprocessableException($e->getMessage());
        }
    }

    //    public function deleteDemoAccount()
    //    {
    //        return getResponseStructure(
    //            ['data' => []],
    //            HttpStatusCodeUtil::OK,
    //            'updated successfully'
    //        );
    //    }

    public function listDemoAccounts()
    {
        $companies = $this->demoAccountService->listDemoAccounts();

        return getResponseStructure(
            ['data' => DemoAccountResource::collection($companies)],
            HttpStatusCodeUtil::OK,
            'retrieved successfully'
        );
    }

    public function getAttendanceDetails(GetAttendanceDetailsRequest $request)
    {
        $request->validated();

        $from = $request->get('from');
        $to = $request->get('to');
        $company_id = $request->get('company_id');
        $branch_id = $request->get('branch_id');
        $sync = $request->get('sync', false);

        $toDate = Carbon::parse($to);
        $month = $toDate->format('m');
        $year = $toDate->format('Y');

        $dataCacheKey = "{$branch_id}-{$month}-{$year}-data";
        $errorsCacheKey = "{$branch_id}-{$month}-{$year}-errors";

        $cachedErrors = Cache::get($errorsCacheKey, []);

        if ($sync) {
            $attendanceDetails = $this->customerSuccessAttendanceService->getAttendanceDetails(
                $from, $to, $company_id, $branch_id
            );

            Cache::put($dataCacheKey, $attendanceDetails, Carbon::now()->addHour());
        } else {
            $attendanceDetails = Cache::remember($dataCacheKey, Carbon::now()->addHour(), function () use (
                $from, $to, $company_id, $branch_id
            ) {
                return $this->customerSuccessAttendanceService->getAttendanceDetails(
                    $from, $to, $company_id, $branch_id
                );
            });
        }

        return getResponseStructure(
            [
                'data' => CustomerSuccessAttendanceResource::collection($attendanceDetails),
                'errors' => $cachedErrors,
            ],
            HttpStatusCodeUtil::OK,
            'retrieved successfully'
        );
    }

    public function addAttendance(StoreAttendanceRecordsRequest $request)
    {
        $request->validated();

        try {
            $response = $this->customerSuccessAttendanceService->handleAttendanceInfoData($request->validated());
            $errors = $response['errors'] ?? [];
            $to = $request->input('to');
            $branchId = $request->input('branch_id');
            $toDate = Carbon::parse($to);
            $month = $toDate->format('m');
            $year = $toDate->format('Y');

            // Generate cache keys
            $dataCacheKey = "{$branchId}-{$month}-{$year}-data";
            $errorsCacheKey = "{$branchId}-{$month}-{$year}-errors";
            $data = $this->customerSuccessAttendanceService->getAttendanceDetails(
                $request->input('from'),
                $to,
                $request->input('company_id'),
                $branchId
            );

            // Update the cache for attendance data
            Cache::put($dataCacheKey, $data, Carbon::now()->addHour());

            Cache::put($errorsCacheKey, $errors, Carbon::now()->addHour());

            return getResponseStructure(
                [
                    'data' => [
                        'message' => $response['message'],
                    ],
                ],
                HttpStatusCodeUtil::OK,
                'created successfully'
            );

        } catch (\Throwable $e) {
            Log::error('Add Attendance Failed', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    public function listCompanies(ListCompaniesRequest $request)
    {
        // validate request
        $data = $request->validated();
        $data['page_size'] = $data['page_size'] ?? 10;
        $companies = $this->customerSuccessCompanyService->listCompanies($data);
        $paginatedData = (ListCompaniesResource::collection($companies))->response()->getData();

        return getResponseStructure([
            'data' => $paginatedData->data,
            'pagination' => $paginatedData->meta,
        ], HttpStatusCodeUtil::OK, 'retrieved successfully');
    }

    public function listIndustries()
    {
        $industries = $this->customerSuccessCompanyService->getIndustries();

        return getResponseStructure(
            ['data' => ListIndustriesResource::collection($industries)],
            HttpStatusCodeUtil::OK,
            'retrieved successfully'
        );
    }

    public function exportDeptTitlesTemplate($companyId)
    {
        return Excel::download(new DeptTitlesExport($companyId), 'Dept_Titles_Template.xlsx');
    }

    public function exportAreasBranchesTemplate()
    {
        return Excel::download(new AreasBranchesExport(), 'Areas_Branches_Template.xlsx');
    }

    public function importAreasBranches(ImportAreasBranchesRequest $request)
    {
        $this->customerSuccessCompanyService->importAreasBranches($request);
        return getResponseStructure(
            ['data' => []],
            HttpStatusCodeUtil::OK,
            'imported successfully'
        );
    }

    public function importDeptTitles(ImportDeptTitlesRequest $request)
    {
        $this->customerSuccessCompanyService->importDeptTitles($request);
        return getResponseStructure(
            ['data' => []],
            HttpStatusCodeUtil::OK,
            'imported successfully'
        );
    }

    public function getDepartments($companyId)
    {
        $departments = $this->customerSuccessAttendanceService->getDepartments($companyId);
        return getResponseStructure(
            ['data' => GetDepartmentsInternalResource::collection($departments)],
            HttpStatusCodeUtil::OK,
            'retrieved successfully'
        );
    }

    public function getBranches($companyId)
    {
        $branches = $this->customerSuccessAttendanceService->getBranches($companyId);
        return getResponseStructure(
            ['data' => GetBranchesInternalResource::collection($branches)],
            HttpStatusCodeUtil::OK,
            'retrieved successfully'
        );
    }

    public function getTitles($companyId)
    {
        $titles = $this->customerSuccessAttendanceService->getTitles($companyId);
        return getResponseStructure(
            ['data' => GetTitlesInternalResource::collection($titles)],
            HttpStatusCodeUtil::OK,
            'retrieved successfully'
        );
    }

    public function getPayrollDates($companyId)
    {
        $payrollDates = $this->customerSuccessAttendanceService->getDraftedPayroll($companyId);
        return getResponseStructure(
            ['data' => new GetPayrollDatesInternalResource($payrollDates)],
            HttpStatusCodeUtil::OK,
            'retrieved successfully'
        );
    }

    public function getLeaveTypes($companyId)
    {
        $leaveTypes = $this->customerSuccessAttendanceService->getLeaveTypes($companyId);
        return getResponseStructure(
            ['data' => GetCompanyLeaveTypesInternalResource::collection($leaveTypes)],
            HttpStatusCodeUtil::OK,
            'retrieved successfully'
        );
    }

    public function getCountries()
    {
        $countries = $this->customerSuccessCompanyService->getCountries();
        return getResponseStructure(
            ['data' => GetCountriesResource::collection($countries)],
            HttpStatusCodeUtil::OK,
            'retrieved successfully'
        );
    }
}
