<?php

namespace App\Console\Commands;

use App\Models\AttendanceDeduction;
use App\Models\EmployeeRequest;
use DB;
use Illuminate\Console\Command;

class MakeEmployeeRequestsClosedForAllWaivedDeductions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:make-employee-requests-closed-for-all-waived-deductions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $deductions = AttendanceDeduction::where('status', 'waived')
                ->where(
                    function ($query) {
                        $query->whereHas(
                            'employeeRequests', function ($query) {
                                $query->where('status', 'pending');
                            }
                        )->orWhere(
                            function ($query) {
                                $query->whereDoesntHave('employeeRequests');
                            }
                        );
                    }
                )->get();

            echo 'Total deductions: '.$deductions->count()."\n";

            foreach ($deductions as $deduction) {
                $employeeRequests = $deduction->employeeRequests()->get();
                echo 'Deduction ID: '.$deduction->id.' Employee Requests: '.$employeeRequests->count()."\n";

                if ($employeeRequests->count() == 0) {
                    echo 'Creating new request for deduction ID: '.$deduction->id."\n";
                    EmployeeRequest::create([
                        'company_id' => $deduction->company_id,
                        'requestable_id' => $deduction->id,
                        'requestable_type' => 'attendance_deduction',
                        'request_name' => 'waive_deduction',
                        'status' => 'waived',
                    ]);
                } else {
                    echo 'Updating status for deduction ID: '.$deduction->id."\n";
                    foreach ($employeeRequests as $employeeRequest) {
                        $employeeRequest->status = 'waived';
                        $employeeRequest->save();
                    }
                }
                echo 'Updating status for deduction ID: '.$deduction->id."\n";
            }

            echo 'All done';

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            // dd($e);
        }
    }
}
