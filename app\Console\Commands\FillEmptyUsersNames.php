<?php

namespace App\Console\Commands;

use App\Services\CompanySetup\FilllEmptyUsersNamesService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FillEmptyUsersNames extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill:users:names';

    /**
     * The console command description.
     *
     * @var string
     */
    public function __construct(private FilllEmptyUsersNamesService $filllEmptyUsersNamesService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        DB::beginTransaction();
        try {
            $this->filllEmptyUsersNamesService->fill();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
