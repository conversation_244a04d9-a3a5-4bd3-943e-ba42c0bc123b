<?php

namespace App\DomainData;

trait TitleDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'name' => 'required|string',
            'color' => 'string',
            'probation_period' => 'integer|min:0',
            'department_id' => 'required|numeric',
            'is_higher_management' => 'boolean',
            'role_id' => 'numeric',
            'company_id' => 'numeric',
            'name_en' => 'string',
            'name_ar' => 'required|string',
            'working_hours' => 'required|numeric|min:1|max:16',
            'sub_department_id' => 'numeric',
            'request_group_id' => 'numeric',
            'absence_deduction_policy_id' => 'numeric',
            'late_deduction_group_id' => 'numeric',
            'work_type_policy_id' => 'numeric',
            'loan_policy_id' => 'numeric',
            'salary_advance_policy_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeTitleDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
