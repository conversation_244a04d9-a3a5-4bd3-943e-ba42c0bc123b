<?php

namespace App\Repositories\V1\Attendance;

use App\Enums\EntityTags\AttendanceTags;
use App\Enums\Missions\MissionsEnum;
use App\Models\Timecard;
use App\Repositories\BaseRepository;
use App\Traits\QueriesHelper;
use App\Traits\V2\WorkTypesTrait;
use App\Util\EmployeeUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class TimecardRepository extends BaseRepository
{
    use QueriesHelper, WorkTypesTrait;

    public function model(): string
    {
        return Timecard::class;
    }

    public function employeeHasTimecardOnDate($employeeId, $date)
    {
        return $this->model->where('employee_id', $employeeId)
            ->whereDate('from', $date)
            ->exists();
    }

    public function getTimecardsByEmployeeIdsAndDate($employeeIds, $date)
    {
        return $this->model->whereIn('employee_id', $employeeIds)
            ->whereDate('from', '>=', $date)
            ->doesntHave('attendance')
            ->select('id')->get();

    }

    public function getClosestTimecardForEmployee($employeeId, $allowedMinutesToClockInBefore, $clockOutDeadlineMinutes)
    {
        return $this->model
            ->with('branch', 'shift', 'attendance.clockIn', 'attendance.entityTags:id,tag,entity_id,entity_type')
            ->where('employee_id', $employeeId)
            ->where(function ($q) use ($allowedMinutesToClockInBefore, $clockOutDeadlineMinutes) {
                $q->whereDate('from', now()->toDateString())
                    ->orWhereDate('to', now()->toDateString())
                    ->orWhere(function ($q) use ($allowedMinutesToClockInBefore) {
                        $q->where('from', '>=', now())
                            ->where('from', '<=', now()->addMinutes($allowedMinutesToClockInBefore));
                    })
                    ->orWhere(function ($q) use ($clockOutDeadlineMinutes) {
                        $q->where('to', '<=', now())
                            ->where('to', '>=', now()->subMinutes($clockOutDeadlineMinutes));
                    });
            })
            ->where(function ($q) {
                $q->doesntHave('attendance')
                    ->orWhereHas('attendance.clockIn', function ($q) {
                        $q->whereNull('paired_clock_id')
                            ->orWhereHas('pairedClock', function ($q) {
                                $q->where('status', 'rejected');
                            });
                    });
            })
            ->whereDoesntHave('entityTags', function ($q) {
                $q->where('tag', config('globals.ATTENDANCE_TAGS.ABSENT'));
            })
            ->orderByRaw('ABS(TIMESTAMPDIFF(SECOND, `from`, NOW()))')
            ->first();
    }

    public function getScheduledTimecards($date, $employee)
    {
        return $this->model->where('employee_id', $employee->id)
            ->whereDate('from', '>=', $date)
            ->whereHas('entityTags', function ($q) {
                $q->where('tag', 'scheduled');
            })
            ->get();
    }

    public function getClosestTimecardForEmployeeOnDate($employeeId, $date)
    {
        return $this->model
            ->select('id', 'from', 'to')
            ->where('employee_id', $employeeId)
            ->whereDate('from', $date)
            ->orderByRaw('ABS(TIMESTAMPDIFF(SECOND, `from`, ?))', [$date])
            ->first();
    }

    public function getTimecardsWithoutAttendanceOnDateForEmployee($employeeId, $date, $excludeIds = null)
    {
        return $this->model
            ->whereNotIn('id', $excludeIds)
            ->where('employee_id', $employeeId)
            ->whereDate('from', $date)
            ->doesntHave('attendance')
            ->get();
    }

    public function getTagOfTimecard($timecard, $tag)
    {
        return $timecard->entityTags()->where('tag', $tag)->first();
    }

    public function getAttendanceDeductionOfTimecard($timecard)
    {
        return $timecard->attendance?->attendanceDeductions()->first();
    }

    public function getAbsentTimecards($employeeId, $from, $to)
    {
        return $this->model->where('employee_id', $employeeId)
            ->whereDate('from', '>=', $from)
            ->whereDate('to', '<=', $to)
            ->whereHas('entityTags', function ($q) {
                $q->where('tag', AttendanceTags::ABSENT)
                    ->orWhere('tag', AttendanceTags::ABSENT_WITHOUT_PERMISSION);
            })
            ->with('EntityTags', function ($q) {
                $q->where('tag', AttendanceTags::ABSENT)
                    ->orWhere('tag', AttendanceTags::ABSENT_WITHOUT_PERMISSION);
            })
            ->get();
    }

    public function getWorkerTimeCardById($timeCardId)
    {
        return $this->model
            ->where('id', $timeCardId)
            ->with([
                'attendance',
                'attendance.clockIn',
                'attendance.clockOut',
                'attendance.entityTags',
                'branch:id,name_en,name_ar',
            ])
            ->with('attendance.attendanceOvertimes', function ($q) {
                $q->where('status', 'approved');
            })
            ->with('attendance.attendanceDeductions', function ($q) {
                $q->where('status', 'applied');
            })
            ->first();
    }

    public function employeeHasTimeCardOverlapping($employeeId, $from, $to)
    {
        return $this->model->where('employee_id', $employeeId)
            ->where(function ($query) use ($from, $to) {
                // Timecard starts within the range:
                $query->where(function ($q) use ($from, $to) {
                    $q->whereDate('from', '>=', $from)
                        ->whereDate('from', '<=', $to);
                })
                    // Or, timecard ends within the range:
                    ->orWhere(function ($q) use ($from, $to) {
                        $q->whereDate('to', '>=', $from)
                            ->whereDate('to', '<=', $to);
                    })
                    // Or, timecard completely spans the range:
                    ->orWhere(function ($q) use ($from, $to) {
                        $q->whereDate('from', '<=', $from)
                            ->whereDate('to', '>=', $to);
                    });
            })
            ->exists();
    }

    public function getEmployeeTimecardOnDate($employeeId, $date)
    {
        return $this->model->where('employee_id', $employeeId)
            ->whereDate('from', $date)
            ->first();
    }

    public function removeEmployeeFromShift($shiftId, $employeeId, $date, $branchId)
    {
        return $this->model->where('employee_id', $employeeId)
            ->where('shift_id', $shiftId)
            ->whereDate('from', $date)
            ->whereHas('employee', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->delete();
    }

    public function removeShiftOnDate($shiftId, $branchId, $date)
    {
        return $this->model
            ->where('shift_id', $shiftId)
            ->whereDate('from', $date)
            ->whereHas('employee', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->delete();
    }

    public function getAbsentWithoutPermissionTimecards($employeeId, $from, $to)
    {

        return $this->model->where('employee_id', $employeeId)
            ->select('id', 'from', 'to')
            ->whereDate('from', '>=', $from)
            ->whereDate('to', '<=', $to)
            ->with('EntityTags', function ($q) {
                $q->where('tag', AttendanceTags::ABSENT_WITHOUT_PERMISSION);
            })
            ->get();
    }

    public function getCustomShiftsTemplate($branchId, $from, $to)
    {
        return $this->model
            ->select(
                'name',
                DB::raw("TIME(CONVERT_TZ(`from`, 'UTC', '".config('app.timezone')."')) as `from_time`"),
                DB::raw("TIME(CONVERT_TZ(`to`, 'UTC', '".config('app.timezone')."')) as `to_time`"),
                'shift_id',
            )
            ->distinct()
            ->whereDate('from', '>=', $from)
            ->whereDate('to', '<=', $to)
            ->whereHas('shift', function ($q) {
                $q->where('is_custom', 1);
            })
            ->whereHas('employee', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
                $this->appendScopeQuery($query);
            })
            ->get();
    }

    public function removeTimecardsOnDateWithBranch($employeeIds, $date, $branchId)
    {
        return $this->model
            ->whereIn('employee_id', $employeeIds)
            ->whereDate('from', $date)
            ->whereHas('employee', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })
            ->delete();
    }

    public function getWorkerMissionTimecards($employeeId, $from, $to)
    {
        $missionTypes = MissionsEnum::MissionTypes;

        return $this->model
            ->where('employee_id', $employeeId)
            ->whereDate('from', '>=', $from)
            ->whereDate('to', '<=', $to)
            ->withWhereHas('timecardType', function ($q) use ($missionTypes) {
                $q->whereIn('name', $missionTypes);
            })
            ->get();
    }

    public function removeEmployeeFromCustomShift($employeeId, $shiftName, $fromDate, $toDate)
    {
        return $this->model
            ->where('employee_id', $employeeId)
            ->where('name', $shiftName)
            ->where('from', $fromDate)
            ->where('to', $toDate)
            ->delete();
    }

    public function removeCustomShiftOnDate($shiftName, $fromDate, $toDate, $branchId)
    {
        return $this->model
            ->where('name', $shiftName)
            ->where('from', $fromDate)
            ->where('to', $toDate)
            ->whereHas('employee', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })
            ->delete();
    }

    public function getMissionTimecardOnDateForEmployee($employeeId, $date)
    {
        return $this->model
            ->join('timecard_types', 'timecard_types.id', '=', 'timecards.timecard_type_id')
            ->where('timecards.employee_id', $employeeId)
            ->whereIn('timecard_types.name', MissionsEnum::MissionTypes)
            ->whereDate('timecards.from', $date)
            ->first();
    }

    public function getMissionsOnDateRange($branchId, $from, $to, $departmentId = null)
    {
        $missionTimecardTypes = MissionsEnum::MissionTypes;

        // Fetch missions with relationships and group by date
        $missions = $this->model
            ->whereDate('from', '>=', $from)
            ->whereDate('to', '<=', $to)
            ->whereHas('employee', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
                $this->appendScopeQuery($query);
            })
            ->whereHas('employee.title.workTypePolicy', function ($query) {
                $query->whereNotIn('work_days_type', $this->getFixedTypes());
            })
            ->whereHas('timecardType', function ($query) use ($missionTimecardTypes) {
                $query->whereIn('name', $missionTimecardTypes);
            })
            ->select('id', 'shift_id', 'branch_id', 'employee_id', 'from', 'to', 'name', 'timecard_type_id')
            ->when($departmentId, function ($query) use ($departmentId) {
                $query->whereHas('employee.title', function ($query) use ($departmentId) {
                    $query->where('department_id', $departmentId);
                });
            })
            ->with([
                'employee' => function ($query) {
                    $query->select('id', 'name_en', 'name_ar', 'title_id', 'status', 'employee_number', 'first_name_en', 'second_name_en', 'first_name_ar', 'second_name_ar', 'branch_id');
                },
                'employee.title' => function ($query) {
                    $query->select('id', 'name_en', 'name_ar', 'department_id', 'color');
                },
                'employee.profilePicture' => function ($query) {
                    $query->select('id', 'attachment_url', 'attachable_id', 'attachable_type');
                },
                'employee.branch' => function ($query) {
                    $query->select('id', 'name_en', 'name_ar');
                },
                'timecardType' => function ($query) {
                    $query->select('id', 'name');
                },
            ])
            ->get();

        // Group missions by `from` date
        return $missions->groupBy(function ($mission) {
            return Carbon::parse($mission->from)->format('Y-m-d');
        });
    }

    public function getEmployeesMissionsCount($fromDate, $toDate, $withoutTerminatedEmployees = false, $employeeIds = null): mixed
    {
        return $this->model
            ->whereHas('entityTags', function ($q) {
                $q->where('tag', AttendanceTags::MISSION->value);
            })
            ->join('employees', 'employees.id', '=', 'timecards.employee_id')
            ->where('employees.company_id', config('globals.company')->id)
            ->where('employees.is_trackable', true)
            ->when($withoutTerminatedEmployees, function ($q) {
                $q->where('employees.status', '!=', EmployeeUtil::STATUSES['TERMINATED']);
            })
            ->when(isset($employeeIds), function ($q) use ($employeeIds) {
                $q->whereIn('employees.id', $employeeIds);
            })
            ->whereDate('from', '>=', $fromDate)
            ->whereDate('from', '<=', $toDate)
            ->select([
                'timecards.employee_id',
                DB::raw('COUNT(DISTINCT DATE(timecards.from)) as missions_count'),
            ])
            ->groupBy('employee_id')
            ->get();
    }

    public function getTimecardsWithoutHalfDayMissions($employeeId, $date)
    {
        return $this->model
            ->where('employee_id', $employeeId)
            ->whereDate('from', $date)
            ->whereDoesntHave('timecardType', function ($q) {
                $q->where('name', MissionsEnum::HALF_DAY_BEFORE_WORK->value)
                    ->orWhere('name', MissionsEnum::HALF_DAY_AFTER_WORK->value);
            })
            ->first();
    }

    public function getTimecardsWithDateRangeAndBranch($data, $employeeIds = null)
    {
        return $this->model
            ->whereDate('from', '>=', $data['start_date'])
            ->whereDate('from', '<=', $data['end_date'])
            ->when(isset($data['branch_id']), function ($query) use ($data) {
                $query->whereHas('employee', function ($query) use ($data) {
                    $query->where('branch_id', $data['branch_id']);
                });
            })
            ->when(isset($employeeIds), function ($query) use ($employeeIds) {
                $query->whereIn('employee_id', $employeeIds);
            })
            ->with([
                'attendance',
                'entityTags',
                'attendance.entityTags',
                'attendance.clockIn',
                'attendance.clockOut',
                'attendance.attendanceDeductions',
                'attendance.attendanceOvertimes',
            ])
            ->get();
    }

    public function getAbsenceTimecardsForTrackableEmployees($employeeIds, $fromDate, $toDate): mixed
    {
        return $this->model
            ->select('timecards.employee_id',
                'timecards.required_co_branch_id as required_branch_id',
                'timecards.branch_id as original_branch_id',
                'timecard_tags.tag as timecard_tag',
                'attendance_tags.tag as attendance_tag',
            )
            ->join('employees', 'employees.id', '=', 'timecards.employee_id')
            ->join('entity_tags as timecard_tags', function ($join) {
                $join->on('timecards.id', '=', 'timecard_tags.entity_id')
                    ->where('timecard_tags.entity_type', '=', 'time_card')
                    ->whereIn('timecard_tags.tag', [
                        AttendanceTags::ABSENT->value,
                        AttendanceTags::ABSENT_WITHOUT_PERMISSION->value,
                    ]);
            })
            ->leftJoin('attendances', function ($join) {
                $join->on('attendances.slotable_id', '=', 'timecards.id')
                    ->where('attendances.slotable_type', '=', 'time_card');
            })
            ->leftJoin('entity_tags as attendance_tags', function ($join) {
                $join->on('attendances.id', '=', 'attendance_tags.entity_id')
                    ->where('attendance_tags.entity_type', '=', 'attendance')
                    ->where('attendance_tags.tag', AttendanceTags::CONSIDERED_ABSENT->value);
            })
            ->whereIn('timecards.employee_id', $employeeIds)
            ->where('employees.is_trackable', 1)
            ->whereDate('timecards.from', '>=', $fromDate)
            ->whereDate('timecards.from', '<=', $toDate)
            ->orderBy('timecards.from', 'asc')
            ->get();
    }

    public function fetchTimecardsByAdmin($employeeIds, $adminId, $insertStartTime)
    {
        return $this->model
            ->whereIn('employee_id', $employeeIds)
            ->where('by_admin', $adminId)
            ->where('created_at', '>=', $insertStartTime)
            ->with([
                'attendance',
                'employee',
            ])
            ->get();
    }

    public function getAbsentTimecardsOnDates(array $employeeIds, array $dates)
    {
        return $this->model
            ->whereIn('employee_id', $employeeIds)
            ->whereHas('entityTags', function ($q) {
                $q->where('tag', AttendanceTags::ABSENT);
            })
            ->whereDate('from', '>=', min($dates))
            ->whereDate('from', '<=', max($dates))
            ->get();
    }

    public function deleteAbsenceTimecard($employeeId, $date)
    {
        return $this->model
            ->where('employee_id', $employeeId)
            ->whereDate('from', $date)
            ->whereHas('entityTags', function ($q) {
                $q->where('tag', AttendanceTags::ABSENT);
            })
            ->delete();
    }

    public function getEmployeeTimecardsOnDates($employeeId, $dates)
    {
        $timezone = config('app.timezone');

        return $this->model
            ->where('employee_id', $employeeId)
            ->whereIn(
                DB::raw("CONVERT_TZ(`from`, 'UTC', '{$timezone}')"),
                $dates
            )->get();
    }

    public function getTimecardsByEmployeeOnDates($employeeId, $dates)
    {
        $timezone = config('app.timezone');

        return $this->model
            ->where('employee_id', $employeeId)
            ->whereIn(
                DB::raw("DATE(CONVERT_TZ(`from`, 'UTC', '{$timezone}'))"),
                $dates
            )->get();
    }

    public function getTimecardWithEntityTags($timecardId)
    {
        return $this->model
            ->with('entityTags')
            ->where('id', $timecardId)
            ->first();
    }
}
