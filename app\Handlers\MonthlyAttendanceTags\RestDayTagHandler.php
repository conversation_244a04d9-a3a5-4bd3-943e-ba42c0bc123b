<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Models\EmployeeLeaveRequest;
use Illuminate\Support\Carbon;

class RestDayTag<PERSON><PERSON><PERSON> implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        return [
            'name' => $employeeAttendance->companyLeaveType->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($employeeAttendance->from)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        return isset($tags[$employeeId]['tags']['rest_day']) ? $tags[$employeeId]['tags']['rest_day']['count'] + 1 : 1;
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return $employeeAttendance instanceof EmployeeLeaveRequest &&
            $employeeAttendance->companyLeaveType &&
            $employeeAttendance->company_leave_type_id == config('globals.rest_day_leave_id'); // that mean he in rest day;
    }

    public function handleTagUnit(): string
    {
        return 'days';
    }
}
