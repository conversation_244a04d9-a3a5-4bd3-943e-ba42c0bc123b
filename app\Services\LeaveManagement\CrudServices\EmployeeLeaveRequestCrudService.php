<?php

namespace App\Services\LeaveManagement\CrudServices;

use App\DomainData\EmployeeLeaveRequestDto;
use App\Enums\V2\WorkTypes\RestDaysTypesEnum;
use App\Exceptions\UnprocessableException;
use App\FeatureToggles\Unleash;
use App\Http\Resources\EmployeeLeaveRequestCollection;
use App\Http\Resources\EmployeeLeaveRequestResource;
use App\Http\Resources\GetEditHistoryCollection;
use App\Jobs\AssignApprovalsJob;
use App\Models\Employee;
use App\Models\EmployeeLeaveBalance;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\Repository;
use App\Repositories\V1\Attendance\TimecardRepository;
use App\Repositories\V1\Files\AttachmentRepository;
use App\Repositories\V1\Holidays\PublicHolidaysRepository;
use App\Repositories\V1\Missions\MissionRepository;
use App\Repositories\V1\RequestOvertime\RequestOvertimeRepository;
use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use App\Services\CrudService;
use App\Services\LeaveManagement\BusinessServices\BalanceAggregatorService;
use App\Services\TimeTracking\BusinessServices\AddRequestsToEmployeeRequest;
use App\Services\TimeTracking\BusinessServices\AssignApprovalRequestToEmployeesService;
use App\Traits\DataPreparation;
use App\Traits\EmployeeChangesHelper;
use App\Traits\LeaveManagementNet;
use App\Traits\UploadFile\UploadFile;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;
use App\Traits\V2\WorkTypesTrait;
use App\Traits\WorkflowTrait;
use App\Util\UserWorkTypesUtil;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use stdClass;

class EmployeeLeaveRequestCrudService extends CrudService
{
    use DataPreparation, EmployeeChangesHelper, EmployeeLeaveRequestDto, LeaveManagementNet, PayrollHelper, UploadFile, WorkflowTrait, WorkTypesTrait, PrepareAssignRequestCycleDataTrait;

    private Repository $employeeLeaveBalanceRepository;

    private Repository $employeeRepository;

    private Repository $attendanceRepository;

    private Repository $companyLeaveTypeRepository;

    private Repository $companyLeaveTypePolicyRepository;

    private Repository $restEmployeeRepository;

    private Repository $requestCycleRepository;

    private Repository $activityLogRepository;

    private Repository $timecardRepository;

    private WorkflowApprovalCycleRepository $workflowApprovalCycleRepository;

    private PublicHolidaysRepository $publicHolidaysRepository;

    private $employee;

    private $requestingEmployeeId;

    private $employeeLeaveBalance;

    private $companyRestDayLeaveTypePolicy;

    private $companyRestDayLeaveType;

    public function __construct(
        private AddRequestsToEmployeeRequest $addRequestsToEmployeeRequest,
        private AssignApprovalRequestToEmployeesService $assignApprovalRequestToEmployeesService,
        private BalanceAggregatorService $balanceAggregatorService,
        private AttachmentRepository $attachmentRepository,
        private PayrollsRepository $payrollsRepository,
        protected TimecardRepository $newTimecardRepository,
        protected MissionRepository $missionRepository,
        protected RequestOvertimeRepository $requestOvertimeRepository

    ) {

        parent::__construct('EmployeeLeaveRequest');

        $this->employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
        $this->employeeRepository = Repository::getRepository('Employee');
        $this->companyLeaveTypeRepository = Repository::getRepository('CompanyLeaveType');
        $this->companyLeaveTypePolicyRepository = Repository::getRepository('CompanyLeaveTypePolicy');
        $this->restEmployeeRepository = Repository::getRepository('RestEmployee');
        $this->requestCycleRepository = Repository::getRepository('RequestCycle');
        $this->activityLogRepository = Repository::getRepository('ActivityLog');
        $this->attendanceRepository = Repository::getRepository('Attendance');
        $this->timecardRepository = Repository::getRepository('Timecard');
        $this->workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $this->publicHolidaysRepository = new PublicHolidaysRepository;

    }

    public function checkSufficentBalance($netQuantity, EmployeeLeaveBalance $employeeLeaveBalance): bool
    {
        $unleash = app(Unleash::class);
        if ($unleash->isLeaveBalanceChangesFlagEnabled()) {
            $leaveIsProratedMonthly = $employeeLeaveBalance->companyLeaveTypePolicy->prorated_monthly;
            if (! $leaveIsProratedMonthly) {
                return $employeeLeaveBalance->balance >= $netQuantity;
            }
            $remainingBalanceToBeAdded = $employeeLeaveBalance->companyLeaveTypePolicy->base_balance - $employeeLeaveBalance->total_prorated_balance;
            if ($remainingBalanceToBeAdded + $employeeLeaveBalance->balance >= $netQuantity) {
                return true;
            }

            return false;
        } else {
            return $employeeLeaveBalance->balance >= $netQuantity;
        }
    }

    public function isValidCreate(array $request, stdClass &$output): bool
    {
        $this->employee = $this->employeeLeaveBalance->employee;
        $leaveTitleIds = array_column($this->employeeLeaveBalance->companyLeaveTypePolicy->titles->toArray(), 'id');

        if (! in_array($this->employee->title_id, $leaveTitleIds)) {
            $output->Error = ['This title is not Allowed to request this leave', 'لا يسمح لهذا المسمى الوظيفى بطلب هذه الأجازة '];

            return false;
        }

        if ($this->employeeLeaveBalance->companyLeaveType->gender != 'all'
            && $this->employeeLeaveBalance->companyLeaveType->gender != $this->employee->employeeInfo->gender) {
            $output->Error = ['You are not Allowed to request this leave', 'غير مسموح لك بطلب هذه الأجازة  '];

            return false;
        }

        $user = $this->employee->user;
        $isAddByManager = ($this->requestingEmployeeId != $this->employee->id) || ($user->hasPermissionTo('manage_leaves', 'user-api') || $user->hasPermissionTo('add_leave', 'user-api'));
        if (! $isAddByManager && $this->employeeLeaveBalance->companyLeaveTypePolicy->is_probation_allowed == 0 && $this->employee->on_probation == 1) {
            $output->Error = ['You are not Allowed to request this leave because you are in probation period',
                'غير مسموح لك بطلب هذه الأجازة لأنك فى فترة الملاحظة  '];

            return false;
        }

        if (! $isAddByManager && $this->employeeLeaveBalance->company_leave_type_id == $user->company?->emergencyLeaveType?->id) {
            $output->Error = ['Emergency leave can only be requested through the manager',
                'لا يمكن طلب الإجازة الطارئة إلا من خلال المدير'];

            return false;
        }

        $unleash = app(Unleash::class);
        //        if ($unleash->isLeaveBalanceChangesFlagEnabled())
        $daysDiff = now()->diffInDays($request['from']) ?? 0;
        $requestBeforeDays = $this->employeeLeaveBalance->companyLeaveTypePolicy->request_before_days;

        if (! $isAddByManager && $unleash->isLeaveBalanceChangesFlagEnabled() && $requestBeforeDays && ($request['from'] < date('Y-m-d') || $requestBeforeDays > $daysDiff)) {
            $output->Error = ['You are not Allowed to request this leave before '.$requestBeforeDays.' days',
                'غير مسموح لك بطلب هذه الأجازة قبل '.$requestBeforeDays.' يوم '];

            return false;
        }
        if ($request['from'] < $this->employeeLeaveBalance->start || $request['from'] > $this->employeeLeaveBalance->end) {
            $output->Error = ['You are not Allowed to request this leave at this date', ' غير مسموح لك بطلب هذه الأجازة فى هذا التاريخ '];

            return false;
        }

        if ($request['from'] <= $this->employeeLeaveBalance->end && $request['to'] > $this->employeeLeaveBalance->end) {
            $toDate = Carbon::parse($this->employeeLeaveBalance->end)->toDateString();
            $nextDate = Carbon::parse($this->employeeLeaveBalance->end)->addDay()->startOfDay()->toDateString();
            $output->Error = ['You need to request two requests one ending on '.$toDate.' and another one starting on '.$nextDate,
                ' يجب أن تقوم بتقديم طلبين ,الأول ينتهى فى '.$toDate.' والثانى يبدأ فى '.$nextDate];

            return false;
        }

        $employeeYears = Carbon::now()->diffInYears($this->employee->career_start_date);
        if ($this->employeeLeaveBalance->companyLeaveTypePolicy->min_requester_years > $employeeYears) {
            $output->Error = ['You Can not request this leave before you complete '.$this->employeeLeaveBalance->companyLeaveTypePolicy->min_requester_years.' years',
                ' لا يمكنك طلب هذه الأجازة قبل إكمال '.$this->employeeLeaveBalance->companyLeaveTypePolicy->min_requester_years.' سنة '];

            return false;
        }

        if ($this->checkSufficentBalance($request['net_quantity'], $this->employeeLeaveBalance) == false) {
            $output->Error = ['You do not have enough balance to request this leave',
                ' لا يوجد لديك رصيد كافٍ لطلب هذه الأجازة '];

            return false;
        }

        $leaveInRange = $this->repository->hasLeaveInRange($request['employee_id'],
            $request['from'], $request['to'], [config('globals.REQUEST_STATUSES.APPROVED'), config('globals.REQUEST_STATUSES.PENDING')]);
        if (! is_null($leaveInRange)) {
            $from = Carbon::parse($leaveInRange->from)->toDateString();
            $to = Carbon::parse($leaveInRange->to)->toDateString();
            $output->Error = ['You already have a leave from '.$from.' to '.$to,
                ' لديك بالفعل أجازة من '.$from.' إلى '.$to];

            return false;
        }

        $isLeaveArestDay = ($this->employeeLeaveBalance->company_leave_type_id == config('globals.rest_day_leave_id')) ?? false;
        if ($isLeaveArestDay) {
            $output->Error = ['You can not request rest day leave', ' لا يمكنك طلب إجازة راحة '];

            return false;
        }

        $verifiedAttendancesOnRange = $this->attendanceRepository->verifiedAttendancesExistsOnRange($request['employee_id'], $request['from'], $request['to']);
        if (! isset($request['partial_leave_type']) && ! is_null($verifiedAttendancesOnRange) && count($verifiedAttendancesOnRange)) {
            $attendanceDates = implode("\n", array_column($verifiedAttendancesOnRange->toArray(), 'date'));
            $output->Error = ['This employee '.$this->employee->name.' has attended already on \n'.$attendanceDates,
                ' لقد حضر هذا الموظف'.$this->employee->name.' بالفعل فى \n'.$attendanceDates];

            return false;
        }

        $terminationDate = $this->employee->employeeInfo?->termination_date;
        if (isset($terminationDate) && Carbon::parse($request['to'])->format('Y-m-d') > Carbon::parse($terminationDate)->format('Y-m-d')) {
            $output->Error = ['You can not request leave after termination date',
                'لا يمكنك طلب أجازة بعد تاريخ الاقالة'];

            return false;
        }

        if (isset($request['partial_leave_type']) && ! in_array($this->employee->title->workTypePolicy->work_days_type, $this->getFixedTypes())) {
            $output->Error = ['Your work type policy does not allow partial leave',
                'سياسة العمل لديك لا تسمح بالأجازات الجزئية'];

            return false;

        }
        if (isset($request['partial_leave_type']) && $request['company_leave_type_id'] != $this->employee->company->annualLeaveType->id) {
            $output->Error = ['You can not request partial leave for this leave type',
                'لا يمكنك طلب أجازة جزئية لهذا النوع من الأجازات'];

            return false;
        }
        $missionRequestExists = $this->missionRepository->getMissionRequestOnDate($request['employee_id'], $request['from']) !== null;
        if ($missionRequestExists) {
            $output->Error = ['You can not request leave on a mission day',
                'لا يمكنك طلب أجازة فى يوم مهمة'];

            return false;
        }

        $requestOvertimeExists = $this->requestOvertimeRepository->getRequestByDateAndStatus($request['employee_id'], $request['from'], ['pending', 'approved']) !== null;
        if ($requestOvertimeExists) {
            $output->Error = ['You can not request leave on a day with overtime request',
                'لا يمكنك طلب أجازة فى يوم يوجد به طلب وقت اضافي'];

            return false;
        }

        $payrollCoversDate = $this->payrollsRepository->payrollCoversDate(Carbon::parse($request['from'])->toDateString());
        if (isset($payrollCoversDate) && $payrollCoversDate->status == 'finalized') {
            $output->Error = ['You can not request a leave on a finalized payroll month',
                'لا يمكنك طلب أجازة بعد إنهاء حساب الرواتب'];

            return false;
        }

        return true;
    }

    private function fixFromAndToDates(&$request, string $unit)
    {
        $request['from'] = Carbon::parse($request['from']);
        $request['to'] = Carbon::parse($request['to']);

        if ($unit == config('globals.LEAVE_UNITS.DAYS')) {
            $request['from'] = $request['from']->startOfDay();
            $request['to'] = $request['to']->endOfDay();
        }

        if ($unit == config('globals.LEAVE_UNITS.HOURS')) {
            $employee = Employee::find($request['employee_id']);
            $workTypePolicy = $employee->title->workTypePolicy;
            if (isset($request['partial_leave_type']) && $request['partial_leave_type'] === 'before') {
                $fromDate = Carbon::parse($request['from']);
                $request['from'] = $fromDate->copy()->setTimeFromTimeString($workTypePolicy->start_time);
                $request['to'] = $request['from']->copy()->addHours($request['duration']);
            } elseif (isset($request['partial_leave_type']) && $request['partial_leave_type'] === 'after') {
                $toDate = Carbon::parse($request['from']);
                $request['to'] = $toDate->copy()->setTimeFromTimeString($workTypePolicy->end_time);
                $request['from'] = $request['to']->copy()->subHours($request['duration']);
            }
        }
        $request['from'] = Carbon::parse($request['from'])->toDateTimeString();
        $request['to'] = Carbon::parse($request['to'])->toDateTimeString();
    }

    public function create(array $request, stdClass &$output): void
    {
        $addLeaveByManager = $output->addLeaveByManager ?? false;
        $this->requestingEmployeeId = $addLeaveByManager ? auth()->user()->employee_id : $request['employee_id'];
        $attachments = $request['attachments'] ?? [];
        unset($request['attachments']);

        $this->employeeLeaveBalance = $this->employeeLeaveBalanceRepository->getById($request['balance_id']);
        if (! isset($this->employeeLeaveBalance)) {
            $output->Error = ['Invalid Balance Id', 'نوع الأجازة غير موجود '];

            return;
        }

        $this->employeeLeaveBalance = $this->employeeLeaveBalanceRepository->leaveBalanceForEmployee($request['employee_id'], $request['from'],
            $this->employeeLeaveBalance->company_leave_type_policy_id
        ); // TODO , to change this flow, frontend will send leave type id

        if (! isset($this->employeeLeaveBalance)) {
            $output->Error = ['No Balance added in this period', 'رصيد الأجازة غير موجود فى هذه الفترة  '];

            return;
        }
        $unit = isset($request['partial_leave_type']) ? 'hours' : $this->employeeLeaveBalance->companyLeaveTypePolicy->unit;

        $this->fixFromAndToDates($request, $unit);
        $request['company_leave_type_id'] = $this->employeeLeaveBalance->companyLeaveType->id;
        $request['company_leave_type_policy_id'] = $this->employeeLeaveBalance->companyLeaveTypePolicy->id;
        $request['net_quantity'] = $this->recalculateNetQuantity($unit, $request['employee_id'], $request['from'], $request['to']);

        if (! $this->isValidCreate($request, $output)) {
            return;
        }
        $request['status'] = $request['status'] ?? config('globals.REQUEST_STATUSES.PENDING');
        $employeeBranchOnDate = $this->employeeBranchOnDate($this->employee, Carbon::parse($request['from'])->toDateString());
        $request['branch_id'] = $employeeBranchOnDate;

        if ($request['net_quantity'] == 0) {
            $output->Error = ['You have already leaves in this period ', ' يوجد لديك بالفعل أجازات فى هذه الفترة'];

            return;
        }

        $entity = $this->repository->create($request, []);
        if (! empty($attachments) && isset($entity)) {
            $companyUuid = auth()->user()->employee->company->uuid;
            $employeeUuid = $this->employee->uuid;

            $filePaths = [];

            foreach ($attachments as $attachment) {
                $filePath = $this->uploadFile($attachment, 's3', "{$companyUuid}/employee-files/{$employeeUuid}/leave-requests");
                $filePaths[] = $filePath;
            }
            $this->saveLeaveAttachmets($filePaths, $entity);

            $entity->uploaded_by_id = auth()->user()->employee_id;
            $entity->save();

        }

        if (isset($request['by_transfer']) && $request['by_transfer'] == true) {
            return;
        }

        $entity->balanceEntity = $this->employeeLeaveBalance;
        $output->employeeLeaveRequest = $entity;
        $output->{$this->entityName} = new EmployeeLeaveRequestResource($entity);

        // $leaveRequestData = $this->prepareEmployeeRequestData($this->requestingEmployeeId, $entity, config('globals.EMPLOYEE_REQUEST_NAMES.EMPLOYEE_LEAVE_REQUEST'));
        // $this->addRequestsToEmployeeRequest->perform($leaveRequestData, $output);
        // $cycle = $this->requestCycleRepository->getRelatedCycle(
        //     $entity->companyLeaveType->name_ar
        // );

        // if (sizeof($cycle) == 0) {
        //     $cycle = $this->requestCycleRepository->getRelatedCycle(
        //         config('globals.EMPLOYEE_REQUEST_NAMES.EMPLOYEE_LEAVE_REQUEST')
        //     );
        // }

        // $titlesIds = array_column($cycle->toArray(), 'title_id');
        // $approvingEmployees = $this->employeeRepository->getEmployeesWithCommonBranchAndTitles($this->employee->branch->id, $titlesIds);
        // if (count($approvingEmployees) > 0) {
        //     $leaveRequest = $this->prepareApprovalCycleRequests($cycle, $approvingEmployees, $entity, $output->employee_request->id);
        //     $this->assignApprovalRequestToEmployeesService->perform($leaveRequest, $output);
        // }
        unset($entity->balanceEntity);
        $requesterRoleIds = config('globals.user')->roles->pluck('id')->toArray();
        $timecardsInLeaveRange = [];

        if (! isset($request['partial_leave_type'])) {
            $timecardsInLeaveRange = $this->getTimecardIdsInLeaveRange($entity);
        }
        $entity = $entity->withoutRelations();

        AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('employee_leave_request', $entity, $requesterRoleIds));
        // $this->initializeRequestWorkflow($entity, $this->employee, $entity->companyLeaveType->uuid,
        //     config('globals.REQUEST_WORKFLOW_TYPES.LEAVE'), $requesterRoleIds, $timecardsInLeaveRange);

        unset($output->employee_request);

        // if (!isset($output->addLeaveByManager) || $output->addLeaveByManager === false) {
        //     dispatch(new ApplyOnLeaveRequestPushNotificationJob($approvingEmployees, $entity))->afterCommit();  // done
        // }
    }

    public function saveLeaveAttachmets(array $filePaths, $entity)
    {

        foreach ($filePaths as $filePath) {
            $attachmentData = $this->prepareAttachment($filePath, $entity);
            $this->attachmentRepository->add(
                $attachmentData
            );
        }
    }

    public function prepareAttachment($filePath, $entity)
    {
        $fileUrl = $this->getFileUrl($filePath);

        return [
            'attachable_type' => 'employee_leave_request',
            'attachable_id' => $entity->id,
            'path' => $filePath,
            'attachment_url' => $fileUrl,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];
    }

    public function isValidUpdate(array $request, stdClass &$output): bool
    {
        $this->entity = $this->repository->getById($request['id']);

        if (is_null($this->entity)) {
            $output->Error = ['There is no leave with this id',
                ' لا توجد أجازة بهذا الرقم '];

            return false;
        }

        $actionHasBeenTaken = $this->workflowApprovalCycleRepository->actionHasBeenTaken($request['id'], config('globals.EMPLOYEE_REQUEST_NAMES.EMPLOYEE_LEAVE_REQUEST'));
        if ($actionHasBeenTaken) { // we ignore that approvers took action if this update is cause by restDay request
            $output->Error = ['You can not edit this request because an action has been taken on it',
                ' لا يمكنك تعديل هذا الطلب. حيث تم اتخاذ قرار بشأنه '];

            return false;
        }

        if ($request['employee_id'] != $this->entity->employee_id) { // you can edit leave type only if you are the employee who requested it
            $output->Error = ['You can not edit this leave request',
                ' لا يمكنك تعديل هذا الطلب '];

            return false;
        }

        $this->employeeLeaveBalance = $this->employeeLeaveBalanceRepository->getById($request['balance_id']);
        if (is_null($this->employeeLeaveBalance)) {
            $output->Error = ['Invalid Balance Id', 'نوع الأجازة غير موجود '];

            return false;
        }

        if (($request['from'] < $this->employeeLeaveBalance->start || $request['to'] > $this->employeeLeaveBalance->end)) {
            $output->Error = ['You are not Allowed to request this leave in this period ',
                ' غير مسموح لك بطلب هذه الأجازة فى هذه المدة '];

            return false;
        }

        if ($request['from'] > $request['to']) {
            $output->Error = ['Leave Start Date Should Be Before Leave End Date ',
                'تاريخ بداية الأجازة يجب أن يكون قبل تاريخ انتهائها '];

            return false;
        }

        $employeeYears = Carbon::now()->diffInYears($this->entity->employee->career_start_date);
        if ($this->employeeLeaveBalance->companyLeaveTypePolicy->min_requester_years > $employeeYears) {
            $output->Error = ['You Can not request this leave now ',
                ' لا يمكنك طلب هذه الأجازة الآن '];

            return false;
        }

        $leaveInRange = $this->repository->hasLeaveInRange($request['employee_id'],
            $request['from'], $request['to'], [config('globals.REQUEST_STATUSES.APPROVED'), config('globals.REQUEST_STATUSES.PENDING')]);
        if (! is_null($leaveInRange)) {
            $from = Carbon::parse($leaveInRange->from)->toDateString();
            $to = Carbon::parse($leaveInRange->to)->toDateString();
            $output->Error = ['You already have a leave from '.$from.' to '.$to,
                ' هذا الموظف لديه بالفعل أجازة من '.$from.' إلى '.$to];

            return false;
        }

        //        $isEmployeeOnRest = $this->repository->restDayExistsOnDate
        //        ($request['employee_id'], Carbon::parse($request['from'])->toDateString());
        //        if ($this->employeeLeaveBalance->CompanyLeaveTypePolicy->unit == config('globals.LEAVE_UNITS.HOURS') && $isEmployeeOnRest) { //  you can no
        //            $output->Error = ['You already have a rest day on this day', ' لديك بالفعل راحة فى هذا اليوم '];
        //            return false;
        //        }

        if ($request['from'] < $this->employeeLeaveBalance->end && $request['to'] > $this->employeeLeaveBalance->end) {
            $toDate = Carbon::parse($this->employeeLeaveBalance->end)->toDateString();
            $nextDate = Carbon::parse($this->employeeLeaveBalance->end)->addDay()->startOfDay()->toDateString();
            $output->Error = ['You need to request two requests one ending on '.$toDate.' and another one starting on '.$nextDate,
                ' يجب أن تقوم بتقديم طلبين ,الأول ينتهى فى '.$toDate.' والثانى يبدأ فى '.$nextDate];

            return false;
        }

        $verifiedAttendancesOnRange = $this->attendanceRepository->verifiedAttendancesExistsOnRange($request['employee_id'], $request['from'], $request['to']);
        if (! is_null($verifiedAttendancesOnRange) && count($verifiedAttendancesOnRange)) {
            $attendanceDates = implode("\n", array_column($verifiedAttendancesOnRange->toArray(), 'date'));
            $output->Error = ['This employee '.$this->entity->employee->name.' has attended already on \n'.$attendanceDates,
                ' لقد حضر هذا الموظف'.$this->entity->employee->name.' بالفعل فى \n'.$attendanceDates];

            return false;
        }

        $terminationDate = $this->entity->employee->employeeInfo?->termination_date;
        if (isset($terminationDate) && $request['to'] >= $terminationDate) {
            $output->Error = ['You can not request leave after termination date',
                ' لا يمكنك طلب أجازة بعد تاريخ إنتهاء عقد الموظف '];

            return false;
        }

        //        $leaveIntersectsWithLeave = $this->publicHolidaysRepository->isLeaveIntersectsWithHoliday(Carbon::parse($request['from'])->toDateString()
        //                        ,Carbon::parse($request['to'])->toDateString());
        //        if($leaveIntersectsWithLeave){
        //            $output->Error = ['You can not request leave in this period because it intersects with a holiday',
        //                ' لا يمكنك طلب أجازة فى هذه الفترة لأنها تتداخل مع إجازة رسمية '];
        //            return false;
        //        }
        return true;
    }

    public function update(array $request, stdClass &$output): void
    {
        if (! $this->isValidUpdate($request, $output)) {
            return;
        }

        $attachments = $request['attachments'] ?? [];
        unset($request['attachments']);

        $existing_attachment_ids = $request['existing_attachment_ids'] ?? [];
        unset($request['existing_attachment_ids']);

        $this->fixFromAndToDates($request, $this->employeeLeaveBalance->CompanyLeaveTypePolicy->unit);
        $request['company_leave_type_id'] = $this->employeeLeaveBalance->company_leave_type_id;
        $request['company_leave_type_policy_id'] = $this->employeeLeaveBalance->company_leave_type_policy_id;
        $request['net_quantity'] = $this->recalculateNetQuantity($this->employeeLeaveBalance->CompanyLeaveTypePolicy->unit, $request['employee_id'], $request['from'], $request['to']);

        if ($request['net_quantity'] == 0) {
            $output->Error = ['You have already leaves in this period ', ' يوجد لديك بالفعل أجازات فى هذه الفترة'];

            return;
        }

        if ($this->checkSufficentBalance($request['net_quantity'], $this->employeeLeaveBalance) == false) {
            $output->Error = ['You do not have enough balance to request this leave',
                ' لا يوجد لديك رصيد كافٍ لطلب هذه الأجازة '];

            return;
        }

        $this->entity = $this->repository->update($this->entity, $request, []);

        if (! empty($existing_attachment_ids)) {
            $oldAttachmentIds = $this->entity->attachments()->pluck('id')->toArray();

            $wrongAttachmentIds = array_diff($existing_attachment_ids, $oldAttachmentIds);

            if ($wrongAttachmentIds) { // attachments ids that are not related to this company file
                throw new UnprocessableException('wrong attachment ids');
            }
            $toDeleteAttachmentIds = array_diff($oldAttachmentIds, $existing_attachment_ids);

            if (count($toDeleteAttachmentIds)) {
                $attachmentsToDelete = $this->attachmentRepository->findByIds($toDeleteAttachmentIds);
                $this->entity->detach($attachmentsToDelete);
            }
        }
        if (empty($existing_attachment_ids)) {
            $this->entity->detach($this->entity->attachments);
        }

        if (! empty($attachments)) {
            $companyUuid = auth()->user()->employee->company->uuid;
            $employeeUuid = auth()->user()->employee->uuid;

            $filePaths = [];

            foreach ($attachments as $attachment) {
                $filePath = $this->uploadFile($attachment, 's3', "{$companyUuid}/employee-files/{$employeeUuid}/leave-requests");
                $filePaths[] = $filePath;
            }
            $this->saveLeaveAttachmets($filePaths, $this->entity);
        }

        $this->entity->balanceEntity = $this->employeeLeaveBalance;
        $output->{$this->entityName} = new EmployeeLeaveRequestResource($this->entity);
    }

    public function getEditHistory(array $request, stdClass &$output): void
    {

        $this->entity = $this->repository->getById($request['id']);

        if (is_null($this->entity)) {
            $output->Error = ['There is no leave with this id',
                ' لا توجد أجازة بهذا الرقم '];

            return;
        }

        $approvals = $this->entity->employeeApproves;

        $approvalIds = $approvals->map(function ($approval) {
            return $approval->pivot->id;
        })->toArray();

        $approvalLogs = $this->activityLogRepository->approveOrCancelLogs($approvalIds, $this->entity->id, 'employee_leave_request');
        $output->edit_history = new GetEditHistoryCollection($approvalLogs);
    }

    public function isValidAddRestDay(array $request, stdClass &$output): bool
    {
        $this->employee = $this->employeeRepository->getById($request['employee_id'], ['title.workTypePolicy']);

        if (is_null($this->employee)) {
            $output->Error = ['Invalid Employee Id', 'المعرف الخاص بالموظف غير صحيح'];

            return false;
        }

        if ($this->employee->title->workTypePolicy->rest_days_type == RestDaysTypesEnum::FIXED->value) {
            $output->Error = ['You can not add restdays because this employee has fixed rest days', 'لا يمكنك إضافة أيام راحة لأن هذا الموظف لديه أيام راحة ثابتة'];

            return false;
        }

        $restOnDate = $this->repository->restDayExistsOnDate($this->employee->id, $request['date']);
        if ($restOnDate) {
            $output->Error = ['This employee has a rest on this day '.$this->employee->name, ' هذا الموظف لديه بالفعل راحة فى هذا اليوم'.$this->employee->name];

            return false;
        }

        $this->companyRestDayLeaveType = $this->companyLeaveTypeRepository->getById(config('globals.rest_day_leave_id'));
        $this->companyRestDayLeaveTypePolicy = $this->companyRestDayLeaveType->companyLeaveTypePolicies[0]; // we do only one policy for the rest days for now

        $isVerifiedAttendanceOnDay = $this->attendanceRepository->verifiedAttendanceExistsOnDate($this->employee->id, $request['date']);
        if ($isVerifiedAttendanceOnDay) {
            $output->Error = ['This employee has attended already on this day '.$this->employee->name, ' لقد حضر هذا الموظف بالفعل فى هذا اليوم'.$this->employee->name];

            return false;

        }
        $this->employeeLeaveBalance = $this->employeeLeaveBalanceRepository->getRestDaysBalance($this->employee->id,
            $request['date'], $this->companyRestDayLeaveTypePolicy);
        if ($this->doesEmployeeHaveFixedRestdays($this->employee)) {
            if (! $this->isValidRestDay($this->employee, $request['date'])) {
                $output->Error = [' Can not add rest day on that day ', ' لا يمكن إضافة راحة في هذا اليوم '];

                return false;
            }
        } else {
            if (! isset($this->employeeLeaveBalance)) {
                $this->employeeLeaveBalance = $this->addBalanceAroundDate($this->employee->id, $this->companyRestDayLeaveTypePolicy, $request['date']);
            }
            $workHours = config('globals.WORKED_HOURS_PER_DAY');
            $baseBalance = (($this->employee->title?->workTypePolicy?->rest_days_count ?? 0) * $workHours);
            $netQuantitiesSum = $this->repository->getSumApprovedLeavesInRangeForBalance($request['employee_id'], $this->employeeLeaveBalance);

            if ($baseBalance < ($netQuantitiesSum + $workHours)) {
                Log::info('balance is: '.$baseBalance.' netSum: '.$netQuantitiesSum.' workhours:'.$workHours);
                $output->Error = [' There is no enough balance to add this rest day for '.$this->employee->name,
                    '  لا يوجد رصيد كافٍ لإضافة يوم راحة لـ '.$this->employee->name];

                return false;
            }
        }

        // we need to covert the rest date to be in datetime format
        $leaveInRange = $this->repository->intersectingApprovedLeaveRequest($request['employee_id'],
            $request['date']);
        if (! is_null($leaveInRange)) {
            $from = Carbon::parse($leaveInRange->from)->toDateString();
            $to = Carbon::parse($leaveInRange->to)->toDateString();
            $output->Error = ['You already have a leave from '.$from.' to '.$to,
                ' لديك بالفعل أجازة من '.$from.' إلى '.$to];

            return false;
        }

        // $restdayIntersectsWithLeave = $this->publicHolidaysRepository->isLeaveIntersectsWithHoliday(Carbon::parse($request['date'])->toDateString(), Carbon::parse($request['date'])->toDateString());
        // if ($restdayIntersectsWithLeave) {
        //     $output->Error = ['You can not request leave in this period because it intersects with a holiday',
        //         ' لا يمكنك طلب أجازة فى هذه الفترة لأنها تتداخل مع إجازة رسمية '];

        //     return false;
        // }

        return true;

    }

    public function doesEmployeeHaveFixedRestdays($employee)
    {
        $workTypePolicy = $employee?->title?->workTypePolicy;

        $unleash = app(Unleash::class);
        $isNewWorkTypes = $unleash->getNewWorkTypesFeatureFlag();

        return $isNewWorkTypes ? $workTypePolicy->rest_days_type == RestDaysTypesEnum::FIXED->value :
            $workTypePolicy->work_days_type != UserWorkTypesUtil::DYNAMIC_ON_SITE;

    }

    public function isValidRestDay($employee, $date)
    {
        $allowedRestDays = $employee?->title?->workTypePolicy?->rest_days;
        $allowedRestDays = explode(',', $allowedRestDays);
        $dayOfWeek = strtolower(date('D', strtotime($date)));

        return in_array($dayOfWeek, $allowedRestDays);
    }

    public function updateBasedOnRestDay($employeeId, $date, $isAdded, stdClass &$output): void
    {
        $intersectingLeave = $this->repository->intersectingLeaveNotRest($employeeId, $date);
        if (is_null($intersectingLeave)) {
            return;
        }
        $newNetQuantity = $intersectingLeave->net_quantity;
        $newNetQuantity = $isAdded ? $newNetQuantity - config('globals.WORKED_HOURS_PER_DAY')
            : $newNetQuantity + config('globals.WORKED_HOURS_PER_DAY');
        $this->repository->update($intersectingLeave, ['net_quantity' => $newNetQuantity]);
        $balanceId = $this->employeeLeaveBalanceRepository->getBalanceOfLeave($intersectingLeave)->id;
        $this->aggregateBalance($balanceId, $employeeId, $output);
    }

    public function addRestDay(array $request, stdClass &$output): void
    {
        $request['date'] = Carbon::parse($request['date'])->toDateString();
        if (! $this->isValidAddRestDay($request, $output)) {
            return;
        }

        $timeCardOnDay = $this->timecardRepository->timecardExistsOnDate($this->employee->id, $request['date']);
        if ($timeCardOnDay) {
            $timeCardOnDay->delete();
        }

        $employeeBranchOnDate = $this->employeeBranchOnDate($this->employee, $request['date']);
        $restDayRequest = [
            'company_leave_type_id' => $this->companyRestDayLeaveType->id,
            'company_leave_type_policy_id' => $this->companyRestDayLeaveTypePolicy->id,
            'employee_id' => $this->employee->id,
            'status' => config('globals.REQUEST_STATUSES.APPROVED'), // rest day is added as approved leave request
            'from' => Carbon::parse($request['date'])->startOfDay()->toDateTimeString(),
            'to' => Carbon::parse($request['date'])->endOfDay()->toDateTimeString(),
            'net_quantity' => config('globals.WORKED_HOURS_PER_DAY'),
            'type' => config('globals.LEAVE_TYPES.REGULAR'),
            'branch_id' => $employeeBranchOnDate,
        ];

        $leaveEntity = $this->repository->create($restDayRequest, []);
        $leaveEntity->balanceEntity = $this->employeeLeaveBalanceRepository->getBalanceOfLeave($leaveEntity);
        $output->rest_day = new EmployeeLeaveRequestResource($leaveEntity);
        $balanceId = $this->employeeLeaveBalance->id;
        $aggregateBalanceRequest = $this->prepareBalanceAggregatorRequest($balanceId, $this->employee->id);
        $aggregateBalanceRequest['work_type_rest_day_balance'] = $this->employee->title?->workTypePolicy?->rest_days_count;
        $this->balanceAggregatorService->perform($aggregateBalanceRequest, $output);
        $isAdded = true;
        $this->updateBasedOnRestDay($this->employee->id, $request['date'], $isAdded, $output);

    }

    public function aggregateBalance($balanceId, $employeeId, stdClass &$output)
    {
        $aggregateBalanceRequest = $this->prepareBalanceAggregatorRequest($balanceId,
            $employeeId);
        $this->balanceAggregatorService->perform($aggregateBalanceRequest, $output);
    }

    public function addManyRestDays(array $request, stdClass &$output): void
    {
        $allErrors = [];
        foreach ($request['entity_array'] as $key => $singleRequest) {
            $this->addRestDay($singleRequest, $output);
            if (isset($output->Error) && count($output->Error)) {
                $allErrors[] = $output->Error;
            }
        }
        $output->Error = $allErrors;
    }

    public function deleteRestDays(array $request, stdClass &$output): void
    {
        $output->deleted_count = 0;
        $restDays = $this->repository->getByKeys('id', $request['ids'], ['employee.title.workTypePolicy'])->get();
        if (! count($restDays)) {
            return;
        }
        $unleash = app(Unleash::class);
        $isNewWorkTypes = $unleash->getNewWorkTypesFeatureFlag($restDays->first()->employee->company_id);
        foreach ($restDays as $restDay) {
            if (Arr::get($request, 'by_verified_attendance', false) == false && $isNewWorkTypes && $restDay->employee->title->workTypePolicy->rest_days_type == RestDaysTypesEnum::FIXED->value) {
                $output->Error = ['You can not delete rest days because this employee has fixed rest days',
                    'لا يمكنك حذف أيام راحة لأن هذا الموظف لديه أيام راحة ثابتة'];
            }
            $isAdded = false;
            $this->updateBasedOnRestDay($restDay->employee_id, Carbon::parse($restDay->from)->toDateString(), $isAdded, $output);
            $balanceId = $this->employeeLeaveBalanceRepository->getBalanceOfLeave($restDay)->id;
            $this->repository->delete([$restDay->id]);
            $output->deleted_count += 1;
            $aggregateBalanceRequest = $this->prepareBalanceAggregatorRequest($balanceId, $restDay->employee_id);
            $aggregateBalanceRequest['work_type_rest_day_balance'] = $restDay->employee->title?->workTypePolicy?->rest_days_count;
            $this->balanceAggregatorService->perform($aggregateBalanceRequest, $output);
        }
    }

    public function getRestDaysForWorker(array $request, stdClass &$output): void
    {
        $restDays = $this->repository->getRestDaysForWorker($request['employee_id'], $request['from'], $request['to']);
        $output->rest_days = new EmployeeLeaveRequestCollection($restDays);
    }

    public function getRestDayForWorker($employeeId)
    {
        return $this->repository->getRestDayForWorker($employeeId);
    }

    public function getEmployeeApprovedLeaves($employeeId, $from, $to)
    {
        $leaves = $this->repository->getEmployeeApprovedLeavesInRangeOrPassByRange($employeeId, $from, $to);
        if ($leaves === null) {
            return collect([]);
        }

        return $leaves->map(function ($item) {
            $item->type = 'leave';
            $item->name = $item->companyLeaveType->name;

            return $item;
        });
    }

    public function netQuantityInRange(array $request, stdClass &$output): void
    {
        $employee_id = auth()->user()->employee_id;

        if (isset($request['employee_id'])) {
            $employee = $this->employeeRepository->getById($request['employee_id']);
            if (! isset($employee)) {
                $output->Error = ['Invalid Employee Id', 'المعرف الخاص بالموظف غير موجود '];

                return;
            }
            $employee_id = $employee->id;
        }

        $netQuantity = $this->recalculateNetQuantity('days', $employee_id,
            $request['start_date'], $request['end_date']);
        $output->net_quantity = $netQuantity;
    }

    public function addProratedRequest(array $request, stdClass &$output): void
    {
        $employee = $this->employeeRepository->getById($request['employee_id']);
        if (is_null($employee)) {
            $output->Error = ['Invalid Employee Id', 'المعرف الخاص بالموظف غير موجود '];

            return;
        }

        $leaveTypePolicies = $employee->title->companyLeaveTypePolicies()->get();
        $sickLeaveTypeId = $employee->company->sickLeaveType->id;
        $joinDate = Carbon::parse($employee->employeeInfo->join_date);

        foreach ($leaveTypePolicies as $leaveTypePolicy) {
            $currentBalance = $this->employeeLeaveBalanceRepository->leaveBalanceForEmployee($employee->id,
                $joinDate->toDateTimeString(), $leaveTypePolicy->id);

            if (
                ! isset($currentBalance)
                || $leaveTypePolicy->prorated_monthly
                || $leaveTypePolicy->unit != 'days'
                || $leaveTypePolicy->company_leave_type_id == $employee->company->rest_day_leave_id
                || $leaveTypePolicy->company_leave_type_id == $sickLeaveTypeId
            ) {
                continue;
            }

            $balanceStart = Carbon::parse($currentBalance->start);
            $balanceEnd = Carbon::parse($currentBalance->end);
            $daysInBalancePeriod = $balanceStart->diffInDays($balanceEnd) + 1;
            $missedDays = $balanceStart->diffInDays($joinDate);
            $toBeSubbedPercentage = $missedDays / $daysInBalancePeriod;
            $baseBalance = $leaveTypePolicy->base_balance / 8;
            $toBeSubbed = (int) (floor($toBeSubbedPercentage * $baseBalance));
            if ($toBeSubbed == 0) {
                continue;
            }

            $newStart = $balanceStart->startOfDay()->toDateTimeString();

            $intersectingLeave = $this->repository->intersectingApprovedLeaveRequest($employee->id, Carbon::parse($currentBalance->start));
            if (isset($intersectingLeave)) {
                $newStart = Carbon::parse($intersectingLeave->to)->addDay()->startOfDay()->toDateTimeString();
            }

            $end = Carbon::parse($newStart)->addDays($toBeSubbed - 1)->endOfDay()->toDateTimeString();

            $proratedLeaveRequest = [
                'company_leave_type_id' => $leaveTypePolicy->company_leave_type_id,
                'company_leave_type_policy_id' => $leaveTypePolicy->id,
                'employee_id' => $employee->id,
                'status' => 'approved',
                'from' => $newStart,
                'to' => $end,
                'net_quantity' => $this->recalculateNetQuantity($leaveTypePolicy->unit, $employee->id, $newStart, $end),
                'type' => 'prorated',
                'branch_id' => $employee->branch_id,
            ];

            $this->repository->create($proratedLeaveRequest);
            $this->aggregateBalance($currentBalance->id, $employee->id, $output);
        }

    }

    public function getTimecardIdsInLeaveRange($leaveEntity)
    {
        $fromDate = Carbon::parse($leaveEntity->from)->toDateString();
        $toDate = Carbon::parse($leaveEntity->to)->toDateString();

        return $this->timecardRepository->timecardsInRange($leaveEntity->employee_id, $fromDate, $toDate)->pluck('id')->toArray() ?? [];
    }

    public function addBalanceAroundDate($employeeId, $companyLeaveTypePolicy, $date)
    {
        $monthlyClosingDay = $this->getMonthlyClosingDay();
        $end = Carbon::parse($date)->days($monthlyClosingDay)->endOfDay()->toDateTimeString();
        $start = $this->getPayrollMonthStartBasedOnMonthEnd($end);

        return $this->employeeLeaveBalanceRepository->create([
            'employee_id' => $employeeId,
            'company_leave_type_id' => $companyLeaveTypePolicy->company_leave_type_id,
            'company_leave_type_policy_id' => $companyLeaveTypePolicy->id,
            'end' => $end,
            'start' => $start,
            'balance' => $companyLeaveTypePolicy->base_balance,
        ]);
    }
}
