<?php

namespace App\Http\Requests\V1\CostCenter;

use App\Enums\CostCenter\CalculationMethodEnum;
use App\Enums\CostCenter\CostCenterFunctionTypeEnum;
use App\Enums\CostCenter\CostCenterTypeEnum;
use App\Enums\CostCenter\ExpensesSplitByEnum;
use App\Enums\CostCenter\ExpensesSplitMethodEnum;
use App\Rules\BranchIdRule;
use App\Rules\CostCenterSubDepartmentIdRule;
use App\Rules\DepartmentIdRule;
use App\Rules\EmployeeIdRule;
use App\Rules\TitleIdRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class AddCostCenterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $costCenterId = $this->route('id');

        $rules = [
            'name' => [
                'required',
                'string',
            ],
            'cost_center_id' => [
                'required',
                'string',
                'unique:cost_centers,cost_center_id,'.($costCenterId ?? 'NULL').',id,company_id,'.auth()->user()->company_id,
            ],
            'description' => [
                'nullable',
                'string',
            ],
            'type' => [
                'required',
                new Enum(CostCenterTypeEnum::class),
            ],
            'calculation_method' => [
                'required_if:type,'.CostCenterTypeEnum::LOCATION->value,
                new Enum(CalculationMethodEnum::class),
            ],
            'function_type' => [
                'required_if:type,'.CostCenterTypeEnum::DEPARTMENT->value,
                new Enum(CostCenterFunctionTypeEnum::class),
            ],
            'monthly_budget' => [
                'nullable',
                'numeric',
                'min:0',
            ],
            'allocation_targets' => [
                'required',
                'array',
            ],
            'allocation_targets.*.target_id' => [
                'required',
                'numeric',
                $this->typeSpecificRule(),
            ],
            'fixed_addition_reason' => [
                'sometimes',
                'string',
                'nullable',
                'exclude_if:fixed_addition_reason,null',
            ],
            'fixed_addition_amount' => [
                'sometimes',
                'numeric',
                'nullable',
                'exclude_if:fixed_addition_amount,null',
            ],
            'rules' => [
                'array',
                'nullable',
            ],
        ];

        if ($this->type == CostCenterTypeEnum::LOCATION->value && isset($this->expenses)) {
            $rules = array_merge($rules, $this->expensesRules());
        } elseif ($this->type == CostCenterTypeEnum::DEPARTMENT->value) {
            if (
                ($this->function_type == CostCenterFunctionTypeEnum::CENTRAL_DEPARTMENT->value || $this->function_type == CostCenterFunctionTypeEnum::CENTRAL_SUB_DEPARTMENT->value)
                 &&
                 isset($this->location_split)
            ) {
                $rules = array_merge($rules, $this->locationSplitRules());
            } elseif (
                ($this->function_type == CostCenterFunctionTypeEnum::NON_CENTRAL_DEPARTMENT->value || $this->function_type == CostCenterFunctionTypeEnum::NON_CENTRAL_SUB_DEPARTMENT->value)
             && isset($this->location_filter)) {
                $rules = array_merge($rules, $this->locationFilterRules());
            }
        }

        return $rules;
    }

    public function expensesRules(): array
    {

        return [
            'expenses' => [
                'array',
            ],
            'expenses.*.split_method' => [
                'required',
                new Enum(ExpensesSplitMethodEnum::class),
            ],
            'expenses.*.split_by' => [
                'required',
                new Enum(ExpensesSplitByEnum::class),

            ],
            'expenses.*.entities' => [
                'required',
                'array',
            ],
            'expenses.*.entities.*.split_by_id' => [
                'required',
                'numeric',
            ],
            'expenses.*.entities.*.splits' => [
                'required_if:expenses.*.split_method,'.ExpensesSplitMethodEnum::CUSTOM_PERCENTAGE->value,
                'array',
            ],
            'expenses.*.entities.*.splits.*.target_id' => [
                'required_with:expenses.*.splits',
                'numeric',
                $this->typeSpecificRule(),
            ],
            'expenses.*.entities.*.splits.*.value' => [
                'required_with:expenses.*.splits',
                'numeric',
                'min:0',
            ],
        ];
    }

    public function locationSplitRules(): array
    {
        return [
            'location_split' => [
                'array',
            ],
            'location_split.split_method' => [
                'required',
                new Enum(ExpensesSplitMethodEnum::class),
            ],
            'location_split.entities.*.splits' => [
                'required',
                'array',
            ],
            'location_split.entities.*.splits.*.split_on_id' => [
                'required',
                'integer',
            ],
            'location_split.entities.*.splits.*.value' => [
                'integer',
            ],
        ];
    }

    public function locationFilterRules(): array
    {
        return [
            'location_filters' => [
                'array',
            ],
            'location_filters.*.location_id' => [
                'required',
                'integer',
                new BranchIdRule,
            ],
        ];
    }

    /**
     * Custom validation logic after the main rules.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if (isset($this->expenses)) {
                $this->validateExpenses($validator);
                $this->validateExpensesIds($validator);
            } elseif (isset($this->location_split)) {
                $this->validateLocationSplit($validator);
                $this->validateLocationSplitIds($validator);
            }
        });
    }

    private function validateLocationSplit($validator): void
    {
        $locationSplit = $this->input('location_split');
        if ($locationSplit['split_method'] == ExpensesSplitMethodEnum::CUSTOM_PERCENTAGE->value) {
            foreach ($locationSplit['entities'] as $entityIndex => $entity) {
                $total = collect($entity['splits'])->sum('value');
                if ($total !== 100) {
                    $validator->errors()->add(
                        "location_split.entities.{$entityIndex}.splits",
                        'The total percentage for splits must equal 100.'
                    );
                }
            }
        } elseif ($locationSplit['split_method'] == ExpensesSplitMethodEnum::EQUAL_SPLIT->value || $locationSplit['split_method'] == ExpensesSplitMethodEnum::HEADCOUNT_RATIO->value) {
            foreach ($locationSplit['splits'] as $splitIndex => $split) {
                $rule = new BranchIdRule;
                if (! $rule->passes("location_split.splits.{$splitIndex}.split_on_id", $split['split_on_id'])) {
                    $validator->errors()->add(
                        "location_split.splits.{$splitIndex}.split_on_id",
                        'The split on id is not a valid branch id.'
                    );
                }
            }
        }
    }

    private function validateLocationSplitIds($validator): void
    {
        $locationSplit = $this->input('location_split');
        if ($locationSplit['split_method'] != ExpensesSplitMethodEnum::CUSTOM_PERCENTAGE->value) {
            return;
        }
        $splits = $locationSplit['entities'];
        $rule = new CostCenterSubDepartmentIdRule;
        foreach ($splits as $splitIndex => $split) {
            if (! $rule->passes("location_split.entities.{$splitIndex}.target_id", $split['target_id'])) {
                $validator->errors()->add(
                    "location_split.entities.{$splitIndex}.target_id",
                    $rule->message()
                );
            }
        }
    }

    private function validateExpenses($validator): void
    {
        $expenses = $this->input('expenses');

        foreach ($expenses as $expenseIndex => $expense) {
            if ($expense['split_method'] == ExpensesSplitMethodEnum::CUSTOM_PERCENTAGE->value) {
                foreach ($expense['entities'] as $entityIndex => $entity) {
                    $total = collect($entity['splits'])->sum('value');
                    if ($total !== 100) {
                        $validator->errors()->add(
                            "expenses.{$expenseIndex}.entities.{$entityIndex}.splits",
                            'The total percentage for splits must equal 100.'
                        );
                    }
                }
            }
        }
    }

    private function validateExpensesIds($validator)
    {
        $expenses = $this->input('expenses');

        foreach ($expenses as $expenseIndex => $expense) {
            if ($expense['split_method'] == ExpensesSplitMethodEnum::CUSTOM_PERCENTAGE->value) {
                foreach ($expense['entities'] as $entityIndex => $entity) {

                    $rule = match ($expense['split_by']) {
                        ExpensesSplitByEnum::DEPARTMENTS->value => new DepartmentIdRule,
                        ExpensesSplitByEnum::SUB_DEPARTMENTS->value => new CostCenterSubDepartmentIdRule,
                        ExpensesSplitByEnum::TITLES->value => new TitleIdRule,
                        ExpensesSplitByEnum::EMPLOYEES->value => new EmployeeIdRule,
                        default => null,
                    };

                    if ($rule && ! $rule->passes("expenses.{$expenseIndex}.entities.{$entityIndex}.split_by_id", $entity['split_by_id'])) {
                        $validator->errors()->add(
                            "expenses.{$expenseIndex}.entities.{$entityIndex}.split_by_id",
                            $rule->message()
                        );
                    }

                }
            }
        }
    }

    private function typeSpecificRule()
    {
        return $this->input('type') === CostCenterTypeEnum::LOCATION->value
            ? new BranchIdRule
            : new CostCenterSubDepartmentIdRule;
    }

    public function prepareForValidation()
    {
        $data = $this->all();
        $rules = $this->input('expenses') ?? $this->input('location_filters') ?? $this->input('location_split') ?? null;
        $data['rules'] = $rules;
        unset($data['expenses'], $data['location_filters'], $data['location_split']);
        $this->replace($data);

    }
}
