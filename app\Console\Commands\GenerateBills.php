<?php

namespace App\Console\Commands;

use App\Services\V1\Billing\BillingService;
use Illuminate\Console\Command;

class GenerateBills extends Command
{
    protected $signature = 'app:generate-bills';

    protected $description = 'Generate company bills based on billing frequency and employee count';

    public function __construct(protected BillingService $billingService)
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $companies = $this->billingService->getCompaniesToBilling();
        foreach ($companies as $company) {
            $this->billingService->generateBillForCompany($company);

        }
    }
}
