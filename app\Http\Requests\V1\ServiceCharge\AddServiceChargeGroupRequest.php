<?php

namespace App\Http\Requests\V1\ServiceCharge;

use App\Rules\BranchIdRule;
use Illuminate\Foundation\Http\FormRequest;

class AddServiceChargeGroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $groupId = $this->route('id');

        return [
            'name_ar' => [
                'required',
                'string',
                'unique:service_charge_groups,name_ar,'.($groupId ?? 'NULL').',id,company_id,'.auth()->user()->company_id,

            ],
            'name_en' => [
                'required',
                'string',
                'unique:service_charge_groups,name_en,'.($groupId ?? 'NULL').',id,company_id,'.auth()->user()->company_id,

            ],
            'branch_ids' => [
                'required',
                'array',
            ],
            'branch_ids.*' => [
                new BranchIdRule,
            ],
        ];

    }
}
