<?php

namespace App\Http\Controllers\Payroll\PayrollHub;

use App\Http\Controllers\NewController;
use App\Http\Requests\V1\BulkApproveHolidayRequest;
use App\Http\Requests\V1\BulkCancelHolidayRequest;
use App\Http\Requests\V1\PayrollHub\CompanyPayrollEntriesRequest;
use App\Http\Resources\V1\PayrollHub\PublicHolidaysAnalyticsResource;
use App\Services\V1\Holidays\PublicHolidaysService;
use App\Util\HttpStatusCodeUtil;

class PublicHolidaysController extends NewController
{
    public function __construct(
        protected PublicHolidaysService $publicHolidaysService,

    )
    {
    }

    public function bulkApprove(BulkApproveHolidayRequest $request)
    {
        $this->publicHolidaysService->bulkApprove($request->validated());
        return getResponseStructure(
            [],
            HttpStatusCodeUtil::OK,
            'Public holidays approved successfully'
        );
    }

    public function bulkCancel(BulkCancelHolidayRequest $request)
    {
        $this->publicHolidaysService->bulkCancel($request->validated());
        return getResponseStructure(
            [],
            HttpStatusCodeUtil::OK,
            'Public holidays cancelled successfully'
        );
    }

    public function getPublicHolidaysAnalytics(CompanyPayrollEntriesRequest $request)
    {
        $data = $this->publicHolidaysService->getPublicHolidaysStatistics($request->validated());
        return getResponseStructure(
            ['data' => new PublicHolidaysAnalyticsResource($data)],
            HttpStatusCodeUtil::OK,
            'Public holidays statistics retrieved successfully'
        );
    }

}