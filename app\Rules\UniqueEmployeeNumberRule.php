<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\DB;

class UniqueEmployeeNumberRule implements Rule
{
    protected ?int $exceptId;


    public function __construct(?int $exceptId = null)
    {
        $this->exceptId = $exceptId;
    }


    public function passes($attribute, $value): bool
    {
        $query = DB::table('employees')
            ->where('employee_number', $value)
            ->where('company_id', config('globals.company')->id)
            ->whereNull('deleted_at');

        if ($this->exceptId) {
            $query->where('id', '!=', $this->exceptId);
        }

        return !$query->exists();
    }

    public function message(): string
    {
        return __('validation.unique_employee_number', ['attribute' => 'employee number']);
    }
}
