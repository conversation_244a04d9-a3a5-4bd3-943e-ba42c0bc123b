<?php

namespace App\Http\Requests\V1\Files;

use Illuminate\Foundation\Http\FormRequest;

class GetWorkerFilesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {

        $rules['page'] = ['integer', 'min:1'];
        $rules['page_size'] = ['integer', 'min:0']; // 0 means no pagination
        $rules['search_value'] = ['min:1', 'max:30'];

        return $rules;
    }
}
