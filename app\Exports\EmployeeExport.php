<?php

namespace App\Exports;

use App\FeatureToggles\Unleash;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class EmployeeExport implements ShouldAutoSize, WithColumnFormatting, WithEvents, WithHeadings, WithStyles
{
    private $counters;

    private $code_type;

    private $unleash;

    private bool $hasTrainingCertification;

    public function __construct($counters = null, $code_type = null)
    {
        if ($counters == null) {
            $counters['branches_count'] = $counters['titles_count'] = $counters['nationality_count'] = $counters['military_status_count'] = 0;
        }
        $this->counters = $counters;
        $this->code_type = $code_type;
        $this->unleash = app(Unleash::class);
        $companyId = config('globals.company')?->id;
        $this->hasTrainingCertification = in_array($companyId, config('globals.TRAINING_CERTIFICATION_COMPANIES'));
    }

    public function headings(): array
    {
        $headings = [];

        if ($this->code_type === 'manual') {
            $headings[] = 'Code';
        }

        // Add the remaining headings
        $headings = array_merge($headings, [
            'Employee First Name (Arabic)',
            'Employee Second Name (Arabic)',
            'Employee Third Name (Arabic)',
            'Employee Fourth Name (Arabic)',
            'Employee Fifth Name (Arabic)',
            'Employee First Name (English)',
            'Employee Second Name (English)',
            'Employee Third Name (English)',
            'Employee Fourth Name (English)',
            'Employee Fifth Name (English)',
            'National ID #',
            'Country Code',
            'Phone Number',
            'Title',
            'Main Branch',
            'Hiring Date',
            'Gender',
            'Experience start date',
            'Years of Experience',
            'Birth Date',
            'Address',
            'Notes',
            'Email',
            'Passport Number',
            'Secondary Phone',
            'Nationality',
            'Military Status',
        ]);

        if ($this->hasTrainingCertification) {
            $headings = array_merge($headings, ['Training Certification Status']);
        }

        return [$headings];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $highestRow = config('globals.MAX_EXCEL_ROWS');
                $headings = $this->headings()[0];

                // Create a mapping of heading names to their column letters
                $columns = [];
                foreach ($headings as $index => $heading) {
                    $columns[$heading] = Coordinate::stringFromColumnIndex($index + 1);
                }

                for ($row = 2; $row <= $highestRow; $row++) {
                    // Add branches dropdown
                    if (isset($columns['Main Branch'])) {
                        $branchesCell = $event->sheet->getCell("{$columns['Main Branch']}{$row}");
                        if ($branchesCell) {
                            $branchesValidation = $branchesCell->getDataValidation();
                            $this->setValidation($branchesValidation);
                            $branchesValidation->setFormula1('\'Worksheet 1\'!$A$2:$A$' . ($this->counters['branches_count'] + 1));
                        }
                    }

                    // Add titles dropdown
                    if (isset($columns['Title'])) {
                        $titlesCell = $event->sheet->getCell("{$columns['Title']}{$row}");
                        if ($titlesCell) {
                            $titlesValidation = $titlesCell->getDataValidation();
                            $this->setValidation($titlesValidation);
                            $titlesValidation->setFormula1('\'Worksheet 1\'!$B$2:$B$' . ($this->counters['titles_count'] + 1));
                        }
                    }

                    // Add nationality dropdown
                    if (isset($columns['Nationality'])) {
                        $nationalityCell = $event->sheet->getCell("{$columns['Nationality']}{$row}");
                        if ($nationalityCell) {
                            $nationalityValidation = $nationalityCell->getDataValidation();
                            $this->setValidation($nationalityValidation);
                            $nationalityValidation->setFormula1('\'Worksheet 1\'!$C$2:$C$' . ($this->counters['nationality_count'] + 1));
                        }
                    }

                    // Add military status dropdown
                    if (isset($columns['Military Status'])) {
                        $militaryStatusCell = $event->sheet->getCell("{$columns['Military Status']}{$row}");
                        if ($militaryStatusCell) {
                            $militaryStatusValidation = $militaryStatusCell->getDataValidation();
                            $this->setValidation($militaryStatusValidation);
                            $militaryStatusValidation->setFormula1('\'Worksheet 1\'!$D$2:$D$' . ($this->counters['military_status_count'] + 1));
                        }
                    }

                    // Add gender dropdown
                    if (isset($columns['Gender'])) {
                        $genderCell = $event->sheet->getCell("{$columns['Gender']}{$row}");
                        if ($genderCell) {
                            $genderValidation = $genderCell->getDataValidation();
                            $this->setValidation($genderValidation);
                            $genderValidation->setFormula1('\'Worksheet 1\'!$F$2:$F$3');
                        }
                    }

                    // Add training certification dropdown (if applicable)
                    if ($this->hasTrainingCertification && isset($columns['Training Certification Status'])) {
                        $trainingCertificationStatusCell = $event->sheet->getCell("{$columns['Training Certification Status']}{$row}");
                        if ($trainingCertificationStatusCell) {
                            $trainingCertificationStatusValidation = $trainingCertificationStatusCell->getDataValidation();
                            $this->setValidation($trainingCertificationStatusValidation);
                            $trainingCertificationStatusValidation->setFormula1('\'Worksheet 1\'!$E$2:$E$' . ($this->counters['training_certification_status_count'] + 1));
                        }
                    }
                }
            },
        ];
    }

    private function setValidation(&$validation): void
    {
        $validation->setType(DataValidation::TYPE_LIST);
        $validation->setErrorStyle(DataValidation::STYLE_INFORMATION);
        $validation->setAllowBlank(false);
        $validation->setShowInputMessage(true);
        $validation->setShowErrorMessage(true);
        $validation->setShowDropDown(true);
    }

    public function columnFormats(): array
    {
        $highestRow = config('globals.MAX_EXCEL_ROWS');
        // Instead of manually computing each column using chr/ord (which fails for multi-letter columns),
        // we loop over the headings array and use Coordinate::stringFromColumnIndex.
        $headings = $this->headings()[0];
        $formats = [];
        for ($i = 1; $i <= count($headings); $i++) {
            $colLetter = Coordinate::stringFromColumnIndex($i);
            $formats[$colLetter . '1:' . $colLetter . $highestRow] = NumberFormat::FORMAT_TEXT;
        }
        return $formats;
    }

    public function styles(Worksheet $sheet)
    {
        // apply bold font to cells from A1 to D1 and from L1 to R1
        $headings = $this->headings()[0];

        // Determine which columns should be bold
        $boldColumns = [];
        foreach ($headings as $index => $heading) {
            $colLetter = Coordinate::stringFromColumnIndex($index + 1);
            // Apply your logic to determine which columns should be bold
            // For example, bold all except the English name columns:
            if (!preg_match('/\(English\)$/', $heading)) {
                $boldColumns[] = $colLetter . '1';
            }
        }
        
        // Apply bold style
        foreach ($boldColumns as $cell) {
            $sheet->getStyle($cell)->getFont()->setBold(true);
        }
    }
}
