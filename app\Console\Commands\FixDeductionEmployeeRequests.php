<?php

namespace App\Console\Commands;

use App\Models\AttendanceDeduction;
use App\Models\EmployeeRequest;
use DB;
use Illuminate\Console\Command;

class FixDeductionEmployeeRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-deduction-employee-requests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            echo 'Fixing deduction employee requests';
            DB::beginTransaction();
            $attendanceDeductions = AttendanceDeduction::whereNull('workflow_id')
                ->orWhere(function ($query) {
                    $query->whereHas('workflow', function ($subQuery) {
                        $subQuery->where('status', 'completed');
                    });
                })
                ->get();

            echo 'Deductions count: '.$attendanceDeductions->count()."\n";

            foreach (
                $attendanceDeductions as $deduction
            ) {
                $deduction->employeeRequests()->delete();
                echo 'Employee requests deleted for deduction: '.$deduction->id."\n";
            }

            $employeeRequestsCount = EmployeeRequest::count();
            echo 'Employee requests count: '.$employeeRequestsCount."\n";

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            echo $e;
        }
    }
}
