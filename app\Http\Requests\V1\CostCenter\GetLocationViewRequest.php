<?php

namespace App\Http\Requests\V1\CostCenter;

use Illuminate\Foundation\Http\FormRequest;

class GetLocationViewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'month' => [
                'required',
                'string',
            ],
            'year' => [
                'required',
                'string',
            ],

        ];
    }

    public function prepareForValidation()
    {
        $dateArr = explode('-', $this->input('month'));
        $this->merge([
            'month' => $dateArr[0],
            'year' => $dateArr[1],
        ]);
    }
}
