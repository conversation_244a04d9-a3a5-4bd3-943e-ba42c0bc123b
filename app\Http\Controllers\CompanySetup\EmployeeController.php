<?php

namespace App\Http\Controllers\CompanySetup;

use App\DomainData\EmployeeDto;
use App\Exports\CompanyEmployeesExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\EmployeeRehireRequest;
use App\Http\Requests\TerminateEmployeeRequest;
use App\Http\Resources\GetPendingTerminationRequestResource;
use App\Models\EmployeeChange;
use App\Repositories\Repository;
use App\Rules\EmployeeIdRule;
use App\Rules\PhoneNumber;
use App\Services\CompanySetup\BusinessServices\EmployeeCsvService;
use App\Services\CompanySetup\BusinessServices\GetBranchEmployeesService;
use App\Services\CompanySetup\BusinessServices\GetEmployeesFiltersService;
use App\Services\CompanySetup\CrudServices\EmployeeCrudService;
use App\Services\CompanySetup\EmployeesService;
use App\Services\EmployeeChange\EmployeeChangesService;
use App\Services\LeaveManagement\FillEmployeeBalancesService;
use App\Services\TerminationService;
use App\Services\TimeTracking\BusinessServices\EmployeeLeavesCsvService;
use App\Services\V1\Attendance\ExtraWorkdayRequestsService;
use App\Util\HttpStatusCodeUtil;
use App\Util\ScopeUtil;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;

class EmployeeController extends Controller
{
    use EmployeeDto;

    public function __construct(
        private EmployeeCrudService         $service,
        private EmployeeCsvService          $employeeCsvService,
        private EmployeeLeavesCsvService    $employeeLeavesCsvService,
        private GetBranchEmployeesService   $getBranchEmployeesService,
        private GetEmployeesFiltersService  $getEmployeeFiltersService,
        private FillEmployeeBalancesService $fillEmployeeBalances,
        private EmployeesService            $employeesService,
        private EmployeeChangesService      $employeeChangesService,
        private TerminationService          $terminationService,
        private ExtraWorkdayRequestsService $extraWorkdayRequestsService,
    )
    {
    }

    public function createMany(array $request, \stdClass &$output): void
    {
        $validator = \Validator::make($request, [
            'entity_array' => 'required|array',
            'related_objects' => 'array',
            'related_objects.*' => 'in:title,branch,governorate,city',
        ]);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        $rules = $this->getRules([]);
        $rules['phone'] = ['required', new PhoneNumber];

        foreach ($request['entity_array'] as $key => $entity) {
            $validator = \Validator::make($entity, $rules);
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }
        }

        $this->service->createMany($request, $output);

    }

    public function create(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules([]);
        $rules = $this->getAdditionalRules($rules);
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        $this->service->create($request, $output);

    }

    public function liteCreate(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['first_name', 'second_name', 'third_name', 'gender',
            'phone', 'national_id', 'title_id', 'country_code']);
        $rules['branch_id'] = 'integer';

        $rules = $this->getAdditionalRules($rules, $request['country_code']);
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        $request['first_name_ar'] = $request['first_name_en'] = $request['first_name'];
        $request['second_name_ar'] = $request['second_name_en'] = $request['second_name'];
        $request['third_name_ar'] = $request['third_name_en'] = $request['third_name'];

        $request['name_ar'] = $request['first_name_ar'] . ' ' . $request['second_name_ar'] . ' ' . $request['third_name_ar'];
        $request['name_en'] = $request['name_ar'];
        $request['name'] = $request['name_ar'];
        if (isset($request['country_code'])) {
            if ($request['country_code'] === '+20' && substr($request['phone'], 0, 1) === '0') {
                $request['phone'] = $request['country_code'] . substr($request['phone'], 1);
            } else {
                $request['phone'] = $request['country_code'] . $request['phone'];
            }
        }

        $request['join_date'] = date('Y-m-d');


        $this->employeesService->liteCreate($request);
    }

    public function update(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules([]);
        $rules = $this->getAdditionalRules($rules);
        $rules['id'] = 'required|integer';
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->update($request, $output);
    }

    public function getByFilter(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules();

        if (!isset($request['page_size'])) {
            $request['page_size'] = config('globals.MAX_PAGE_SIZE');
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->getEmployeeFiltersService->perform($request, $output);
    }

    public function getById(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules(['related_objects.*', 'related_objects_count.*']);
        $rules['id'] = 'required|numeric';
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (!isset($request['related_objects'])) {
            $request['related_objects'] = ['title', 'branch', 'branches', 'managedDepartments', 'managedSubDepartments'];
        }
        if (!isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $this->service->getById($request, $output);
        $employee = $output->employee;
        $employee->profile_image = $employee->profilePicture->attachment_url ?? null;
        $scopes = $employee->title?->role?->scopes?->pluck('key')->toArray() ?? [];
        $employee->scope = $scopes[0] ?? ScopeUtil::BRANCH_SCOPE;
    }

    public function delete(array $request, \stdClass &$output): void
    {
        $rules['ids'] = ['required', 'array'];
        $rules['ids.*'] = ['required', 'numeric'];
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $this->failMessages($validator->messages());

            return;
        }

        $request = $validator->validate();

        $this->service->delete($request, $output);
    }

    public function employeeCsvUpload(array $request, \stdClass &$output): void
    {
        $rules['csv_file'] = 'required|mimes:xlsx,txt';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $fileSize = request()->file('csv_file')->getSize() / (1024);

        if ($fileSize > config('globals.MAX_EXCEL_FILE_SIZE_KB')) {
            $output->Error = ['File size is too large, max file size is 15M', 'حجم الملف كبير جدًا ، الحد الأقصى لحجم الملف هو 15 ميجا بايت'];

            return;
        }
        $this->employeeCsvService->perform($request, $output);
    }

    public function employeeCsvDownload(array $request, \stdClass &$output): void
    {

        $validator = \Validator::make($request, []);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $this->employeeCsvService->download($request, $output);
    }



    public function getAllInBranch(array $request, \stdClass &$output)
    {

        $rules['branch_ids'] = 'required';
        $rules['with_revoked_employees'] = 'boolean';
        $request['branch_ids'] = [];
        $request['status'] = 'string|in:active,terminated';

        if (!isset($request['branch_id']) && !is_null(config('globals.branchId'))) {
            $employeeId = config('globals.user')->employee_id;
            $employeeRepository = Repository::getRepository('Employee');
            $employee = $employeeRepository->getById($employeeId, ['branches']);
            $request['branch_ids'] = $this->getBranchIds($employee->branches);
        } else {
            array_push($request['branch_ids'], $request['branch_id']);
        }

        if (!isset($request['status'])) {
            $request['status'] = 'active';
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        if (!isset($request['with_revoked_employees'])) {
            $request['with_revoked_employees'] = false;
        }
        $this->getBranchEmployeesService->perform($request, $output);
    }

    public function leavesCsvUpload(array $request, \stdClass &$output): void
    {
        $rules['csv_file'] = 'required|mimes:xlsx,txt';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $fileSize = request()->file('csv_file')->getSize() / (1024);
        if ($fileSize > config('globals.MAX_EXCEL_FILE_SIZE_KB')) {
            $output->Error = ['File size is too large, max file size is 15M', 'حجم الملف كبير جدًا ، الحد الأقصى لحجم الملف هو 15 ميجا بايت'];

            return;
        }

        $this->employeeLeavesCsvService->perform($request, $output);
    }

    public function leavesCsvDownload(array $request, \stdClass &$output): void
    {

        // this is used in employeeCsvDownload. why?
        $validator = \Validator::make($request, []);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        ///////////////////////

        $request = $validator->validate();
        $this->employeeLeavesCsvService->download($request, $output);

    }

    public function getBranchIds($branches)
    {
        return array_map(function ($branch) {
            return $branch['id'];
        }, $branches->toArray());
    }

    /**
     * @return void
     */
    public function timecardsAndLeaves(array $data, \stdClass &$output)
    {
        $validator = Validator::make($data, $this->timecardsAndLeavesRules());
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $this->service->timecardsAndLeaves($validator->validate(), $output);
    }

    public function timecardsAndLeavesRules(): array
    {
        return [
            'branch_id' => ['required', 'exists:branches,id', Rule::exists('branch_employee', 'branch_id')
                ->where(function ($query) {
                    $query->where('employee_id', auth()->user()->employee_id);
                })],
            'from' => 'required|date',
            'to' => 'required|date|after:date_from',
            'employee_id' => ['integer', new EmployeeIdRule],
        ];
    }

    public function activate(array $request, \stdClass &$output): void
    {
        $rules['employee_id'] = 'required|integer';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->activate($request, $output);
    }

    public function deactivate(array $request, \stdClass &$output): void
    {
        $rules['employee_id'] = 'required|integer';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->deactivate($request, $output);
    }

    public function fillRestDays(array $request, \stdClass &$output)
    {
        return $this->fillEmployeeBalances->fill();
    }

    // public function requestsPendingOnMe(array $request, \stdClass &$output)
    // {
    //     $rules['filter'] = 'string|in:overtimes,waive_deductions,unverified_attendances,employee_leave_requests,missing_info';

    //     $validator = \Validator::make($request, $rules);
    //     if ($validator->fails()){
    //         $output->Error = $validator->messages();
    //         return;
    //     }
    //     if(!isset($request['filter']))
    //         $request['filter'] = 'all';
    //     return $this->service->requestsPendingOnMe($request, $output);
    // }

    // public function requestsPendingOnMeCount(array $request, \stdClass &$output)
    // {
    //     return $this->service->requestsPendingOnMeCount($request, $output);
    // }

    public function transferEmployeeToBranch(array $request, \stdClass &$output)
    {
        $validator = Validator::make($request, EmployeeChange::getTransferToBranchRules());
        if ($validator->fails()) {
            $output->Error = $validator->messages();
        }

        $request = $validator->validate();

        $request['change_type'] = 'branch';

        $this->employeeChangesService->perfrom($request, $output);

        if (isset($output->message)) {
            return response()->json($output, HttpStatusCodeUtil::OK);
        }
    }

    public function terminateEmployee(TerminateEmployeeRequest $request)
    {
        $payload = $request->validated();
        DB::beginTransaction();
        try {
            $this->service->terminateEmployee($payload);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();

            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function cancelTermination($id)
    {
        //        $payload = $request->validated();

        $this->terminationService->cancelTermination($id);

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);

    }

    public function exportCompanyEmployees()
    {
        return Excel::download(new CompanyEmployeesExport, 'Employees.xlsx', \Maatwebsite\Excel\Excel::XLSX);

    }

    public function requestTermination(TerminateEmployeeRequest $request)
    {
        $payload = $request->validated();
        DB::beginTransaction();
        try {
            $this->terminationService->requestTerminate($payload);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();

            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function directTermination(TerminateEmployeeRequest $request)
    {
        $payload = $request->validated();
        DB::beginTransaction();
        try {
            $this->terminationService->directTerminate($payload);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();

            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function fetchTerminationRequests(int $id)
    {
        try {
            $requests = $this->terminationService->fetchPendingTerminationRequest($id);
        } catch (Exception $e) {
            Log::error($e);

            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }

        return getResponseStructure(['data' => new GetPendingTerminationRequestResource($requests)], HttpStatusCodeUtil::OK);
    }

    public function terminationWarning(Request $request)
    {
        try {
            $payload = $request->all();
            $warningInfo = $this->terminationService->checkUnverifiedAttendanceAndLeaves($payload);

            return getResponseStructure(['data' => $warningInfo], HttpStatusCodeUtil::OK);
        } catch (Exception $e) {
            Log::info($e);

            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }
    }

    public function getLastVerifiedAttendance(int $employeeId): \Illuminate\Http\JsonResponse
    {
        try {
            $lastVerifiedAttendance = $this->terminationService->lastVerifiedAttendance($employeeId);

            if ($lastVerifiedAttendance === null) {
                return getResponseStructure(['data' => null], HttpStatusCodeUtil::OK);
            }

            return getResponseStructure(['data' => ['last_verified_attendance' => $lastVerifiedAttendance]], HttpStatusCodeUtil::OK);
        } catch (Exception $e) {
            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }
    }

    public function rehiring(int $id, EmployeeRehireRequest $request)
    {
        $payload = $request->validated();
        DB::beginTransaction();
        try {
            $this->terminationService->employeeRehire($id, $payload);
            $this->terminationService->fixLeaveBalancesForRehiredEmployee($id);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();
            throw $e;
            //            return getErrorResponseStructure($e, $e->getMessage());
        }

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function approveTermination(int $id)
    {
        $this->terminationService->approveTermination($id);

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function rejectTermination(int $id)
    {
        $this->terminationService->rejectTermination($id);

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function directCancelTermination(int $id)
    {
        DB::beginTransaction();
        try {
            $this->terminationService->directCancelTermination($id);
            DB::commit();

            return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to cancel termination: ' . $e->getMessage(), ['id' => $id]);

            throw $e;
        }
    }

    public function approveExtraWorkdayRequest(int $id)
    {
        $this->extraWorkdayRequestsService->approveExtraWorkdayRequest($id);

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function rejectExtraWorkdayRequest(int $id)
    {
        $this->extraWorkdayRequestsService->rejectExtraWorkdayRequest($id);

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function cancelExtraWorkdayRequest(int $id)
    {
        $this->extraWorkdayRequestsService->cancelExtraWorkdayRequest($id);

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }
}
