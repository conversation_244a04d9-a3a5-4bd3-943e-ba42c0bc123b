<?php

namespace App\Http\Controllers\Payroll\PayrollHub;

use App\Http\Controllers\NewController;
use App\Http\Requests\V1\PayrollHub\CompanyPayrollEntriesRequest;
use App\Http\Requests\V1\PayrollHub\EmployeePayrollEntriesRequest;
use App\Http\Resources\V1\PayrollHub\EmployeeResource;
use App\Http\Resources\V1\PayrollHub\GetCompanyPenaltiesResource;
use App\Http\Resources\V1\PayrollHub\GetEmployeePenaltiesResource;
use App\Services\V1\PayrollHub\PenaltiesService;
use App\Util\HttpStatusCodeUtil;

class PenaltiesController extends NewController
{

    public function __construct(
        protected PenaltiesService $penaltiesService,
    )
    {
    }

    public function getAllPenaltiesForCompany(CompanyPayrollEntriesRequest $request){
        $penalties = $this->penaltiesService->getCompanyPenalties($request->validated());
        $data = GetCompanyPenaltiesResource::collection($penalties);
        $paginatedData = $data->response()->getData();
        return getResponseStructure(
            ['data' => $data, 'pagination' => $paginatedData->meta],
            HttpStatusCodeUtil::OK,
            'Penalties fetch successfully'
        );
    }

    public function getPenaltiesForEmployee(EmployeePayrollEntriesRequest $request){
        $data = $this->penaltiesService->getEmployeePenalties($request->validated());
        return getResponseStructure(
            ['data' => $data->has_penalties ? new GetEmployeePenaltiesResource($data) : new EmployeeResource($data)],
            HttpStatusCodeUtil::OK,
            'Penalties fetch successfully'
        );
    }
}