<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\EmployeeLeaveBalance;
use App\Models\EmployeeLeaveRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixOvertimeLeaveBalances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'leaves:fix-overtime-balances {company_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate overtime leave requests to annual leave type and update balances';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companyId = $this->argument('company_id');
        
        try {
            // Validate company exists
            $company = Company::find($companyId);
            if (!$company) {
                $this->error("Company with ID {$companyId} not found.");
                return 1;
            }

            // Validate company has overtime and annual leave types
            if (!$company->OvertimeLeaveType || !$company->annualLeaveType) {
                $this->error("Company must have both overtime and annual leave types configured.");
                return 1;
            }

            $this->info("Starting migration for company ID: {$companyId}");
            Log::info("Starting overtime to annual leave migration for company ID: {$companyId}");

            DB::beginTransaction();

            // Get all leave requests with overtime leave type
            $overtimeLeaveRequests = EmployeeLeaveRequest::where('company_id', $companyId)
                ->where('company_leave_type_id', $company->OvertimeLeaveType->id)
                ->get();

            $this->info("Found {$overtimeLeaveRequests->count()} overtime leave requests to migrate");
            Log::info("Found {$overtimeLeaveRequests->count()} overtime leave requests to migrate");

            // Update leave requests to annual leave type
            foreach ($overtimeLeaveRequests as $request) {
                $this->info("Processing leave request ID: {$request->id}");
                Log::info("Processing leave request ID: {$request->id}");

                // Update both leave type fields
                $request->company_leave_type_id = $company->annualLeaveType->id;
                $request->company_leave_type_policy_id = $company->annualLeaveType->companyLeaveTypePolicy->id;
                $request->save();

                // Deduct from annual leave balance for the specific period
                $annualBalance = EmployeeLeaveBalance::where('employee_id', $request->employee_id)
                    ->where('company_leave_type_id', $company->annualLeaveType->id)
                    ->where('start', '<=', $request->from)
                    ->where('end', '>=', $request->to)
                    ->first();

                if ($annualBalance) {
                    $oldBalance = $annualBalance->balance;
                    $annualBalance->balance -= $request->net_quantity;
                    $annualBalance->save();

                    $this->info("Updated annual balance for employee {$request->employee_id} for period {$annualBalance->from_date} to {$annualBalance->to_date}: {$oldBalance} -> {$annualBalance->balance}");
                    Log::info("Updated annual balance for employee {$request->employee_id} for period {$annualBalance->from_date} to {$annualBalance->to_date}: {$oldBalance} -> {$annualBalance->balance}");
                } else {
                    $this->warn("No annual balance found for employee {$request->employee_id} covering period {$request->from_date} to {$request->to_date}");
                    Log::warning("No annual balance found for employee {$request->employee_id} covering period {$request->from_date} to {$request->to_date}");
                }
            }

            // Reset overtime balances to zero for the affected periods
            foreach ($overtimeLeaveRequests as $request) {
                $overtimeBalance = EmployeeLeaveBalance::where('employee_id', $request->employee_id)
                    ->where('company_leave_type_id', $company->OvertimeLeaveType->id)
                    ->where('start', '<=', $request->from)
                    ->where('end', '>=', $request->to)
                    ->first();

                if ($overtimeBalance) {
                    $oldBalance = $overtimeBalance->balance;
                    $overtimeBalance->balance = 0;
                    $overtimeBalance->save();

                    $this->info("Reset overtime balance for employee {$request->employee_id} for period {$overtimeBalance->from_date} to {$overtimeBalance->to_date}: {$oldBalance} -> 0");
                    Log::info("Reset overtime balance for employee {$request->employee_id} for period {$overtimeBalance->from_date} to {$overtimeBalance->to_date}: {$oldBalance} -> 0");
                }
            }

            DB::commit();
            $this->info("Migration completed successfully!");
            Log::info("Overtime to annual leave migration completed successfully for company ID: {$companyId}");

            return 0;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("An error occurred: " . $e->getMessage());
            Log::error("Error in overtime to annual leave migration: " . $e->getMessage());
            return 1;
        }
    }
}
