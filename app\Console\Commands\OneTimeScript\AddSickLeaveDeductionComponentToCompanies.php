<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Company;
use App\Services\PayrollSetup\SalaryComponentsCategoriesService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AddSickLeaveDeductionComponentToCompanies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add-sick_leave:component';

    /**
     * The console command description.
     *
     * @var string
     */
    public function __construct(private SalaryComponentsCategoriesService $salaryComponentsCategoriesService)
    {
        parent::__construct();
    }

    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $companies = Company::withWhereHas('sickLeaveType', function ($q) {
                $q->where('leave_deduction_percentage', '>', 0);
            })
                ->whereDoesntHave('salaryComponents', function ($q) {
                    $q->where('name', 'sick_leave_deduction');
                })
                ->with('employees', function ($q) {
                    $q->where('status', '!=', 'terminated')
                        ->with('user');
                })
                ->get();

            foreach ($companies as $company) {
                if (count($company->employees) == 0) {
                    continue;
                }

                config(config(['globals.user' => $company->employees[0]->user]));

                $this->salaryComponentsCategoriesService->setSickLeaveDeductionComponent();
            }
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
            // dd($e);
        }
    }
}
