<?php

namespace App\Handlers;

use App\Util\HttpStatusCodeUtil;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Throwable;

class ValidationExceptionHandler extends BaseExceptionHandler
{
    /**
     * {@inheritDoc}
     */
    public static function getResponseStatusCode(Throwable $exception): int
    {
        return HttpStatusCodeUtil::UNPROCESSABLE_ENTITY;
    }

    /**
     * @param  ValidationException  $exception
     */
    public static function getPayload(Request $request, Throwable $exception): array
    {
        $payload = parent::getPayload($request, $exception);
        $payload['errors']['validations'] = $exception->errors();

        return $payload;
    }
}
