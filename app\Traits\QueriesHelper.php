<?php

namespace App\Traits;

use App\Models\Role;
use App\Models\Title;
use App\Util\EmployeeUtil;
use App\Util\ScopeUtil;
use Illuminate\Support\Facades\DB;
use App\Models\Employee;

trait QueriesHelper
{
    public function appendPendingWaiveDeductionQuery(&$query, $from = null, $to = null)
    {
        if (class_basename($query->getModel()) == 'AttendanceDeduction') {
            $query
                ->when((isset($from) && isset($to)), function ($q) use ($from, $to) {
                    $q->whereBetween('attendance_deductions.date', [$from, $to]);
                })
                ->whereHas('employeeRequests', function ($q) {
                    $q->where('employee_requests.status', 'pending');
                });
        }

    }

    public function appendPendingPenaltyQuery(&$query)
    {
        if (class_basename($query->getModel()) == 'Penalty') {
            $query->where('penalties.status', 'pending')
                ->whereHas('employeeApproves', function ($q) {
                    $q->where('workflow_approval_cycle.order', 1)
                        ->where('workflow_approval_cycle.status', '!=', 'rejected');

                });
        }
    }

    public function appendScopeQuery(&$query, $data = [], bool $getOriginalBranch = false, $considerTransferredEmployees = false) // original branch is the branch that's stored in the model itself, attendances->branch_id ... overtimes->branch_id ...etc
    {
        $branchIds = $data['branch_ids'] ?? [];
        $departmentIds = $data['department_ids'] ?? [];
        $titleIds = $data['title_ids'] ?? [];
        $subDepartmentIds = $data['sub_department_ids'] ?? [];

        $branchIds = count($branchIds) ? array_intersect($branchIds, config('globals.scope_branch_ids'))
            : config('globals.scope_branch_ids');

        if (config('globals.scope_key') == ScopeUtil::DEPARTMENT_SCOPE) {
            $departmentIds = count($departmentIds) ? array_intersect($departmentIds, config('globals.scope_department_ids'))
                : config('globals.scope_department_ids');
        }

        if (config('globals.scope_key') == ScopeUtil::SUB_DEPARTMENT_SCOPE) {
            $subDepartmentIds = count($subDepartmentIds) ? array_intersect($subDepartmentIds, config('globals.scope_sub_department_ids'))
                : config('globals.scope_sub_department_ids');
        }

        if (class_basename($query->getModel()) == 'Employee') {
            $this->appendScopeQueryToEmployeeModel($query, $branchIds, $departmentIds, $titleIds, $subDepartmentIds, $getOriginalBranch, $considerTransferredEmployees);
        } else {
            $this->appendScopeQueryToOtherModels($query, $branchIds, $departmentIds, $titleIds, $subDepartmentIds, $getOriginalBranch, $considerTransferredEmployees);
        }
    }

    private function appendScopeQueryToOtherModels(&$query, $branchIds = [], $departmentIds = [], $titleIds = [], $scopeSubDepartmentIds = [], bool $getOriginalBranch = false, $considerTransferredEmployees = false)
    {
        $query->when($getOriginalBranch == true, function ($q) use ($branchIds) {
            $q->whereIn('branch_id', $branchIds);
        })
            ->whereHas('employee', function ($q) use ($branchIds, $departmentIds, $titleIds, $scopeSubDepartmentIds, $getOriginalBranch, $considerTransferredEmployees) {
                $this->appendScopeQueryToEmployeeModel($q, $branchIds, $departmentIds, $titleIds, $scopeSubDepartmentIds, $getOriginalBranch, $considerTransferredEmployees);
            });
    }

    private function appendScopeQueryToEmployeeModel(&$query, $branchIds = [], $departmentIds = [], $titleIds = [], $scopeSubDepartmentIds = [], bool $getOriginalBranch = false, $considerTransferredEmployees = false)
    {
        $isMeScope = config('globals.scope_key') == ScopeUtil::ME_SCOPE;
        $isDepartmentScope = config('globals.scope_key') == ScopeUtil::DEPARTMENT_SCOPE;
        $isSubDepartmentScope = config('globals.scope_key') == ScopeUtil::SUB_DEPARTMENT_SCOPE;

        if ($isMeScope) {
            $query
                ->when($isMeScope, function ($q) {
                    $q->where('id', auth()->user()->employee->id);
                });

            return;
        }
        $query
            ->when($getOriginalBranch == false, function ($q) use ($branchIds, $considerTransferredEmployees) {
                $q->where(function ($q) use ($branchIds, $considerTransferredEmployees) {
                    $q->whereIn('branch_id', $branchIds)
                        ->when($considerTransferredEmployees == true, function ($q) use ($branchIds) {
                            $q->orWhereHas('employeeChanges', function ($q) use ($branchIds) {
                                $q->whereIn('from_value', $branchIds)
                                    ->where('change_type', 'branch')
                                    ->where('status', 'completed');
                            });
                        });
                });

            })
            ->when(count($departmentIds) || $isDepartmentScope, function ($q) use ($departmentIds) {
                $q->whereHas('title', function ($q) use ($departmentIds) {
                    $q->whereIn('department_id', $departmentIds);
                });
            })
            ->when(count($scopeSubDepartmentIds) || $isSubDepartmentScope, function ($q) use ($scopeSubDepartmentIds) {
                $q->whereHas('title', function ($q) use ($scopeSubDepartmentIds) {
                    $q->whereIn('sub_department_id', $scopeSubDepartmentIds);
                });
            })
            ->when(count($titleIds), function ($q) use ($titleIds) { // title filter
                $q->whereIn('title_id', $titleIds);
            });
    }

    public function appendUnverifiedQuery(&$query, $request, $showUnverifiedInWithoutOut = true, $showPendingOnMeOnly = false)
    {
        $request['branch_ids'] = $request['branch_ids'] ?? [];
        $request['branch_ids'] = count($request['branch_ids']) ? array_intersect($request['branch_ids'], config('globals.scope_branch_ids'))
            : config('globals.scope_branch_ids');

        $query->where('in_out', 'in')
            ->where(function ($q) use ($request, $showUnverifiedInWithoutOut, $showPendingOnMeOnly) {
                $q->where(function ($q) use ($request, $showPendingOnMeOnly) {
                    $q->whereIn('cicos.status', ['verified', 'approved']);
                    $q->whereHas('pairedClock', function ($iQuery) use ($request, $showPendingOnMeOnly) {
                        $iQuery->where('status', 'unverified');
                        $iQuery->where(function ($q) use ($request, $showPendingOnMeOnly) {
                            $q->when($showPendingOnMeOnly == false, function ($q) use ($request) {
                                $q->whereHas('employee', function ($q) use ($request) {
                                    $q->whereIn('branch_id', $request['branch_ids']);
                                });
                            });
                            $q->orwhereIn('branch_id', $request['branch_ids']);
                        });
                    });
                });
                $q->orWhere(function ($query) use ($request, $showPendingOnMeOnly) {
                    $query->where('cicos.status', 'unverified');
                    $query->whereHas('pairedClock', function ($iQuery) use ($request, $showPendingOnMeOnly) {
                        $iQuery->when($showPendingOnMeOnly == false, function ($q) use ($request) {
                            $q->whereHas('employee', function ($q) use ($request) {
                                $q->whereIn('branch_id', $request['branch_ids']);
                            });
                        });
                        $iQuery->orwhereIn('branch_id', $request['branch_ids']);
                    });
                });
                $q->when($showUnverifiedInWithoutOut == true, function ($q) use ($request) {
                    $q->orWhere(function ($query) use ($request) {
                        $query->where('cicos.status', 'unverified');
                        $query->doesntHave('pairedClock');
                        $query->whereHas('employee.branch', function ($q) use ($request) {
                            $q->whereIn('id', $request['branch_ids']);
                        });
                    });
                });
            });
    }

    public function getApprovalCycle($request, $employee)
    {
        $employeeBranchId = $employee?->branch_id ?? null;
        $employeeDepartmentId = $employee?->title?->department_id ?? null;
        $employeeSubDepartmentId = $employee?->title?->sub_department_id ?? null;
        $lang = app()->getLocale();
        if (! isset($request->employeeApproves)) {
            return [];

        }
        foreach ($request->employeeApproves as $approval) {
            $employees = [];
            foreach ($approval->users as $user) {
                if (! is_null($user?->employee) && $user?->employee?->status != EmployeeUtil::STATUSES['TERMINATED']) {
                    $employees[] = $user->employee;
                }
            }
            $roleScopes = $approval?->scopes?->pluck('key')->toArray() ?? [];
            $filteredEmployees = array_filter($employees, function ($employee) use ($roleScopes, $employeeBranchId, $employeeDepartmentId, $employeeSubDepartmentId) {

                return $this->checkEmployeeHasMatchingScope($employee, $roleScopes, $employeeBranchId, $employeeDepartmentId, $employeeSubDepartmentId);
            });

            unset($approval->users);
            $approval->employees = array_values($filteredEmployees);
            if ($approval->scopes->count() > 0) {
                $approval->scope = $approval->scopes->first()->{'name_'.$lang};
            } else {
                $approval->scope = null;
            }
        }

        return $request->employeeApproves;
    }

    public function appendEmployeeTextSearchQuery(&$query, $searchValue)
    {
        $query->where(function ($q) use ($searchValue) {
            $q->where(function ($searchQuery) use ($searchValue) {
                $searchQuery->
                where('name_ar', 'LIKE', '%'.$searchValue.'%')
                    ->orWhere('name_en', 'LIKE', '%'.$searchValue.'%')
                    ->orWhere('phone', $searchValue)
                    ->orWhere('employee_number', $searchValue);
            });
        });
    }

    public function appendPendingOnMeToApprovers(&$query, bool $isPendingOnMe)
    {
        $query->whereHas('employeeApproves', function ($q) use ($isPendingOnMe) {
            $q->whereIn('role_id', config('globals.user_role_ids'))
                ->when($isPendingOnMe,
                    function ($q) {
                        $q->where('workflow_approval_cycle.status', 'pending')
                            ->where(function ($q) {
                                $q->where('operator', 'or')
                                    ->orWhere(function ($q) {
                                        $q->where('operator', 'then')
                                            ->whereNotExists(function ($query) {
                                                $query->select(DB::raw(1))
                                                    ->from('workflow_approval_cycle as ac2')
                                                    ->whereColumn('ac2.requestable_id', 'workflow_approval_cycle.requestable_id')
                                                    ->whereColumn('ac2.requestable_type', 'workflow_approval_cycle.requestable_type')
                                                    ->whereColumn('ac2.order', '<', 'workflow_approval_cycle.order')
                                                    ->where('ac2.status', 'pending');
                                            });
                                    });
                            });
                    },
                );
        });
    }

    public function appendBranchAndDepartmentScopeToWorkflowQuery(&$query, $data)
    {
        $branchIds = $data['branch_ids'] ?? [];
        $departmentIds = $data['department_ids'] ?? [];
        $subDepartmentIds = $data['sub_department_ids'] ?? [];

        $branchIds = count($branchIds) ? array_intersect($branchIds, config('globals.scope_branch_ids'))
            : config('globals.scope_branch_ids');

        if (config('globals.scope_key') == ScopeUtil::DEPARTMENT_SCOPE) {
            $departmentIds = count($departmentIds) ? array_intersect($departmentIds, config('globals.scope_department_ids'))
                : config('globals.scope_department_ids');
        }

        if (config('globals.scope_key') == ScopeUtil::SUB_DEPARTMENT_SCOPE) {
            $subDepartmentIds = count($subDepartmentIds) ? array_intersect($subDepartmentIds, config('globals.scope_sub_department_ids'))
                : config('globals.scope_sub_department_ids');
        }

        $query->when(count($branchIds), function ($q) use ($branchIds) {
            $q->whereIn('branch_id', $branchIds);
        });

        $query->when(count($departmentIds) || config('globals.scope_key') == ScopeUtil::DEPARTMENT_SCOPE, function ($q) use ($departmentIds) {
            $q->whereIn('department_id', $departmentIds);
        });

        $query->when(count($subDepartmentIds) || config('globals.scope_key') == ScopeUtil::SUB_DEPARTMENT_SCOPE, function ($q) use ($subDepartmentIds) {
            $q->whereIn('sub_department_id', $subDepartmentIds);
        });
    }

    public function getUserHighestScopeKey($user): string
    {
        $highstScopeKey = '';
        $user->load('customRoles');
        $roleIds = $user->customRoles->pluck('id')->toArray() ?? [];
        $roles = Role::whereIn('id', $roleIds)->get();
        $scopes = [];
        foreach ($roles as $role) {
            $roleScopes = $role->scopes ?? [];
            foreach ($roleScopes as $roleScope) {
                $highstScopeKey = $this->compareScopes($highstScopeKey, $roleScope->key);
            }
        }

        return $highstScopeKey;
    }

    public function compareScopes(string $highstScopeKey, string $scope): string
    {
        $builtInScopes = ScopeUtil::Scopes;
        $highstScopeKeyIndex = (int) array_search($highstScopeKey, $builtInScopes);
        $scopeIndex = (int) array_search($scope, $builtInScopes);

        if ($scopeIndex >= $highstScopeKeyIndex) {
            return $scope;
        }

        return $highstScopeKey;

    }

    public function checkEmployeeHasMatchingScope($approvingEmployee, $roleScopes, $employeeBranchId, $employeeDepartmentId, $employeeSubDepartmentId): bool
    {
        $scopes = [];
        $roles = $approvingEmployee->user?->customRoles ?? [];
        foreach ($roles as $role) {
            $scopes = array_merge($scopes, $role->scopes->pluck('key')->toArray());
        }

        $isEmployeeHasMatchingScope = count(array_intersect($scopes, $roleScopes)) > 0;
        if ($isEmployeeHasMatchingScope) {
            $isEmployeeBranchWithinScope = false;
            $managedBranchesArray = $approvingEmployee?->branches?->toArray() ?? [];

            if (count($managedBranchesArray) && array_key_exists('branch_id', $managedBranchesArray[0])) {
                $managedBranches = array_column($managedBranchesArray, 'branch_id');
                if (in_array($employeeBranchId, $managedBranches)) {
                    $isEmployeeBranchWithinScope = true;
                }
            } elseif (count($managedBranchesArray) && ! array_key_exists('branch_id', $managedBranchesArray[0])) {
                $managedBranches = array_column($managedBranchesArray, 'id');
                if (in_array($employeeBranchId, $managedBranches)) {
                    $isEmployeeBranchWithinScope = true;
                }
            }

            if (in_array(ScopeUtil::BRANCH_SCOPE, $scopes) || in_array(ScopeUtil::COMPANY_SCOPE, $scopes)) {
                return $isEmployeeBranchWithinScope;
            } elseif ($isEmployeeBranchWithinScope && in_array(ScopeUtil::DEPARTMENT_SCOPE, $scopes)) {
                $managedDepartments = $approvingEmployee->managedDepartments->pluck('id')->toArray();
                if (in_array($employeeDepartmentId, $managedDepartments)) {
                    return true;
                }

            } elseif ($isEmployeeBranchWithinScope && in_array(ScopeUtil::SUB_DEPARTMENT_SCOPE, $scopes)) {
                $managedSubDepartments = $approvingEmployee->managedSubDepartments->pluck('id')->toArray();
                if (in_array($employeeSubDepartmentId, $managedSubDepartments)) {
                    return true;
                }
            }
        }

        return false;

    }

    public function CheckOverlappingBetweenTwoDates($from1, $to1, $from2, $to2 = null): bool
    {
        if ($from1 <= $from2 && $from2 <= $to1) {
            return true;
        }
        if (! is_null($to2)) {
            if ($from1 <= $to2 && $to2 <= $to1) {
                return true;
            } elseif ($from1 >= $from2 && $to1 <= $to2) {
                return true;
            }
        }

        return false;
    }

    public function checkIfPendingOnMe($approvalCycle): bool
    {
        $myEmployeeId = auth()->user()->employee_id;

        $pendingAnotherRole = false;
        foreach ($approvalCycle as $approval) {

            if ($approval->pivot->status == config('globals.REQUEST_STATUSES.PENDING') &&
                ! in_array($approval->id, config('globals.user_role_ids'))) {
                $pendingAnotherRole = true;
            }
            if (isset($approval->employees)) {
                foreach ($approval->employees as $employee) {
                    if ($employee['id'] == $myEmployeeId) {
                        if ($approval->pivot->operator == 'or' || ! $pendingAnotherRole) {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }

    public function appendWithEmployeeApproves(&$query)
    {
        $query->with('employeeApproves', function ($q) {
            $q
                ->with('scopes')
                ->with('users.employee', function ($q) {
                    $q
                        ->with('title')
                        ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                        ->with('branches', function ($q) {
                            $q->select('branch_employee.id', 'branch_id');
                        });
                });
        });
    }

    public function getTitleHighestScopeKey(int $titleId): ?string
    {
        $title = Title::with('role.scopes')->find($titleId);

        if (!$title || !$title->role) {
            return null;
        }

        $highestScopeKey = '';
        $scopes = $title->role->scopes;

        foreach ($scopes as $scope) {
            $highestScopeKey = $this->compareScopes($highestScopeKey, $scope->key);
        }

        return $highestScopeKey;
    }

    public function getRoleHighestScopeKey(int $roleId): ?string
    {
        $role = Role::with('scopes')->find($roleId);

        if (!$role) {
            return null;
        }

        $highestScopeKey = '';
        $scopes = $role->scopes;

        foreach ($scopes as $scope) {
            $highestScopeKey = $this->compareScopes($highestScopeKey, $scope->key);
        }

        return $highestScopeKey;
    }

    public function getEmployeeHighestScopeKey(int $employeeId): ?string
    {
        $employee = Employee::with('title.role.scopes')->find($employeeId);

        if (!$employee || !$employee->title || !$employee->title->role) {
            return null;
        }

        $highestScopeKey = '';
        $scopes = $employee->title->role->scopes;

        foreach ($scopes as $scope) {
            $highestScopeKey = $this->compareScopes($highestScopeKey, $scope->key);
        }

        return $highestScopeKey;
    }
}
