<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\AttendanceOvertime;
use App\Traits\CICOHelper;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ApproveOvertimes extends Command
{
    use CICOHelper;

    private $entityTagRepository;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'approve:overtimes {--company_id=company_id} {--start_date=start_date} {--end_date=end_date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $companyId = $this->option('company_id');
        $start_date = $this->option('start_date');
        $end_date = $this->option('end_date');

        DB::beginTransaction();
        try {
            $this->approveOvertimes($companyId, $start_date, $end_date);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::error($e);
            DB::rollBack();
            dd($e);
        }
    }

    private function approveOvertimes($companyId, $startDate, $endDate)
    {
        $overtimes = AttendanceOvertime::with('employeeRequest')->where('company_id', $companyId)
            ->where('status', 'pending')
            ->whereBetween('date', [$startDate, $endDate])
            ->get();

        $overtimes->each(function ($overtime) {
            $overtime->status = 'approved';
            $overtime->save();

            if (isset($overtime->employeeRequest)) {
                $overtime->employeeRequest->status = 'approved';
                $overtime->employeeRequest->save();
            }

        });

    }
}
