<?php

namespace App\Console\Commands;

use App\Models\Timecard;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DeleteFoodicsTimecards extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:delete-foodics-timecards';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $this->info('Deleting timecards with foodics tag');
            $timecards = Timecard::withWhereHas('attendance.entityTags', function ($q) {
                $q->where('tag', 'foodics');
            })->get();

            $this->info('Timecards count is: '.$timecards->count());

            foreach ($timecards as $timecard) {
                $attendance = $timecard->attendance;
                foreach ($attendance->entityTags as $entityTag) {
                    $entityTag->delete();
                }
                $attendance->delete();
                $timecard->delete();
            }

            $timecards = Timecard::withWhereHas('attendance.entityTags', function ($q) {
                $q->where('tag', 'foodics');
            })->get();

            $this->info('Timecards after delete action count is: '.$timecards->count());
            $this->info('Timecards deleted successfully');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error: '.$e->getMessage());
        }

    }
}
