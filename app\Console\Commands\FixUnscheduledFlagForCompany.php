<?php

namespace App\Console\Commands;

use App\Models\Cico;
use App\Util\AttendanceUtil;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixUnscheduledFlagForCompany extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-unscheduled-flag-for-company';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix unscheduled flag for CICO records of employees in a specific company.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companyId = 40; // Replace with the desired company ID
        $startDate = '2024-12-21';
        $endDate = '2025-01-20';

        DB::beginTransaction();

        try {
            // Fetch affected CICO records using JOIN
            $cicosToUpdate = Cico::query()
                ->join('employees', 'cicos.employee_id', '=', 'employees.id')
                ->where('employees.company_id', $companyId)
                ->where('cicos.is_unscheduled', false)
                ->where('cicos.status', AttendanceUtil::CICO_UNVERIFIED_STATUS)
                ->whereBetween('cicos.date', [$startDate, $endDate])
                ->select('cicos.id', 'cicos.date', 'cicos.employee_id')
                ->get();

            $this->info("Found {$cicosToUpdate->count()} CICO records to update.");

            if ($cicosToUpdate->isEmpty()) {
                $this->info('No records to update. Exiting.');
                return;
            }

            // Bulk update to minimize query overhead
            $updatedCount = DB::table('cicos')
                ->whereIn('id', $cicosToUpdate->pluck('id'))
                ->update(['is_unscheduled' => true]);

            // Commit the transaction
            DB::commit();

            // Log the changes
            Log::info("Updated {$updatedCount} CICO records for company {$companyId}.", [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]);

            $this->info("Successfully updated {$updatedCount} CICO records.");
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();

            // Log the error
            Log::error("Error updating CICO records for company {$companyId}: {$e->getMessage()}", [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]);

            $this->error("Failed to update CICO records: {$e->getMessage()}");
        }
    }
}
