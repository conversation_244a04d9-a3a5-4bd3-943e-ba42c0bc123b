<?php

namespace App\Console\Commands;

use App\Models\PublicHolidayAbsence;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DeletePublicHolidaysAbsencesAfterTerminationDate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:delete-public-holidays-absences-after-termination-date';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Deletes public holiday absences after the employee\'s termination date.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('--- Starting process: Deleting public holidays absences after termination date ---');
        DB::beginTransaction();
        try {
            $this->info('Fetching absences linked to terminated employees...');
            $absences = PublicHolidayAbsence::whereHas('employee', function ($query) {
                $query->whereHas('employeeInfo', function ($q) {
                    $q->whereNotNull('termination_date')
                        ->where('termination_date', '<=', now());
                });
            })->get();

            $this->info('Total absences found: ' . $absences->count());

            foreach ($absences as $absence) {
                $terminationDate = optional($absence->employee->employeeInfo)->termination_date;

                if (!$terminationDate) {
                    $this->warn("Skipping absence ID {$absence->id} because termination date is missing.");
                    continue;
                }

                if ($absence->from > $terminationDate) {
                    $this->info("Deleting absence ID {$absence->id}: starts after termination date ({$terminationDate}).");
                    $absence->delete();
                } elseif ($absence->to > $terminationDate) {
                    $this->info("Updating absence ID {$absence->id}: adjusting end date from {$absence->to} to {$terminationDate}.");
                    $absence->to = $terminationDate;
                    $absence->save();
                } else {
                    $this->info("No action needed for absence ID {$absence->id}.");
                }
            }

            DB::commit();
            $this->info('--- Process finished successfully! ---');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error occurred: ' . $e->getMessage());
            $this->error('Rolling back transaction.');
        }
    }
}
