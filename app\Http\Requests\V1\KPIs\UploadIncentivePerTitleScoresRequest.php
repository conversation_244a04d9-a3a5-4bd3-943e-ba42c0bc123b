<?php

namespace App\Http\Requests\V1\KPIs;

use Illuminate\Foundation\Http\FormRequest;

class UploadIncentivePerTitleScoresRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'Incentive_per_title_scores_template' => 'required|mimes:xlsx',
            'month' => 'required|string',
            'year' => 'required|string',
        ];
    }
}
