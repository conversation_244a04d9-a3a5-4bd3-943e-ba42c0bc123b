<?php

namespace App\Helpers;

use App\Rules\PhoneNumberByCountryRule;

class PhoneParserHelper
{
    public static function parsePhone(?string $fullPhone): array
    {
        if (empty($fullPhone)) {
            return [
                'code' => '+20',
                'phone' => ''
            ];
        }

        $cleanPhone = trim($fullPhone);
        
        if (!str_starts_with($cleanPhone, '+')) {
            return [
                'code' => '+20',
                'phone' => $cleanPhone
            ];
        }

        $attempts = [
            1 => substr($cleanPhone, 0, 2),
            2 => substr($cleanPhone, 0, 3),
            3 => substr($cleanPhone, 0, 4),
        ];

        foreach ($attempts as $digits => $countryCode) {
            if (strlen($cleanPhone) <= $digits + 1) {
                continue;
            }

            $phoneNumber = substr($cleanPhone, $digits + 1);
            
            if (empty($phoneNumber)) {
                continue;
            }

            $validator = new PhoneNumberByCountryRule($countryCode);
            if ($validator->passes('phone', $phoneNumber)) {
                return [
                    'code' => $countryCode,
                    'phone' => $phoneNumber
                ];
            }
        }

        $phoneWithoutPlus = ltrim($cleanPhone, '+');
        
        return [
            'code' => '+20',
            'phone' => ''
        ];
    }
}
