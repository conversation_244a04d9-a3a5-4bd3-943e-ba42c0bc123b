name: Demo Deployment

on:
  push:
    branches: [ "master" ]

jobs:
  demo-deployment:
    runs-on: ubuntu-latest

    steps:
      - name: Demo deployment
        run: |
            install -m 600 -D /dev/null ~/.ssh/id_rsa
            echo "${{ secrets.DEMO_PRIVATE_KEY }}" > ~/.ssh/id_rsa
            ssh-keyscan -H ${{ secrets.DEMO_SSH_HOST }} > ~/.ssh/known_hosts

      - name: ssh
        run: ssh bluworks@${{ secrets.DEMO_SSH_HOST }} "cd ${{secrets.DEMO_DIR}} && ${{vars.PRODUCTION_DEPLOYMENT_COMMANDS}} && exit"
