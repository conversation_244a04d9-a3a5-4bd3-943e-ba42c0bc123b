<?php

namespace App\Http\Controllers\V1\CompanySetup;

use App\Exports\V1\TitleSalaryExport;
use App\Exports\V1\TitleSalarySample;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\TitlesSalaries\ExportTitlesSalariesRequest;
use App\Http\Requests\V1\TitlesSalaries\GetTitlesSalariesRequest;
use App\Http\Requests\V1\TitlesSalaries\SetBulkTitlesSalariesRequest;
use App\Http\Requests\V1\TitlesSalaries\SetTitleSalaryRequest;
use App\Http\Resources\V1\TitlesSalaries\TitlesSalariesCollection;
use App\Http\Resources\V1\TitlesSalaries\TitlesSalariesResource;
use App\Imports\V1\TitlesSalariesImport;
use App\Services\V1\CompanySetup\TitleSalaryService;
use App\Traits\GetLastDraftedPayRollOrCreate;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Util\HttpStatusCodeUtil;
use Maatwebsite\Excel\Facades\Excel;

class TitleSalaryController extends NewController
{
    use GetLastDraftedPayRollOrCreate, PayrollHelper;

    public function __construct(
        protected TitleSalaryService $titleSalaryService,
    ) {}

    public function listTitles(GetTitlesSalariesRequest $request)
    {
        $titles = $this->titleSalaryService->list($request->validated());

        $data = new TitlesSalariesCollection($titles);
        $paginatedData = $data->response()->getData();

        return getResponseStructure(
            ['data' => $data, 'pagination' => $paginatedData->meta],
            HttpStatusCodeUtil::OK,
            'Titles return successfully'
        );
    }

    public function exportTitles(ExportTitlesSalariesRequest $request)
    {
        $data = $this->titleSalaryService->exportTitlesData($request->validated());

        return Excel::download(new TitleSalaryExport($data->toArray()), 'TitlesSalaries.xlsx');
    }

    public function exportSample()
    {
        $data = $this->titleSalaryService->exportTitlesData([]);

        return Excel::download(new TitleSalarySample($data->toArray()), 'TitlesSalariesSample.xlsx');
    }

    public function getTitleSalary($id)
    {
        $titleSalary = $this->titleSalaryService->find($id);

        return getResponseStructure(
            ['data' => new TitlesSalariesResource($titleSalary)],
            HttpStatusCodeUtil::OK,
            'Salary set successfully'
        );
    }

    public function setSalaryToTitle($id, SetTitleSalaryRequest $request)
    {
        $this->titleSalaryService->setSalaryToTitle($id, $request->validated());

        return getResponseStructure(
            ['data' => []],
            HttpStatusCodeUtil::OK,
            'Salary set successfully'
        );
    }

    public function setBulkTitleSalary(SetBulkTitlesSalariesRequest $request)
    {
        Excel::import(new TitlesSalariesImport, $request->file('file'));

        return getResponseStructure(
            ['data' => []],
            HttpStatusCodeUtil::OK,
            'Salary set successfully'
        );
    }
}
