<?php

namespace App\Http\Requests\V1\RequestGroups;

use Illuminate\Foundation\Http\FormRequest;

class GetRequestGroupTitlesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'request_group_id' => 'integer',
            'has_request_group' => 'boolean',
            'without_request_group' => 'boolean',
        ];

    }
}
