<?php

namespace App\Http\Requests\V1\Employee;

use App\Enums\LoanPolicyFilterEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class GetScopeEmployeesForLoansRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'policy_type' => [
                'nullable',
                new Enum(LoanPolicyFilterEnum::class),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'policy_type.in' => trans('validation.in', ['attribute' => 'policy_type']),
        ];
    }
}
