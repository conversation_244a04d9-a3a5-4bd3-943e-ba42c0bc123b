<?php

namespace App\Http\Controllers\Payroll\PayrollHub;

use App\Http\Controllers\NewController;
use App\Http\Requests\V1\PayrollHub\CompanyPayrollEntriesRequest;
use App\Http\Requests\V1\PayrollHub\EmployeePayrollEntriesRequest;
use App\Http\Resources\V1\PayrollHub\GetCompanyMissionRequestsResource;
use App\Http\Resources\V1\PayrollHub\GetEmployeeAbsencesResource;
use App\Http\Resources\V1\PayrollHub\GetEmployeeMissionRequestsResource;
use App\Services\V1\PayrollHub\MissionsService;
use App\Util\HttpStatusCodeUtil;

class MissionsController extends NewController
{

    public function __construct(
        protected MissionsService            $missionsService,
    )
    {
    }

    public function getAllMissionsForCompany(CompanyPayrollEntriesRequest $request){
        $absences = $this->missionsService->getCompanyMissions($request->validated());
        $data = GetCompanyMissionRequestsResource::collection($absences);
        $paginatedData = $data->response()->getData();
        return getResponseStructure(
            ['data' => $data, 'pagination' => $paginatedData->meta],
            HttpStatusCodeUtil::OK,
            'Mission requests fetched successfully'
        );
    }

    public function getEmployeeMissions(EmployeePayrollEntriesRequest $request){
        $data = $this->missionsService->getEmployeeMissions($request->validated());
        return getResponseStructure(
            ['data' => new GetEmployeeMissionRequestsResource($data)],
            HttpStatusCodeUtil::OK,
            'Absences fetched successfully'
        );
    }
}