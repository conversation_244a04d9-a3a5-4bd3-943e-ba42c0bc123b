<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Attendance;
use App\Models\Timecard;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixTcAndAttendanceDates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:tc_attendance:dates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $timecards = Timecard::whereHas('attendance.clockIn', function ($query) {
                $query->where(DB::raw('DATE(cicos.date)'), '!=', DB::raw('DATE(timecards.from)'))->where('source', 'app');
            })
                ->with('attendance.clockIn')->get();
            $numTcEdited = 0;
            $numAttEdited = 0;

            foreach ($timecards as $timecard) {
                $attendance = $timecard?->attendance;
                $clockIn = $attendance?->clockIn;
                if (! $clockIn || $clockIn->date < '2023-01-01') {
                    continue;
                }
                $cicoDate = Carbon::parse($clockIn->date);
                $timecardFrom = Carbon::parse($timecard->from);

                // Update only the date part of 'from'
                echo 'old timecard id => '.$timecard->id.' from  '.$timecard->from.'   ';
                $timecard->from = $timecardFrom->setDate($cicoDate->year, $cicoDate->month, $cicoDate->day);
                echo 'new timecard '.$timecard->from.'  '.PHP_EOL;
                $timecard->save();
                $numTcEdited++;

            }

            $attendances = Attendance::whereHas('clockIn', function ($query) {
                $query->where(DB::raw('DATE(cicos.date)'), '!=', DB::raw('DATE(attendances.date)'))->where('source', 'app');
            })
                ->with('clockIn')->get();
            foreach ($attendances as $attendance) {
                $clockIn = $attendance?->clockIn;
                if (! $clockIn || $clockIn->date < '2023-01-01') {
                    continue;
                }
                $cicoDate = Carbon::parse($clockIn->date);
                if ($attendance->date != $cicoDate->toDateString()) {
                    echo 'old attendance id => '.$attendance->id.' date  '.$attendance->date.'   ';
                    $attendance->date = $cicoDate->toDateString();
                    echo 'new attendance '.$attendance->date.'  '.PHP_EOL;
                    $attendance->save();
                    $numAttEdited++;
                }
            }

            // echo (PHP_EOL . "Total wrong TC date: " . $numTcEdited . PHP_EOL);
            echo 'total timecards edited: '.$numTcEdited.PHP_EOL;
            echo 'total attendance edited: '.$numAttEdited.PHP_EOL;
            DB::commit();
        } catch (Exception $e) {
            // dd($e);
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
            //// dd($e);
        }
    }
}
