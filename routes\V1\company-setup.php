<?php

use App\Http\Controllers\NewCompanySetup\CompanySetupController as OldCompanySetupCompanySetupController;
use App\Http\Controllers\V1\Billing\BillingController;
use App\Http\Controllers\V1\CompanySetup\CompanySetupController;
use App\Http\Controllers\V1\Employee\EmployeeController;
use App\Http\Controllers\V1\Excuse\ExcuseController;
use App\Http\Controllers\V1\Loans\LoanController;
use App\Http\Controllers\V1\Loans\SalaryAdvanceController;
use Illuminate\Support\Facades\Route;

Route::prefix('admin')->group(function () {

    Route::group(['prefix' => 'company'], function () {

        Route::group(['prefix' => 'branches'], function () {

            Route::post('', [CompanySetupController::class, 'exportAllEmployeesBranches']);
        });

        Route::group(['prefix' => 'file-categories'], function () {
            Route::get('', [CompanySetupController::class, 'getAllCompanyFileCategories']);
            Route::post('', [CompanySetupController::class, 'addCompanyFileCategory']);
            Route::put('/{id}', [CompanySetupController::class, 'updateCompanyFileCategory']);
            Route::delete('/{id}', [CompanySetupController::class, 'deleteCompanyFileCategory']);
        });

        Route::group(['prefix' => 'employees'], function () {
            Route::group(['prefix' => 'file-categories'], function () {
                Route::get('', [CompanySetupController::class, 'getAllEmployeeFileCategories']);
                Route::post('', [CompanySetupController::class, 'addEmployeeFileCategory']);
                Route::put('/{id}', [CompanySetupController::class, 'updateEmployeeFileCategory']);
                Route::delete('/{id}', [CompanySetupController::class, 'deleteEmployeeFileCategory']);
            });
            Route::post('change-title/{id}', [EmployeeController::class, 'changeTitle'])->middleware(['permission:change_title']);

        });

        Route::group(['prefix' => 'request-groups'], function () {
            Route::get('titles', [CompanySetupController::class, 'getRequestGroupTitles']);

            Route::get('', [CompanySetupController::class, 'getAllRequestGroups']);
            Route::post('', [CompanySetupController::class, 'addRequestGroup']);
            Route::put('/{id}', [CompanySetupController::class, 'updateRequestGroup']);
            Route::delete('{id}', [CompanySetupController::class, 'deleteRequestGroup']);

            Route::post('/{id}/add-workflows', [CompanySetupController::class, 'addWorkFlowsToRequestGroup']);
        });

        Route::group(['prefix' => 'scopes'], function () {
            Route::get('', [CompanySetupController::class, 'getCompanyScopes']);
        });

        Route::group(['prefix' => 'departments'], function () {
            Route::group(['prefix' => '{department}'], function () {
                Route::group(['prefix' => 'sub-departments'], function () {
                    Route::post('', [CompanySetupController::class, 'addSubDepartment']);
                });
            });

            Route::group(['prefix' => 'sub-departments'], function () {
                Route::get('', [CompanySetupController::class, 'getCompanySubDepartments']);
                Route::put('/{id}', [CompanySetupController::class, 'updateSubDepartment']);
                Route::delete('/{id}', [CompanySetupController::class, 'deleteSubDepartment']);
            });
        });

        Route::group(['prefix' => 'holidays'], function () {
            Route::post('toggle-public-holidays', [CompanySetupController::class, 'togglePublicHolidaysPolicy']);
            Route::get('get-public-holidays-status', [CompanySetupController::class, 'getPublicHolidaysStatus']);
            Route::get('has-custom-policy', [CompanySetupController::class, 'hasCustomPublicHolidaysPolicy']);
            Route::group(['prefix' => 'policies'], function () {
                Route::get('', [CompanySetupController::class, 'getPublicHolidaysPolicies']);
                Route::post('', [CompanySetupController::class, 'addPublicHolidaysPolicy']);
                Route::put('/{id}', [CompanySetupController::class, 'updatePublicHolidaysPolicy']);
                Route::delete('/{id}', [CompanySetupController::class, 'deletePublicHolidaysPolicy']);
            });
        });

        Route::group(['prefix' => 'absence-deductions'], function () {
            Route::get('', [CompanySetupController::class, 'getAbsenseDeductionPolicies']);
            Route::post('', [CompanySetupController::class, 'addAbsenseDeductionPolicy']);
            Route::put('/{id}', [CompanySetupController::class, 'editAbsenseDeductionPolicy']);
            Route::delete('/{id}', [CompanySetupController::class, 'deleteAbsenseDeductionPolicy']);
        });

        Route::group(['prefix' => 'late-deductions'], function () {
            Route::get('', [CompanySetupController::class, 'getLateDeductionGroups']);
            Route::get('deduction-method', [CompanySetupController::class, 'getDeductionMethod']);
            Route::put('deduction-method', [CompanySetupController::class, 'setDeductionMethod']);
            Route::post('', [CompanySetupController::class, 'addLateDeductionGroup']);
            Route::put('/{id}', [CompanySetupController::class, 'editLateDeductionGroup']);
            Route::delete('/{id}', [CompanySetupController::class, 'deleteLateDeductionGroup']);
        });

        Route::group(['prefix' => 'attendance-overtimes'], function () {
            Route::get('', [OldCompanySetupCompanySetupController::class, 'getAttendanceOvertimePolicies']);
            Route::post('', [OldCompanySetupCompanySetupController::class, 'setAttendanceOvertimePolicies']);
            Route::put('/{id}', [OldCompanySetupCompanySetupController::class, 'editAttendanceOvertimePolicies']);
            Route::delete('/{id}', [OldCompanySetupCompanySetupController::class, 'deleteAttendanceOvertimePolicy']);
        });

        Route::group(['prefix' => 'attendance-extra-workdays'], function () {
            Route::get('', [CompanySetupController::class, 'getExtraWorkdayPolicies']);
            Route::post('', [CompanySetupController::class, 'addExtraWorkdayPolicy']);
            Route::put('/{id}', [CompanySetupController::class, 'editExtraWorkdayPolicy']);
            Route::delete('/{id}', [CompanySetupController::class, 'deleteExtraWorkdayPolicy']);
        });

        Route::group(['prefix' => 'policies'], function () {
            Route::get('', [CompanySetupController::class, 'getCompanyAttendancePolicies']);
            Route::post('', [CompanySetupController::class, 'setAttendancePoliciesToggles']);
        });

        Route::group(['prefix' => 'work-type-policies'], function () {
            Route::get('', [CompanySetupController::class, 'getWorkTypePolicies']);
            Route::post('', [CompanySetupController::class, 'addWorkTypePolicy']);
            Route::put('/{id}', [CompanySetupController::class, 'updateWorkTypePolicy']);
        });

        Route::group(['prefix' => 'billing'], function () {
            Route::get('', [BillingController::class, 'getBillingSettings']);
            Route::post('setup', [BillingController::class, 'setFirstPriceToGenerateBill']);
            Route::post('', [BillingController::class, 'editBillingSettings']);
            Route::post('cancel-contract', [BillingController::class, 'cancelContract']);
            Route::get('months', [BillingController::class, 'employeesConversionsBasedMonths']);

            Route::group(['prefix' => 'bills'], function () {
                Route::get('', [BillingController::class, 'listBills']);
                Route::get('/{id}', [BillingController::class, 'getBillDetails']);
                Route::post('/{id}/pay', [BillingController::class, 'payBill']);
                Route::post('/{id}/comment', [BillingController::class, 'addComment']);
                Route::delete('/{id}/comment', [BillingController::class, 'deleteComment']);
                Route::get('/{id}/details', [BillingController::class, 'exportDetails']);

            });
        });
        Route::group(['prefix' => 'loans'], function () {
            Route::post('loans-enable-policy', [LoanController::class, 'enableLoansPolicy']);
            Route::post('enable-SalaryAdvance-policy', [SalaryAdvanceController::class, 'enableSalaryAdvancePolicy']);
            Route::post('add-loan-policy', [LoanController::class, 'addLoanPolicy']);
            Route::post('add-salary-advance-policy', [SalaryAdvanceController::class, 'addSalaryAdvancePolicy']);
            Route::put('edit-loan-policy/{id}', [LoanController::class, 'editLoanPolicy']);
            Route::put('edit-salary-advance-policy/{id}', [SalaryAdvanceController::class, 'editSalaryAdvancePolicy']);
            Route::get('get-loan-policies', [LoanController::class, 'getLoanPolicies']);
            Route::get('create-loan-policy-warning', [LoanController::class, 'titleHasLoanPolicyWarning']);
            Route::get('create-salary-advance-policy-warning', [SalaryAdvanceController::class, 'titleHasSalaryAdvancePolicyWarning']);

        });

        Route::group(['prefix' => 'excuse-policies'], function () {
            Route::post('', [ExcuseController::class, 'addExcusePolicy']);
            Route::put('/{id}', [ExcuseController::class, 'updateExcusePolicy']);
            Route::get('', [ExcuseController::class, 'getExcusePolicies']);
        });

        Route::group(['prefix' => 'leaves'], function () {
            Route::get('', [CompanySetupController::class, 'getCompanyLeavesSetup']);
            Route::put('/{id}', [CompanySetupController::class, 'editCompanyLeaveSetupV1']);
            Route::post('', [CompanySetupController::class, 'addCompanyLeaveSetup']);
            Route::delete('/{id}', [CompanySetupController::class, 'deleteCompanyLeaveTypeWithPolicy']);
        });

    })->middleware(['permission:manage_company']);
});
