<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\AttendanceDeduction;
use App\Models\StateMachines\RequestApproved;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateEmployeeRequestOfWaivedDeductions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:deduction:emplyoee-requests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $deductions = AttendanceDeduction::where('excuse_applied', true)->where('status', 'applied')->with('employeeRequest')->get();

            foreach ($deductions as $deduction) {
                $employeeRequest = $deduction->employeeRequest;
                if ($employeeRequest && $employeeRequest->status = 'pending') {
                    $employeeRequest->status->transitionTo(RequestApproved::class);
                    $employeeRequest->save();
                }
            }
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            DB::rollBack();
        }
    }
}
