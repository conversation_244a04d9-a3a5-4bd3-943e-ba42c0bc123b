<?php

namespace App\Services\PayrollSetup;

use App\Repositories\Repository;
use App\Repositories\TerminationRequestRepository;
use App\Repositories\V1\Holidays\PublicHolidaysRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use App\Repositories\V1\Loans\LoanRepository;
use App\Repositories\V1\Loans\SalaryAdvanceRepository;
use App\Services\BaseService;
use App\Traits\DataPreparation;
use App\Traits\EmployeeChangesHelper;
use App\Traits\LeaveManagementNet;
use App\Traits\V2\WorkTypesTrait;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class MonthlySummariesService extends BaseService
{
    use DataPreparation, EmployeeChangesHelper, LeaveManagementNet, WorkTypesTrait;

    private $attendanceOvertimeRepository;

    private $attendanceDeductionRepository;

    private $employeeLeaveRequestRepository;

    private $cicoRepository;

    private $employeeRepository;

    private $branchRepository;

    private $attendanceRepository;

    private $employeesMonthylSummary;

    private $publicHolidayRepository;

    private $loanRepository;

    private $salaryAdvanceRepository;

    private $terminationRepository;

    public function __construct()
    {
        $this->attendanceOvertimeRepository = Repository::getRepository('AttendanceOvertime');
        $this->branchRepository = Repository::getRepository('Branch');
        $this->employeeRepository = Repository::getRepository('Employee');
        $this->employeeLeaveRequestRepository = Repository::getRepository('EmployeeLeaveRequest');
        $this->cicoRepository = Repository::getRepository('Cico');
        $this->attendanceDeductionRepository = Repository::getRepository('AttendanceDeduction');
        $this->attendanceRepository = Repository::getRepository('Attendance');
        $this->publicHolidayRepository = new PublicHolidaysRepository;
        $this->loanRepository = new LoanRepository;
        $this->salaryAdvanceRepository = new SalaryAdvanceRepository;
        $this->employeesMonthylSummary = [];
        $this->terminationRepository = new TerminationRequestRepository;
    }

    public function getPendingRequests($data)
    {
        $branchIds = config('globals.scope_branch_ids');
        $pendingRequests = $this->branchRepository
            ->getAllPendingRequestsCounters($data['payroll_from_date'], $data['payroll_to_date'], $branchIds);

        return $pendingRequests;
    }

    public function getPendingRequestsCounter($data)
    {
        $branchIds = config('globals.scope_branch_ids');

        $pendingLeaveRequestsCounter = $this->employeeLeaveRequestRepository->pendingLeaveRequestsCountOnBranches($data['payroll_from_date'], $data['payroll_to_date']);

        $pendingCicosCounter = $this->cicoRepository->pendingUnverifiedCountOnBranches($data['payroll_from_date'], $data['payroll_to_date']);

        $pendingOvertimesCounter = $this->attendanceOvertimeRepository->pendingOvertimeRequestsCountOnBranches($data['payroll_from_date'], $data['payroll_to_date']);

        $pendingDeductionsCounter = $this->attendanceDeductionRepository->pendingWaiveDeductionRequestsCountOnBranches($data['payroll_from_date'], $data['payroll_to_date']);
        // $pendingTerminationCounter = $this->terminationRepository->pendingTerminationRequestsCountOnBranches($data['payroll_from_date'], $data['payroll_to_date']);

        // $pendingLoans = $this->loanRepository->pendingLoanRequestsCount($data['payroll_from_date'], $data['payroll_to_date']);

        // $pendingSaaryAdvances = $this->salaryAdvanceRepository->pendingSalaryAdvanceRequestsCount($data['payroll_from_date'], $data['payroll_to_date']);

        $allPendingRequestsCounter = $pendingCicosCounter + $pendingDeductionsCounter +
            $pendingLeaveRequestsCounter + $pendingOvertimesCounter;
        // $pendingTerminationCounter + $pendingLoans + $pendingSaaryAdvances;

        return $allPendingRequestsCounter;

    }

    public function getPendingLoansAndSalaryAdvancesCounter($data)
    {
        $pendingLoans = $this->loanRepository->pendingLoanRequestsCount($data['payroll_from_date'], $data['payroll_to_date']);
        $pendingSaaryAdvances = $this->salaryAdvanceRepository->pendingSalaryAdvanceRequestsCount($data['payroll_from_date'], $data['payroll_to_date']);

        return $pendingLoans + $pendingSaaryAdvances;
    }

    public function getPendingTerminationRequestsCounter($data)
    {
        $pendingTerminationCounter = $this->terminationRepository->pendingTerminationRequestsCountOnBranches($data['payroll_from_date'], $data['payroll_to_date']);

        return $pendingTerminationCounter;
    }

    public function getUnaccountedDaysCounter($data)
    {
        $branchesCounter = $this->unaccountedDayCounterInBranches($data);
        // get sum of unaccounted_days attribute in $branchesCount and store it in $allCounts
        $allCountsSum = array_reduce($branchesCounter->toArray(), function ($carry, $branch) {
            return $carry + $branch['unaccounted_days'];
        }, 0);

        return $allCountsSum;
    }

    public function calculateNetLeavesForEmployees($employeeIds, $restDays, $leaves, $fromDate, $toDate)
    {
        $netDaysMap = [];
        foreach ($employeeIds as $employeeId) {
            $netDaysMap[$employeeId] = 0;
        }

        $employeeRestDaysMap = [];
        foreach ($restDays as $restDay) {
            if (! isset($employeeRestDaysMap[$restDay->employee_id])) {
                $employeeRestDaysMap[$restDay->employee_id] = [];
            }
            $employeeRestDaysMap[$restDay->employee_id][] = $restDay;
        }

        foreach ($leaves as &$leave) {
            $leave->from = max($leave->from, $fromDate);
            $leave->to = min($leave->to, $toDate);
            $netDaysMap[$leave->employee_id] = Carbon::parse($leave->from)->diffInDays($leave->to) + 1;

            if (isset($employeeRestDaysMap[$leave->employee_id])) {
                foreach ($employeeRestDaysMap[$leave->employee_id] as $employeeRestDay) {
                    if ($employeeRestDay['from'] >= $leave->from) {
                        $netDaysMap[$leave->employee_id] -= 1;
                    }
                }
            }
        }

        return $netDaysMap;
    }

    public function allUnaccountedDays($data)
    {
        $payrollFromDate = $data['payroll_from_date'];
        $payrollToDate = min(Carbon::yesterday()->format('Y-m-d'), $data['payroll_to_date']);

        $employees = $this->employeeRepository->getTrackableEmployeesWithEmployeeChanges($data);

        $employeeIds = $employees->pluck('id')->toArray();

        $employeeCountersMap = [];
        $employeePendingLeaveRequests = [];
        $newEmployeeLeaveRequestsRepository = new EmployeeLeaveRequestRepository;
        foreach ($employees as $employee) {
            $employeeCountersMap[$employee->id] = ['all_attendance_count_P' => 0, 'absent_days_A' => 0,
                'rest_days_count_R' => 0, 'net_leave_days_count_L' => 0];
            $employeePendingLeaveRequests[$employee->id] = $newEmployeeLeaveRequestsRepository->getPendingLeaveRequestsForEmployeeInRange($employee->id, $payrollFromDate, $payrollToDate);
        }

        $employeesMonthlyRecords = $this->employeeRepository->employeesAttendanceAndLeavesInDateRange($payrollFromDate, $payrollToDate, $employeeIds);

        $employeeDaysMap = [];
        foreach ($employeesMonthlyRecords as $employeesMonthlyRecord) {
            $employeeId = $employeesMonthlyRecord->id;

            foreach ($employeesMonthlyRecord->timecards as $timecard) {
                $date = Carbon::parse($timecard->from)->toDateString();
                $key = $employeeId.'_'.$date;
                if (array_key_exists($key, $employeeDaysMap) == false && isset($employeeCountersMap[$employeeId])) {
                    $employeeCountersMap[$employeeId]['absent_days_A'] += 1;
                    $employeeDaysMap[$key] = 'timecard';
                }
            }

            $restDayLeaveId = config('globals.rest_day_leave_id');
            foreach ($employeesMonthlyRecord->employeeLeaveRequests as $leave) {
                // loop through each day on this leave and add it to the map
                $from = Carbon::parse($leave->from);
                $to = Carbon::parse($leave->to);
                for ($day = $from; $day->lte($to); $day->addDay()) {
                    $date = $day->toDateString();

                    $key = $employeeId.'_'.$date;
                    if (array_key_exists($key, $employeeDaysMap) == false && isset($employeeCountersMap[$employeeId])) {

                        if ($leave->company_leave_type_id == $restDayLeaveId) {
                            $employeeCountersMap[$employeeId]['rest_days_count_R'] += 1;
                        } else {
                            $employeeCountersMap[$employeeId]['net_leave_days_count_L'] += 1;
                        }

                        $employeeDaysMap[$key] = 'leave';
                    }
                }
            }
        }
        $allUnaccountedDays = [];
        $startDay = Carbon::parse($payrollFromDate)->startOfDay();
        $endDay = Carbon::parse($payrollToDate)->endOfDay();
        for ($day = $startDay; $day->lte($endDay); $day->addDay()) {
            $date = Carbon::parse($day)->toDateString();
            $holiday = $this->publicHolidayRepository->publicHolidayOnDate($date);

            foreach ($employees as $employee) {
                if (isset($employee->employeeInfo->termination_date) && $employee->employeeInfo->termination_date <= $date) {
                    continue;
                }
                if (in_array($date, $employeePendingLeaveRequests[$employee->id])) {
                    continue;
                }

                if (isset($employee->employeeInfo->join_date) && $employee->employeeInfo->join_date > $date) {
                    continue;
                }
                if (isset($holiday)) {
                    continue;
                }

                if (isset($employee->title->workTypePolicy) && in_array($employee->title->workTypePolicy->work_days_type, $this->getFixedTypes($employee->company_id))) {
                    continue;
                }

                if (
                    isset($data['search_value']) &&
                    !(
                        mb_stripos($employee->name_ar ?? '', $data['search_value']) !== false ||
                        mb_stripos($employee->name_en ?? '', $data['search_value']) !== false ||
                        mb_stripos($employee->employee_number ?? '', $data['search_value']) !== false
                    )
                ) {
                    continue;
                }


                $key = $employee->id . '_' . $date;
                if (array_key_exists($key, $employeeDaysMap) == false) {
                    $unaccountedDayEmployeeObject = clone $employee; // shallow copy
                    $employeeBranchOnDate = $this->employeeBranchOnDate($unaccountedDayEmployeeObject, $date);

                    if (isset($data['branch_id']) && $employeeBranchOnDate != $data['branch_id']) {
                        continue;
                    }
                    if (isset($data['branch_ids']) && !in_array($employeeBranchOnDate, $data['branch_ids'])) {
                        continue;
                    }
                    if (isset($data['department_ids']) && !in_array($unaccountedDayEmployeeObject->title->department_id, $data['department_ids'])) {
                        continue;
                    }

                    if (! isset($data['branch_id']) && ! in_array($employeeBranchOnDate, config('globals.scope_branch_ids'))) {
                        continue;
                    }

                    $unaccountedDayEmployeeObject->unaccounted_date = $date;
                    $unaccountedDayEmployeeObject->counters = $employeeCountersMap[$employee->id];
                    $unaccountedDayEmployeeObject->branch_id = $employeeBranchOnDate;
                    $allUnaccountedDays[] = $unaccountedDayEmployeeObject;
                }

            }
        }

        return $allUnaccountedDays;
    }

    public function unaccountedDayCounterInBranches($data)
    {
        $branchesUnaccountedDays = $this->allUnaccountedDays($data);
        $branchesCount = [];
        foreach ($branchesUnaccountedDays as $unaccountedDayEmployeeObject) {
            $branchId = $unaccountedDayEmployeeObject->branch_id;
            if (! isset($branchesCount[$branchId])) {
                $branchesCount[$branchId] = 0;
            }
            $branchesCount[$branchId] += 1;
        }

        // get the keys only from $branchesCount
        $branchesIds = array_keys($branchesCount);

        $lang = app()->getLocale();

        $branches = $this->branchRepository->getByKeys('id', $branchesIds)->get(['id', 'name_'.$lang]);
        foreach ($branches as &$branch) {
            $branch->unaccounted_days = $branchesCount[$branch->id];
        }

        return $branches;
    }
}
