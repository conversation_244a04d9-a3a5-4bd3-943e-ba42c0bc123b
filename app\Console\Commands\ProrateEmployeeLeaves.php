<?php

namespace App\Console\Commands;

use App\Imports\ProrateEmployeeLeavesImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class ProrateEmployeeLeaves extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:prorate-employee-leaves {file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This script to take number of leave days with employee phone number from excel sheet and
                              prorate the leave balance for each employee';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $this->info('Prorating employee leaves');
            $file = $this->argument('file');
            $filePath = storage_path($file);

            if (! file_exists($filePath)) {
                $this->error("File not found: $filePath");

                return 1;
            }

            Excel::import(new ProrateEmployeeLeavesImport, $filePath);
            $this->info('Employee leaves prorated successfully');

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error: '.$e);
        }
    }
}
