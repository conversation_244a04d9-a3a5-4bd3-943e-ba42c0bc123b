<?php

namespace App\Http\Requests\V1\Leaves;

use App\Rules\EmployeeIdRule;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class AddEmergencyLeaveByManagerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'from' => 'required|date',
            'to' => 'required|date|after_or_equal:from',
            'employee_id' => ['integer', new EmployeeIdRule],
        ];
    }

    public function prepareForValidation()
    {
        // convert 'from' and 'to' date time format with from at the beginning of the day and to at the end of the day
        $this->merge([
            'from' => date('Y-m-d 00:00:00', strtotime($this->from)),
            'to' => date('Y-m-d 23:59:59', strtotime($this->to)),
            'net_quantity' => (Carbon::parse($this->from)->diffInDays(Carbon::parse($this->to)) + 1) * 8,
            'check_warning' => true,
        ]);
    }
}
