<?php

namespace App\Repositories;

use App\Traits\QueriesHelper;
use App\Traits\V2\WorkTypesTrait;
use App\Util\EmployeeUtil;

class TitleRepository extends Repository
{
    use QueriesHelper, WorkTypesTrait;

    public function getTitleEmployessByBranch(array $request): ?object
    {
        $branchIds = $request['branch_ids'];
        $withRevokedEmployees = $request['with_revoked_employees'] === '1';

        $query = $this->getModel
            ->whereHas('employees.branches', function ($q) use ($branchIds) {
                $q->whereIn('branches.id', $branchIds);
            })
            ->orWhereHas('employees', function ($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })
            ->with(['employees.branches' => function ($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            }, 'employees' => function ($q) use ($request, $withRevokedEmployees) {
                $this->appendScopeQuery($q, $request);
                $q->when(! $withRevokedEmployees, function ($q) {
                    $q->whereNotIn('status', [EmployeeUtil::STATUSES['TERMINATED'], EmployeeUtil::STATUSES['TERMINATION_PENDING']]);
                });
            }]);

        return $query->get();
    }

    public function getTitlesByEmployeeBranches($branchIds)
    {
        return $this->getModel
            ->whereHas('employees.branches', function ($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })
        //this query (employees) should be removed after testing
            ->orWhereHas('employees', fn ($q) => $q->whereIn('branch_id', $branchIds)
            )
            ->get();
    }

    public function getTitlesByDepartments(array $data)
    {
        return $this->getModel
            ->whereHas('department', function ($q) use ($data) {
                $q->whereIn('id', $data['department_ids'])
                    ->when(isset($data['is_central']), function ($q) use ($data) {
                        $q->where('is_central', $data['is_central']);
                    });
            })
            ->get();
    }

    public function checkIfTitlesForNonTimeCardAble($titleIds, $branchIds)
    {
        return $this->getModel
            ->when(count($titleIds) > 0, function ($q) use ($titleIds) {
                $q->whereIn('id', $titleIds);
            })
            ->when(count($titleIds) == 0, function ($q) use ($branchIds) {
                $q->whereHas('employees.branch', function ($query) use ($branchIds) {
                    $query->whereIn('id', $branchIds);
                });
            })
            ->where('is_timecardable', 0)
            ->exists();
    }

    public function getCompanyTitlesWithEmployeesCount()
    {
        return $this->getModel
            ->withCount('employees')
            ->with('department', 'role.scopes', 'subDepartment', 'overtimeGroups', 'workTypePolicy')
            ->get();
    }

    public function updateTitlesWorkTypePolicy($titleIds, $workTypePolicyId = null)
    {
        return $this->getModel->whereIn('id', $titleIds)->update(['work_type_policy_id' => $workTypePolicyId]);
    }

    public function getTitleIdsByWorkTypePolicy($workTypePolicyId)
    {
        return $this->getModel->where('work_type_policy_id', $workTypePolicyId)
            ->select('id')->get()->pluck('id')->toArray();
    }

    public function getTitlesWithAssignableTypes($titleIds = [])
    {
        return $this->getModel->whereIn('id', $titleIds)
            ->withWhereHas('workTypePolicy', function ($q) {
                $q->whereIn('work_days_type', $this->getAssignableTypes());
            })->get();
    }

    public function getTitlesWorkTypeOnlyDynamicOnSite($titleIds = [])
    {
        return $this->getModel->whereIn('id', $titleIds)
            ->withWhereHas('workTypePolicy', function ($q) {
                $q->whereNotIn('work_days_type', $this->getFixedTypes());
            })->get();
    }
}
