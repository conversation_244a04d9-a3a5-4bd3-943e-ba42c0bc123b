<?php

namespace App\Http\Requests\V1\Holidays;

use Illuminate\Foundation\Http\FormRequest;

class EditPublicHolidaysPolicyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'policy_name' => ['required', 'string'],
            'compensation_method' => ['required', 'in:holidays_leave_balance,extra_work_day,custom'],
            'compensation_pay_rate' => ['required_if:compensation_method,extra_work_day', 'numeric'],
            'compensation_holidays_rate' => ['required_if:compensation_method,holidays_leave_balance', 'numeric'],
            'expiration_months' => ['integer'],
            'title_ids' => ['array'],
            'title_ids.*' => ['integer', 'exists:titles,id'],
        ];
    }
}
