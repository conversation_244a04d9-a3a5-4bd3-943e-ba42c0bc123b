<?php

namespace App\Http\Controllers\V1\Excuse;

use App\Http\Controllers\NewController;
use App\Http\Requests\CreateExcusePolicyRequest;
use App\Http\Requests\GetRemainingExcuseHoursRequest;
use App\Http\Resources\ExcusePolicyCollection;
use App\Services\V1\Excuse\ExcuseService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ExcuseController extends NewController
{
    public function __construct(ExcuseService $service)
    {
        parent::__construct($service);
    }

    public function addExcusePolicy(CreateExcusePolicyRequest $request)
    {
        $request->validated();
        try {
            DB::beginTransaction();
            $excusePolicy = $this->service->createExcusePolicy($request);
            DB::commit();

            return getResponseStructure(['data' => $excusePolicy],
                HttpStatusCodeUtil::OK, 'Excuse Policy Added Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);

            return getErrorResponseStructure(HttpStatusCodeUtil::BAD_REQUEST, 'Could not add excuse policy');
        }

    }

    public function updateExcusePolicy(CreateExcusePolicyRequest $request, int $id)
    {
        $request->validated();
        try {
            DB::beginTransaction();
            $updatedExcusePolicy = $this->service->updatePolicy($request, $id);
            DB::commit();

            return getResponseStructure(['data' => $updatedExcusePolicy],
                HttpStatusCodeUtil::OK, 'Excuse Policy Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);

            return getErrorResponseStructure(HttpStatusCodeUtil::BAD_REQUEST, $e->getMessage());
        }
    }

    public function getExcusePolicies()
    {
        $excusePolicies = $this->service->getExcusePolicies();

        return getResponseStructure(['data' => new ExcusePolicyCollection($excusePolicies)],
            HttpStatusCodeUtil::OK, 'Excuse Policy List');
    }

    public function getRemainingLateHours(GetRemainingExcuseHoursRequest $request)
    {
        $remainingLateHours = $this->service->getRemainingLateHours($request);

        return getResponseStructure(['data' => ['remaining_late_with_permission_balance' => $remainingLateHours]],
            HttpStatusCodeUtil::OK);
    }

    public function makeExcuse(int $id)
    {

        try {
            DB::beginTransaction();
            $this->service->convertDeductionToExcuse($id);
            DB::commit();

            return getResponseStructure(['data' => []],
                HttpStatusCodeUtil::OK, 'Excuse Made Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);

            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }

    }
}
