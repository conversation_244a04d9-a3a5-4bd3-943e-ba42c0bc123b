<?php

namespace App\Http\Resources;

use App\Http\Resources\V1\WorkFlows\ApprovalCycleCollection;
use App\Traits\QueriesHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SalaryAdvanceTrackerResource extends JsonResource
{
    use QueriesHelper;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $lang = (config('globals.lang') ?? 'ar');

        return [
            'id' => $this->id,
            'status' => $this->status,
            'amount' => $this->amount,
            'request_date' => \Carbon\Carbon::parse($this->created_at)->format('Y-m-d'),
            'final_payment_date' => $this->final_payment_date,
            'can_disburse' => $this->disbursable,
            'can_edit' => $this->editable,
            'can_cancel' => $this->can_cancel ?? false,
            'actionable' => $this->actionable,
            'comment' => $this->comment,
            'employee' => [
                'id' => $this->employee->id,
                'status' => $this->employee->status,
                'profile_image' => $this->employee->profilePicture->attachment_url ?? null,
                'name' => $this->employee->name,
                'employee_number' => $this->employee->employee_number,
                'first_name' => $this->employee->first_name,
                'second_name' => $this->employee->second_name,
                'first_name_ar' => $this->employee->first_name_ar,
                'second_name_ar' => $this->employee->second_name_ar,
                'name_ar' => $this->employee->name_ar,
                'title' => [
                    'id' => $this->employee->title->id ?? null,
                    //                    'name' => $this->employee->title->name ?? null,
                    //                    'name_ar' => $this->employee->title->name_ar ?? null,
                    'name' => $lang == 'ar' ? $this->employee->title->name_ar : $this->employee->title->name_en,

                    'color' => $this->employee->title->color ?? null,
                ],
                'branch' => [
                    'id' => $this->employee->branch->id ?? null,
                    'name' => $this->employee->branch->name ?? null,
                    'name_ar' => $this->employee->branch->name_ar ?? null,
                ],
                'employee_info' => [
                    'termination_date' => $this->employee->employeeInfo->termination_date ?? null,
                ],

            ],
            'approval_cycle' => new ApprovalCycleCollection($this->getApprovalCycle($this, $this->employee)),

        ];
    }
}
