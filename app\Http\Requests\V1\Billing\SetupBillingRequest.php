<?php

namespace App\Http\Requests\V1\Billing;

use Illuminate\Foundation\Http\FormRequest;

class SetupBillingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'price_per_employee' => [
                'required',
                'integer',
            ],
            'billing_frequency' => [
                'required',
                'integer',
                'in:3,6,12',
            ],
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'next_bill' => $this->input('page_size', 10),
        ]);
    }
}
