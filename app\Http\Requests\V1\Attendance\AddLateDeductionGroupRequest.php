<?php

namespace App\Http\Requests\V1\Attendance;

use App\Enums\Attendance\AttendanceDeductionEnum;
use App\Models\SystemSetting;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;

class AddLateDeductionGroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'policy_name' => 'required|string',
            'deduction_method' => 'string|in:' . AttendanceDeductionEnum::DEDUCT_MONTHLY->value . ',' . AttendanceDeductionEnum::DEDUCT_FROM_ANNUAL_LEAVE_BALANCE->value,
            'late_policies' => 'required|array',
            'late_policies.*' => 'required|array',
            'late_policies.*.from' => 'required|integer|min:0',
            'late_policies.*.to' => 'required|integer|gte:late_policies.*.from',
            'late_policies.*.sequence' => 'required|array',
            'late_policies.*.sequence.*' => 'required|numeric',
            'title_ids' => 'required|array',
            'title_ids.*' => [
                'required',
                'integer',
                (new Exists('titles', 'id'))->where(function ($query) {
                    $query->where('company_id', auth()->user()->company_id)
                        ->whereNull('deleted_at');
                }),
            ],
            'excluded_branch_ids' => 'array',
            'excluded_branch_ids.*' => [
                'required',
                'integer',
                (new Exists('branches', 'id'))->where(function ($query) {
                    $query->where('company_id', auth()->user()->company_id)
                        ->whereNull('deleted_at');
                }),
            ],
        ];

        return $rules;
    }

    public function prepareForValidation()
    {

        $deductionMethod = SystemSetting::where('company_id', auth()->user()->company_id)
            ->where('key', AttendanceDeductionEnum::POLICY_KEY->value)
            ->first();

        $this->merge([
            'deduction_method' => $deductionMethod->value ?? $this->input('deduction_method', AttendanceDeductionEnum::DEDUCT_MONTHLY->value),
        ]);
    }
}
