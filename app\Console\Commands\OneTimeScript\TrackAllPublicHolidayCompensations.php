<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Employee;
use App\Models\EmployeeLeaveRequest;
use App\Models\PublicHolidaysAttendance;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\V1\Attendance\AttendanceRepository;
use App\Services\V1\Holidays\PublicHolidaysAttendanceService;
use App\Util\BasicInfoUtil;
use App\Util\HolidaysUtil;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class TrackAllPublicHolidayCompensations extends Command
{
    public function __construct(
        private PayrollsRepository $payrollsRepository,
        private AttendanceRepository $attendanceRepository,
        private SystemSettingRepository $systemSettingRepository
    ) {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill:public_holiday:compensations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $this->putAllPayoutPublicHolidays();
            $this->updateAllBalancesOfLeavePolicy();
            DB::commit();
        } catch (Exception $e) {
            // dd($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }

    private function putAllPayoutPublicHolidays()
    {
        $employeesWithPayoutPolicy = Employee::withWhereHas('title.publicHolidaysPolicy', function ($q) {
            $q->select('id', 'compensation_method', 'compensation_pay_rate', 'compensation_holidays_rate', 'expiration_months');
        })->with('company')->get();

        foreach ($employeesWithPayoutPolicy as $employee) {

            config(['globals.company' => $employee->company]);

            $holidayAttendanceData = [
                'employee_id' => $employee->id,
                'company_id' => $employee->company_id,
            ];

            $publicHolidaysAttendanceIncidents = $this->attendanceRepository->publicHolidaysAttendanceForEmployee($employee->id);

            if (PublicHolidaysAttendance::find($employee->id)) {
                continue;
            }

            if ($employee->title->publicHolidaysPolicy->compensation_method == HolidaysUtil::EXTRA_WORK_DAY) {
                foreach ($publicHolidaysAttendanceIncidents as $attendance) {
                    $compensationPayRate = $employee->title->publicHolidaysPolicy?->compensation_pay_rate ?? 0;
                    $employeeHolidayBalance = PublicHolidaysAttendance::where('employee_id', $employee->id)->where('public_holiday_id', $attendance->public_holiday_id)->first();
                    if (! isset($attendance->payroll_id) || $attendance->payroll_status == 'draft') {

                        if ($employeeHolidayBalance) {
                            $employeeHolidayBalance->increment('worked_days', 1);
                            $employeeHolidayBalance->increment('holiday_compensation', (int) $compensationPayRate);
                            $employeeHolidayBalance->increment('remaining_days', (int) $compensationPayRate);
                            $employeeHolidayBalance->expiration_date = $attendance->payroll_end ?? '2025-01-01';
                            $employeeHolidayBalance->save();

                        } else {
                            $holidayAttendanceData['public_holiday_id'] = $attendance->public_holiday_id;
                            $holidayAttendanceData['worked_days'] = 1;
                            $holidayAttendanceData['holiday_compensation'] = (int) $compensationPayRate;
                            $holidayAttendanceData['remaining_days'] = (int) $compensationPayRate;
                            $holidayAttendanceData['days_used_as_leave'] = 0;
                            $holidayAttendanceData['days_used_as_pay'] = 0;
                            $holidayAttendanceData['cancelled_days'] = 0;
                            $holidayAttendanceData['expiration_date'] = $attendance->payroll_end ?? '2025-01-01';
                            PublicHolidaysAttendance::create($holidayAttendanceData);
                            echo 'Public Holiday Balance was created for payout employee : '.$employee->id.' on date : '.$attendance->attendance_date.' for draft payroll '.PHP_EOL;
                        }
                    } else {
                        if ($employeeHolidayBalance) {
                            $employeeHolidayBalance->increment('worked_days', 1);
                            $employeeHolidayBalance->increment('holiday_compensation', (int) $compensationPayRate);
                            $employeeHolidayBalance->increment('days_used_as_pay', (int) $compensationPayRate);
                            $employeeHolidayBalance->expiration_date = $attendance->payroll_end ?? '2025-01-01';
                            $employeeHolidayBalance->save();

                        } else {
                            $holidayAttendanceData['public_holiday_id'] = $attendance->public_holiday_id;
                            $holidayAttendanceData['worked_days'] = 1;
                            $holidayAttendanceData['holiday_compensation'] = (int) $compensationPayRate;
                            $holidayAttendanceData['days_used_as_pay'] = (int) $compensationPayRate;
                            $holidayAttendanceData['days_used_as_leave'] = 0;
                            $holidayAttendanceData['remaining_days'] = 0;
                            $holidayAttendanceData['cancelled_days'] = 0;
                            $holidayAttendanceData['expiration_date'] = $attendance->payroll_end ?? '2025-01-01';
                            PublicHolidaysAttendance::create($holidayAttendanceData);
                            echo 'Public Holiday Balance was created for payout employee : '.$employee->id.' on date : '.$attendance->attendance_date.' for finalized payroll '.PHP_EOL;
                        }
                    }

                }
            } elseif ($employee->title->publicHolidaysPolicy->compensation_method == HolidaysUtil::holidays_leave_balance) {
                foreach ($publicHolidaysAttendanceIncidents as $attendance) {
                    $expirationDate = $this->getExpirationDateBasedOnExpirationMonths($employee, $attendance->attendance_date, $employee->title->publicHolidaysPolicy->expiration_months);
                    $compensationHolidaysRate = $employee->title->publicHolidaysPolicy?->compensation_holidays_rate ?? 0;
                    $employeeHolidayBalance = PublicHolidaysAttendance::where('employee_id', $employee->id)->where('public_holiday_id', $attendance->public_holiday_id)->first();
                    if ($employeeHolidayBalance) {
                        $employeeHolidayBalance->increment('worked_days', 1);
                        $employeeHolidayBalance->increment('holiday_compensation', (int) $compensationHolidaysRate);
                        $employeeHolidayBalance->increment('remaining_days', (int) $compensationHolidaysRate);
                        $employeeHolidayBalance->expiration_date = $attendance->payroll_end ?? '2025-01-01';
                        $employeeHolidayBalance->save();

                    } else {
                        $holidayAttendanceData['public_holiday_id'] = $attendance->public_holiday_id;
                        $holidayAttendanceData['worked_days'] = 1;
                        $holidayAttendanceData['holiday_compensation'] = (int) $compensationHolidaysRate;
                        $holidayAttendanceData['days_used_as_pay'] = 0;
                        $holidayAttendanceData['days_used_as_leave'] = 0;
                        $holidayAttendanceData['remaining_days'] = (int) $compensationHolidaysRate;
                        $holidayAttendanceData['cancelled_days'] = 0;
                        $holidayAttendanceData['expiration_date'] = $expirationDate;
                        PublicHolidaysAttendance::create($holidayAttendanceData);
                        echo 'Public Holiday Balance was created for holiday leave balance employee : '.$employee->id.' on date : '.$attendance->attendance_date.' for finalized payroll '.PHP_EOL;
                    }
                }

            }
        }

    }

    private function getExpirationDateBasedOnExpirationMonths($employee, $date, $expirationMonths)
    {
        $expirationDay = ($this->systemSettingRepository
            ->getFirstByAttributes(['key' => BasicInfoUtil::MONTHLY_CLOSING_DAY, 'company_id' => $employee->company_id])->value) ?? 1;  //TODO to fix monthly closing day if needed to run this script on a company with dynamic closing day
        $expirationDate = Carbon::parse($date)->addMonths($expirationMonths - 1)->day($expirationDay)->toDateString();
        $maxExpirationDate = Carbon::now()->endOfYear()->addMonth()->day($expirationDay)->toDateString();

        return min($expirationDate, $maxExpirationDate);
    }

    private function updateAllBalancesOfLeavePolicy()
    {
        $leaveRequests = EmployeeLeaveRequest::where('status', 'approved')->whereHas('companyLeaveType', function ($q) {
            $q->where('name', 'Public Holiday Compensation');
        })->get();

        $publicHolidaysAttendanceService = new PublicHolidaysAttendanceService;

        foreach ($leaveRequests as $leave) {
            $leaveFullyPaied = $publicHolidaysAttendanceService->updateHolidayBalanceAfterLeaveApproved($leave);
            if ($leaveFullyPaied) {
                echo ' leave was updated for employee : '.$leave->employee_id.' leave id: '.$leave->id.PHP_EOL;
            }
        }
    }
}
