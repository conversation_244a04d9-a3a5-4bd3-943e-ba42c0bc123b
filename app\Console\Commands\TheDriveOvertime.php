<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use App\Models\AttendanceOvertime;
use App\Traits\WorkflowTrait;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class TheDriveOvertime extends Command
{
    use WorkflowTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:the-drive-overtime-script';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {

            DB::beginTransaction();
            $this->info('The Drive Overtime Script Started');
            $startDate = '2024-04-10';
            $endDate = '2024-04-13';
            $overtimePolicyInMinutes = 9 * 60; //hard coded for now
            $attendances = Attendance::with(
                'clockIn',
                'clockOut',
            )
                ->where('company_id', 39)
                ->where('branch_id', 286)
                ->doesntHave('attendanceOvertimes')
                ->whereNotNull('ci_id')
                ->whereNotNull('co_id')
                ->whereBetween('date', [$startDate, $endDate])
                ->get();

            $attendancesWithOvertime = [];
            foreach ($attendances as $attendance) {
                $clockInDate = $attendance->clockIn->date;
                $clockOutDate = $attendance->clockOut->date;
                $attendanceDate = $attendance->date;
                $attendanceMinutes = Carbon::parse($clockInDate)->diffInMinutes($clockOutDate);
                $attendanceOvertimeInMinutes = $attendanceMinutes - $overtimePolicyInMinutes;
                if ($attendanceOvertimeInMinutes < 0) {
                    continue;
                }
                $dailyOvertimePolicyId = 36; //hard coded for now
                $overtimePercentage = 2;    //hard coded for now

                $attendance->overtimePercentage = $overtimePercentage;
                $attendance->attendanceOvertimeInMinutes = $attendanceOvertimeInMinutes;
                $attendance->dailyOvertimePolicyId = $dailyOvertimePolicyId;
                $attendancesWithOvertime[] = $attendance;

            }
            foreach ($attendancesWithOvertime as $attendance) {
                $employee = $attendance->employee;
                $overtime = AttendanceOvertime::create([
                    'overtime_value' => $attendance->overtimePercentage,
                    'daily_overtime_policy_id' => $attendance->dailyOvertimePolicyId,
                    'overtime_minutes' => $attendance->attendanceOvertimeInMinutes,
                    'status' => 'pending',
                    'date' => $attendance->date,
                    'start_time' => $attendance->clockIn->date,
                    'end_time' => $attendance->clockOut->date,
                    'employee_id' => $attendance->employee_id,
                    'branch_id' => $attendance->branch_id,
                    'company_id' => $attendance->company_id,
                    'attendance_id' => $attendance->id,
                ]);

                $this->info('The Drive Overtime Script Created Overtime for'.$employee->name_en.' on '.$attendance->date.' with '.$attendance->attendanceOvertimeInMinutes.' minutes of overtime.');

                $this->initializeRequestWorkflow($overtime->withoutRelations(), $employee, config('globals.REQUEST_CYCLE_TYPES.ATTENDANCE_OVERTIME'), config('globals.REQUEST_WORKFLOW_TYPES.ATTENDANCE_OVERTIME'));
            }

            DB::commit();
            $this->info('The Drive Overtime Script Ended');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('The Drive Overtime Script Failed');
            $this->error($e->getMessage());
        }

    }
}
