<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Models\PublicHolidayAbsence;
use App\Repositories\V1\Holidays\PublicHolidayAbsenceRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use App\Services\V1\Holidays\PublicHolidayAbsenceService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SplitPublicHolidayAbsences extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:split-holiday-absences {company_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Split public holiday absences based on employee rest days ';

    protected $publicHolidayAbsenceRepository;

    protected $publicHolidayAbsenceService;

    protected $employeeLeaveRequestRepository;

    public function __construct()
    {
        parent::__construct();
        $this->publicHolidayAbsenceRepository = new PublicHolidayAbsenceRepository;
        $this->publicHolidayAbsenceService = new PublicHolidayAbsenceService;
        $this->employeeLeaveRequestRepository = new EmployeeLeaveRequestRepository;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting public holiday absences split process...');

        try {
            DB::beginTransaction();

            $companyId = $this->argument('company_id');
            // Get all public holiday absences
            if ($companyId) {
                $holidayAbsences = PublicHolidayAbsence::where('company_id', $companyId)
                    ->whereDate('from', '>=', '2025-01-01')->get();
            } else {
                $holidayAbsences = PublicHolidayAbsence::get();
            }

            $this->info("Found {$holidayAbsences->count()} public holiday absences to process");

            $processedCount = 0;
            $errorCount = 0;

            foreach ($holidayAbsences as $absence) {
                try {
                    config(['globals.user' => null]);

                    $employee = Employee::with('company', 'user')->find($absence->employee_id);
                    if (! $employee) {
                        $this->warn("⚠️ Employee not found for absence ID: {$absence->id}");

                        continue;
                    }
                    $restDayLeaveId = $employee->company->rest_day_leave_id;
                    config(['globals.rest_day_leave_id' => $restDayLeaveId]);
                    config(['globals.user' => $employee->user]);
                    // Get all dates between from and to
                    $startDate = Carbon::parse($absence->from);
                    $endDate = Carbon::parse($absence->to);
                    $currentDate = $startDate->copy();

                    while ($currentDate <= $endDate) {
                        // Check if the current date is a rest day for the employee
                        if ($this->isRestDay($employee, $currentDate)) {
                            // Split the absence record at this date
                            $this->publicHolidayAbsenceService->splitHolidayAbsenceBasedOnAttendance(
                                $employee,
                                $absence->public_holiday_id,
                                $currentDate->toDateString()
                            );

                            $this->info("✅ Split holiday absence for employee {$employee->id} on date {$currentDate->toDateString()}");
                        }

                        $currentDate->addDay();
                    }

                    $processedCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    Log::error("Error processing absence ID {$absence->id}: ".$e->getMessage());
                    $this->error("❌ Error processing absence ID {$absence->id}: ".$e->getMessage());
                }
            }

            DB::commit();

            $this->info('📊 Summary:');
            $this->info("✅ Successfully processed: {$processedCount} absences");
            $this->info("❌ Failed to process: {$errorCount} absences");

            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in SplitPublicHolidayAbsences: '.$e->getMessage());
            $this->error('❌ Error: '.$e->getMessage());

            return Command::FAILURE;
        }
    }

    /**
     * Check if a given date is a rest day for the employee
     */
    private function isRestDay(Employee $employee, Carbon $date)
    {
        // Get the rest day leave ID from config
        $restDayLeaveId = config('globals.rest_day_leave_id');
        // Check if there's a rest day leave request for this employee on this date
        $restDayLeave = $this->employeeLeaveRequestRepository->getEmployeeRestDayByDate(
            $employee->id,
            $restDayLeaveId,
            $date->toDateString()
        );

        return ! is_null($restDayLeave);
    }
}
