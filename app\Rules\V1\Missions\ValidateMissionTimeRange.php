<?php

namespace App\Rules\V1\Missions;

use App\Enums\Missions\MissionsEnum;
use Carbon\Carbon;
use Illuminate\Contracts\Validation\Rule;

class ValidateMissionTimeRange implements Rule
{
    protected $request; // Stores the request data for contextual validation

    /**
     * Create a new rule instance.
     *
     * @param  array  $request
     */
    public function __construct($request)
    {
        $this->request = $request;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        // Dynamically find the "from" attribute (e.g., from_time)
        $from = $this->request['from'] ?? null;

        // Ensure we have a valid "from" time
        if (! $from) {
            return false;
        }

        $fromTime = Carbon::parse($from);
        $toTime = Carbon::parse($value);

        // Get the mission type from the request (e.g., FULL_DAY or PARTIAL)
        $missionType = $this->request['mission_type'] ?? null;

        if (! $missionType) {
            return false;
        }

        // Determine the maximum duration
        $maxDuration = $missionType === MissionsEnum::FULL_DAY->value ? 24 : 14;

        return $toTime->diffInHours($fromTime) <= $maxDuration;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        $missionType = $this->request['mission_type'] ?? null;
        $maxDuration = $missionType === MissionsEnum::FULL_DAY->value ? 24 : 14;

        return 'The :attribute must be within '.$maxDuration.' hours after the from time.';
    }
}
