<?php

namespace App\Http\Requests\V1\Employee;

use App\Enums\Employee\EmployeeBulkOperationTypeEnum;
use App\Enums\Employee\EmployeeExportCategoryEnum;
use App\Enums\Employee\EmployeeExportFieldEnum;
use App\Rules\ScopeSubDepartmentIdsRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rule;

class EmployeeBulkOperationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            
        ];
        $rules = [
            'page' => ['integer', 'min:1'],
            'page_size' => ['integer', 'min:0'],
            'branch_ids' => ['array'],
            'branch_ids.*' => ['integer'],
            'department_ids' => ['array'],
            'department_ids.*' => ['integer'],
            'title_ids' => ['array'],
            'title_ids.*' => ['integer'],
            'statuses' => ['array'],
            'statuses.*' => ['string', 'in:new_hire,on_probation,active,termination_pending,terminated'],
            'search_value' => ['min:1', 'max:30'],
            'missing_employee_info' => 'boolean',
            'sub_department_ids' => 'array',
            'sub_department_ids.*' => 'integer', new ScopeSubDepartmentIdsRule,
            'role_ids' => 'array',
            'role_ids.*' => ['integer', Rule::exists('spatie_roles', 'id')->where('company_id', auth()->user()->company_id)],
            'termination_from_date' => 'date_format:Y-m-d',
            'termination_to_date' => 'date_format:Y-m-d|after_or_equal:termination_from_date|required_with:termination_from_date',
            'is_trackable' => 'boolean',
            'is_central' => 'boolean',
            'is_unregistered_face_id' => ['boolean'],
            'type' => ['required', new Enum(EmployeeBulkOperationTypeEnum::class)],
        ];

        $categoryValues = EmployeeExportCategoryEnum::values();

        foreach ($categoryValues as $category) {
            // Skip essential category as it's always included
            if ($category === EmployeeExportCategoryEnum::ESSENTIAL->value) {
                continue;
            }

            $categoryFields = null;
            if($this->input('type') == EmployeeBulkOperationTypeEnum::ADD->value) {
                $categoryFields = EmployeeExportFieldEnum::getFieldsForCategoryForBulkAdd(EmployeeExportCategoryEnum::from($category));
            } else if($this->input('type') == EmployeeBulkOperationTypeEnum::EDIT->value) {
                $categoryFields = EmployeeExportFieldEnum::getFieldsForCategoryForBulkEdit(EmployeeExportCategoryEnum::from($category));
            }
            
            if($categoryFields && is_array($categoryFields) && !empty($categoryFields)) {
                $rules[$category] = 'required|array';

                foreach ($categoryFields as $field) {
                    $rules["{$category}.{$field}"] = 'required|boolean';
                }
                if ($this->has($category)) {
                    $categoryData = $this->input($category, []);

                    $fieldGroup = [];

                    if ($category === EmployeeExportCategoryEnum::CONTACT->value) {
                        $emergencyContactFields = [
                            EmployeeExportFieldEnum::EMERGENCY_CONTACT_NAME->value,
                            EmployeeExportFieldEnum::EMERGENCY_CONTACT_PHONE->value,
                            EmployeeExportFieldEnum::EMERGENCY_CONTACT_RELATION->value,
                        ];
                        $fieldGroup = array_intersect($categoryFields, $emergencyContactFields);
                    } else if ($category === EmployeeExportCategoryEnum::EMPLOYMENT->value) {
                        $contractFields = [
                            EmployeeExportFieldEnum::LATEST_CONTRACT_START_DATE->value,
                            EmployeeExportFieldEnum::LATEST_CONTRACT_DURATION->value,
                            EmployeeExportFieldEnum::LATEST_CONTRACT_END_DATE->value,
                        ];
                        $fieldGroup = array_intersect($categoryFields, $contractFields);
                    } else if ($category === EmployeeExportCategoryEnum::EDUCATION->value) {
                        $fieldGroup = $categoryFields;
                    }

                    if (!empty($fieldGroup)) {
                        $hasAnyTrueField = false;

                        foreach ($fieldGroup as $field) {
                            if (isset($categoryData[$field]) &&
                                ($categoryData[$field] === true || $categoryData[$field] === '1' || $categoryData[$field] === 1)) {
                                $hasAnyTrueField = true;
                                break;
                            }
                        }

                        if ($hasAnyTrueField) {
                            foreach ($fieldGroup as $field) {
                                $rules["{$category}.{$field}"] = 'required|boolean|accepted';
                            }
                        }
                    }
                }
            }
        }

        return $rules;
    }

    public function messages(): array
    {
        $messages = [
            'type.required' => trans('validation.required', ['attribute' => 'operation type']),
            'type.enum' => trans('validation.in', ['attribute' => 'operation type']),
        ];

        foreach (EmployeeExportCategoryEnum::values() as $category) {
            if ($category === EmployeeExportCategoryEnum::ESSENTIAL->value) {
                continue;
            }

            $messages["{$category}.required"] = trans('validation.category_fields_required', ['category' => $category]);
            $messages["{$category}.array"] = trans('validation.category_fields_array', ['category' => $category]);

            $categoryFields = null;
            if($this->input('type') == EmployeeBulkOperationTypeEnum::ADD->value) {
                $categoryFields = EmployeeExportFieldEnum::getFieldsForCategoryForBulkAdd(EmployeeExportCategoryEnum::from($category));
            } else if($this->input('type') == EmployeeBulkOperationTypeEnum::EDIT->value && $category) {
                $categoryFields = EmployeeExportFieldEnum::getFieldsForCategoryForBulkEdit(EmployeeExportCategoryEnum::from($category));
            }
            if($categoryFields && is_array($categoryFields) && !empty($categoryFields)) {
                foreach ($categoryFields as $field) {
                    $messages["{$category}.{$field}.required"] = trans('validation.field_required', [
                        'field' => $field,
                        'category' => $category
                    ]);
                    $messages["{$category}.{$field}.boolean"] = trans('validation.field_boolean', [
                        'field' => $field,
                        'category' => $category
                    ]);
                }
            }
        }

        return $messages;
    }

}
