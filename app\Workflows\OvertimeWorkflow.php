<?php

namespace App\Workflows;

use App\Traits\V1\NotificationRedirection;
use Illuminate\Support\Facades\Log;
use Workflow\ActivityStub;
use Workflow\WorkflowStub;

class OvertimeWorkflow extends RequestWorkflow
{
    use NotificationRedirection;

    public $queue = 'overtime_workflow';

    public function execute($data)
    {
        try {

            Log::info('Overtime workflow is started');
            yield ActivityStub::make(AssignRequestActivity::class, $data);

            $request = $this->getRequest($data);

            $this->checkApprovalCycleStatus($request);
            yield WorkflowStub::await(fn () => ($this->is_completed));

            $this->getFinalStatus($data);

            $this->updateRequestStatus($data['request']);

            $this->updateEmployeeRequestStatus($data['employee_request']);

        } catch (\Exception $e) {
            Log::info($e);
            // \Sentry\captureException($e);

        }

    }

    public function getFinalStatus($data)
    {
        $data['request']?->refresh();
        Log::info('request in final status: '.json_encode($data['request']));
        if (isset($data['request']) && $data['request']->status != 'pending') {
            $this->finalStatus = $data['request']->status;

            Log::info('Final status force changed to : '.$this->finalStatus);

            return;
        }
        Log::info('notForceApproved');
        if (in_array(config('globals.REQUEST_STATUSES.CANCELLED'), $this->requestWorkflowApprovals['then'])
        || in_array(config('globals.REQUEST_STATUSES.CANCELLED'), $this->requestWorkflowApprovals['or'])) {
            $this->finalStatus = config('globals.REQUEST_STATUSES.CANCELLED');
        } elseif (count($this->requestWorkflowApprovals['or']) > 0 && ! in_array('pending', $this->requestWorkflowApprovals['or'])) {
            $this->finalStatus = $this->requestWorkflowApprovals['or'][0];
        } elseif (count($this->requestWorkflowApprovals['then']) > 0
        && in_array('rejected', $this->requestWorkflowApprovals['then'])) {
            $this->finalStatus = 'rejected';
        } else {
            $this->finalStatus = 'approved';
        }

        Log::info('Final status is: '.$this->finalStatus);
    }

    public function updateRequestStatus($requestObj)
    {
        Log::info('updateRequestStatus');
        if ($this->finalStatus == 'approved') {
            $requestObj->update(['status' => 'approved']);
            $this->redirectNotificationsAfterRequestFinalized($requestObj, 'approved');
            Log::info('Request is approved');
        } elseif ($this->finalStatus == config('globals.REQUEST_STATUSES.CANCELLED')) {
            $requestObj->update(['status' => 'cancelled']);
            $this->redirectNotificationsAfterRequestFinalized($requestObj, 'cancelled'); // TODO fix this notifications redirection
            Log::info('Request is cancelled');
        } else {
            $requestObj->update(['status' => 'rejected']);
            $this->redirectNotificationsAfterRequestFinalized($requestObj, 'rejected');
            Log::info('Request is rejected');
        }
    }

    public function updateEmployeeRequestStatus($employeeRequestObj)
    {
        Log::info('updateEmployeeRequestStatus');
        if ($this->finalStatus == 'approved') {
            $employeeRequestObj->update(['status' => 'approved']);
            Log::info('Employee Request is approved');
        } elseif ($this->finalStatus == config('globals.REQUEST_STATUSES.CANCELLED')) {
            $employeeRequestObj->update(['status' => 'cancelled']);
            Log::info('Employee Request is cancelled');
        } else {
            $employeeRequestObj->update(['status' => 'rejected']);
            Log::info('Employee Request is rejected');
        }
    }
}
