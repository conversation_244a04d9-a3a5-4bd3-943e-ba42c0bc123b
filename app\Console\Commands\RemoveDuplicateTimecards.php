<?php

namespace App\Console\Commands;

use App\Models\Timecard;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RemoveDuplicateTimecards extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:duplicate-timecards';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $count = 0;
            $timecardIds = '';

            $timecards = Timecard::whereHas('entityTags', function ($query) {
                $query->whereIn('tag', ['absent_without_permission', 'absent']);
            })->get();

            foreach ($timecards as $timecard) {
                $from = Carbon::parse($timecard->from)->format('Y-m-d');
                $dayHasAttendance = Timecard::where('employee_id', $timecard->employee_id)
                    ->whereDate('from', $from)
                    ->whereHas('attendance')->exists();
                if ($dayHasAttendance) {
                    $count++;
                    $timecardIds = $timecardIds.', '.$timecard->id;
                    $timecard->entityTags()->delete();
                    $timecard->delete();
                }
            }
            Log::info('Total timecards deleted: '.$count);
            Log::info('Total timecards deleted ids: '.$timecardIds);

            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::error($e);
            DB::rollBack();
        }
    }
}
