<?php

namespace App\DomainData;

trait RejectedLeaveDayDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'company_id' => 'integer',
            'employee_leave_id' => 'required|integer',
            'date' => 'required|date',
            'rejected_by' => 'required|integer',

        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeApprovalCycleDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
