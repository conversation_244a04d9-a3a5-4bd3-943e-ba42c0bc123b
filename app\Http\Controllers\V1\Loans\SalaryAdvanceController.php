<?php

namespace App\Http\Controllers\V1\Loans;

use App\Http\Controllers\NewController;
use App\Http\Requests\CreateSalaryAdvancePolicyRequest;
use App\Http\Requests\EditSalaryAvancePolicy;
use App\Http\Requests\V1\Loans\AddSalaryAdvanceRequest;
use App\Http\Requests\V1\Loans\DisburseRequest;
use App\Services\V1\Loans\SalaryAdvanceService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SalaryAdvanceController extends NewController
{
    public function __construct(
        SalaryAdvanceService $salaryAdvanceService,
    ) {
        parent::__construct($salaryAdvanceService);
    }

    public function getMaxSalaryAdvancePercentage()
    {
        $maxPercentage = $this->service->getMaxSalaryAdvancePercentage();

        return getResponseStructure(
            ['data' => $maxPercentage],
            HttpStatusCodeUtil::OK
        );
    }

    public function requestSalaryAdvance(AddSalaryAdvanceRequest $request)
    {
        $data = $request->validated();
        $salaryAdvance = $this->service->requestSalaryAdvance($data);

        return getResponseStructure(
            ['data' => $salaryAdvance],
            HttpStatusCodeUtil::OK
        );
    }

    public function disburseSalaryAdvance($id, DisburseRequest $request)
    {
        $request = $request->validated();
        DB::beginTransaction();
        try {
            $loanRequest = $this->service->disburseSalaryAdvance($id, $request);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }

        return getResponseStructure(['data' => $loanRequest],
            HttpStatusCodeUtil::OK, 'Loan requested successfully');

    }

    public function enableSalaryAdvancePolicy()
    {
        $this->service->enableSalaryAdvancePolicyToggle();

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function titleHasSalaryAdvancePolicyWarning(Request $request)
    {
        $titles = $request->query('titles', []);

        if (empty($titles)) {
            return getResponseStructure(['message' => 'No titles provided'], HttpStatusCodeUtil::BAD_REQUEST);
        }

        try {
            $assignedTitles = $this->service->salaryAdvancePolicyWarning($titles);
            DB::commit();

            return getResponseStructure(['data' => $assignedTitles], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }
    }

    public function addSalaryAdvancePolicy(CreateSalaryAdvancePolicyRequest $request)
    {
        $request->validated();
        DB::beginTransaction();
        try {
            $SalaryAdvancePolicy = $this->service->createSalaryAdvancePolicy($request);
            DB::commit();

            return getResponseStructure(['data' => $SalaryAdvancePolicy], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }

    }

    public function editSalaryAdvancePolicy(EditSalaryAvancePolicy $request, int $id)
    {
        $request->validated();
        DB::beginTransaction();
        try {
            $SalaryAdvancePolicy = $this->service->editSalaryAdvancePolicy($request, $id);
            DB::commit();

            return getResponseStructure(['data' => $SalaryAdvancePolicy], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }
    }

    public function approveSalaryAdvance(int $id)
    {
        Log::info('approveSalaryAdvance');
        DB::beginTransaction();
        try {
            $loanRequest = $this->service->approveSalaryAdvance($id);
            DB::commit();

            return getResponseStructure(['data' => $loanRequest], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }
    }

    public function rejectSalaryAdvance(int $id)
    {
        DB::beginTransaction();
        try {
            $loanRequest = $this->service->rejectSalaryAdvance($id);
            DB::commit();

            return getResponseStructure(['data' => $loanRequest], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }
    }

    public function cancelSalaryAdvance(int $id)
    {
        DB::beginTransaction();
        try {
            $loanRequest = $this->service->forceCancelSalaryAdvance($id);
            DB::commit();

            return getResponseStructure(['data' => $loanRequest], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }
    }
}
