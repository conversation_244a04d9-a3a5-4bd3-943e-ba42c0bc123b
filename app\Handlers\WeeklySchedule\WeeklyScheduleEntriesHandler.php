<?php

namespace App\Handlers\WeeklySchedule;

use App\Enums\V2\WorkTypes\RestDaysTypesEnum;
use App\FeatureToggles\Unleash;
use App\Handlers\HandlerHelper;
use App\Http\Resources\EmployeeLeaveRequestResource;
use App\Http\Resources\NewSchedule\V1\NewShiftResource;
use App\Http\Resources\TimeCard\TimeCardResource;
use App\Models\EmployeeLeaveRequest;
use App\Models\ShiftTitleBranch;
use App\Models\Timecard;
use App\Repositories\V1\Holidays\PublicHolidaysRepository;
use App\Traits\V2\WorkTypesTrait;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class WeeklyScheduleEntriesHandler
{
    use HandlerHelper, WorkTypesTrait;

    private $typeToAppearInSchedule;

    private $unleash;

    private $newWorkTypesFlag;

    public function handleEntries($schedules, $request): array
    {
        $this->unleash = app(Unleash::class);
        $this->typeToAppearInSchedule = $this->getTypesToAppearInSchedule();
        $this->newWorkTypesFlag = $this->unleash->getNewWorkTypesFeatureFlag();
        $entries = [];

        foreach ($schedules as $employeeSchedules) {
            $employeeId = $employeeSchedules->id;
            $branchIds = [$request['branch_id']];
            $weekDates = $this->generateDaysFromTwoDates($request['from'], $request['to']);
            $addedDates = []; // 🔥 NEW: track dates already added for this employee
            $timeCards = $employeeSchedules->timeCards;

            if (count($timeCards) && $timeCards[0]->employee_id == 15576) {
                Log::info('Employee ID: ' . $timeCards[0]->employee_id . ' has size Timecards: ' . count($timeCards));
            }

            $shiftTitleBranch = new ShiftTitleBranch;
            $newShifts = $shiftTitleBranch->shifts($branchIds, $employeeSchedules->title_id);

            $this->handleEmployeeInfo($employeeSchedules, $entries);

            // 1. Handle leaves first
            foreach ($employeeSchedules->employeeLeaveRequests as $leave) {
                $fromDate = Carbon::parse(max($leave->from, $request['from']))->startOfDay();
                $toDate = Carbon::parse($leave->to)->startOfDay();

                while ($fromDate->lte($toDate)) {
                    $dateStr = $fromDate->format('Y-m-d');
                    if (isset($weekDates[$dateStr]) && !in_array($dateStr, $addedDates)) {
                        $weekDates[$dateStr] = true;
                        $shiftTemplates = $this->createShiftTemplates($newShifts, $dateStr);
                        $entries[$employeeId]['entries'][] = $this->createEntry(
                            $dateStr,
                            new EmployeeLeaveRequestResource($leave),
                            [],
                            $shiftTemplates
                        );
                        $addedDates[] = $dateStr;
                    }
                    $fromDate->addDay();
                }
            }

            // 2. Handle timecards (skip dates already handled by leave)
            $timecardsByDate = collect($timeCards)->groupBy('date');

            foreach ($timecardsByDate as $date => $employeeSchedule) {
                if ($date >= $request['from'] && $date <= $request['to'] && !in_array($date, $addedDates)) {
                    $weekDates[$date] = true;
                    $shiftTemplates = $this->createShiftTemplates($newShifts, $date);
                    $this->formatAttendanceData(
                        $employeeSchedule,
                        $employeeId,
                        $date,
                        $entries,
                        $shiftTemplates,
                        $weekDates
                    );
                    $addedDates[] = $date;
                }
            }

            // 3. Fill empty days (only if still not added)
            foreach ($weekDates as $date => $value) {
                if (!$value && !in_array($date, $addedDates)) {
                    $shiftTemplates = $this->createShiftTemplates($newShifts, $date);
                    $entries[$employeeId]['entries'][] = $this->createEntry($date, null, [], $shiftTemplates);
                    $addedDates[] = $date;
                }
            }

            $this->sortEntryByDate($employeeId, $entries);
        }

        return count($entries) > 0 ? array_values($entries) : [];
    }


    protected function formatAttendanceData($employeeSchedule, $employeeId, $date, &$entries, $shiftTemplates = [], &$weekDates = []): void
    {
        $leave = null;
        $timecards = [];
        foreach ($employeeSchedule as $schedule) {
            if ($schedule instanceof Timecard) {
                $timecards[] = new TimeCardResource($schedule);
            } elseif ($schedule instanceof EmployeeLeaveRequest) {
                $leave = new EmployeeLeaveRequestResource($schedule);
                $fromDate = Carbon::parse($schedule->from)->startOfDay();
                $toDate = Carbon::parse($schedule->to)->startOfDay();

                while ($fromDate->lte($toDate)) {
                    $formattedDate = $fromDate->format('Y-m-d');
                    if (isset($weekDates[$formattedDate])) {
                        $weekDates[$formattedDate] = true;
                        $entries[$employeeId]['entries'][] = $this->createEntry($formattedDate, $leave, $timecards, $shiftTemplates);
                    }
                    $fromDate->addDay();
                }
            }
        }

        if (!isset($leave)) {
            $entries[$employeeId]['entries'][] = $this->createEntry($date, $leave, $timecards, $shiftTemplates);
        }
    }

    public function createShiftTemplates($newShifts, $date)
    {
        $shiftTemplates = [];
        foreach ($newShifts as $newShift) {
            if ($date >= $newShift->from_date && $date <= $newShift->to_date
                && !is_null($newShift->sun_from_time) && !is_null($newShift->sun_duration)) {
                $shiftTemplates[] = new NewShiftResource($newShift);
            }
        }

        return $shiftTemplates;
    }

    protected function handleEmployeeInfo($employee, &$entries): void
    {
        $entries[$employee->id]['employee_info'] = [
            'id' => $employee->id,
            'profile_image' => $employee->profilePicture->attachment_url ?? null,
            'first_name' => $employee->first_name,
            'second_name' => $employee->second_name,
            'employee_number' => $employee->employee_number,
            'title' => [
                'name' => $employee->title?->name,
                'color' => $employee->title?->color,
            ],
            'branch_name' => $employee->branch_name,
            'status' => $employee->status,
            'termination_date' => $employee->termination_date,
            'can_add_timecard' => (!$this->newWorkTypesFlag) ? true : in_array($employee->work_days_type, $this->typeToAppearInSchedule),
            'can_add_rest_day' => (!$this->newWorkTypesFlag) ? true : $employee->rest_days_type == RestDaysTypesEnum::ROTATIONAL->value,
        ];
    }

    public function createEntry($date, $leave = null, $timecards = [], $shiftTemplates = [])
    {
        $dailySchedule = [
            'date' => $date,
            'leave' => $leave,
            'timecards' => $timecards,
            'shift_templates' => $shiftTemplates,
        ];

        $publicHolidayRepository = new PublicHolidaysRepository;
        $publicHolidayOnDate = $publicHolidayRepository->publicHolidayOnDate($date);
        if (isset($publicHolidayOnDate)) {
            $dailySchedule['holiday'] = [
                'id' => $publicHolidayOnDate->id,
                'name' => $publicHolidayOnDate->name,
                'start_date' => $publicHolidayOnDate->start,
                'end_date' => $publicHolidayOnDate->end,
            ];
        }

        return $dailySchedule;
    }
}
