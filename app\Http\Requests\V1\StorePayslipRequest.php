<?php

namespace App\Http\Requests\V1;

use Illuminate\Foundation\Http\FormRequest;

class StorePayslipRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'end_date' => 'required|date',
            'employee_ids' => 'nullable|array',
            'employee_ids.*' => 'nullable|integer|exists:employees,id',
        ];
    }

    // append data to validated data
    public function validated($key = null, $default = null)
    {
        $data = parent::validated($key, $default);
        $data['lang'] = $this->header('Accept-Language');
        return $data;
    }
}
