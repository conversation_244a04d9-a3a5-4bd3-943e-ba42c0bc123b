<?php

namespace App\Http\Requests\V1;

use Illuminate\Foundation\Http\FormRequest;

class StorePayslipRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'payroll_id' => 'required|integer|exists:payrolls,id',
            'month' => 'required|integer|min:1|max:12',
            'year' => 'required|integer',
            'employee_ids' => 'nullable|array',
            'employee_ids.*' => 'nullable|integer|exists:employees,id',
            'branch_id' => 'nullable|integer|exists:branches,id',
            'print_type' => 'nullable|integer|in:1,2',
            'print_lang' => 'nullable|string|in:en,ar',
        ];
    }
}
