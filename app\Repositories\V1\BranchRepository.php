<?php

namespace App\Repositories\V1;

use App\Models\Branch;
use App\Repositories\BaseRepository;
use App\Traits\QueriesHelper;
use App\Util\EmployeeUtil;

class BranchRepository extends BaseRepository
{
    use QueriesHelper;

    public function model(): string
    {
        return Branch::class;
    }

    public function missingInfoOnBranches($data = [])
    {
        $lang = '_' . (config('globals.lang') ?? 'ar');

        return $this->model->select('id')
            ->withWhereHas('employees', function ($query) use ($lang, $data) {
                $query
                    ->select('id', 'branch_id', 'title_id', 'first_name' . $lang, 'second_name' . $lang, 'name' . $lang, 'employee_number')
                    ->with('title:id,name' . $lang . ',color')
                    ->with('branch:id,name' . $lang);

                if (isset($data['employee_id'])) {
                    $query->where('id', $data['employee_id']);
                } else {
                    $this->appendScopeQuery($query, $data);
                }

                $query
                    ->when(isset($data['search_value']), function ($q) use ($data) {
                        $this->appendEmployeeTextSearchQuery($q, $data['search_value']);
                    })
                    ->when(isset($data['title_ids']), function ($q) use ($data) {
                        $q->whereIn('title_id', $data['title_ids']);
                    })
                    ->where(function ($q) {
                        $q->WhereDoesntHave('employeeInfo')
                            ->orWhereHas('employeeInfo', function ($infoQuery) {
                                $infoQuery->whereNull('join_date');
                            });
                    });
            })
            ->groupBy('id')
            ->get();
    }

    public function pendingCicosByBranch($data = [])
    {
        $lang = '_' . (config('globals.lang') ?? 'ar');

        return $this->model->select('id')
            ->withWhereHas('employees', function ($query) use ($lang, $data) {
                $query
                    ->select('id', 'branch_id', 'title_id')
                    ->when(isset($data['employee_id']), function ($q) use ($data) {
                        $q->where('id', $data['employee_id']);
                    });

                if (!isset($data['employee_id'])) {
                    $this->appendScopeQuery($query, $data);
                }

                $query
                    ->when(isset($data['search_value']), function ($q) use ($data) {
                        $this->appendEmployeeTextSearchQuery($q, $data['search_value']);
                    })
                    ->when(isset($data['title_ids']), function ($q) use ($data) {
                        $q->whereIn('title_id', $data['title_ids']);
                    })
                    ->withWhereHas('cicos', function ($cicosQuery) use ($lang) {
                        $cicosQuery
                            ->select('cicos.id', 'cicos.in_out', 'cicos.date', 'cicos.paired_clock_id', 'cicos.status', 'cicos.employee_id', 'cicos.branch_id')
                            ->selectRaw('CAST(cicos.created_at AS datetime) AS requested_at')
                            ->with(
                                'employee:id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id',
                                'employee.title:id,name' . $lang . ',color',
                                'branch:id,name' . $lang,
                                'pairedClock:id,in_out,employee_id,branch_id,status,date',
                                'pairedClock.branch:id,name' . $lang
                            );
                        $this->appendUnverifiedQuery($cicosQuery, [], false, true); // branch scope is applied in this function

                    });
            }
            )->groupBy('id')
            ->get();
    }

    public function getAllOvertimesPendingOnMe($data = [])
    {
        $lang = '_' . (config('globals.lang') ?? 'ar');

        return $this->model->select('id')
            ->withWhereHas('workFlowApprovalCycles', function ($workflowQuery) use ($lang, $data) {
                $workflowQuery
                    ->when(isset($data['employee_id']), function ($q) use ($data) {
                        $q->where('employee_id', $data['employee_id']);
                    })
                    ->whereIn('workflow_approval_cycle.role_id', config('globals.user_role_ids'))
                    ->where('workflow_approval_cycle.status', 'pending');

                if (!isset($data['employee_id'])) {
                    $this->appendBranchAndDepartmentScopeToWorkflowQuery($workflowQuery, $data);
                }
                $workflowQuery->when(isset($data['search_value']), function ($q) use ($data) {
                    $q->whereHas('employee', function ($employeeQuery) use ($data) {
                        $this->appendEmployeeTextSearchQuery($employeeQuery, $data['search_value']);
                    });
                })
                    ->when(isset($data['title_ids']), function ($q) use ($data) {
                        $q->whereHas('employee', function ($employeeQuery) use ($data) {
                            $employeeQuery->whereIn('title_id', $data['title_ids']);
                        });
                    })
                    ->where('requestable_type', 'attendance_overtime')
                    ->whereHas('requestable', function ($pendingOvertimeQuery) {
                        $pendingOvertimeQuery->where('status', 'pending');
                    })
                    ->with('requestable', function ($q) use ($lang) {
                        $q->select('overtime_value', 'id', 'overtime_minutes', 'updated_value',
                            'employee_id', 'attendance_id')
                            ->selectRaw('CAST(created_at AS datetime) AS requested_at') // created at is timestamps so we need to cast it
                            ->selectRaw('CAST(date AS datetime) AS date')
                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                            ->with('employeeRequests', function ($q) use ($lang) {
                                $q->with('requestedBy:id,name_ar,name_en,name', 'requestedBy:id,first_name' . $lang . ',second_name' . $lang)
                                    ->select('id', 'requested_by', 'requestable_type', 'requestable_id', 'comment', 'status');
                            })
                            ->with('attendance:id,ci_id,co_id,slotable_id,slotable_type', 'attendance.clockIn:id,date', 'attendance.clockOut:id,date', 'attendance.slotable')
                            ->with('employeeApproves', function ($q) {
                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                    ->with('users.employee', function ($employeeQuery) {
                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                            ->with('branches', function ($q) {
                                                $q->select('branch_employee.id', 'branch_id');
                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                    })
                                    ->with('scopes')
                                    ->orderBy('workflow_approval_cycle.order');
                            });

                    });
            })
            ->groupBy('id')
            ->get();
    }

    public function getAllLeavesPendingOnMe($data = [])
    {
        $lang = '_' . (config('globals.lang') ?? 'ar');

        return $this->model->select('id')
            ->withWhereHas('workFlowApprovalCycles', function ($workflowQuery) use ($lang, $data) {
                $workflowQuery
                    ->when(isset($data['employee_id']), function ($q) use ($data) {
                        $q->where('employee_id', $data['employee_id']);
                    })
                    ->whereIn('workflow_approval_cycle.role_id', config('globals.user_role_ids'))
                    ->where('workflow_approval_cycle.status', 'pending');

                if (!isset($data['employee_id'])) {
                    $this->appendBranchAndDepartmentScopeToWorkflowQuery($workflowQuery, $data);
                }

                $workflowQuery->when(isset($data['search_value']), function ($q) use ($data) {
                    $q->whereHas('employee', function ($employeeQuery) use ($data) {
                        $this->appendEmployeeTextSearchQuery($employeeQuery, $data['search_value']);
                    });
                })
                    ->when(isset($data['title_ids']), function ($q) use ($data) {
                        $q->whereHas('employee', function ($employeeQuery) use ($data) {
                            $employeeQuery->whereIn('title_id', $data['title_ids']);
                        });
                    })
                    ->where('requestable_type', 'employee_leave_request')
                    ->whereHas('requestable', function ($pendingOvertimeQuery) {
                        $pendingOvertimeQuery->where('status', 'pending');
                    })
                    ->with('requestable', function ($q) use ($lang) {
                        $q->select('id', 'from', 'to', 'company_leave_type_id',
                            'employee_id', 'note', 'company_leave_type_policy_id')
                            ->selectRaw('CAST(created_at AS datetime) AS requested_at')
                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                            ->with('employeeRequests', function ($q) use ($lang) {
                                $q->with('requestedBy:id,first_name' . $lang . ',second_name' . $lang)
                                    ->select('id', 'requested_by', 'requestable_type', 'requestable_id')->first();
                            })
                            ->with('employeeApproves', function ($q) {
                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                    ->with('users.employee', function ($employeeQuery) {
                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                            ->with('branches', function ($q) {
                                                $q->select('branch_employee.id', 'branch_id');
                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                    })
                                    ->with('scopes')
                                    ->orderBy('workflow_approval_cycle.order');
                            })
                            ->with('companyLeaveType:id,name' . $lang)
                            ->with('companyLeaveTypePolicy:id,unit')
                            ->with('attachments');

                    });
            })
            ->groupBy('id')
            ->get();
    }

    public function getAllDeductionsPendingOnMe($data = [])
    {
        $lang = '_' . (config('globals.lang') ?? 'ar');

        return $this->model->select('id')
            ->withWhereHas('workFlowApprovalCycles', function ($workflowQuery) use ($lang, $data) {
                $workflowQuery
                    ->when(isset($data['employee_id']), function ($q) use ($data) {
                        $q->where('employee_id', $data['employee_id']);
                    })
                    ->whereIn('workflow_approval_cycle.role_id', config('globals.user_role_ids'))
                    ->where('workflow_approval_cycle.status', 'pending');

                if (!isset($data['employee_id'])) {
                    $this->appendBranchAndDepartmentScopeToWorkflowQuery($workflowQuery, $data);
                }

                $workflowQuery->when(isset($data['search_value']), function ($q) use ($data) {
                    $q->whereHas('employee', function ($employeeQuery) use ($data) {
                        $this->appendEmployeeTextSearchQuery($employeeQuery, $data['search_value']);
                    });
                })
                    ->when(isset($data['title_ids']), function ($q) use ($data) {
                        $q->whereHas('employee', function ($employeeQuery) use ($data) {
                            $employeeQuery->whereIn('title_id', $data['title_ids']);
                        });
                    })
                    ->where('requestable_type', 'attendance_deduction')
                    ->whereHas('requestable', function ($q) {
                        $this->appendPendingWaiveDeductionQuery($q);
                    })
                    ->with('requestable', function ($q) use ($lang) {

                        $q->select('id', 'deduction_value', 'employee_id', 'attendance_id', 'updated_value')
                            ->selectRaw('CAST(created_at AS datetime) AS requested_at')
                            ->selectRaw('CAST(date AS datetime) AS date')
                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                            ->with('employeeRequests', function ($q) use ($lang) {
                                $q->with('requestedBy:id,name_ar,name_en,name', 'requestedBy:id,first_name' . $lang . ',second_name' . $lang)
                                    ->select('id', 'requested_by', 'requestable_type', 'requestable_id', 'status', 'comment');
                            })
                            ->with('attendance:id,ci_id,co_id,slotable_id,slotable_type', 'attendance.clockIn:id,date', 'attendance.clockOut:id,date', 'attendance.slotable')
                            ->with('employeeApproves', function ($q) {
                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                    ->with('users.employee', function ($employeeQuery) {
                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                            ->with('branches', function ($q) {
                                                $q->select('branch_employee.id', 'branch_id');
                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                    })
                                    ->with('scopes')
                                    ->orderBy('workflow_approval_cycle.order');
                            });
                    });
            })
            ->groupBy('id')
            ->get();
    }

    public function getAllPenaltiesPendingOnMe($data = [])
    {
        $lang = '_' . (config('globals.lang') ?? 'ar');

        return $this->model->select('id')
            ->withWhereHas('workFlowApprovalCycles', function ($workflowQuery) use ($lang, $data) {
                $workflowQuery
                    ->when(isset($data['employee_id']), function ($q) use ($data) {
                        $q->where('employee_id', $data['employee_id']);
                    })
                    ->whereIn('workflow_approval_cycle.role_id', config('globals.user_role_ids'))
                    ->where('workflow_approval_cycle.status', 'pending');

                if (!isset($data['employee_id'])) {
                    $this->appendBranchAndDepartmentScopeToWorkflowQuery($workflowQuery, $data);
                }

                $workflowQuery->when(isset($data['search_value']), function ($q) use ($data) {
                    $q->whereHas('employee', function ($employeeQuery) use ($data) {
                        $this->appendEmployeeTextSearchQuery($employeeQuery, $data['search_value']);
                    });
                })
                    ->when(isset($data['title_ids']), function ($q) use ($data) {
                        $q->whereHas('employee', function ($employeeQuery) use ($data) {
                            $employeeQuery->whereIn('title_id', $data['title_ids']);
                        });
                    })
                    ->where('requestable_type', 'penalty')
                    ->whereHas('requestable', function ($q) {
                        $q->where('status', 'pending');
                    })
                    ->with('requestable', function ($q) use ($lang) {
                        $q->select('id', 'amount', 'recurrence', 'unit', 'date', 'reason', 'submitted_by', 'employee_id')
                            // ->selectRaw('CAST(created_at AS datetime) AS requested_at')
                            ->selectRaw('CAST(date AS datetime) AS date')
                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                            ->with('employeeApproves', function ($q) {
                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                    ->with('users.employee', function ($employeeQuery) {
                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                            ->with('branches', function ($q) {
                                                $q->select('branch_employee.id', 'branch_id');
                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                    })
                                    ->with('scopes')
                                    ->orderBy('workflow_approval_cycle.order');
                            });
                    });
            })
            ->groupBy('id')
            ->get();

    }

    public function getBranchesBasicInfoByIds($branchIds)
    {
        $lang = '_' . (config('globals.lang') ?? 'ar');

        return $this->model->select('id', 'name' . $lang)
            ->whereIn('id', $branchIds)
            ->get();
    }

    public function getAllBranches()
    {
        return $this->selectColumns(['id', 'name_ar', 'name_en'])->keyBy('id')->toArray();
    }

    public function findByNameAndCompany($name, $companyId)
    {
        return $this->model->where('name_ar', $name)
            ->where('company_id', $companyId)
            ->first();
    }

    public function getNameUsingId($branchId){
        return $this->model
        ->select('id','name_ar', 'name_en')
        ->where('id', $branchId)
        ->first();
    }

    public function getForBulkUpload()
    {
        return $this->model
            ->select('id', 'name_en', 'name_ar')
            ->get();
    }
}
