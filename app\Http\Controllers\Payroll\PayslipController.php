<?php

namespace App\Http\Controllers\Payroll;

use App\Http\Controllers\Controller;
use App\Http\Requests\V1\StorePayslipRequest;
use App\Services\V1\Payroll\PayslipService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use InvalidArgumentException;
use RuntimeException;
use App\Repositories\NewCompanyRepository;

/**
 * PayslipController - Handles payslip generation and download requests
 * 
 * @version 2.0
 */
class PayslipController extends Controller
{
    public function __construct(private PayslipService $payslipService)
    {
    }

    /**
     * Store payslips for multiple employees as ZIP
     */
    public function store(StorePayslipRequest $request): JsonResponse
    {
        $data = $request->validated();

        try {
            // Add company ID from global config
            $companyId = config('globals.company.id');
            if (!$companyId) {
                throw new RuntimeException('Company configuration not found');
            }

            $result = $this->payslipService->generateAndStorePayslips([
                'company_id' => $companyId,
                'payroll_id' => $data['payroll_id'],
                'month' => $data['month'],
                'year' => $data['year'],
                'employee_ids' => $data['employee_ids'] ?? null,
                'print_type' => $data['print_type'] ?? null,
                'print_lang' => $data['print_lang'] ?? 'en'
            ]);

            Log::info('Payslips generated successfully', [
                'total_employees' => $result['total_employees'],
                'file_path' => $result['path'],
                'user_id' => auth()->id()
            ]);


            return response()->json([
                'success' => true,
                'message' => "Successfully generated {$result['total_employees']} payslips",
                'data' => [
                    'file_path' => Storage::url($result['path']),
                    'total_employees' => $result['total_employees'],
                    'download_url' => route('payslip.download', [
                        'company_id' => $companyId,
                        'month' => $data['month'],
                        'year' => $data['year']
                    ])
                ]
            ], 201);

        } catch (InvalidArgumentException $e) {
            Log::warning('Invalid payslip generation request', [
                'error' => $e->getMessage(),
                'data' => $data,
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Invalid request data',
                'error' => $e->getMessage()
            ], 422);

        } catch (RuntimeException $e) {
            Log::error('Payslip generation runtime error', [
                'error' => $e->getMessage(),
                'data' => $data,
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate payslips',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);

        } catch (\Exception $e) {
            Log::error('Unexpected error during payslip generation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data,
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Download stored payslips ZIP file
     */
    public function download(Request $request): JsonResponse|\Symfony\Component\HttpFoundation\StreamedResponse
    {
        try {
            $validated = $request->validate([
                'company_id' => 'required|integer|min:1',
                'month' => 'required|integer|between:1,12',
                'year' => 'required|integer|between:2000,2100'
            ]);

            $filePath = $this->payslipService->getStoredPayslipsPath($validated);

            if (!Storage::exists($filePath)) {
                Log::warning('Payslips file not found for download', [
                    'file_path' => $filePath,
                    'request_data' => $validated,
                    'user_id' => auth()->id()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Payslips file not found. Please generate payslips first.'
                ], 404);
            }

            $fileName = sprintf(
                'payslips_%02d_%d_%s.zip',
                $validated['month'],
                $validated['year'],
                date('Y-m-d_H-i-s')
            );

            Log::info('Payslips downloaded', [
                'file_path' => $filePath,
                'download_name' => $fileName,
                'user_id' => auth()->id()
            ]);

            return Storage::download($filePath, $fileName);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request parameters',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Payslip download failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to download payslips',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Check if payslips exist for given criteria
     */
    public function checkExists(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'company_id' => 'required|integer|min:1',
                'month' => 'required|integer|between:1,12',
                'year' => 'required|integer|between:2000,2100'
            ]);

            $filePath = $this->payslipService->getStoredPayslipsPath($validated);
            $exists = Storage::exists($filePath);

            return response()->json([
                'success' => true,
                'data' => [
                    'exists' => $exists,
                    'file_path' => $exists ? Storage::url($filePath) : null,
                    'last_modified' => $exists ? Storage::lastModified($filePath) : null
                ]
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request parameters',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Error checking payslip existence', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check payslip existence'
            ], 500);
        }
    }

    /**
     * Get payslips data (without generating PDFs)
     */
    public function getPayslips(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'company_id' => 'required|integer',
                'month' => 'required|integer|min:1|max:12',
                'year' => 'required|integer|min:2020'
            ]);

            $payslips = $this->payslipService->getPayslips($data);

            return response()->json([
                'success' => true,
                'data' => $payslips
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get payslips',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get stored payslips path
     */
    public function getStoredPath(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'company_id' => 'required|integer',
                'month' => 'required|integer|min:1|max:12',
                'year' => 'required|integer|min:2020'
            ]);

            $path = $this->payslipService->getStoredPayslipsPath($data);

            return response()->json([
                'success' => true,
                'path' => $path
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get stored path',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function view(Request $request)
    {
        // Sample data structure based on the provided array
        $samplePayslip = [
            "employee" => [
                "id" => 4501,
                "name" => "محمد إبراهيم محمود",
                "code" => "14",
                "title" => "مشرف خدمة العملاء",
                "net_salary" => "9,534.43"
            ],
            "categories" => [
                "Public Holidays" => [
                    [
                        "name" => "public_holidays",
                        "amount" => "568.13"
                    ]
                ],
                "Attendance Overtime" => [
                    [
                        "name" => "overtime",
                        "amount" => "179.32"
                    ]
                ],
                "Attendance Deduction" => [
                    [
                        "name" => "absence_deduction",
                        "amount" => "0.00"
                    ],
                    [
                        "name" => "late_deduction",
                        "amount" => "0.00"
                    ]
                ],
                "بدلات" => [
                    [
                        "name" => "بدل نقل للفروع",
                        "amount" => "936.00"
                    ]
                ]
            ],
            "tax_amount" => "666.39",
            "insurance_amount" => "0.00"
        ];

        // Prepare data for the template
        $templateData = [
            'employee' => $samplePayslip['employee'],
            'categories' => $samplePayslip['categories'],
            'tax_amount' => $samplePayslip['tax_amount'],
            'insurance_amount' => $samplePayslip['insurance_amount'],
            'company_name' => 'شركة المثال للتكنولوجيا', // Sample company name in Arabic
            'month' => 'December',
            'year' => '2024',
            'direction' => 'rtl', // RTL for Arabic content
            'language' => 'ar'    // Arabic language
        ];

        return view('admin.pdf-templates.payslip', $templateData);
    }

    /**
     * Get single payslip data for preview/testing
     */
    public function getPayslip(Request $request)
    {
        try {
            $validated = $request->validate([
                'employee_id' => 'required|integer',
                'company_id' => 'required|integer',
                'month' => 'required|integer|between:1,12',
                'year' => 'required|integer|between:2000,2100',
                'language' => 'sometimes|string|in:en,ar'
            ]);

            // Get payslip data from service
            $payslipData = $this->payslipService->getPayslip($validated['employee_id']);

            if (!$payslipData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payslip not found'
                ], 404);
            }

            // Get company information
            $company = app(NewCompanyRepository::class)->getCompanyInfo($validated['company_id']);

            // Prepare template data
            $language = $validated['language'] ?? 'en';
            $direction = $language === 'ar' ? 'rtl' : 'ltr';

            $templateData = [
                'employee' => [
                    'id' => $payslipData['employee']['id'],
                    'name' => $payslipData['employee']['name'],
                    'code' => $payslipData['employee']['code'],
                    'title' => $payslipData['employee']['title'],
                    'net_salary' => $payslipData['employee']['net_salary']
                ],
                'categories' => $payslipData['categories'],
                'tax_amount' => $payslipData['tax_amount'],
                'insurance_amount' => $payslipData['insurance_amount'],
                'company_name' => $company->name,
                'month' => date('F', mktime(0, 0, 0, $validated['month'], 1)),
                'year' => $validated['year'],
                'direction' => $direction,
                'language' => $language
            ];

            return view('admin.pdf-templates.payslip', $templateData);

        } catch (\Exception $e) {
            Log::error('Error getting payslip data', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get payslip data',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
}
