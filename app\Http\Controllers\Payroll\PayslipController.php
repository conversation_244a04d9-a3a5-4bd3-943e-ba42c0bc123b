<?php

namespace App\Http\Controllers\Payroll;

use App\Http\Controllers\Controller;
use App\Http\Requests\V1\StorePayslipRequest;
use App\Services\V1\Payroll\PayslipService;
use App\Http\Resources\V1\Payroll\Payslip\EmployeesCollection;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use RuntimeException;

/**
 * PayslipController - Handles payslip generation and download requests
 * 
 * @version 2.0
 */
class PayslipController extends Controller
{
    public function __construct(private PayslipService $payslipService)
    {
    }

    /**
     * Download payslips directly (without saving to server)
     */
    public function download(StorePayslipRequest $request): \Symfony\Component\HttpFoundation\Response
    {
        try {
            $data = $request->validated();

            Log::info('Starting direct payslip download', [
                'request_data' => $data,
                'user_id' => auth()->id()
            ]);

            $result = $this->payslipService->generatePayslipsZipForDownload([
                'end_date' => $data['end_date'],
                'employee_ids' => $data['employee_ids'] ?? null,
                'lang' => $data['lang'] ?? 'en'
            ]);

            Log::info('Direct payslip download completed', [
                'filename' => $result['filename'],
                'total_employees' => $result['total_employees'],
                'successfully_downloaded' => $result['successfully_downloaded'],
                'zip_size' => strlen($result['content']) . ' bytes',
                'user_id' => auth()->id()
            ]);

            // Return the ZIP file as a download response
            return response($result['content'])
                ->header('Content-Type', $result['mime_type'])
                ->header('Content-Disposition', 'attachment; filename="' . $result['filename'] . '"')
                ->header('Content-Length', strlen($result['content']))
                ->header('X-Total-Employees', $result['total_employees'])
                ->header('X-Successfully-Downloaded', $result['successfully_downloaded'])
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (ValidationException $e) {
            Log::warning('Validation failed for direct payslip download', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'errors' => $e->errors(),
                'message' => 'Invalid request data'
            ], HttpStatusCodeUtil::UNPROCESSABLE_ENTITY);

        } catch (RuntimeException $e) {
            Log::error('Runtime error in direct payslip download', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'errors' => ['message' => $e->getMessage()]
            ], HttpStatusCodeUtil::BAD_REQUEST);

        } catch (\Exception $e) {
            Log::error('Direct payslip download failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'errors' => [
                    'message' => config('app.debug') ? $e->getMessage() : 'Internal server error'
                ]
            ], HttpStatusCodeUtil::INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get employees list with filters for payslip management
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getEmployees(Request $request): JsonResponse
    {
        try {
            // Validate required data
            $validated = $request->validate([
                'end_date' => 'required|date',
                // Filter parameters
                'branches' => 'sometimes|array',
                'branches.*' => 'integer|min:1',
                'departments' => 'sometimes|array',
                'departments.*' => 'integer|min:1',
                'cash_only' => 'sometimes|boolean',
                'is_downloaded' => 'sometimes|boolean',
                'search' => 'sometimes|string|max:255'
            ]);

            // Prepare data for service (company_id resolved automatically)
            $data = [
                'end_date' => $validated['end_date']
            ];

            // Prepare filters
            $filters = [];
            if (isset($validated['branches'])) {
                $filters['branches'] = $validated['branches'];
            }
            if (isset($validated['departments'])) {
                $filters['departments'] = $validated['departments'];
            }
            if (isset($validated['cash_only'])) {
                $filters['cash_only'] = $validated['cash_only'];
            }
            if (isset($validated['is_downloaded'])) {
                $filters['is_downloaded'] = $validated['is_downloaded'];
            }
            if (isset($validated['search'])) {
                $filters['search'] = $validated['search'];
            }

            // Get employees from service
            $employees = $this->payslipService->getEmployees($data, $filters);

            Log::info('Employees retrieved successfully', [
                'total_employees' => $employees->count(),
                'filters_applied' => array_keys($filters),
                'user_id' => auth()->id()
            ]);

            // Return using getResponseStructure with resource collection data
            $employeesCollection = new EmployeesCollection($employees);
            $collectionData = $employeesCollection->toResponse(request())->getData();

            return getResponseStructure([
                'data' => $collectionData->data,
            ], HttpStatusCodeUtil::OK, 'Employees retrieved successfully');

        } catch (ValidationException $e) {
            return getResponseStructure([
                'errors' => $e->errors()
            ], HttpStatusCodeUtil::UNPROCESSABLE_ENTITY, 'Invalid request parameters');

        } catch (\Exception $e) {
            Log::error('Failed to get employees', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            return getResponseStructure([
                'errors' => [
                    'message' => config('app.debug') ? $e->getMessage() : 'Internal server error'
                ]
            ], HttpStatusCodeUtil::INTERNAL_SERVER_ERROR, 'Failed to retrieve employees');
        }
    }
}
