<?php

namespace App\Http\Resources\V1\Penalties;

use App\Http\Resources\BaseResource;
use App\Http\Resources\V1\EmployeesInApprovalCycleResource;
use App\Http\Resources\V1\WorkFlows\ApprovalCycleCollection;
use App\Traits\QueriesHelper;
use App\Traits\RequestsHelper;

class PenaltiesResource extends BaseResource
{
    use QueriesHelper, RequestsHelper;

    public $employeeData;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        if ($this->employee) {
            $this->employeeData = clone $this->employee;
            $this->employeeData->profile_image = $this->employee->profilePicture?->attachment_url ?? null;
        }

        return [
            'id' => $this->id,
            'can_approve' => $this->whenAttributeExists('can_approve'),
            'penalty_group' => $this->whenLoaded('penaltyGroup', function () {
                return [
                    'id' => $this->penaltyGroup->id,
                    'name' => $this->penaltyGroup->name,
                ];
            }),
            'recurrence' => $this->whenAttributeExists('recurrence'),
            'amount' => $this->whenAttributeExists('amount'),
            'unit' => $this->whenAttributeExists('unit'),
            'date' => $this->whenAttributeExists('date'),
            'submitted_by' => $this->whenLoaded('submittedBy', function () {
                return [
                    'id' => $this->submittedBy->id,
                    'name' => $this->submittedBy->name,
                ];
            }),
            'status' => $this->whenAttributeExists('status'),
            'reason' => $this->whenAttributeExists('reason'),
            'employee' => $this->whenLoaded('employee', function () {
                return $this->employeeData;
            }),
            // 'employee_approves' => $this->whenLoaded('employeeApproves', function () {
            //     return new EmployeeApprovesCollection($this->employeeApproves ?? []);
            // }),
            'approval_cycle' => $this->checkRelationIfExists('employeeApproves') ?
                new ApprovalCycleCollection($this->getApprovalCycle($this, $this->employeeData))
                : null,
            'created_at' => $this->whenAttributeExists('created_at'),
            'edit_history' => $this->whenAttributeExists('edit_history'),
            'can_cancel' => $this->checkRelationIfExists('employeeApproves')
                && $this?->status != config('globals.REQUEST_STATUSES.CANCELLED')
                && $this?->status != config('globals.REQUEST_STATUSES.REJECTED')
                && $this->canCancel($this->employeeApproves, $this->employeeRequest->status),
            'actionable' => $this->status == 'pending' && $this->isActionable($this->employeeApproves, $this->employeeRequest->status),
            'decider_admin' => $this->relationLoaded('employeeRequest') && isset($this->employeeRequest->deciderAdmin) ?
                new EmployeesInApprovalCycleResource($this->employeeRequest->deciderAdmin) : null,
        ];
    }
}
