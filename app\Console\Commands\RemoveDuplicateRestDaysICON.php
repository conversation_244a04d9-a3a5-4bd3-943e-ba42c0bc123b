<?php

namespace App\Console\Commands;

use App\Models\EmployeeLeaveRequest;
use Illuminate\Console\Command;

class RemoveDuplicateRestDaysICON extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-duplicate-rest-days-icon';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove duplicate rest day leave requests';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $restdays = EmployeeLeaveRequest::where('company_leave_type_id', 624)->get();

        $grouped = $restdays->groupBy(function ($item) {
            return $item->employee_id.'_'.$item->from.'_'.$item->to;
        });

        foreach ($grouped as $group) {
            $duplicates = $group->skip(1);
            foreach ($duplicates as $duplicate) {
                $duplicate->delete();
            }
        }

        $this->info('Duplicate rest day leave requests have been removed.');
    }
}
