<?php

namespace App\Console\Commands;

use App\Jobs\OvertimeCalculationService;
use App\Models\Cico;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixOffBranchCico extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-off-branch-cico {company_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companyId = $this->argument('company_id') ?? null;
        DB::beginTransaction();
        try {
            $this->info('Starting to fix off branch cico');

            $cicos = Cico::where('in_out', 'in')->where('status', 'verified')
                ->whereHas('clockOut', function ($query) {
                    $query->where('status', 'unverified');
                })
                ->when(! is_null($companyId), function ($query) use ($companyId) {
                    $query->whereHas('employee', function ($query) use ($companyId) {
                        $query->where('company_id', $companyId);
                    });
                })
                ->get();

            foreach ($cicos as $cico) {
                $cico->clockOut->status = 'verified';
                $cico->clockOut->save();
                $attendance = $cico->attendance;
                if ($attendance) {
                    $attendance->co_id = $cico->clockOut->id;
                    $attendance->save();
                } else {
                    $this->error('Attendance not found for cico '.$cico->id);

                    continue;
                }

                $this->info('Dispatching overtime calculation job for attendance '.$attendance->id);

                dispatch(new OvertimeCalculationService($attendance->employee_id, $attendance->date))
                    ->onConnection(config('globals.OVERTIME_JOB.CONNECTION'))
                    ->onQueue(config('globals.OVERTIME_JOB.QUEUE'))->afterCommit();

                $this->info('deleting the wrong entity tags');
                $attendance->entityTags()->where('entity_type', 'attendance')
                    ->where('entity_id', $attendance->id)
                    ->where('tag', 'no_clock_out')
                    ->delete();
            }

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            $this->error('Error: '.$th);
        }
    }
}
