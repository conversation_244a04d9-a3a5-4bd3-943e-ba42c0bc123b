<?php

namespace App\Console\Commands;

use App\Imports\EventTitlesMinutesImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class EventTitleMinutes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:event-titles-minutes {file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            // $s3Path = $this->argument('file');
            // $fileContents = Storage::disk('s3')->get($s3Path);
            // $tempDir = tempnam(sys_get_temp_dir(), 'titles_working_minutes.xlsx');
            // file_put_contents($tempDir, $fileContents);

            $this->info('adding employee event title minutes');
            $file = $this->argument('file');
            $filePath = storage_path($file);

            if (! file_exists($filePath)) {
                $this->error("File not found: $filePath");

                return 1;
            }

            Excel::import(new EventTitlesMinutesImport, $filePath);
            $this->info('Excel sheet parsed successfully.');
            // unlink($tempDir);
            DB::commit();
        } catch (\Exception $e) {
            Log::info($e);
            DB::rollBack();
            $this->error($e->getMessage());
        }
    }
}
