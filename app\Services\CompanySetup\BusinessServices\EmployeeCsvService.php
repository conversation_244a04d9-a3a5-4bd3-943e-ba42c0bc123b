<?php

namespace App\Services\CompanySetup\BusinessServices;

use App\CsvEntity\EmployeeCsvEntity;
use App\DomainData\EmployeeDto;
use App\Enums\Employee\EmployeeBulkOperationTypeEnum;
use App\Enums\Employee\EmployeeExportFieldEnum;
use App\Enums\EmployeeInfo\ContractDurationEnum;
use App\Enums\EmployeeInfo\DegreeTypeEnum;
use App\Enums\EmployeeInfo\EmploymentTypeEnum;
use App\Enums\EmployeeInfo\GenderEnum;
use App\Enums\EmployeeInfo\MaritalStatusEnum;
use App\Enums\EmployeeInfo\MilitaryStatus;
use App\Enums\EmployeeInfo\Nationalities;
use App\Enums\EmployeeInfo\ReligionEnum;
use App\Enums\EmployeeInfo\TrainingCertificationStatusEnum;

use App\Exports\MultiSheetExport;
use App\Exports\DynamicMultiSheetExport;
use App\Imports\DynamicEmployeeImport;
use App\Repositories\Repository;
use App\Repositories\V1\CompanySettings\TitleRepository;
use App\Repositories\V1\BranchRepository;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\NewEmployeeInfoRepository;
use App\Services\CompanySetup\CrudServices\EmployeeCrudService;
use App\Services\CompanySetup\EmployeesService;
use App\Services\IBusinessService;
use App\Services\PayrollSetup\SystemSettingsService;
use App\Services\V1\EmployeeProfile\EmployeeProfileService;

use App\Traits\DateTimeHelper;
use App\Traits\StringLanguageValidation;
use App\Traits\V1\PhoneHelper;
use Illuminate\Support\Facades\Validator;
use App\Rules\BulkUniqueValueRule;
use App\Rules\IsArabic;
use App\Rules\IsEnglish;
use App\Rules\NationalIdRule;
use App\Rules\PhoneNumberByCountryRule;
use App\Rules\UniqueNationalIdRule;
use App\Rules\UniquePassportNumberRule;
use App\Rules\UniquePersonalEmailRule;
use App\Rules\UniqueWorkEmailRule;
use App\Enums\EmployeeInfo\GenderEnum as EmployeeGenderEnum;
use App\Enums\EmployeeInfo\ReligionEnum as EmployeeReligionEnum;
use App\Enums\EmployeeInfo\MaritalStatusEnum as EmployeeMaritalStatusEnum;
use App\Enums\EmployeeInfo\MilitaryStatus as EmployeeMilitaryStatus;
use App\Enums\EmployeeInfo\EmploymentTypeEnum as EmployeeEmploymentTypeEnum;
use App\Enums\EmployeeInfo\DegreeTypeEnum as EmployeeDegreeTypeEnum;
use App\Models\EmployeeEmergencyContact;
use App\Repositories\V1\Employee\EmployeeEmergencyContactRepository;
use Illuminate\Validation\Rules\Enum;
use App\Traits\GenerateRandomString;
use App\Traits\Validators;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class EmployeeCsvService implements IBusinessService
{
    use EmployeeDto, GenerateRandomString, Validators, DateTimeHelper, PhoneHelper, StringLanguageValidation;

    private array $rules;

    private array $updatedRules;

    private ?string $companyCode;

    private array $employees;

    private Repository $employeeRepository;

    private array $employeeNumbers;

    private array $employeePhones;

    private array $employeeNationalIds;

    private array $employeesDictionary;

    private array $rootEmployees;

    private array $dbManagerEmployees;

    private Repository $titleRepository;

    private TitleRepository $newTitleRepository;

    private EmployeeEmergencyContactRepository $emergencyContactRepository;

    private array $titles;

    private array $branches;

    private Repository $cityRepository;

    private array $cities;

    private Repository $governorateRepository;

    private array $governorates;

    private int $recordCreated;

    private Repository $branchRepository;

    private BranchRepository $newBranchRepository;

    private array $titlesMapAr = [];
    private array $titlesMapEn = [];
    private array $branchesMapAr = [];
    private array $branchesMapEn = [];

    private array $existingEmployeeNumbers = [];
    private array $existingNationalIds = [];
    private array $existingPassportNumbers = [];
    private array $existingPhones = [];
    private array $existingWorkEmails = [];
    private array $existingPersonalEmails = [];
    private array $phoneToCurrentValues = [];
    private array $phoneToEmployeeNumberMap = [];
    private array $phoneToNationalIdMap = [];
    private array $phoneToPassportNumberMap = [];
    private array $phoneToWorkEmailMap = [];
    private array $phoneToPersonalEmailMap = [];
    private array $nationalitiesMapEn = [];
    private array $nationalitiesMapAr = [];
    private array $genderMapEn = [];
    private array $genderMapAr = [];
    private array $militaryStatusMapEn = [];
    private array $militaryStatusMapAr = [];
    private array $religionMapEn = [];
    private array $religionMapAr = [];
    private array $maritalStatusMapEn = [];
    private array $maritalStatusMapAr = [];
    private array $employmentTypeMapEn = [];
    private array $employmentTypeMapAr = [];
    private array $degreeTypeMapEn = [];
    private array $degreeTypeMapAr = [];
    private array $contractDurationMapEn = [];
    private array $contractDurationMapAr = [];

    private ?string $cachedEmployeeCodeType = null;

    public function __construct(
        private EmployeeCrudService $employeeCrudService,
        private EmployeeCsvEntity $employeeCsvEntity,
        private EmployeesService $employeesService,
        private SystemSettingsService $systemSettingsService,
        private EmployeeProfileService $employeeProfileService,
        TitleRepository $newTitleRepository,
        BranchRepository $newBranchRepository,
        private NewEmployeeRepository $newEmployeeRepository,
        private NewEmployeeInfoRepository $newEmployeeInfoRepository,
        EmployeeEmergencyContactRepository $emergencyContactRepository,
    )
    {
        $this->rules = $this->getRules([
        ]);

        $this->employeesDictionary = [];
        $this->rootEmployees = [];
        $this->dbManagerEmployees = [];
        $this->companyCode = null;
        $this->employeeRepository = Repository::getRepository('Employee');
        $this->titleRepository = Repository::getRepository('Title');
        $this->branchRepository = Repository::getRepository('Branch');
        $this->newTitleRepository = $newTitleRepository;
        $this->newBranchRepository = $newBranchRepository;
        $this->emergencyContactRepository = $emergencyContactRepository;
        $this->cityRepository = Repository::getRepository('City');
        $this->governorateRepository = Repository::getRepository('Governorate');
        $this->recordCreated = 0;
    }

    public function isValid(array $request, \stdClass &$output): bool
    {
        $output->Error = [];
        $employees = $request;
        if (count($employees) == 0) {
            $output->Error = ['file is empty', 'الملف فارغ'];
            return false;
        }

        foreach ($employees as $key => $row) {
            $mappedEmployee = [];

            try {
                $employeeCodeType = $this->systemSettingsService->getCompanyEmployeeCodeType();
                $this->employeeCsvEntity->setEmployeeCodeType($employeeCodeType);
                $mappedEmployee = $this->employeeCsvEntity->AppendToEmployeeArray($employees[$key]);

            } catch (Exception $e) {
                $output->Error = array_merge($output->Error, ['Can not parse data'.' at row number# '.$key + 2 .($e->getCode() == 404 ? ' => wrong date format' : '').'  '.$e->getMessage().'  '.PHP_EOL]);

                continue;
            }
            // all validations are in employeesService

            // validate employee title
            if (isset($mappedEmployee['title']) && ! empty($mappedEmployee['title']) && ! isset($this->titles[$mappedEmployee['title']])) {
                $output->Error = array_merge($output->Error, ['Title: '.$mappedEmployee['title'].' not defined before, at row number# '.$key + 2]);
            }

            // validate employee main branch
            if (isset($mappedEmployee['branch']) && ! empty($mappedEmployee['branch']) && ! isset($this->branches[$mappedEmployee['branch']])) {

                $output->Error = array_merge($output->Error, ['Branch: '.$mappedEmployee['branch'].' not defined before, at row number# '.$key + 2]);
            }

            // validate employee branches
            if (isset($mappedEmployee['branches']) && $mappedEmployee['branches'] != '') {
                $tmpBranches = explode(',', $mappedEmployee['branches']);
                // if input branch matches original branch(case insensitive) then assign it to the original branch to be in the same case
                $allBranchesCorrect = true;
                foreach ($tmpBranches as $key => $inputBranch) {
                    if ($inputBranch == '') {
                        break;
                    }
                    $correctBranch = false;
                    foreach ($this->branches as $branch) {
                        if (strcasecmp($inputBranch, $branch['name']) == 0) { // matches with case insensitive
                            $correctBranch = true;
                            $tmpBranches[$key] = $branch['name'];
                        }
                    }
                    if ($correctBranch == false) { // this input branch doesn't match any branch in database(case insensitive)
                        $output->Error = array_merge($output->Error, ['Branch: '.$inputBranch.' not defined before, at row number# '.$key + 2]);
                        $allBranchesCorrect = false;

                    }
                }
                if ($allBranchesCorrect) {
                    $mappedEmployee['branches'] = $tmpBranches;
                }

            }

            //            if (isset($mappedEmployee['nationality'])) {
            //                $nationality = $mappedEmployee['nationality'];
            //                $nationalityParts = explode(' - ', $nationality);
            //                $englishNationality = strtolower(end($nationalityParts));
            //                $mappedEmployee['nationality'] = $englishNationality;
            //
            //                if ($englishNationality === 'egyptian') {
            //                    if (empty($mappedEmployee['national_id'])) {
            //                        $output->Error = array_merge($output->Error, ['national id is not defined at row number# ' . $key + 2]);
            //                    }
            //                } else {
            //                    if (empty($mappedEmployee['passport_number'])) {
            //                        $output->Error = array_merge($output->Error, ['passport number is not defined at row number# ' . $key + 2]);
            //                    }
            //                }
            //            }

            if (isset($mappedEmployee['military_status'])) {
                $militaryStatus = $mappedEmployee['military_status'];
                // Check if it's a legacy bilingual value (contains ' - ')
                if (strpos($militaryStatus, ' - ') !== false) {
                    // Use the enum to convert from legacy value to the new format
                    $enumCase = MilitaryStatus::fromLegacyValue($militaryStatus);
                    if ($enumCase) {
                        $mappedEmployee['military_status'] = $enumCase->value;
                    }
                }
            }

            $fourthName = isset($mappedEmployee['fourth_name_ar']) ? ' '.trim($mappedEmployee['fourth_name_ar']) : '';
            $fifthName = isset($mappedEmployee['fifth_name_ar']) ? ' '.trim($mappedEmployee['fifth_name_ar']) : '';
            $name = trim(trim($mappedEmployee['first_name_ar']).' '.trim($mappedEmployee['second_name_ar']).' '.trim($mappedEmployee['third_name_ar']).$fourthName.$fifthName);
            $employees[$key]['name'] = $name;
            $mappedEmployee['name'] = $name;

            // employee with no managers to root employess array
            $this->rootEmployees[$mappedEmployee['phone']] = $mappedEmployee;
            // collect all employee names
            if (! isset($this->employeesDictionary[$mappedEmployee['phone']])) {
                $this->employeesDictionary[$mappedEmployee['phone']] = new EmployeeNode($mappedEmployee, $key + 2);
            }

            if (! isset($employees[$key]['country_code']) || trim($employees[$key]['country_code']) === '') {
                $output->Error = array_merge($output->Error, ['Country Code is not defined at row number# '.($key + 2)]);
            }

            if (isset($employees[$key]['country_code']) && ! str_starts_with($employees[$key]['country_code'], '+')) {
                $output->Error = array_merge($output->Error, ['Country Code should start with + at row number# '.($key + 2)]);
            }
        }

        // $this->employeesDictionary = array_merge($this->employeesDictionary, $this->dbManagerEmployees);
        if (isset($output->Error) && count($output->Error) > 0) {
            return false;
        }
        if (isset($output->Error)) {
            unset($output->Error);
        }

        return true;
    }

    public function perform(array $request, \stdClass &$output): void
    {
        $employeesArray = Excel::toArray($this->employeeCsvEntity, request()->file('csv_file'));
        $employeesArray = $employeesArray[0];
        $this->companyCode = config('globals.company')->company_code;
        $this->employeeNumbers = $this->employeeRepository->getAll()->get()->keyBy('employee_number')->toArray();
        $this->employeePhones = $this->employeeRepository->getAll()->get()->keyBy('phone')->toArray();
        $this->employeeNationalIds = $this->employeeRepository->getAll()->get()->keyBy('national_id')->toArray();
        $this->employees = $this->employeeRepository->getAll()->get()->keyBy('name_ar')->toArray();
        $this->titles = $this->titleRepository->getAll()->get()->keyBy('name_ar')->toArray();
        $this->branches = $this->branchRepository->getAll()->get()->keyBy('name_ar')->toArray();
        $this->cities = $this->cityRepository->getAll()->get()->keyBy('name')->toArray();
        $this->governorates = $this->governorateRepository->getAll()->get()->keyBy('name')->toArray();
        //        $this->is_trackable = $this->employeeRepository->getAll()->pluck('is_trackable')->first();
        if (! $this->isValid($employeesArray, $output)) {
            return;
        }

        foreach ($this->rootEmployees as $rootname => $value) {
            $this->addEmployee($this->employeesDictionary[$rootname], $output);
        }
        $output->EmployeeCreated = [$this->recordCreated.' Employees records created'];

    }

    private function addEmployee(EmployeeNode &$employeeNode, \stdClass &$output): void
    {

        if ($employeeNode->needInsert) {
            $employeeData = $employeeNode->baseEmployeeArray;
            if (! isset($employeeData['phone']) || empty($employeeData['phone'])) {
                return;
            }
            // title
            if (isset($employeeData['title']) && ! empty($employeeData['title'])) {
                $employeeData['title_id'] = $this->titles[$employeeData['title']]['id'];
            }
            // main branch
            if (isset($employeeData['branch']) && ! empty($employeeData['branch'])) {
                $employeeData['branch_id'] = $this->branches[$employeeData['branch']]['id'];
            }
            // employee branches
            $employeeData['branch_ids'] = [];
            if (isset($employeeData['branches']) && $employeeData['branches'] != '') {
                $inputBranches = [];
                foreach ($employeeData['branches'] as $branch) {
                    array_push($inputBranches, $this->branches[$branch]['id']);
                }
                $employeeData['additional_branch_ids'] = $inputBranches;
            }
            if ($this->systemSettingsService->getCompanyEmployeeCodeType() === 'auto') {
                $employeeData['employee_number'] = $this->employeesService->getRecentEmployeeCode();
            }
            $employeeData = $this->fixEmployeeData($employeeData);

            $employeeWithSamePhone = $this->employeeRepository->getByKey('phone', $employeeData['phone'] ?? null)->first();
            $employeeWithSamePhoneWithoutGlobalScopes = $this->employeeRepository->getWithPhoneWithoutGlobalScope($employeeData['phone'] ?? null);
            $this->updatedRules = $this->getCsvRules($this->rules, $employeeData['country_code']);
            $validator = \Validator::make($employeeData, $this->updatedRules);
            if (! isset($employeeWithSamePhoneWithoutGlobalScopes) && $validator->fails()) { // if employee with same phone exists then it's an update not add
                if (! isset($output->Error)) {
                    $output->Error = [];
                }
                $output->Error = array_merge($output->Error, $this->failMessages($validator->messages(), 'at row number# '.$employeeNode->row));
            }

            try {
                $payroll = config('globals.company')?->latestPayroll->first() ?? null;
                $employeeData['status'] = EmployeesService::getEmployeeStatus($employeeData['join_date'] ?? null, $payroll);
                if (isset($employeeWithSamePhone)) {
                    unset($employeeData['title_id'], $employeeData['title']);
                    $this->employeesService->update($employeeWithSamePhone->id, $employeeData);
                    Log::info('update');

                } elseif (! isset($employeeWithSamePhone) && ! isset($employeeWithSamePhoneWithoutGlobalScopes)) {
                    unset($employeeData['country_code']);
                    $this->employeesService->add($employeeData);
                    Log::info('add');

                } elseif (isset($employeeWithSamePhoneWithoutGlobalScopes)) {
                    if (! isset($output->Error)) {
                        $output->Error = [];
                        Log::info('error');

                    }
                    $output->Error = array_merge($output->Error, ['Employee with phone number: '.$employeeData['phone'].' already exists on another company, at row number# '.$employeeNode->row]);
                }
            } catch (Exception $e) {
                if (! isset($output->Error)) {
                    $output->Error = [];
                }
                Log::error($e);
                $output->Error = array_merge($output->Error, [$e->getMessage().' at row number# '.$employeeNode->row]);

                return;
            }
            $this->recordCreated += 1;
        }

    }

    private function createUserCode(int $digits): string
    {
        $generatedCode = '';
        $existCode = true;
        while ($existCode) {
            $generatedCode = $this->generateRandomDigits($digits);
            $existCode = isset($this->employeeNumbers[$this->companyCode.$generatedCode]);
        }

        return $this->companyCode.$generatedCode;
    }

    public function download(array $request, \stdClass &$output): void
    {
        $branches = $this->branchRepository->getAll()->get('name_ar')->toArray();
        $titles = $this->titleRepository->getAll()->get('name_ar')->toArray();
        $nationalities = Nationalities::getLegacyValues();
        $militaryStatuses = MilitaryStatus::getLegacyValues();
        $trainingCertificationStatuses = TrainingCertificationStatusEnum::all();
        $maxCount = max(count($branches), count($titles), count($nationalities), count($militaryStatuses));

        $autoFill = [];
        for ($i = 0; $i < $maxCount; $i += 1) {
            $autoFill[] = [
                'Company Branches' => $branches[$i]['name_ar'] ?? null,
                'Company Titles' => $titles[$i]['name_ar'] ?? null,
                'Nationality' => $nationalities[$i % count($nationalities)],
                'Military Status' => $militaryStatuses[$i % count($militaryStatuses)],
                'Training Certification Status' => $trainingCertificationStatuses[$i % count($trainingCertificationStatuses)],
            ];
        }

        // Add gender values
        $autoFill[0]['Gender'] = 'male';
        $autoFill[1]['Gender'] = 'female';

        // Prepare data for Excel
        $data['counters'] = [
            'branches_count' => count($branches),
            'titles_count' => count($titles),
            'nationality_count' => count($nationalities),
            'military_status_count' => count($militaryStatuses),
            'training_certification_status_count' => count($trainingCertificationStatuses),
        ];
        $data['auto_fill'] = $autoFill;
        $data['employee_code_type'] = $this->systemSettingsService->getCompanyEmployeeCodeType();

        // Export to Excel
        $output->return_link = Excel::download(new MultiSheetExport($data), 'employees.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function downloadDynamicTemplate(array $request, \stdClass &$output, $employees = null): void
    {
        $userLang = config('globals.lang') ?? 'ar';
        $nameField = $userLang === 'en' ? 'name_en' : 'name_ar';

        $branches = $this->branchRepository->getAll()->pluck($nameField)->toArray();
        $titles = $this->titleRepository->getAll()->pluck($nameField)->toArray();
        $nationalities = Nationalities::getTranslatedValues();
        $militaryStatuses = MilitaryStatus::getTranslatedValues();
        $genderValues = GenderEnum::getTranslatedValues();
        $religionValues = ReligionEnum::getTranslatedValuesForBulkTemplate();
        $maritalStatusValues = MaritalStatusEnum::getTranslatedValues();
        $employmentTypeValues = EmploymentTypeEnum::getTranslatedValues();
        $degreeTypeValues = DegreeTypeEnum::getTranslatedValues();
        $contractDurationValues = ContractDurationEnum::getTranslatedValues();

        $branchesCount = count($branches);
        $titlesCount = count($titles);
        $nationalitiesCount = count($nationalities);
        $militaryStatusesCount = count($militaryStatuses);
        $genderCount = count($genderValues);
        $religionCount = count($religionValues);
        $maritalStatusCount = count($maritalStatusValues);
        $employmentTypeCount = count($employmentTypeValues);
        $degreeTypeCount = count($degreeTypeValues);
        $contractDurationCount = count($contractDurationValues);

        $maxCount = max(
            $branchesCount,
            $titlesCount,
            $nationalitiesCount,
            $militaryStatusesCount,
            $genderCount,
            $religionCount,
            $maritalStatusCount,
            $employmentTypeCount,
            $degreeTypeCount,
            $contractDurationCount
        );

        $dropdownData = [];
        for ($i = 0; $i < $maxCount; $i += 1) {
            $dropdownData[] = [
                $i < $branchesCount ? $branches[$i] : '',
                $i < $titlesCount ? $titles[$i] : '',
                $i < $nationalitiesCount ? $nationalities[$i] : '',
                $i < $militaryStatusesCount ? $militaryStatuses[$i] : '',
                $i < $genderCount ? $genderValues[$i] : '',
                $i < $religionCount ? $religionValues[$i] : '',
                $i < $maritalStatusCount ? $maritalStatusValues[$i] : '',
                $i < $employmentTypeCount ? $employmentTypeValues[$i] : '',
                $i < $degreeTypeCount ? $degreeTypeValues[$i] : '',
                $i < $contractDurationCount ? $contractDurationValues[$i] : '',
            ];
        }

        // Get the company's country dial code for default values
        $companyCountryDialCode = auth()->user()->company->country->dial_code ?? '';

        $data = [
            'counters' => [
                'branches_count' => $branchesCount,
                'titles_count' => $titlesCount,
                'nationality_count' => $nationalitiesCount,
                'military_status_count' => $militaryStatusesCount,
                'gender_count' => $genderCount,
                'religion_count' => $religionCount,
                'marital_status_count' => $maritalStatusCount,
                'employment_type_count' => $employmentTypeCount,
                'degree_type_count' => $degreeTypeCount,
                'contract_duration_count' => $contractDurationCount,
            ],
            'dropdown_data' => $dropdownData,
            'employee_code_type' => $this->systemSettingsService->getCompanyEmployeeCodeType(),
            'selected_fields' => $request,
            'operation_type' => $request['type'],
            'company_country_dial_code' => $companyCountryDialCode,
            'employees' => $employees
        ];

        $output->return_link = Excel::download(new DynamicMultiSheetExport($data), 'employees.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function fixEmployeeData($data)
    {
        $data['company_id'] = auth()->user()->company_id;

        foreach ($data as $key => $value) {
            // Keep fields with 'false' or other default values
            if ($value === '' || ! isset($value) || (is_array($value) && empty($value))) {
                // Do not unset fields that are explicitly set to 'false'
                //                if ($key === 'is_trackable') {
                //                    continue;
                //                }
                unset($data[$key]);
            }
        }

        if (isset($data['first_name_ar']) && isset($data['second_name_ar']) && isset($data['third_name_ar'])) {
            $data['name_ar'] = $data['first_name_ar'].' '.$data['second_name_ar'].' '.$data['third_name_ar'];
        }

        if (isset($data['first_name_en']) && isset($data['second_name_en']) && isset($data['third_name_en'])) {
            $data['name_en'] = $data['first_name_en'].' '.$data['second_name_en'].' '.$data['third_name_en'];
        }

        if (isset($data['first_name_ar'])) {
            $data['first_name'] = $data['first_name_ar']; // TODO these line to be removed after we drop old name columns
            $data['first_name_en'] = $data['first_name_en'] ?? $data['first_name_ar'];
        }

        if (isset($data['second_name_ar'])) {
            $data['second_name'] = $data['second_name_ar']; // TODO these line to be removed after we drop old name columns
            $data['second_name_en'] = $data['second_name_en'] ?? $data['second_name_ar'];
        }

        if (isset($data['third_name_ar'])) {
            $data['third_name'] = $data['third_name_ar']; // TODO these line to be removed after we drop old name columns
            $data['third_name_en'] = $data['third_name_en'] ?? $data['third_name_ar'];
        }

        if (isset($data['name_ar'])) {
            $data['name'] = $data['name_ar'];
        } // TODO these line to be removed after we drop old name columns

        if (isset($data['first_name_en']) && isset($data['second_name_en']) && isset($data['third_name_en'])) {
            $data['name_en'] = $data['first_name_en'].' '.$data['second_name_en'].' '.$data['third_name_en'];
        }

        if (isset($data['phone'])) {

            $countryCode = $data['country_code'];

            $formattedPhone = $countryCode.
                (str_starts_with($data['phone'], '0') && $countryCode == '+20'
                    ? substr($data['phone'], 1)
                    : $data['phone']);
            $data['phone'] = $formattedPhone;
        }

        return $data;
    }

    private function mapExcelRowToProfileData(array $headers, array $row, string $operationType, array $fieldMapping = []): array
    {
        $mappedData = [];

        $rowData = array_combine($headers, $row);

        if (empty($fieldMapping)) {
            $fieldMapping = $this->createFieldMapping($headers);
        }

        if (isset($fieldMapping['employee_name_ar'])) {
            $nameParts = explode(' ', trim($rowData[$fieldMapping['employee_name_ar']]));
            $mappedData['first_name_ar'] = $nameParts[0] ?? null;
            $mappedData['second_name_ar'] = $nameParts[1] ?? null;
            $mappedData['third_name_ar'] = $nameParts[2] ?? null;
            $mappedData['fourth_name_ar'] = $nameParts[3] ?? null;
            $mappedData['fifth_name_ar'] = $nameParts[4] ?? null;
        }

        if (isset($fieldMapping['employee_name_en'])) {
            $namePartsEn = explode(' ', trim($rowData[$fieldMapping['employee_name_en']]));
            $mappedData['first_name_en'] = $namePartsEn[0] ?? null;
            $mappedData['second_name_en'] = $namePartsEn[1] ?? null;
            $mappedData['third_name_en'] = $namePartsEn[2] ?? null;
            $mappedData['fourth_name_en'] = $namePartsEn[3] ?? null;
            $mappedData['fifth_name_en'] = $namePartsEn[4] ?? null;
        }

        if (isset($fieldMapping['employee_code'])) {
            $mappedData['employee_number'] = $rowData[$fieldMapping['employee_code']];
        }

        if (isset($fieldMapping['job_title'])) {
            $titleName = $rowData[$fieldMapping['job_title']];
            $title = $this->findTitleByName($titleName);
            if ($title) {
                $mappedData['title_id'] = $title['id'];
                if ($title['role_id']) {
                    $mappedData['role_id'] = $title['role_id'];
                }
                if ($title['department_id']) {
                    $mappedData['managed_department_ids'] = [$title['department_id']];
                }
                if ($title['sub_department_id']) {
                    $mappedData['managed_sub_department_ids'] = [$title['sub_department_id']];
                }
            }
        }

        if (isset($fieldMapping['location'])) {
            $branchName = $rowData[$fieldMapping['location']];
            $branch = $this->findBranchByName($branchName);
            if ($branch) {
                $mappedData['branch_id'] = $branch['id'];
                $mappedData['managed_branch_ids'] = [$branch['id']];
            }
        }

        if ($operationType === EmployeeBulkOperationTypeEnum::EDIT->value) {
            if (isset($fieldMapping['primary_phone'])) {
                $phone = $rowData[$fieldMapping['primary_phone']];
                if (!empty($phone)) {
                    $mappedData['phone'] = $this->trimStringSpaces($phone);
                }
            }
        } else {
            if (isset($fieldMapping['primary_phone_country_code']) && isset($fieldMapping['primary_phone'])) {
                $countryCode = $rowData[$fieldMapping['primary_phone_country_code']];
                $phone = $rowData[$fieldMapping['primary_phone']];
                if (!empty($phone)) {
                    $mappedData['phone_country_code'] = $countryCode;
                    $mappedData['phone'] = $this->trimStringSpaces($phone);
                }
            }
        }

        $this->mapBasicDetails($rowData, $mappedData, $fieldMapping);

        $this->mapContactDetails($rowData, $mappedData, $fieldMapping);

        $this->mapEducationDetails($rowData, $mappedData, $fieldMapping);

        $this->mapEmploymentDetails($rowData, $mappedData, $fieldMapping);

        $mappedData['is_trackable'] = true;
        Log::debug('Mapped data: ' . json_encode($mappedData));
        return $mappedData;
    }

    public function performBulkUpload(array $request, \stdClass &$output): void
    {
        try {
            $operationType = $request['type'];
            $excelFile = request()->file('excel_file');

            $fileSize = $excelFile->getSize() / 1024;
            $maxSize = config('globals.MAX_EXCEL_FILE_SIZE_KB', 15360);

            if ($fileSize > $maxSize) {
                $output->Error = [
                    __('validation.file_size_too_large', ['max' => $maxSize . 'KB'])
                ];
                return;
            }

            $excelData = Excel::toArray(new DynamicEmployeeImport(), $excelFile);

            if (empty($excelData) || empty($excelData[0])) {
                $output->Error = [
                    __('validation.file_empty')
                ];
                return;
            }
            if ($operationType === EmployeeBulkOperationTypeEnum::ADD->value) {
                $this->loadTitlesAndBranchesForBulkUpload();
            }

            $employees = $this->newEmployeeRepository->getEmployeesWithCurrentValuesForBulk();
            $this->loadCachedUniquenessData($employees);
            $this->loadEnumLookupMaps();

            if ($operationType === EmployeeBulkOperationTypeEnum::EDIT->value) {
                $this->loadPhoneToCurrentValuesMapping($employees);
            }

            $employeeData = $excelData[0];
            $headers = array_shift($employeeData);

            $fieldMapping = $this->createFieldMapping($headers);

            $validatedMappedData = [];
            if (!$this->isValidBulkUpload($employeeData, $headers, $operationType, $output, $validatedMappedData, $fieldMapping)) {
                return;
            }

            // PHASE 2: If all validations pass, process records with transaction using cached mapped data
            DB::beginTransaction();

            $processedCount = 0;
            $errors = [];
            $successfulEmployees = [];

            try {
                foreach ($validatedMappedData as $rowIndex => $mappedData) {
                    $rowNumber = $rowIndex + 2;
                    try {
                        if ($operationType === EmployeeBulkOperationTypeEnum::ADD->value) {
                            $employee = $this->employeeProfileService->addProfile($mappedData);
                            $successfulEmployees[] = ['employee_id' => $employee->id, 'row' => $rowNumber];
                            $processedCount++;
                        } elseif ($operationType === EmployeeBulkOperationTypeEnum::EDIT->value) {
                            $employee = $this->updateEmployeeFromBulkData($mappedData);
                            $successfulEmployees[] = ['employee_id' => $employee->id, 'row' => $rowNumber];
                            $processedCount++;
                        }
                    } catch (\Exception $e) {
                        $errors[] = $e->getMessage() . ' ' . __('validation.at_row_number', ['row' => $rowNumber]);
                        Log::error('Bulk upload error at row ' . $rowNumber, [
                            'error' => $e->getMessage(),
                            'mapped_data' => $mappedData
                        ]);
                    }
                }

                if (!empty($errors)) {
                    DB::rollBack();
                    $output->Error = array_merge(
                        [__('messages.bulk_upload_failed_with_errors')],
                        $errors
                    );
                    return;
                }
                DB::commit();

                $output->result = [
                    'processed_count' => $processedCount,
                    'successful_employees' => $successfulEmployees,
                    'operation_type' => $operationType,
                    'errors' => []
                ];

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('Bulk upload failed', [
                'error' => $e->getMessage(),
                'request' => $request
            ]);

            $output->Error = [
                __('messages.bulk_upload_failed') . ': ' . $e->getMessage()
            ];
        }
    }

    private function isValidBulkUpload(array $employeeData, array $headers, string $operationType, \stdClass &$output, array &$validatedMappedData = [], array $fieldMapping = []): bool
    {
        $output->Error = [];

        if (empty($employeeData)) {
            $output->Error = [
                __('validation.no_data_rows')
            ];
            return false;
        }

        $allErrors = [];
        $allMappedData = [];

        foreach ($employeeData as $rowIndex => $row) {
            $rowNumber = $rowIndex + 2;

            try {
                $mappedData = $this->mapExcelRowToProfileData($headers, $row, $operationType, $fieldMapping);
                if ($operationType === EmployeeBulkOperationTypeEnum::EDIT->value && empty($mappedData['phone'])) {
                    Log::debug("Stopping bulk edit processing at row {$rowNumber} - empty primary phone indicates end of data");
                    break;
                }

                if ($operationType === EmployeeBulkOperationTypeEnum::ADD->value && $this->isRecordEmpty($mappedData)) {
                    Log::debug("Skipping empty record at row {$rowNumber}");
                    continue;
                }

                // Store ALL mapped data for cross-row validation (even if individual validation fails)
                $allMappedData[$rowIndex] = $mappedData;

                $validationResult = $operationType === EmployeeBulkOperationTypeEnum::EDIT->value
                    ? $this->validateEmployeeDataForBulkEdit($mappedData)
                    : $this->validateEmployeeData($mappedData);
                if (!$validationResult['valid']) {
                    foreach ($validationResult['errors'] as $error) {
                        $allErrors[] = $error . ' ' . __('validation.at_row_number', ['row' => $rowNumber]);
                    }
                } else {
                    $validatedMappedData[$rowIndex] = $mappedData;
                }

            } catch (\Exception $e) {
                $allErrors[] = 'Cannot parse data: ' . $e->getMessage() . ' ' . __('validation.at_row_number', ['row' => $rowNumber]);
            }
        }
        if (empty($allMappedData)) {
            $output->Error = [
                __('validation.no_data_rows')
            ];
            return false;
        }
        // Run cross-row validation on ALL mapped data (including rows with individual validation errors)
        $crossRowValidationResult = $this->validateCrossRowUniqueness($allMappedData, $operationType);
        if (!$crossRowValidationResult['valid']) {
            foreach ($crossRowValidationResult['errors'] as $errors) {
                $allErrors = array_merge($allErrors, $errors);
            }
        }

        if (!empty($allErrors)) {
            $output->Error = $allErrors;
            return false;
        }

        if (isset($output->Error)) {
            unset($output->Error);
        }

        return true;
    }

    private function updateEmployeeFromBulkData(array $mappedData)
    {
        $primaryPhone = $mappedData['phone'];

        $employee = $this->newEmployeeRepository->getByPhone($primaryPhone);
        $employeeInfo = $employee->employeeInfo;

        $this->updateEmployeeBasicFields($employee, $mappedData);
        $this->updateEmployeeInfoFields($employeeInfo, $mappedData);
        $this->updateEmployeeContactFields($employeeInfo, $mappedData);
        $this->updateEmployeeEmergencyContacts($employee, $mappedData);
        $this->employeeProfileService->updateRelatedEntities($employee);
        $employee->save();
        $employeeInfo->save();

        return $employee;
    }

    private function updateEmployeeBasicFields($employee, array $mappedData): void
    {
        if (!empty($mappedData['employee_number']) && $this->cachedEmployeeCodeType === 'manual') {
            $employee->employee_number = $mappedData['employee_number'];
        }
        if (!empty($mappedData['national_id'])) {
            $employee->national_id = $mappedData['national_id'];
        }
    }

    private function updateEmployeeInfoFields($employeeInfo, array $mappedData): void
    {
        if (!empty($mappedData['nationality'])) {
            $employeeInfo->nationality = $mappedData['nationality'];
        }
        if (!empty($mappedData['passport_number'])) {
            $employeeInfo->passport_number = $mappedData['passport_number'];
        }
        if (!empty($mappedData['gender'])) {
            $employeeInfo->gender = $mappedData['gender'];
        }
        if (!empty($mappedData['birth_date'])) {
            $employeeInfo->birth_date = $mappedData['birth_date'];
        }
        if (!empty($mappedData['place_of_birth'])) {
            $employeeInfo->place_of_birth = $mappedData['place_of_birth'];
        }
        if (!empty($mappedData['address'])) {
            $employeeInfo->address = $mappedData['address'];
        }
        if (!empty($mappedData['military_status'])) {
            $employeeInfo->military_status = $mappedData['military_status'];
        }
        if (!empty($mappedData['religion'])) {
            $employeeInfo->religion = $mappedData['religion'];
            if ($mappedData['religion'] === EmployeeReligionEnum::OTHER->value) {
                $employeeInfo->other_religion = $mappedData['other_religion'] ?? null;
            } else {
                $employeeInfo->other_religion = null;
            }
        }
        if (!empty($mappedData['marital_status'])) {
            $employeeInfo->marital_status = $mappedData['marital_status'];
        }
        if (isset($mappedData['number_kids'])) {
            $employeeInfo->number_kids = $mappedData['number_kids'];
        }
        if (isset($mappedData['years_of_experience'])) {
            $employeeInfo->number_of_years_of_experience = $mappedData['years_of_experience'];
        }
        if (!empty($mappedData['join_date'])) {
            $employeeInfo->join_date = $mappedData['join_date'];
        }
        if (!empty($mappedData['employment_type'])) {
            $employeeInfo->employment_type = $mappedData['employment_type'];
        }
    }

    private function updateEmployeeContactFields($employeeInfo, array $mappedData): void
    {
        if (!empty($mappedData['secondary_phone']) && !empty($mappedData['secondary_phone_country_code'])) {
            $employeeInfo->secondary_phone = $this->formatPhoneWithCountryCode(
                $mappedData['secondary_phone_country_code'],
                $mappedData['secondary_phone']
            );
        }

        if (!empty($mappedData['work_email'])) {
            $employeeInfo->email = $mappedData['work_email'];
        }
        if (!empty($mappedData['personal_email'])) {
            $employeeInfo->personal_email = $mappedData['personal_email'];
        }
    }



    private function updateEmployeeEmergencyContacts($employee, array $mappedData): void
    {
        if (isset($mappedData['emergency_contacts']) && is_array($mappedData['emergency_contacts']) && !empty($mappedData['emergency_contacts'])) {
            $emergencyContactData = $mappedData['emergency_contacts'][0];

            if (!empty($emergencyContactData['name']) ||
                !empty($emergencyContactData['phone']) ||
                !empty($emergencyContactData['relation'])) {

                $emergencyContact = $employee->emergencyContacts->first();
                if (!$emergencyContact) {
                    $emergencyContact = new EmployeeEmergencyContact();
                    $emergencyContact->employee_id = $employee->id;
                    $emergencyContact->company_id = config('globals.company')->id;
                }

                if (!empty($emergencyContactData['name'])) {
                    $emergencyContact->name = $emergencyContactData['name'];
                }
                if (!empty($emergencyContactData['phone']) && !empty($emergencyContactData['phone_country_code'])) {
                    $emergencyContact->phone = $this->formatPhoneWithCountryCode(
                        $emergencyContactData['phone_country_code'],
                        $emergencyContactData['phone']
                    );
                }
                if (!empty($emergencyContactData['relation'])) {
                    $emergencyContact->relation = $emergencyContactData['relation'];
                }

                $emergencyContact->save();
            }
        }
    }

    private function validateCrossRowUniqueness(array $validatedMappedData, $operationType = null): array
    {
        $nationalIds = [];
        $phones = [];
        $employeeCodes = [];
        $passportNumbers = [];
        $workEmails = [];
        $personalEmails = [];
        $allErrors = [];

        foreach ($validatedMappedData as $rowIndex => $mappedData) {
            $rowNumber = $rowIndex + 2;

            if (!empty($mappedData['national_id'])) {
                $nationalId = $mappedData['national_id'];
                if (isset($nationalIds[$nationalId])) {
                    $allErrors[] = __('validation.duplicate_national_id_in_excel', ['national_id' => $nationalId]) . ' ' . __('validation.at_row_number', ['row' => $rowNumber]);
                } else {
                    $nationalIds[$nationalId] = $rowNumber;
                }
            }

            if (!empty($mappedData['phone']) && !empty($mappedData['phone_country_code'])) {
                if($operationType === EmployeeBulkOperationTypeEnum::ADD->value) {
                    $fullPhone = $this->formatPhoneWithCountryCode($mappedData['phone_country_code'], $mappedData['phone']);
                } else {
                    $fullPhone = $mappedData['phone'];
                }
                if (isset($phones[$fullPhone])) {
                    $allErrors[] = __('validation.duplicate_phone_in_excel', ['phone' => $fullPhone]) . ' ' . __('validation.at_row_number', ['row' => $rowNumber]);
                } else {
                    $phones[$fullPhone] = $rowNumber;
                }
            }

            if (!empty($mappedData['employee_number'])) {
                $employeeCode = $mappedData['employee_number'];
                if (isset($employeeCodes[$employeeCode])) {
                    $allErrors[] = __('validation.duplicate_employee_code_in_excel', ['employee_code' => $employeeCode]) . ' ' . __('validation.at_row_number', ['row' => $rowNumber]);
                } else {
                    $employeeCodes[$employeeCode] = $rowNumber;
                }
            }

            if (!empty($mappedData['passport_number'])) {
                $passportNumber = $mappedData['passport_number'];
                if (isset($passportNumbers[$passportNumber])) {
                    $allErrors[] = __('validation.duplicate_passport_number_in_excel', ['passport_number' => $passportNumber]) . ' ' . __('validation.at_row_number', ['row' => $rowNumber]);
                } else {
                    $passportNumbers[$passportNumber] = $rowNumber;
                }
            }

            if (!empty($mappedData['work_email'])) {
                $workEmail = $mappedData['work_email'];
                if (isset($workEmails[$workEmail])) {
                    $allErrors[] = __('validation.duplicate_work_email_in_excel', ['work_email' => $workEmail]) . ' ' . __('validation.at_row_number', ['row' => $rowNumber]);
                } else {
                    $workEmails[$workEmail] = $rowNumber;
                }
            }

            if (!empty($mappedData['personal_email'])) {
                $personalEmail = $mappedData['personal_email'];
                if (isset($personalEmails[$personalEmail])) {
                    $allErrors[] = __('validation.duplicate_personal_email_in_excel', ['personal_email' => $personalEmail]) . ' ' . __('validation.at_row_number', ['row' => $rowNumber]);
                } else {
                    $personalEmails[$personalEmail] = $rowNumber;
                }
            }
        }

        return [
            'valid' => empty($allErrors),
            'errors' => $allErrors
        ];
    }

    private function mapBasicDetails(array $rowData, array &$mappedData, array $fieldMapping): void
    {

        if (isset($fieldMapping['nationality'])) {
            $nationality = $rowData[$fieldMapping['nationality']];
            $nationalityEnum = $this->findEnumByTranslatedValue(Nationalities::class, $nationality);
            if ($nationalityEnum) {
                $mappedData['nationality'] = $nationalityEnum->value;
            } else if (!empty($nationality)) {
                $mappedData['nationality'] = $nationality;
            }
        }

        if (isset($fieldMapping['national_id']) && !empty($rowData[$fieldMapping['national_id']])) {
            $mappedData['national_id'] = (string) $rowData[$fieldMapping['national_id']];
        }

        if (isset($fieldMapping['passport_number']) && !empty($rowData[$fieldMapping['passport_number']])) {
            $mappedData['passport_number'] = (string) $rowData[$fieldMapping['passport_number']];
        }

        if (isset($fieldMapping['gender'])) {
            $gender = $rowData[$fieldMapping['gender']];
            $genderEnum = $this->findEnumByTranslatedValue(EmployeeGenderEnum::class, $gender);
            if ($genderEnum) {
                $mappedData['gender'] = $genderEnum->value;
            } else if (!empty($gender)) {
                $mappedData['gender'] = $gender;
            }
        }

        if (isset($fieldMapping['date_of_birth'])) {
            $mappedData['birth_date'] = $this->parseDateFromExcel($rowData[$fieldMapping['date_of_birth']]);
            if(empty($mappedData['birth_date'])) {
                $mappedData['birth_date'] = $rowData[$fieldMapping['date_of_birth']];
            }
        }

        if (isset($fieldMapping['place_of_birth'])) {
            $mappedData['place_of_birth'] = $rowData[$fieldMapping['place_of_birth']];
        }

        if (isset($fieldMapping['address'])) {
            $mappedData['address'] = $rowData[$fieldMapping['address']];
        }

        if (isset($fieldMapping['military_status'])) {
            $militaryStatus = $rowData[$fieldMapping['military_status']];
            $militaryStatusEnum = $this->findEnumByTranslatedValue(EmployeeMilitaryStatus::class, $militaryStatus);
            if ($militaryStatusEnum) {
                $mappedData['military_status'] = $militaryStatusEnum->value;
            } else if (!empty($militaryStatus)) {
                $mappedData['military_status'] = $militaryStatus;
            }
        }

        if (isset($fieldMapping['religion'])) {
            $religion = $rowData[$fieldMapping['religion']];
            $religionEnum = $this->findEnumByTranslatedValue(EmployeeReligionEnum::class, $religion);
            if ($religionEnum && $religionEnum !== EmployeeReligionEnum::OTHER) {
                $mappedData['religion'] = $religionEnum->value;
                $mappedData['other_religion'] = null;
            } else if (!empty($religion)) {
                $otherReligion = $rowData[$fieldMapping['religion']];
                $mappedData['other_religion'] = $otherReligion;
                $mappedData['religion'] = EmployeeReligionEnum::OTHER->value;
            }
        }

        if (isset($fieldMapping['marital_status'])) {
            $maritalStatus = $rowData[$fieldMapping['marital_status']];
            $maritalStatusEnum = $this->findEnumByTranslatedValue(EmployeeMaritalStatusEnum::class, $maritalStatus);
            if ($maritalStatusEnum) {
                $mappedData['marital_status'] = $maritalStatusEnum->value;
            } else if (!empty($maritalStatus)) {
                $mappedData['marital_status'] = $maritalStatus;
            }
        }

        if (isset($mappedData['marital_status'])
        && $mappedData['marital_status'] !== MaritalStatusEnum::SINGLE->value
        && isset($fieldMapping['number_of_children'])) {
            $mappedData['number_kids'] = $rowData[$fieldMapping['number_of_children']];
        }

        if (isset($fieldMapping['years_of_experience'])) {
            $mappedData['years_of_experience'] = $rowData[$fieldMapping['years_of_experience']];
        }
    }

    private function mapContactDetails(array $rowData, array &$mappedData, array $fieldMapping): void
    {
        if (isset($fieldMapping['secondary_phone_country_code']) && isset($fieldMapping['secondary_phone'])) {
            $countryCode = $rowData[$fieldMapping['secondary_phone_country_code']];
            $phone = $rowData[$fieldMapping['secondary_phone']];
            if (!empty($phone)) {
                $mappedData['secondary_phone_country_code'] = $countryCode;
                $mappedData['secondary_phone'] = $this->trimStringSpaces($phone);
            }
        }

        if (isset($fieldMapping['work_email'])) {
            $mappedData['work_email'] = $rowData[$fieldMapping['work_email']];
        }

        if (isset($fieldMapping['personal_email'])) {
            $mappedData['personal_email'] = $rowData[$fieldMapping['personal_email']];
        }

        if (isset($fieldMapping['emergency_contact_name']) ||
            isset($fieldMapping['emergency_contact_relation']) ||
            isset($fieldMapping['emergency_contact_phone'])) {

            $emergencyContact = [];

            if (isset($fieldMapping['emergency_contact_name'])) {
                $emergencyContact['name'] = $rowData[$fieldMapping['emergency_contact_name']];
            }

            if (isset($fieldMapping['emergency_contact_relation'])) {
                $emergencyContact['relation'] = $rowData[$fieldMapping['emergency_contact_relation']];
            }

            if (isset($fieldMapping['emergency_contact_phone_country_code']) && isset($fieldMapping['emergency_contact_phone'])) {
                $countryCode = $rowData[$fieldMapping['emergency_contact_phone_country_code']];
                $phone = $rowData[$fieldMapping['emergency_contact_phone']];
                if (!empty($phone)) {
                    $emergencyContact['phone_country_code'] = $countryCode;
                    $emergencyContact['phone'] = $this->trimStringSpaces($phone);
                }
            }

            if ($this->hasNonEmptyValues($emergencyContact)) {
                $mappedData['emergency_contacts'] = [$emergencyContact];
            }
        }
    }

    private function mapEducationDetails(array $rowData, array &$mappedData, array $fieldMapping): void
    {
        $education = [];

        if (isset($fieldMapping['degree_type'])
        ||  isset($fieldMapping['degree_name'])
        || isset($fieldMapping['institution_name'])
        || isset($fieldMapping['graduation_year'])) {
            $educationRecord = [];

            if(isset($fieldMapping['degree_type'])) {
                $degreeType = $rowData[$fieldMapping['degree_type']];
                $degreeTypeEnum = $this->findEnumByTranslatedValue(EmployeeDegreeTypeEnum::class, $degreeType);
                if ($degreeTypeEnum) {
                    $educationRecord['degree_type'] = $degreeTypeEnum->value;
                } else if (!empty($degreeType)) {
                    $educationRecord['degree_type'] = $degreeType;
                }
            }

            if (isset($fieldMapping['degree_name'])) {
                $educationRecord['degree_name'] = $rowData[$fieldMapping['degree_name']];
            }

            if (isset($fieldMapping['institution_name'])) {
                $educationRecord['institution_name'] = $rowData[$fieldMapping['institution_name']];
            }

            if (isset($fieldMapping['graduation_year'])) {
                $educationRecord['graduation_year'] = $rowData[$fieldMapping['graduation_year']];
            }

            if ($this->hasNonEmptyValues($educationRecord)) {
                $education[] = $educationRecord;
            }
        }

        if (!empty($education)) {
            $mappedData['education'] = $education;
        }
    }

    private function mapEmploymentDetails(array $rowData, array &$mappedData, array $fieldMapping): void
    {
        if (isset($fieldMapping['hiring_date'])) {
            $mappedData['join_date'] = $this->parseDateFromExcel($rowData[$fieldMapping['hiring_date']]);
            if(empty($mappedData['join_date'])) {
                $mappedData['join_date'] = $rowData[$fieldMapping['hiring_date']];
            }
        }

        if (isset($fieldMapping['employment_type'])) {
            $employmentType = $rowData[$fieldMapping['employment_type']];
            $employmentTypeEnum = $this->findEnumByTranslatedValue(EmployeeEmploymentTypeEnum::class, $employmentType);
            if ($employmentTypeEnum) {
                $mappedData['employment_type'] = $employmentTypeEnum->value;
            } else if (!empty($employmentType)) {
                $mappedData['employment_type'] = $employmentType;
            }
        }

        $this->mapContractDetails($rowData, $mappedData, $fieldMapping);
    }

    private function mapContractDetails(array $rowData, array &$mappedData, array $fieldMapping): void
    {
        $hasContractData = false;

        if (isset($fieldMapping['latest_contract_start_date']) && !empty($rowData[$fieldMapping['latest_contract_start_date']])) {
            $mappedData['contract_start_date'] = $this->parseDateFromExcel($rowData[$fieldMapping['latest_contract_start_date']]);
            if(empty($mappedData['contract_start_date'])) {
                $mappedData['contract_start_date'] = $rowData[$fieldMapping['latest_contract_start_date']];
            }
            $hasContractData = true;
        }

        if (isset($fieldMapping['latest_contract_duration']) && !empty($rowData[$fieldMapping['latest_contract_duration']])) {
            $contractDuration = $rowData[$fieldMapping['latest_contract_duration']];
            $contractDurationEnum = $this->findEnumByTranslatedValue(ContractDurationEnum::class, $contractDuration);
            if ($contractDurationEnum) {
                $mappedData['contract_duration'] = $contractDurationEnum->value;
            } else if (!empty($contractDuration)) {
                $mappedData['contract_duration'] = $contractDuration;
            }
            $hasContractData = true;
        }
        if (isset($fieldMapping['latest_contract_end_date']) &&
         !empty($rowData[$fieldMapping['latest_contract_end_date']]) &&
         (!isset($mappedData['contract_duration']) || $mappedData['contract_duration'] === ContractDurationEnum::CUSTOM->value)) {
            $mappedData['contract_end_date'] = $this->parseDateFromExcel($rowData[$fieldMapping['latest_contract_end_date']]);
            if(empty($mappedData['contract_end_date'])) {
                $mappedData['contract_end_date'] = $rowData[$fieldMapping['latest_contract_end_date']];
            }
            $hasContractData = true;
        }

        $mappedData['include_contract_details'] = $hasContractData;
        if($hasContractData) {
            $mappedData['contract_start_date_same_as_join_date'] = false;
        }
    }


    private function findEnumByTranslatedValue(string $enumClass, $translatedValue = null): ?object
    {
        if (empty($translatedValue)) {
            return null;
        }

        $lowerValue = strtolower($translatedValue);

        // Use O(1) lookup maps based on enum class
        return match ($enumClass) {
            Nationalities::class => $this->nationalitiesMapEn[$lowerValue] ?? $this->nationalitiesMapAr[$lowerValue] ?? null,
            EmployeeGenderEnum::class => $this->genderMapEn[$lowerValue] ?? $this->genderMapAr[$lowerValue] ?? null,
            EmployeeMilitaryStatus::class => $this->militaryStatusMapEn[$lowerValue] ?? $this->militaryStatusMapAr[$lowerValue] ?? null,
            EmployeeReligionEnum::class => $this->religionMapEn[$lowerValue] ?? $this->religionMapAr[$lowerValue] ?? null,
            EmployeeMaritalStatusEnum::class => $this->maritalStatusMapEn[$lowerValue] ?? $this->maritalStatusMapAr[$lowerValue] ?? null,
            EmployeeEmploymentTypeEnum::class => $this->employmentTypeMapEn[$lowerValue] ?? $this->employmentTypeMapAr[$lowerValue] ?? null,
            EmployeeDegreeTypeEnum::class => $this->degreeTypeMapEn[$lowerValue] ?? $this->degreeTypeMapAr[$lowerValue] ?? null,
            ContractDurationEnum::class => $this->contractDurationMapEn[$lowerValue] ?? $this->contractDurationMapAr[$lowerValue] ?? null,
            default => null,
        };
    }



    private function hasNonEmptyValues(array $data): bool
    {
        return !empty(array_filter($data, function($value) {
            return $value !== null && $value !== '' && $value !== false;
        }));
    }

    private function validateEmployeeData(array $data): array
    {
        $rules = $this->getAddEmployeeProfileValidationRules($data);

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            $errors = [];
            foreach ($validator->errors()->all() as $error) {
                $errors[] = $error;
            }

            return [
                'valid' => false,
                'errors' => $errors
            ];
        }

        return [
            'valid' => true,
            'errors' => []
        ];
    }

    private function validateEmployeeDataForBulkEdit(array $data): array
    {
        $rules = $this->getBulkEditEmployeeProfileValidationRules($data);

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            $errors = [];
            foreach ($validator->errors()->all() as $error) {
                $errors[] = $error;
            }

            return [
                'valid' => false,
                'errors' => $errors
            ];
        }

        return [
            'valid' => true,
            'errors' => []
        ];
    }

    private function getBulkEditEmployeeProfileValidationRules(array $data): array
    {
        if ($this->cachedEmployeeCodeType === null) {
            $this->cachedEmployeeCodeType = $this->systemSettingsService->getCompanyEmployeeCodeType();
        }
        $rules = [
            'phone' => [
                'required',
                function ($attribute, $value, $fail) {
                    if (!isset($this->phoneToEmployeeNumberMap[$value])) {
                        $fail(__('validation.employee_not_found_with_phone', ['phone' => $value]));
                    }
                }
            ],
            'nationality' => ['nullable', 'string', 'max:50', new Enum(Nationalities::class)],
            'national_id' => [
                'nullable',
                'string',
                'max:50',
                new NationalIdRule($data['nationality'] ?? null),
                new BulkUniqueValueRule(
                    $this->existingNationalIds,
                    'validation.unique_national_id',
                    'national ID',
                    $this->phoneToNationalIdMap,
                    $data['phone'] ?? null
                )
            ],
            'passport_number' => [
                'nullable',
                'string',
                'max:50',
                new BulkUniqueValueRule(
                    $this->existingPassportNumbers,
                    'validation.unique_passport_number',
                    'passport number',
                    $this->phoneToPassportNumberMap,
                    $data['phone'] ?? null
                )
            ],
            'gender' => ['nullable', new Enum(EmployeeGenderEnum::class)],
            'place_of_birth' => ['nullable', 'string', 'max:100'],
            'address' => ['nullable', 'string', 'max:255'],
            'military_status' => ['nullable', new Enum(EmployeeMilitaryStatus::class)],
            'birth_date' => 'nullable|date_format:Y-m-d|before:' . now()->subYears(16)->format('Y-m-d'),
            'religion' => ['nullable', new Enum(EmployeeReligionEnum::class)],
            'other_religion' => ['required_if:religion,' . EmployeeReligionEnum::OTHER->value, 'nullable', 'string', 'max:100'],
            'marital_status' => ['nullable', new Enum(EmployeeMaritalStatusEnum::class)],
            'number_kids' => 'nullable|integer|min:0',
            'years_of_experience' => 'nullable|integer|min:0',

            'secondary_phone_country_code' => 'nullable|required_with:secondary_phone|string|starts_with:+',
            'secondary_phone' => ['nullable', 'string'],
            'work_email' => [
                'nullable',
                'email',
                new BulkUniqueValueRule(
                    $this->existingWorkEmails,
                    'validation.unique_work_email',
                    'work email',
                    $this->phoneToWorkEmailMap,
                    $data['phone'] ?? null
                )
            ],
            'personal_email' => [
                'nullable',
                'email',
                new BulkUniqueValueRule(
                    $this->existingPersonalEmails,
                    'validation.unique_personal_email',
                    'personal email',
                    $this->phoneToPersonalEmailMap,
                    $data['phone'] ?? null
                )
            ],
            'emergency_contacts' => 'nullable|array',
            'emergency_contacts.*.name' => 'nullable|string|max:100',
            'emergency_contacts.*.phone_country_code' => 'nullable|required_with:emergency_contacts.*.phone|string|starts_with:+',
            'emergency_contacts.*.phone' => ['nullable'],
            'emergency_contacts.*.relation' => 'nullable|string|max:100',

            'join_date' => 'nullable|date_format:Y-m-d|before_or_equal:' . now()->addDays(30)->format('Y-m-d'),
            'employment_type' => ['nullable', new Enum(EmployeeEmploymentTypeEnum::class)],
        ];

        if ($this->cachedEmployeeCodeType === 'manual') {
            $rules['employee_number'] = [
                'nullable',
                'string',
                'max:50',
                new BulkUniqueValueRule(
                    $this->existingEmployeeNumbers,
                    'validation.unique_employee_number',
                    'employee number',
                    $this->phoneToEmployeeNumberMap,
                    $data['phone'] ?? null
                )
            ];
        }

        if (!empty($data['secondary_phone_country_code'])) {
            $rules['secondary_phone'][] = new PhoneNumberByCountryRule($data['secondary_phone_country_code']);
        }

        $phone = $data['phone'] ?? null;
        $hasExistingEmergencyContact = false;

        if ($phone && isset($this->phoneToCurrentValues[$phone])) {
            $currentValues = $this->phoneToCurrentValues[$phone];
            $hasExistingEmergencyContact = !empty($currentValues['emergency_contact_name']) ||
                                         !empty($currentValues['emergency_contact_phone']) ||
                                         !empty($currentValues['emergency_contact_relation']);
        }
        if (!$hasExistingEmergencyContact && isset($data['emergency_contacts']) && is_array($data['emergency_contacts'])) {
            foreach ($data['emergency_contacts'] as $index => $contact) {
                $rules["emergency_contacts.{$index}.phone"][] = 'required_with:emergency_contacts.' . $index . '.name,emergency_contacts.' . $index . '.relation';

                if (!empty($contact['phone']) && !empty($contact['phone_country_code'])) {
                    $rules["emergency_contacts.{$index}.phone"][] = new PhoneNumberByCountryRule($contact['phone_country_code']);
                }
            }
        } elseif (isset($data['emergency_contacts']) && is_array($data['emergency_contacts'])) {
            foreach ($data['emergency_contacts'] as $index => $contact) {
                if (!empty($contact['phone']) && !empty($contact['phone_country_code'])) {
                    $rules["emergency_contacts.{$index}.phone"][] = new PhoneNumberByCountryRule($contact['phone_country_code']);
                }
            }
        }

        return $rules;
    }



    private function getAddEmployeeProfileValidationRules(array $data): array
    {
        if ($this->cachedEmployeeCodeType === null) {
            $this->cachedEmployeeCodeType = $this->systemSettingsService->getCompanyEmployeeCodeType();
        }

        $rules = [
            'first_name_ar' => ['required', 'string', 'max:50', new IsArabic],
            'second_name_ar' => ['required', 'string', 'max:50', new IsArabic],
            'third_name_ar' => ['required', 'string', 'max:50', new IsArabic],
            'fourth_name_ar' => ['nullable', 'string', 'max:50', new IsArabic],
            'fifth_name_ar' => ['nullable', 'string', 'max:50', new IsArabic],
            'first_name_en' => ['nullable', 'string', 'max:50', new IsEnglish],
            'second_name_en' => ['nullable', 'string', 'max:50', new IsEnglish],
            'third_name_en' => ['nullable', 'string', 'max:50', new IsEnglish],
            'fourth_name_en' => ['nullable', 'string', 'max:50', new IsEnglish],
            'fifth_name_en' => ['nullable', 'string', 'max:50', new IsEnglish],
            'gender' => ['required', new Enum(EmployeeGenderEnum::class)],
            'phone_country_code' => 'required|string|starts_with:+',
            'phone' => [
                'required',
                new PhoneNumberByCountryRule($data['phone_country_code'] ?? ''),
                function ($attribute, $value, $fail) use ($data) {
                    $fullPhone = $this->formatPhoneWithCountryCode($data['phone_country_code'] ?? '', $value);
                    if (isset($this->existingPhones[$fullPhone])) {
                        $fail(__('validation.unique_phone'));
                    }
                }
            ],
            'title_id' => [
                'required',
                'integer',
            ],
            'branch_id' => [
                'required',
                'integer',
            ],
            'nationality' => ['nullable', 'string', 'max:50', new Enum(Nationalities::class)],
            'national_id' => [
                'nullable',
                'string',
                'max:50',
                new NationalIdRule($data['nationality'] ?? null),
                new BulkUniqueValueRule(
                    $this->existingNationalIds,
                    'validation.unique_national_id',
                    'national ID',
                    null,
                    null
                )
            ],
            'passport_number' => [
                'nullable',
                'string',
                'max:50',
                new BulkUniqueValueRule(
                    $this->existingPassportNumbers,
                    'validation.unique_passport_number',
                    'passport number',
                    null,
                    null
                )
            ],
            'birth_date' => 'nullable|date_format:Y-m-d|before:' . now()->subYears(16)->format('Y-m-d'),
            'address' => 'nullable|string|max:255',
            'place_of_birth' => 'nullable|string|max:100',
            'religion' => ['nullable', new Enum(EmployeeReligionEnum::class)],
            'other_religion' => ['required_if:religion,' . EmployeeReligionEnum::OTHER->value, 'nullable', 'string', 'max:100'],
            'marital_status' => ['nullable', new Enum(EmployeeMaritalStatusEnum::class)],
            'military_status' => ['nullable', new Enum(EmployeeMilitaryStatus::class)],
            'number_kids' => 'nullable|integer|min:0',
            'join_date' => 'required|date_format:Y-m-d|before_or_equal:' . now()->addDays(30)->format('Y-m-d'),
            'employment_type' => ['nullable', new Enum(EmployeeEmploymentTypeEnum::class)],
            'years_of_experience' => 'nullable|integer|min:0',
            'secondary_phone_country_code' => 'nullable|required_with:secondary_phone|string|starts_with:+',
            'secondary_phone' => ['nullable', 'string'],
            'work_email' => [
                'nullable',
                'email',
                new BulkUniqueValueRule(
                    $this->existingWorkEmails,
                    'validation.unique_work_email',
                    'work email',
                    null,
                    null
                )
            ],
            'personal_email' => [
                'nullable',
                'email',
                new BulkUniqueValueRule(
                    $this->existingPersonalEmails,
                    'validation.unique_personal_email',
                    'personal email',
                    null,
                    null
                )
            ],
            'emergency_contacts' => 'nullable|array',
            'emergency_contacts.*.name' => 'nullable|string|max:100',
            'emergency_contacts.*.phone_country_code' => 'nullable|required_with:emergency_contacts.*.phone|string|starts_with:+',
            'emergency_contacts.*.phone' => [
                'nullable',
                'required_with:emergency_contacts.*.name,emergency_contacts.*.relation',
            ],
            'emergency_contacts.*.relation' => 'nullable|string|max:100',
            'education' => 'nullable|array',
            'education.*.degree_type' => ['nullable', 'required_with:education.*.degree_name,education.*.institution_name,education.*.graduation_year', new Enum(EmployeeDegreeTypeEnum::class)],
            'education.*.degree_name' => 'nullable|string|max:100',
            'education.*.institution_name' => 'nullable|string|max:100',
            'education.*.graduation_year' => 'nullable|integer|min:1950',
            'include_contract_details' => 'required|boolean',
            'contract_start_date' => ['nullable','date_format:Y-m-d', 'after_or_equal:join_date'],
            'contract_duration' => ['nullable', new Enum(ContractDurationEnum::class)],
            'contract_end_date' => ['nullable','date_format:Y-m-d','after:contract_start_date'],
        ];

        if ($this->cachedEmployeeCodeType === 'manual') {
            $rules['employee_number'] = [
                'required',
                'string',
                'max:50',
                new BulkUniqueValueRule(
                    $this->existingEmployeeNumbers,
                    'validation.unique_employee_number',
                    'employee number',
                    null,
                    null
                )
            ];
        }

        if (!empty($data['first_name_en']) || !empty($data['second_name_en']) || !empty($data['third_name_en'])) {
            $rules['first_name_en'] = ['required', 'string', 'max:50', new IsEnglish];
            $rules['second_name_en'] = ['required', 'string', 'max:50', new IsEnglish];
            $rules['third_name_en'] = ['required', 'string', 'max:50', new IsEnglish];
        }
        if (!empty($data['secondary_phone_country_code'])) {
            $rules['secondary_phone'][] = new PhoneNumberByCountryRule($data['secondary_phone_country_code']);
        }

        if (isset($data['religion']) && $data['religion'] === EmployeeReligionEnum::OTHER->value) {
            $rules['other_religion'] = ['required', 'string'];
        }

        if (isset($data['include_contract_details']) && $data['include_contract_details']) {
            $rules['contract_start_date'][] = 'required';
            $rules['contract_duration'][] = 'required';

            if (isset($data['contract_duration']) && $data['contract_duration'] === ContractDurationEnum::CUSTOM->value) {
                $rules['contract_end_date'][] = 'required';
            }
        }

        if (isset($data['emergency_contacts']) && is_array($data['emergency_contacts'])) {
            foreach ($data['emergency_contacts'] as $index => $contact) {
                if (!empty($contact['phone']) && !empty($contact['phone_country_code'])) {
                    $rules["emergency_contacts.{$index}.phone"][] = new PhoneNumberByCountryRule($contact['phone_country_code']);
                }
            }
        }



        return $rules;
    }

    private function isRecordEmpty(array $mappedData): bool
    {
        foreach ($mappedData as $key => $value) {
            if (in_array($key, ['is_trackable', 'include_contract_details'])) {
                continue;
            }

            if (!empty($value) && $value !== null && $value !== '') {
                return false;
            }
        }

        return true;
    }



    private function createFieldMapping(array $headers): array
    {
        $mapping = [];

        foreach ($headers as $header) {
            $fieldKey = $this->getFieldKeyFromHeader($header);
            if ($fieldKey) {
                $mapping[$fieldKey] = $header;
            }
        }

        return $mapping;
    }

    private function getFieldKeyFromHeader($header = null): ?string
    {
        if (empty($header)) {
            return null;
        }

        $allFields = array_column(EmployeeExportFieldEnum::cases(), 'value');

        $countryCodeFields = [
            'primary_phone_country_code',
            'secondary_phone_country_code',
            'emergency_contact_phone_country_code'
        ];
        $allFields = array_merge($allFields, $countryCodeFields);

        foreach ($allFields as $fieldKey) {
            $englishTranslation = __('employeeexport.' . $fieldKey, [], 'en');
            $arabicTranslation = __('employeeexport.' . $fieldKey, [], 'ar');

            if ($header === $englishTranslation || $header === $arabicTranslation) {
                return $fieldKey;
            }
        }

        return null;
    }

    private function loadTitlesAndBranchesForBulkUpload(): void
    {
        $titlesData = $this->newTitleRepository->getForBulkUpload();
        $this->titles = [];
        $this->titlesMapAr = [];
        $this->titlesMapEn = [];

        foreach ($titlesData as $title) {
            $titleArray = [
                'id' => $title->id,
                'name_ar' => $title->name_ar,
                'name_en' => $title->name_en,
                'role_id' => $title->role_id,
                'department_id' => $title->department_id,
                'sub_department_id' => $title->sub_department_id,
            ];
            $this->titles[] = $titleArray;

            if (!empty($titleArray['name_ar'])) {
                $this->titlesMapAr[strtolower($titleArray['name_ar'])] = $titleArray;
            }
            if (!empty($titleArray['name_en'])) {
                $this->titlesMapEn[strtolower($titleArray['name_en'])] = $titleArray;
            }
        }

        $branchesData = $this->newBranchRepository->getForBulkUpload();
        $this->branches = [];
        $this->branchesMapAr = [];
        $this->branchesMapEn = [];

        foreach ($branchesData as $branch) {
            $branchArray = [
                'id' => $branch->id,
                'name_ar' => $branch->name_ar,
                'name_en' => $branch->name_en,
            ];
            $this->branches[] = $branchArray;

            if (!empty($branchArray['name_ar'])) {
                $this->branchesMapAr[strtolower($branchArray['name_ar'])] = $branchArray;
            }
            if (!empty($branchArray['name_en'])) {
                $this->branchesMapEn[strtolower($branchArray['name_en'])] = $branchArray;
            }
        }
    }

    private function loadCachedUniquenessData($employees): void
    {
        $currentCompanyId = config('globals.company')->id;

        $this->existingEmployeeNumbers = [];
        $this->existingNationalIds = [];
        $this->existingPassportNumbers = [];
        $this->existingPhones = [];
        $this->existingWorkEmails = [];
        $this->existingPersonalEmails = [];

        foreach ($employees as $employee) {
            if (!empty($employee->employee_number) && $employee->company_id == $currentCompanyId) {
                $this->existingEmployeeNumbers[$employee->employee_number] = true;
            }

            if (!empty($employee->national_id)) {
                $this->existingNationalIds[$employee->national_id] = true;
            }
            if (!empty($employee->phone)) {
                $this->existingPhones[$employee->phone] = true;
            }
            if (!empty($employee->passport_number)) {
                $this->existingPassportNumbers[$employee->passport_number] = true;
            }
            if (!empty($employee->work_email)) {
                $this->existingWorkEmails[$employee->work_email] = true;
            }
            if (!empty($employee->personal_email)) {
                $this->existingPersonalEmails[$employee->personal_email] = true;
            }
        }
    }

    private function loadPhoneToCurrentValuesMapping($employees): void
    {
        $currentCompanyId = config('globals.company')->id;

        foreach ($employees as $employee) {
            // Get the first emergency contact if it exists
            $emergencyContact = $employee->emergencyContacts->first();

            $this->phoneToCurrentValues[$employee->phone] = [
                'employee_number' => $employee->employee_number,
                'national_id' => $employee->national_id,
                'passport_number' => $employee->passport_number,
                'work_email' => $employee->work_email,
                'personal_email' => $employee->personal_email,
                'company_id' => $employee->company_id,
                'emergency_contact_name' => $emergencyContact?->name,
                'emergency_contact_phone' => $emergencyContact?->phone,
                'emergency_contact_relation' => $emergencyContact?->relation,
            ];

            $this->phoneToNationalIdMap[$employee->phone] = $employee->national_id;
            $this->phoneToPassportNumberMap[$employee->phone] = $employee->passport_number;
            $this->phoneToWorkEmailMap[$employee->phone] = $employee->work_email;
            $this->phoneToPersonalEmailMap[$employee->phone] = $employee->personal_email;

            if ($employee->company_id == $currentCompanyId) {
                $this->phoneToEmployeeNumberMap[$employee->phone] = $employee->employee_number;
            }
        }
    }

    private function loadEnumLookupMaps(): void
    {
        $this->buildEnumLookupMap(Nationalities::class, $this->nationalitiesMapEn, $this->nationalitiesMapAr);
        $this->buildEnumLookupMap(EmployeeGenderEnum::class, $this->genderMapEn, $this->genderMapAr);
        $this->buildEnumLookupMap(EmployeeMilitaryStatus::class, $this->militaryStatusMapEn, $this->militaryStatusMapAr);
        $this->buildEnumLookupMap(EmployeeReligionEnum::class, $this->religionMapEn, $this->religionMapAr);
        $this->buildEnumLookupMap(EmployeeMaritalStatusEnum::class, $this->maritalStatusMapEn, $this->maritalStatusMapAr);
        $this->buildEnumLookupMap(EmployeeEmploymentTypeEnum::class, $this->employmentTypeMapEn, $this->employmentTypeMapAr);
        $this->buildEnumLookupMap(EmployeeDegreeTypeEnum::class, $this->degreeTypeMapEn, $this->degreeTypeMapAr);
        $this->buildEnumLookupMap(ContractDurationEnum::class, $this->contractDurationMapEn, $this->contractDurationMapAr);
    }

    private function buildEnumLookupMap(string $enumClass, array &$englishMap, array &$arabicMap): void
    {
        foreach ($enumClass::cases() as $case) {
            $englishLabel = __('employeeexport.' . $case->value, [], 'en');
            $arabicLabel = __('employeeexport.' . $case->value, [], 'ar');

            $englishMap[strtolower($englishLabel)] = $case;
            $arabicMap[strtolower($arabicLabel)] = $case;
        }
    }

    private function findTitleByName($name = null): ?array
    {
        if($name == null){
            return null;
        }
        $name = strtolower($name);
        if (isset($this->titlesMapAr[$name])) {
            return $this->titlesMapAr[$name];
        }

        if (isset($this->titlesMapEn[$name])) {
            return $this->titlesMapEn[$name];
        }

        return null;
    }

    private function findBranchByName($name = null): ?array
    {
        if($name == null){
            return null;
        }
        $name = strtolower($name);
        if (isset($this->branchesMapAr[$name])) {
            return $this->branchesMapAr[$name];
        }

        if (isset($this->branchesMapEn[$name])) {
            return $this->branchesMapEn[$name];
        }

        return null;
    }

}

class EmployeeNode
{
    public array $childrenNodes;

    public ?EmployeeNode $parent;

    public array $baseEmployeeArray;

    public int $row;

    public bool $needInsert;

    public ?string $id;

    public function __construct(array $baseEmployeeArray, $row = 0)
    {
        $this->childrenNodes = [];
        $this->parent = null;
        $this->baseEmployeeArray = $baseEmployeeArray;
        $this->row = $row;
        $this->needInsert = true;
        $this->id = null;
    }
}
