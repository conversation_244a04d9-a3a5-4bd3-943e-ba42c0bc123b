<?php

namespace App\Repositories\V1\CompanySettings;

use App\Models\Title;
use App\Repositories\BaseRepository;
use App\Traits\QueriesHelper;

class TitleRepository extends BaseRepository
{
    use QueriesHelper;

    public function model(): string
    {
        return Title::class;
    }

    public function list($filters)
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');

        return $this->model
            ->select('id', 'name'.$lang, 'gross_salary', 'net_salary')
            ->withCount('employees as employees_count')
            ->when(isset($filters['title_ids']), function ($q) use ($filters) {
                $q->whereIn('id', $filters['title_ids']);
            })
            ->when(isset($filters['department_ids']), function ($q) use ($filters) {
                $q->whereIn('department_id', $filters['department_ids']);

            })
            ->when(isset($filters['search_value']), function ($q) use ($filters) {
                $q->where(function ($query) use ($filters) {
                    $query->where('name_en', 'LIKE', '%'.$filters['search_value'].'%')
                        ->orWhere('name_ar', 'LIKE', '%'.$filters['search_value'].'%');
                });
            })
            ->paginate($filters['page_size'] ?? 10);

    }

    public function exportTitlesData($filters)
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');

        return $this->model
            ->select('id', 'name'.$lang, 'gross_salary', 'net_salary')
            ->when(isset($filters['title_ids']), function ($q) use ($filters) {
                $q->whereIn('id', $filters['title_ids']);
            })
            ->when(isset($filters['department_ids']), function ($q) use ($filters) {
                $q->whereIn('department_id', $filters['department_ids']);
            })
            ->when(isset($filters['search_value']), function ($q) use ($filters) {
                $q->where(function ($query) use ($filters) {
                    $query->where('name_en', 'LIKE', '%'.$filters['search_value'].'%')
                        ->orWhere('name_ar', 'LIKE', '%'.$filters['search_value'].'%');
                });
            })
            ->get();
    }

    public function getTitlesApplyMissionPolicy()
    {
        return $this->model
            ->select('id', 'name_en', 'name_ar', 'color')
            ->where('apply_missions', 1)
            ->get();
    }

    public function getAllTitles()
    {
        return $this->selectColumns(['id', 'name_ar', 'name_en'])->keyBy('id')->toArray();
    }

    public function getTitleNameColor($titleId){
        return $this->model
        ->select('id', 'name_ar', 'name_en', 'color')
        ->where('id', $titleId)
        ->first();
    }

    public function getForBulkUpload()
    {
        return $this->model
            ->select('id', 'name_en', 'name_ar', 'role_id', 'department_id', 'sub_department_id')
            ->get();
    }
}
