<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use App\Models\ExtraWorkDayRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AttachAttendanceIdToExtraWorkDay extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:attach-attendance-id-to-extra-work-day';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fill in attendance_id on extra work day requests where it’s missing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $requests = ExtraWorkDayRequest::whereNull('attendance_id')->get();
        $total = $requests->count();

        if ($total === 0) {
            $this->info('✅ No extra work day requests need updating.');
            return 0;
        }

        $this->info("🔍 Found {$total} requests without attendance_id. Processing…");

        DB::beginTransaction();

        try {
            foreach ($requests as $req) {
                $attendance = Attendance::where('employee_id', $req->employee_id)
                    ->whereDate('date', $req->extra_work_day_date)
                    ->first();

                if ($attendance) {
                    $req->attendance_id = $attendance->id;
                    $req->save();
                    $this->info("   • [ID {$req->id}] → attendance_id set to {$attendance->id}");
                } else {
                    $this->warn("   • [ID {$req->id}] No matching attendance on {$req->extra_work_day_date}");
                }
            }

            DB::commit();
            $this->info('🎉 All requests processed successfully.');
            return 0;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->error("🚨 Failed to attach attendance IDs: {$e->getMessage()}");
            return 1;
        }
    }
}
