<?php

namespace App\Console\Commands;

use App\Services\V1\Billing\BillingService;
use Illuminate\Console\Command;

class FixGenerateFirstBills extends Command
{
    protected $signature = 'app:fix-generate-first-bills';

    protected $description = 'Generate company bills based on billing frequency and employee count';

    public function __construct(protected BillingService $billingService)
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $companies = $this->billingService->getCompaniesToFirstBilling();
        foreach ($companies as $company) {
            $this->billingService->generateFirstBillForCompany($company->id);
        }
    }
}
