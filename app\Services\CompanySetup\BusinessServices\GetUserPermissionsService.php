<?php

namespace App\Services\CompanySetup\BusinessServices;

use App\Enums\Missions\MissionsEnum;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\Repository;
use App\Services\IBusinessService;
use App\Traits\RolesAndPermissionsTrait;
use App\Traits\V2\WorkTypesTrait;

class GetUserPermissionsService implements IBusinessService
{
    use RolesAndPermissionsTrait, WorkTypesTrait;

    private Repository $userRepository;

    private $systemSettingRepository;

    private ?object $user;

    public function __construct()
    {
        $this->userRepository = Repository::getRepository('user');
        $this->systemSettingRepository = new SystemSettingRepository;
        $this->user = null;
        $this->systemSettingRepository = new SystemSettingRepository;
    }

    public function isValid(array $request, \stdClass &$output): bool
    {

        if (!isset($request['user_id'])) {
            $output->Error = ['User id is required', 'معرف المستخدم مطلوب'];

            return false;
        }

        $this->user = $this->userRepository->getById($request['user_id']);
        if (is_null($this->user)) {
            $output->Error = ['Incorrect user id', 'معرف المستخدم غير صحيح'];

            return false;
        }

        return true;
    }

    public function perform(array $request, \stdClass &$output): void
    {
        if (!$this->isValid($request, $output)) {
            return;
        }

        $employeePermissions = $this->getRolePermissions($this->user?->roles?->first());
        $branchManager = (count($this->user?->employee?->branches ?? []) > 0
            && isset($employeePermissions) && $employeePermissions->count() > 3);

        $workTypePolicy = $this->user->employee->title?->workTypePolicy?->work_days_type ?? '';

        $loanPolicyEnabled = $this->systemSettingRepository->getSettingByKey('loan_policy', $this->user->company_id)?->value ?? false;
        $salaryAdvancePolicyEnabled = $this->systemSettingRepository->getSettingByKey('salary_advance_policy', $this->user->company_id)?->value ?? false;
        $missionPolicyEnabled = $this->systemSettingRepository->getSettingByKey(MissionsEnum::POLICY->value, $this->user->company_id)?->value ?? false;

        if ($this->user->hasRole('Super Admin')) {
            $output->permissions = $this->getAllPermissions()->pluck('name');
            if ($branchManager) {
                $output->permissions[] = 'branch_manager';
            }

            if (in_array($workTypePolicy, $this->getTypesToAppearInSchedule($this->user->company_id))) {
                $output->permissions[] = 'worker_view_schedule';
            }

            if ($loanPolicyEnabled || $salaryAdvancePolicyEnabled) {
                $output->permissions[] = 'loan_policy_enabled';
            }

            if ($missionPolicyEnabled) {
                $output->permissions[] = 'mission_policy_enabled';
            }

            if (env('APP_ENV') != 'local') {
                if ($this->user->employee->enable_impersonation == 1) {
                    $output->permissions[] = 'enable_impersonate';
                }

                $output->permissions[] = 'deviceid_' . $this->user->device_id;
            }

            return;
        }

        // if(is_null($this->user->employee_id)) return; // we need to login with user in company setup

        // $employee = $this->user->employee;

        // check if employee has custom role
        // PostPoned to after release
        // if($employee->custom_role){
        //     $customRoleRepository = Repository::getRepository('CustomRole');
        //     $customRole = $customRoleRepository->GetEmployeeActiveCustomRole($employee->id);
        //     if($customRole){id
        //         $output->permissions = $customRole->permissions;
        //         return;
        //     }
        // }
        $output->permissions = $this->getUserPermissionsByRoles($this->user)->pluck('name');
        if ($branchManager) {
            $output->permissions[] = 'branch_manager';
        }

        if (in_array($workTypePolicy, $this->getTypesToAppearInSchedule($this->user->company_id))) {
            $output->permissions[] = 'worker_view_schedule';
        }

        if ($loanPolicyEnabled || $salaryAdvancePolicyEnabled) {
            $output->permissions[] = 'loan_policy_enabled';
        }

        if ($missionPolicyEnabled) {
            $output->permissions[] = 'mission_policy_enabled';
        }

        if (env('APP_ENV') != 'local') {
            if ($this->user->employee->enable_impersonation == 1) {
                $output->permissions[] = 'enable_impersonate';
            }

            $output->permissions[] = 'deviceid_' . $this->user->device_id;
        }
    }
}
