<?php

namespace App\Http\Controllers\TimeTracking;

use App\DomainData\DefaultRestDaysDto;
use App\Http\Controllers\Controller;
use App\Services\TimeTracking\CrudServices\DefaultRestDaysCrudService;
use stdClass;

class DefaultRestDaysController extends Controller
{
    use DefaultRestDaysDto;

    public function __construct(
        private DefaultRestDaysCrudService $service
    ) {}

    public function update(array $request, stdClass &$output): void
    {
        $rules = $this->getRules([]);
        $rules = $this->getRestDaysDataRules($rules); // rules for every element in rest_days_data

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        // sort the days || then put them in a single string
        foreach ($request['rest_days_data'] as &$element) {
            $element['rest_days'] = array_unique($element['rest_days']);
            sort($element['rest_days']);
            $element['rest_days'] = implode(',', $element['rest_days']); // Compress the days in one string seperated by ','
        }
        if (! isset($request['branch_id']) && ! is_null(config('globals.branchId'))) {
            $request['branch_id'] = config('globals.branchId');
        }
        $request['company_id'] = config('globals.company')->id;
        $this->service->update($request, $output);
    }

    public function getAllRests(array $request, stdClass &$output)
    {

        $rules['branch_id'] = 'required|integer';
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();
        $this->service->getAllRests($request, $output);
    }
}
