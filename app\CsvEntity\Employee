<?php

namespace App\CsvEntity;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Shared\Date as ExcelDate;

class EmployeeSalariesCsvEntity  implements WithCustomValueBinder, WithHeadingRow, WithHeadings, SkipsEmptyRows
{


    //exporting
   
    public function headings(): array
    {
        return [
            [

                /*
                
                'basic_salary' => 'numeric',
            'social_insurance_salary' => 'numeric',
            'salary_disbursement_method' => 'string|in:cash,bank',
            'bank_id' =>  'required_if:social_disbursement_method,transfer|integer|exists:banks,id',
            'bank_account_number' => 'string|required_if:social_disbursement_method,transfer',
            'social_insurance_date' => 'required|date_format:Y-m-d',
            'social_insurance_number' => 'required|string',
            'government_insurance_number' => 'string',
            'medical_insurance_number' => 'string',
            'social_insurance_office' => 'required|string',
                */
                'Code',
                'Employee First Name',
                'Employee Second Name',
                'Employee Third Name',
                'Employee Fourth Name',
                'Employee Fifth Name',
                'National ID #',
                'Phone Number',
                'Title',
                'Main Branch',
                'Additional Branches',
                'Direct Manager',
                'Hiring Date',
                // 'Street Name',
                // 'Area',
                // 'Governorate',
                // 'Birthdate',
                // 'Gender',
           //     'career start date',
                // 'Marital Status',
                // '# of Children',
                // 'National ID Expiration Date',
                // 'Military Status',
                // 'Military Status Expiration Date',
                
            ]
        ];
    }


   /* 
    public function map($row): array{
        return [
           // Date::dateTimeToExcel(Carbon::parse($row['hiring_date']))
        ];
    }
   */

    
   

    //importing
    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
      
    }     

    public function AppendToEmployeeArray($inputArray) :array
    {
       $targetEmployee['employee_number'] = $inputArray['code'] ;
       $targetEmployee['first_name'] = $inputArray['employee_first_name'];
       $targetEmployee['second_name'] = $inputArray['employee_second_name'];
       $targetEmployee['third_name'] = $inputArray['employee_third_name'];
       $targetEmployee['fourth_name'] = $inputArray['employee_fourth_name'];
       $targetEmployee['fifth_name'] = $inputArray['employee_fifth_name'];
       $targetEmployee['national_id'] = $inputArray['national_id'];
       $targetEmployee['phone'] = $inputArray['phone_number'];
       $targetEmployee['title'] = $inputArray['title'];
       $targetEmployee['branch'] = $inputArray['main_branch'];
       $targetEmployee['join_date'] = $inputArray['hiring_date'] ?
       Carbon::parse(ExcelDate::excelToTimestamp($inputArray['hiring_date']))->toDateString() : null;
    //    $targetEmployee['address'] = $inputArray['street_name'];
    //    $targetEmployee['city'] = $inputArray['area'];
    //    $targetEmployee['governorate'] = $inputArray['governorate'];
    //    $targetEmployee['birth_date'] = $inputArray['birthdate'];
    //    $targetEmployee['gender'] = $inputArray['gender'];
    //   // $targetEmployee['career_start_date'] = $inputArray['career_start_date'];
    //    $targetEmployee['marital_status'] = $inputArray['marital_status'];
    //    $targetEmployee['number_kids'] = $inputArray['of_children'];
    //    $targetEmployee['national_id_expiration_date'] = $inputArray['national_id_expiration_date'];
    //    $targetEmployee['military_status'] = $inputArray['military_status'];
    //    $targetEmployee['military_end_date'] = $inputArray['military_status_expiration_date'];
       $targetEmployee['branches'] = $inputArray['additional_branches']?? "";       
       $targetEmployee['gender'] = $inputArray['gender'];
       return $targetEmployee;
    }   

}
