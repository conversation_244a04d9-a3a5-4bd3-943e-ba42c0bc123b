<?php

namespace App\Console\Commands;

use App\Services\CompanySetup\MigrateEmployeesInfoService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateEmployeesInfo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:employees:info';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate employees info from employees table to employees_info table ';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function __construct(private MigrateEmployeesInfoService $migrateEmployeesInfoService)
    {
        parent::__construct();
    }

    public function handle()
    {
        DB::beginTransaction();
        try {
            $this->migrateEmployeesInfoService->run();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
