<?php

namespace App\Console\Commands;

use App\Enums\V2\WorkTypes\RestDaysTypesEnum;
use App\FeatureToggles\Unleash;
use App\Models\Attendance;
use App\Models\ExtraWorkDayRequest;
use App\Models\SystemSetting;
use App\Repositories\NewAttendanceSettingRepository;
use App\Repositories\NewTitleRepository;
use App\Repositories\Repository;
use App\Repositories\V1\Attendance\AttendanceRepository;
use App\Repositories\V1\Attendance\ExtraWorkdayPolicyRepository;
use App\Repositories\V1\CompanySettings\WorkTypePolicyRepository;
use App\Repositories\V1\EmployeeRequestRepository;
use App\Repositories\V1\EntityTagRepository;
use App\Repositories\V1\ExtraWorkdayRequestRepository;
use App\Traits\WorkflowTrait;
use App\Util\BasicInfoUtil;
use App\Util\PayrollUtil;
use App\Util\UserWorkTypesUtil;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RegenerateExtraWorkDaysForCompany extends Command
{
    use WorkflowTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:re-generate-extra-work-days-for-company 
                            {company_id : The ID of the company}
                            {from_date : Start date in Y-m-d format}
                            {to_date : End date in Y-m-d format}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerates extra work days for a specific company between two dates.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companyId = $this->argument('company_id');
        $fromDate = $this->argument('from_date');
        $toDate = $this->argument('to_date');

        $this->info("--- Starting regeneration for company ID: {$companyId}, from: {$fromDate} to: {$toDate} ---");

        DB::beginTransaction();
        try {
            // Fetch attendances
            $attendances = Attendance::where('company_id', $companyId)
                ->whereDate('date', '>=', $fromDate)
                ->whereDate('date', '<=', $toDate)
                ->orderBy('date', 'asc')
                ->get();

            $employeeIds = $attendances->pluck('employee_id')->unique();

            $this->info('Fetched ' . $attendances->count() . ' attendance records.');

            // Fetch extra work days
            $extraWorkDays = ExtraWorkDayRequest::whereIn('employee_id', $employeeIds)
                ->whereDate('extra_work_day_date', '>=', $fromDate)
                ->whereDate('extra_work_day_date', '<=', $toDate)
                ->get();

            $this->info('Fetched ' . $extraWorkDays->count() . ' extra work day requests.');

            // First: Reset attendance fields
            foreach ($attendances as $attendance) {
                $attendance->extra_work_day_workflow_id = null;
                $attendance->extra_work_day_status = null;
                $attendance->save();

                // Clear related entity tags
                foreach ($attendance->entityTags as $entityTag) {
                    if ($entityTag->tag === config('globals.ATTENDANCE_TAGS.EXTRA_WORKDAY')) {
                        $entityTag->delete();
                        $this->info("Deleted entity tag ID {$entityTag->id} for attendance ID {$attendance->id}");
                    }
                }

                $this->info("Cleared extra work day info for Attendance ID {$attendance->id} (Employee ID {$attendance->employee_id})");
            }


            // Second: Delete extra work days and related employee requests
            foreach ($extraWorkDays as $extraWorkDay) {
                if ($extraWorkDay->employeeRequest) {
                    $extraWorkDay->employeeRequest->delete();
                    $this->info("Deleted employee request for Extra Work Day ID {$extraWorkDay->id}");
                }
                $extraWorkDayId = $extraWorkDay->id;
                $extraWorkDay->delete();
                $this->info("Deleted Extra Work Day ID {$extraWorkDayId}");
            }

            // Group attendances by employee_id for better handling
            $attendancesGroupedByEmployee = $attendances->groupBy('employee_id');

            foreach ($attendancesGroupedByEmployee as $employeeId => $employeeAttendances) {
                $employee = $employeeAttendances->first()->employee;
                $title = $employee->title;
                $workTypePolicy = $title->workTypePolicy ?? null;

                if (!$workTypePolicy) {
                    $this->warn("Employee ID {$employeeId} has no work type policy. Skipping...");
                    continue;
                }

                if ($this->doesEmployeeHaveFixedRestdays($employee)) {
                    // Static rest days: pass all attendances
                    foreach ($employeeAttendances as $attendance) {
                        $this->handleExtraWorkday($attendance);
                    }
                } else {
                    // Dynamic rest days: apply threshold
                    $restDaysCount = $workTypePolicy->rest_days_count ?? 0;
                    $threshold = 30 - $restDaysCount;

                    $sortedAttendances = $employeeAttendances->sortBy('date')->values();
                    $limitedAttendances = $sortedAttendances
                        ->unique('date')
                        ->skip($threshold)
                        ->values();

                    foreach ($limitedAttendances as $attendance) {
                        $this->handleExtraWorkday($attendance);
                    }
                }
            }


            DB::commit();
            $this->info('--- Extra work days regeneration completed successfully! ---');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error occurred: ' . $e->getMessage());
            report($e); // Best practice to report the exception
        }
    }

    public function handleExtraWorkday(Attendance $attendance)
    {
        $titleRepository = new NewTitleRepository;
        $newAttendanceRepository = new AttendanceRepository;
        $newAttendanceSettingRepository = new NewAttendanceSettingRepository;
        $extraWorkdayPolicyRepository = new ExtraWorkdayPolicyRepository;
        $workTypePolicyRepository = new WorkTypePolicyRepository;
        $attendanceRepository = Repository::getRepository('Attendance');

        $employee = $attendance->employee;
        $title = $titleRepository->find($employee->title_id);
        $extraWorkdayPolicyId = $title->extra_workday_policy_id ?? null;
        $extraWorkdayPolicy = $extraWorkdayPolicyRepository->find($extraWorkdayPolicyId);

        $attendanceDate = $attendance->date;
        $attendanceDay = strtolower(date('D', strtotime($attendanceDate)));
        if (!isset($extraWorkdayPolicy)) {
            return;
        }

        $extraWorkdaySettingPolicy = $newAttendanceSettingRepository->findFirstByKeys(['key' => 'extra_workday', 'company_id' => $employee->company_id]);
        if (isset($extraWorkdaySettingPolicy) && !$extraWorkdaySettingPolicy['is_used']) {
            return;
        }

        $extraWorkDayAttendanceWithSameDay = $newAttendanceRepository->getExistedExtraWorkDayAttendance($employee->id, $attendance->id, $attendanceDate);
        if (!empty($extraWorkDayAttendanceWithSameDay)) {
            return;
        }
        $attendanceUpdateData = ['extra_work_day_status' => 'pending'];
        $newAttendanceRepository->update($attendance->id, $attendanceUpdateData);
        $workTypePolicyId = $title->work_type_policy_id;
        $workTypePolicy = $workTypePolicyRepository->find($workTypePolicyId);
        if (!$this->doesEmployeeHaveFixedRestdays($employee)) {
            if ($this->applyDynamicMonthlyClosingDay($attendance->company_id)) {
                $attendanceDate = Carbon::parse($attendance->date);

                $payrollStartDate = $attendanceDate->copy()->startOfMonth()->toDateString();
                $payrollEndDate = $attendanceDate->copy()->endOfMonth()->toDateString();
            } else {
                $payrollMonthlyClosingDate = $this->getMonthlyClosingDay($attendance->company_id);
                $attendanceDate = Carbon::parse($attendance->date)->copy();

                if ($attendanceDate->day <= $payrollMonthlyClosingDate) {
                    $payrollStartDate = $attendanceDate->copy()->subMonth()->day($payrollMonthlyClosingDate)->addDay()->toDateString();
                    $payrollEndDate = $attendanceDate->copy()->day($payrollMonthlyClosingDate)->toDateString();
                } else {
                    $payrollStartDate = $attendanceDate->copy()->day($payrollMonthlyClosingDate)->addDay()->toDateString();
                    $payrollEndDate = $attendanceDate->copy()->addMonth()->day($payrollMonthlyClosingDate)->toDateString();
                }
            }
            $workingDaysCount = $attendanceRepository->getEmployeeDistinctAttendanceCountInDateRange($employee->id, $payrollStartDate, $payrollEndDate);
            $restDaysCount = $workTypePolicy->rest_days_count;
            Log::info('payroll start ' . $payrollStartDate . ' payroll end ' . $payrollEndDate);
            if ($workingDaysCount > (PayrollUtil::PAYROLL_MONTH_DAYS - $restDaysCount)) {
                $this->addExtraWorkDayRequest($attendance, $employee, $extraWorkdayPolicy['compensation_rate']);
            }
        } else {
            $restDays = $workTypePolicy->rest_days;
            $restDaysArray = explode(',', $restDays);
            if (in_array($attendanceDay, $restDaysArray)) {
                $this->addExtraWorkDayRequest($attendance, $employee, $extraWorkdayPolicy['compensation_rate']);
            }
        }
    }

    private function addExtraWorkDayRequest($attendance, $employee, $compensationRate)
    {
        $employeeRequestRepository = new EmployeeRequestRepository;
        $extraWorkdayRequestRepository = new ExtraWorkdayRequestRepository;
        $entityTagRepository = new EntityTagRepository;

        $extraWorkDayRequest = $extraWorkdayRequestRepository->add([
            'employee_id' => $employee->id,
            'extra_work_day_date' => $attendance?->date,
            'status' => 'pending',
            'compensation_rate' => $compensationRate,
            'attendance_id' => $attendance?->id,
        ]);
        $employeeRequestRepository->add([
            'company_id' => $attendance->company_id,
            'requested_by' => $attendance->employee_id,
            'requestable_id' => $extraWorkDayRequest->id,
            'requestable_type' => 'extra_work_day_request',
            'request_name' => 'extra_work_day_request',
            'comment' => null,
            'status' => 'pending',
            'employee_id' => $employee->id,
            'date' => $attendance?->date,
        ]);
        $entityTagRepository->add([
            'tag' => config('globals.ATTENDANCE_TAGS.EXTRA_WORKDAY'),
            'entity_id' => $attendance->id,
            'entity_type' => 'attendance',
            'company' => $attendance->company_id,
        ]);
        $requesterRoleIds = $attendance->employee->user->roles->pluck('id')->toArray();
        $this->initializeRequestWorkflow(
            $extraWorkDayRequest,
            $employee,
            config('globals.REQUEST_CYCLE_TYPES.EXTRA_WORKDAY'),
            config('globals.REQUEST_WORKFLOW_TYPES.EXTRA_WORKDAY'),
            $requesterRoleIds
        );
    }

    public function doesEmployeeHaveFixedRestdays($employee)
    {
        $workTypePolicy = $employee?->title?->workTypePolicy;

        $unleash = app(Unleash::class);
        $isNewWorkTypes = $unleash->getNewWorkTypesFeatureFlag($employee->company_id);

        return $isNewWorkTypes ? $workTypePolicy->rest_days_type == RestDaysTypesEnum::FIXED->value :
            $workTypePolicy->work_days_type != UserWorkTypesUtil::DYNAMIC_ON_SITE;

    }

    public function applyDynamicMonthlyClosingDay($companyId)
    {
        $applyDynamicClosingDay = SystemSetting::where('company_id', $companyId)
            ->where('key', BasicInfoUtil::APPLY_DYNAMIC_CLOSING_DAY)
            ->first();

        return isset($applyDynamicClosingDay) && $applyDynamicClosingDay->value == 1;
    }

    public function getMonthlyClosingDay($companyId)
    {
        if ($this->applyDynamicMonthlyClosingDay($companyId)) {
            $date = $date ?? date('Y-m-d');

            return Carbon::parse($date)->endOfMonth()->day;
        }

        return SystemSetting::where('key', BasicInfoUtil::MONTHLY_CLOSING_DAY)
            ->where('company_id', $companyId)
            ->first()?->value;;
    }
}
