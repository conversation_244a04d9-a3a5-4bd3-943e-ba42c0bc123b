<?php

namespace App\Console\Commands;

use App\Imports\AddTransferredLeaveBalancesImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class AddTransferedLeaveBalances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:add-transferred-leave-balances {file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add transferred leave balances';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $this->info('Adding transferred leave balances');
            $file = $this->argument('file');
            $filePath = storage_path($file);

            if (! file_exists($filePath)) {
                $this->error("File not found: $filePath");

                return 1;
            }

            Excel::import(new AddTransferredLeaveBalancesImport, $filePath);
            $this->info('Transferred leave balances added successfully');

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error: '.$e);
        }
    }
}
