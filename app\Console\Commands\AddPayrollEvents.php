<?php

namespace App\Console\Commands;

use App\Models\Payroll;
use App\Models\PayrollEvents;
use Illuminate\Console\Command;

class AddPayrollEvents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:add-payroll-events';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add payroll events for multiple companies based on their closing dates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companyPayrollEvents = [
            39 => ['id' => 39,  'payroll_end_date' => 25],
            105 => ['id' => 105, 'payroll_end_date' => 25],
            129 => ['id' => 129, 'payroll_end_date' => 27],
            138 => ['id' => 138, 'payroll_end_date' => 25],
            152 => ['id' => 152, 'payroll_end_date' => 25],
            156 => ['id' => 156, 'payroll_end_date' => 25],
            164 => ['id' => 164, 'payroll_end_date' => 25],
            165 => ['id' => 165, 'payroll_end_date' => 27],
            166 => ['id' => 166, 'payroll_end_date' => 25],
            167 => ['id' => 167, 'payroll_end_date' => 20],
            176 => ['id' => 176, 'payroll_end_date' => 25],
            181 => ['id' => 181, 'payroll_end_date' => 25],
            182 => ['id' => 182, 'payroll_end_date' => 25],
            194 => ['id' => 194, 'payroll_end_date' => 25], // corrected to 25
            195 => ['id' => 195, 'payroll_end_date' => 27],
            197 => ['id' => 197, 'payroll_end_date' => 20],
            199 => ['id' => 199, 'payroll_end_date' => 25],
            200 => ['id' => 200, 'payroll_end_date' => 24], // added Mini Melts Trading
            218 => ['id' => 218, 'payroll_end_date' => 27],
            227 => ['id' => 227, 'payroll_end_date' => 26], // added Golden Lizard
            231 => ['id' => 231, 'payroll_end_date' => 25], // added AMM
        ];

        foreach ($companyPayrollEvents as $companyId => $payrollEvent) {
            $payroll = Payroll::where('company_id', $companyId)
                ->where('status', 'draft')
                ->first();

            if ($payroll) {
                PayrollEvents::create([
                    'payroll_id' => $payroll->id,
                    'payroll_event_end' => now()->format('Y-m')
                        .'-'
                        .str_pad($payrollEvent['payroll_end_date'], 2, '0', STR_PAD_LEFT),
                    'event_name' => 'Eid Al Fitr',
                ]);
                $this->info("Created payroll event for company ID: {$companyId}");
            } else {
                $this->info("No draft payroll found for company ID: {$companyId}");
            }
        }
    }
}
