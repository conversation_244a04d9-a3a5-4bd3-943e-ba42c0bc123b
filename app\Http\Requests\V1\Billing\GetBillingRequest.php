<?php

namespace App\Http\Requests\V1\Billing;

use Illuminate\Foundation\Http\FormRequest;

class GetBillingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'page' => [
                'integer',
                'min:1',
            ],
            'page_size' => [
                'integer',
                'min:0',
            ],
            'search_value' => [
                'min:1',
                'max:30',

            ],
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'page_size' => $this->input('page_size', 10),
        ]);
    }
}
