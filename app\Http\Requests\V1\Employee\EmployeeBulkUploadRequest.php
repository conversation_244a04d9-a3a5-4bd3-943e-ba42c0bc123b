<?php

namespace App\Http\Requests\V1\Employee;

use App\Enums\Employee\EmployeeBulkOperationTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class EmployeeBulkUploadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'type' => ['required', new Enum(EmployeeBulkOperationTypeEnum::class)],
            'excel_file' => [
                'required',
                'file',
                'mimes:xlsx,xls',
                'max:' . config('globals.MAX_EXCEL_FILE_SIZE_KB', 15360) // 15MB default
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'type.required' => trans('validation.required', ['attribute' => 'operation type']),
            'type.enum' => trans('validation.in', ['attribute' => 'operation type']),
            'excel_file.required' => trans('validation.required', ['attribute' => 'Excel file']),
            'excel_file.file' => trans('validation.file', ['attribute' => 'Excel file']),
            'excel_file.mimes' => trans('validation.mimes', ['attribute' => 'Excel file', 'values' => 'xlsx, xls']),
            'excel_file.max' => trans('validation.max.file', ['attribute' => 'Excel file', 'max' => config('globals.MAX_EXCEL_FILE_SIZE_KB', 15360)]),
        ];
    }
}
