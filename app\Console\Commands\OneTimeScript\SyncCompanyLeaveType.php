<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\CompanyLeaveType;
use App\Models\Employee;
use App\Repositories\Repository;
use App\Services\LeaveManagement\BusinessServices\BalanceAggregatorService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyncCompanyLeaveType extends Command
{
    public function __construct(
        private BalanceAggregatorService $balanceAggregatorService
    ) {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:diwan:duplicate_leave_type';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
        DB::beginTransaction();
        try {
            $wrongLeaveType = CompanyLeaveType::find(66); //
            $correctLeaveTypePolicyId = 79;
            $correctLeaveTypeId = 98;
            foreach ($wrongLeaveType->employeeLeaveRequests as $leave) {

                $leave->company_leave_type_policy_id = $correctLeaveTypePolicyId;
                $leave->company_leave_type_id = $correctLeaveTypeId;
                $leave->save();

                $balanceEntity = $employeeLeaveBalanceRepository->getBalanceOfLeave($leave);
                $dummyOutput = new \stdClass;
                $employee = Employee::find($leave->employee_id);
                if (isset($employee) && isset($balanceEntity)) {
                    $this->balanceAggregatorService->perform(['id' => $balanceEntity->id,
                        'employee_id' => $leave->employee_id], $dummyOutput);
                }

            }
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
