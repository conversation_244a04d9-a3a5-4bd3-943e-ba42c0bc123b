<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\CompanyLeaveType;
use App\Models\CompanyLeaveTypePolicy;
use Illuminate\Database\Seeder;

class CompanyLeaveTypesSeeder extends Seeder
{
    public function run()
    {
        $companies = Company::all();

        foreach ($companies as $company) {
            // Annual Leave
            $annualLeave = CompanyLeaveType::create([
                'company_id' => $company->id,
                'name_ar' => 'إجازة سنوية',
                'name_en' => 'Annual Leave',
                'is_primary' => true,
                'balance_period' => 'calendar year',
                'gender' => 'all',
            ]);

            CompanyLeaveTypePolicy::create([
                'company_id' => $company->id,
                'company_leave_type_id' => $annualLeave->id,
                'base_balance' => 21,
                'unit' => config('globals.LEAVE_UNITS.DAYS'),
            ]);

            // Sick Leave
            $sickLeave = CompanyLeaveType::create([
                'company_id' => $company->id,
                'name_ar' => 'إجازة مرضية',
                'name_en' => 'Sick Leave',
                'is_primary' => true,
                'balance_period' => 'calendar year',
                'gender' => 'all',
            ]);

            CompanyLeaveTypePolicy::create([
                'company_id' => $company->id,
                'company_leave_type_id' => $sickLeave->id,
                'base_balance' => 30,
                'unit' => config('globals.LEAVE_UNITS.DAYS'),
            ]);

            // Rest Day
            $restDay = CompanyLeaveType::create([
                'company_id' => $company->id,
                'name_ar' => 'يوم راحة',
                'name_en' => 'Rest Day',
                'is_primary' => true,
                'balance_period' => 'calendar month',
                'gender' => 'all',
            ]);

            CompanyLeaveTypePolicy::create([
                'company_id' => $company->id,
                'company_leave_type_id' => $restDay->id,
                'base_balance' => 1,
                'unit' => config('globals.LEAVE_UNITS.DAYS'),
            ]);
        }
    }
} 