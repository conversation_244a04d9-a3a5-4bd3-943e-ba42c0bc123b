<?php

namespace App\Console\Commands;

use App\Models\AttendanceOvertime;
use DB;
use Illuminate\Console\Command;

class FixOldOvertimesEmployeeRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-old-overtimes-employee-requests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $pendingOvertimes = AttendanceOvertime::where('status', 'pending')
                ->get();
            echo 'Pending overtimes count: '.$pendingOvertimes->count()."\n";

            foreach ($pendingOvertimes as $overtime) {
                $employeeRequests = $overtime->employeeRequests;
                foreach ($employeeRequests as $employeeRequest) {
                    if ($employeeRequest->status == 'pending') {
                        continue;
                    }
                    $employeeRequest->status = 'pending';
                    $employeeRequest->save();
                    echo 'Employee request updated: '.$employeeRequest->id."\n";
                }
            }

            echo 'Employee requests updated for overtimes'."\n";
            echo 'Pending overtimes count: '.$pendingOvertimes->count()."\n";

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            echo $e;
        }
    }
}
