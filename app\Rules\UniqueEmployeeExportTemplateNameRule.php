<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UniqueEmployeeExportTemplateNameRule implements Rule
{
    public function passes($attribute, $value): bool
    {
        $query = DB::table('employee_export_templates')
            ->where('name', $value)
            ->where('company_id', Auth::user()->company_id)
            ->whereNull('deleted_at');

        return !$query->exists();
    }

    public function message(): string
    {
        return trans('validation.template_name_exists');
    }
}
