<?php

namespace App\Repositories\V1\Employee;

use App\Models\EmployeeContract;
use App\Repositories\BaseRepository;

class EmployeeContractRepository extends BaseRepository
{
    public function model(): string
    {
        return EmployeeContract::class;
    }
    public function getByEmployeeId(int $employeeId)
    {
        return $this->model->where('employee_id', $employeeId)
            ->orderBy('contract_start_date', 'desc')
            ->get();
    }
}