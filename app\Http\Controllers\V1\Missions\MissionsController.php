<?php

namespace App\Http\Controllers\V1\Missions;

use App\Exceptions\UnprocessableException;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\Missions\AddMissionRequest;
use App\Http\Requests\V1\Missions\AddMissionsPolicyRequest;
use App\Http\Requests\V1\Missions\ApproveMissionRequest;
use App\Http\Requests\V1\Missions\EditMissionRequest;
use App\Services\V1\Missions\MissionService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MissionsController extends NewController
{
    public function __construct(private MissionService $missionService)
    {
    }

    public function addMissionsPolicy()
    {
        try {
            DB::transaction(function () {
                $this->missionService->toggleMissionsPolicy();
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'updated successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function assignTitlesToMissionPolicy(AddMissionsPolicyRequest $request)
    {
        try {
            DB::transaction(function () use ($request) {
                $this->missionService->assignTitlesToMissionsPolicy($request->validated());
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'updated successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function getMissionPolicy()
    {
        $policy = $this->missionService->getMissionPolicy();

        return getResponseStructure(
            ['data' => $policy],
            HttpStatusCodeUtil::OK,
            'Mission Policy'
        );
    }

    public function requestMission(AddMissionRequest $request)
    {
        try {
            DB::transaction(function () use ($request) {
                $data = $request->validated();
                $this->missionService->validateMissionRequest($data);
                $this->missionService->validateNoPendingMissionRequest($data);
                $this->missionService->requestMission($data);
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Mission requested successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function editMission($id, EditMissionRequest $request)
    {
        try {
            DB::transaction(function () use ($id, $request) {
                $data = $request->validated();
                $this->missionService->validateEditMissionRequest($id);
                $this->missionService->editMission($id, $data);
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Mission edited successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function approveMission($missionId, ApproveMissionRequest $request)
    {
        try {
            DB::transaction(function () use ($missionId, $request) {
                $data = $request->validated();
                $this->missionService->validateApproveMissionAction($missionId, $data);
                $this->missionService->actionOnMission($missionId, 'approve', $data['with_attendance'] == true ? $data : null);
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Mission approved successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function cancelMission($missionId)
    {
        try {
            DB::transaction(function () use ($missionId) {
                $this->missionService->validateCancelMissionAction($missionId);
                $this->missionService->actionOnMission($missionId, 'cancel');
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Mission cancelled successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function rejectMission($missionId)
    {
        try {
            DB::transaction(function () use ($missionId) {
                $this->missionService->actionOnMission($missionId, 'reject');
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Mission rejected successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function getOverlappingAttendance($missionId)
    {

        $overlappingAttendance = $this->missionService->getOverlappingAttendance($missionId);

        return getResponseStructure(['data' => ['overlapping_attendance' => isset($overlappingAttendance)]],
            HttpStatusCodeUtil::OK,
            'Overlapping attendance checked successfully');
    }
}
