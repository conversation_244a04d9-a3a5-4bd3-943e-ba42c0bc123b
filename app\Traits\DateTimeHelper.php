<?php

namespace App\Traits;

use Carbon\Carbon;

trait DateTimeHelper
{
    public function convertToHoursMinutes(int $minutes): string
    {
        if ($minutes <= 0) {
            return '';
        }

        $hours = floor($minutes / 60);
        $minutes = $minutes % 60;

        return sprintf('%u:%02u', $hours, $minutes);
    }

    public function parseDateFromExcel($date)
    {
        if (! isset($date) || $date === '') {
            return null;
        }
        if (is_numeric($date)) {
            try {
                $dateTime = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($date);

                return $dateTime->format('Y-m-d');
            } catch (\Exception $e) {
            }
        }

        $formats = [
            'Y-m-d', 'm/d/Y', 'm-d-Y', 'd/m/Y', 'd-m-Y', 'Y/m/d', 'Y.m.d',
            'd.m.Y', 'm.d.Y', 'Ymd', 'dmY', 'mdY',
            'd-M-Y', 'd/M/Y', 'M-d-Y', 'M/d/Y', 
        ];
        foreach ($formats as $format) {
            $dateTime = \DateTime::createFromFormat($format, $date);
            $errors = \DateTime::getLastErrors();
            if ($dateTime !== false && empty($errors['warning_count']) && empty($errors['error_count'])) {
                return $dateTime->format('Y-m-d');
            }
        }
        try {
            $dateTime = Carbon::parse($date);

            return $dateTime->format('Y-m-d');
        } catch (\Exception $e) {
        }

        return null;
    }
}
