<?php

namespace App\Console\Commands;

use App\Repositories\NewEmployeeRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CheckTerminatedEmployees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-terminated-employees';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will get all the employees whose termination date is in the past until today
     and convert their status to terminated';

    protected $employeeRepository;

    public function __construct(NewEmployeeRepository $employeeRepository)
    {
        parent::__construct();
        $this->employeeRepository = $employeeRepository;
    }

    /**
     * Execute the console command.
     * remove only active employees that are from the past until today
     */
    public function handle()
    {
        DB::beginTransaction();

        try {
            $employees = $this->employeeRepository->getActiveEmployeesWithPastTerminationDate();

            foreach ($employees as $employee) {
                $employee->status = 'terminated';
                $employee->save();

                $this->info("Employee ID {$employee->id} status updated to terminated.");
            }

            DB::commit();
            $this->info('All applicable employees have been updated.');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Failed to update employees: '.$e->getMessage());
        }
    }
}
