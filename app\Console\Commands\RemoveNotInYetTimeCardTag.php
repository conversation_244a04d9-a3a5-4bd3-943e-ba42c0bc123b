<?php

namespace App\Console\Commands;

use App\Services\TimeTracking\CrudServices\EntityTagCrudService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RemoveNotInYetTimeCardTag extends Command
{
    public function __construct(private EntityTagCrudService $entityTagCrudService)
    {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trigger:remove:noInYet:timeCards';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $this->entityTagCrudService->removeNotInYetTagForTimeCardsHasAttendance();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
