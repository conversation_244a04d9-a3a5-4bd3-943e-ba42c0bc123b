<?php

namespace App\Services\V1\Payroll;

use App\Repositories\NewCompanyRepository;
use App\Repositories\V1\Payroll\PayslipRepositoryV2;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Collection;
use ZipArchive;
use Exception;

/**
 * Simple PayslipService - Generate PDFs and create ZIP
 */
class PayslipServiceV2
{
    private PayslipRepositoryV2 $payslipRepository;
    private NewCompanyRepository $newCompanyRepository;
    private PayrollsRepository $payrollsRepository;
    private array $config;
    private int|null $companyId;

    public function __construct(PayslipRepositoryV2 $payslipRepository, NewCompanyRepository $newCompanyRepository, PayrollsRepository $payrollsRepository)
    {
        $this->payslipRepository = $payslipRepository;
        $this->newCompanyRepository = $newCompanyRepository;
        $this->payrollsRepository = $payrollsRepository;

        // Load configuration from config/payslips.php
        $this->config = config('payslips', [
            'font_path' => storage_path('fonts/'),
            'default_language' => 'en'
        ]);

        // Don't resolve company ID in constructor - do it lazily when needed
        $this->companyId = null;

        // Ensure we have the required configuration structure
        if (!isset($this->config['fonts'])) {
            $this->config['fonts'] = [
                'arabic' => 'amiri-regular.ttf'
            ];
        }

        // Register Arabic font if needed
        $this->registerAmiriFont();
    }

    /**
     * Register Arabic fonts for Spatie PDF
     */
    private function registerAmiriFont(): void
    {
        try {
            // Google Fonts or system fonts will be used in the blade template
            // This method validates availability but doesn't register fonts like DomPDF

            $fontPath = storage_path('fonts/');
            $regularFont = $fontPath . 'amiri_normal_672f774abaeb413c5b04eda0ce2f0675.ttf';
            $boldFont = $fontPath . 'amiri_bold_513907a30625db677a0d0853f0ebf18f.ttf';

            // Check if font files exist for fallback scenarios
            if (!file_exists($regularFont) || !file_exists($boldFont)) {
                Log::info('Local Arabic font files not found, will use Google Fonts', [
                    'regular_expected' => $regularFont,
                    'bold_expected' => $boldFont
                ]);
            } else {
                Log::info('Local Arabic fonts available as fallback', [
                    'regular_font' => $regularFont,
                    'bold_font' => $boldFont
                ]);
            }

        } catch (Exception $e) {
            Log::error('Failed to check Arabic fonts', [
                'error' => $e->getMessage()
            ]);
            // Don't throw exception, just log warning - system can still work without Arabic fonts
        }
    }

    /**
     * Get the company ID with lazy loading and fallback
     */
    private function getCompanyId(): int

    {
        if ($this->companyId === null) {
            $this->companyId = config('globals.company')?->id ?? 20;

            if (!$this->companyId) {
                throw new Exception('Company ID could not be resolved. Please ensure user is authenticated and company is configured.');
            }
        }

        return $this->companyId;
    }

    /**
     * Get payslips data
     */
    public function getPayslips(array $data): Collection
    {
        // Prepare payroll data from end_date (company_id resolved automatically)
        $payrollData = $this->preparePayrollData($data);

        return $this->payslipRepository->getPayslips($payrollData);
    }

    /**
     * Main method: Generate and store payslips as ZIP
     */
    public function generateAndStorePayslips(array $data): array
    {
        try {
            // Prepare payroll data from end_date (company_id resolved automatically)
            $payrollData = $this->preparePayrollData($data);

            Log::info('Starting payslip generation', [
                'original_data' => $data,
                'payroll_data' => $payrollData
            ]);

            // 1. Get data
            $rawPayslips = $this->payslipRepository->getPayslips($payrollData);
            if ($rawPayslips->isEmpty()) {
                throw new Exception('No payslip data found');
            }

            $payslips = $this->processPayslipsData($rawPayslips);
            // Use company_id from the resolved payroll data
            $company = $this->newCompanyRepository->getCompanyInfo($payrollData['company_id']);

            // 2. Create ZIP file path
            $zipInfo = $this->createZipPath($company, $payrollData['month'], $payrollData['year']);

            // 3. Generate PDFs and create ZIP
            $language = $data['lang'] ?? 'en';
            Log::info('Payslip generation language', [
                'requested_language' => $data['lang'] ?? 'not_set',
                'final_language' => $language,
                'data_keys' => array_keys($data)
            ]);

            $this->generatePDFsAndCreateZip($payslips, $zipInfo, $company->name, $language);

            Log::info('Payslip generation completed', [
                'total_employees' => $payslips->count(),
                'zip_path' => $zipInfo['full_path']
            ]);

            return [
                'path' => $zipInfo['full_path'],
                'total_employees' => $payslips->count()
            ];

        } catch (Exception $e) {
            Log::error('Payslip generation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Process raw payslip data
     */
    private function processPayslipsData(Collection $rawPayslips): Collection
    {
        return $rawPayslips->groupBy('employee_id')->map(function ($employeeComponents) {
            $firstRecord = $employeeComponents->first();

            $categories = $employeeComponents->groupBy('category_name')->map(function ($components) {
                $firstComponent = $components->first();
                $isAddition = $firstComponent->category_is_addition ?? true; // Default to true if not set

                return [
                    'is_addition' => $isAddition,
                    'components' => $components->map(function ($component) {
                        return [
                            'name' => $component->component_name,
                            'amount' => number_format($component->amount, 2)
                        ];
                    })->toArray()
                ];
            })->toArray();

            return [
                'employee' => [
                    'id' => $firstRecord->employee_id,
                    'name' => $firstRecord->employee_name,
                    'code' => $firstRecord->code,
                    'title' => $firstRecord->title,
                    'net_salary' => number_format($firstRecord->net_salary, 2)
                ],
                'categories' => $categories,
                'tax_amount' => number_format($firstRecord->tax_amount, 2),
                'insurance_amount' => number_format($firstRecord->insurance_amount, 2)
            ];
        })->values();
    }

    /**
     * Create ZIP file path structure
     */
    private function createZipPath(object $company, int $month, int $year): array
    {
        $companyCode = preg_replace('/[^a-zA-Z0-9_-]/', '_', $company->code);
        $baseDir = "companies/{$companyCode}/payslips";
        $fileName = sprintf('%02d_%d_payslips.zip', $month, $year);
        $fullPath = "{$baseDir}/{$fileName}";

        return [
            'base_dir' => $baseDir,
            'file_name' => $fileName,
            'full_path' => $fullPath,
            'absolute_path' => storage_path("app/public/{$fullPath}")
        ];
    }

    /**
     * Core method: Generate PDFs and create ZIP
     */
    private function generatePDFsAndCreateZip(Collection $payslips, array $zipInfo, string $companyName, string $language): void
    {
        // Ensure ZIP directory exists
        $zipDir = dirname($zipInfo['absolute_path']);
        if (!File::exists($zipDir)) {
            File::makeDirectory($zipDir, 0755, true);
        }

        // Remove existing ZIP file
        if (File::exists($zipInfo['absolute_path'])) {
            File::delete($zipInfo['absolute_path']);
        }

        // Create ZIP
        $zip = new ZipArchive();
        $result = $zip->open($zipInfo['absolute_path'], ZipArchive::CREATE);

        if ($result !== true) {
            throw new Exception("Cannot create ZIP file. Error code: {$result}");
        }

        try {
            // Generate PDF for each employee and add to ZIP
            foreach ($payslips as $payslip) {
                // 1. Generate PDF for employee
                $pdf = $this->generateEmployeePDF($payslip, $companyName, $language);

                // 2. Add PDF to ZIP
                $fileName = "payslip_{$payslip['employee']['code']}.pdf";
                $pdfContent = base64_decode($pdf->base64());

                $zip->addFromString($fileName, $pdfContent);

                Log::debug("Added PDF to ZIP: {$fileName}");
            }

            // 3. Store ZIP
            $zip->close();

            // Verify ZIP was created
            if (!File::exists($zipInfo['absolute_path'])) {
                throw new Exception('ZIP file was not created');
            }

            Log::info('ZIP file created successfully', [
                'path' => $zipInfo['absolute_path'],
                'size' => File::size($zipInfo['absolute_path']) . ' bytes'
            ]);

        } catch (Exception $e) {
            $zip->close();
            if (File::exists($zipInfo['absolute_path'])) {
                File::delete($zipInfo['absolute_path']);
            }
            throw $e;
        }
    }

    /**
     * Generate PDF for single employee using Spatie PDF
     */
    private function generateEmployeePDF(array $payslip, string $companyName, string $language)
    {
        // Set the application locale for translations
        $previousLocale = app()->getLocale();
        app()->setLocale($language);

        $langConfig = $this->config['supported_languages'][$language] ?? $this->config['supported_languages']['en'];

        // Debug logging
        Log::info('Language configuration debug', [
            'language' => $language,
            'langConfig' => $langConfig,
            'direction' => $langConfig['direction'] ?? 'not_set',
            'full_config' => $this->config
        ]);

        $data = [
            'employee' => $payslip['employee'],
            'categories' => $payslip['categories'],
            'tax_amount' => $payslip['tax_amount'],
            'insurance_amount' => $payslip['insurance_amount'],
            'company_name' => $companyName,
            'direction' => $langConfig['direction'] ?? 'ltr',
            'language' => $language,
            'month' => $language === 'ar' ? $this->getArabicMonth(date('n')) : date('F'),
            'year' => date('Y')
        ];

        try {
            // Configure PDF with Arabic font support using Spatie PDF
            $pdf = SpatiePdf::view('admin.pdf-templates.payslip', $data)
                ->format(\Spatie\LaravelPdf\Enums\Format::A4)
                ->margins(20, 20, 20, 20)
                ->footerHtml('')
                ->headerHtml('');

            // Configure Browsershot for Arabic fonts and Chrome options
            $pdf->withBrowsershot(function ($browsershot) use ($language) {
                // Set Chrome executable path for Windows
                $chromePath = 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe';
                if (file_exists($chromePath)) {
                    $browsershot->setChromePath($chromePath);
                }

                $args = [
                    '--no-sandbox',
                    '--disable-web-security',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--headless',
                    '--disable-software-rasterizer',
                    '--remote-allow-origins=*'
                ];

                // Enhanced Arabic font support
                if ($language === 'ar') {
                    $args = array_merge($args, [
                        '--font-render-hinting=none',
                        '--disable-font-subpixel-positioning',
                        '--enable-font-antialiasing',
                        '--disable-features=VizDisplayCompositor'
                    ]);
                }

                $browsershot->setOption('args', $args);
                $browsershot->timeout(60);

                // Wait for fonts to load for Arabic content
                if ($language === 'ar') {
                    $browsershot->waitUntilNetworkIdle(1000, 3);
                }
            });

            // Restore the previous locale
            app()->setLocale($previousLocale);

            return $pdf;

        } catch (Exception $e) {
            app()->setLocale($previousLocale);
            Log::error("Failed to generate PDF for employee", [
                'employee_id' => $payslip['employee']['id'],
                'error' => $e->getMessage()
            ]);
            throw new Exception("PDF generation failed for employee {$payslip['employee']['id']}: " . $e->getMessage());
        }
    }

    /**
     * Prepare payroll data from end_date parameter
     * 
     * @param array $data - contains end_date (company_id is resolved automatically)
     * @return array - contains month, year, payroll_id, company_id
     */
    private function preparePayrollData(array $data): array
    {
        // Extract month and year from end_date
        $dateTo = \Carbon\Carbon::parse($data['end_date']);
        $month = $dateTo->month;
        $year = $dateTo->year;

        // Use the globally resolved company ID from constructor
        $companyId = $this->getCompanyId();

        // Find payroll_id using PayrollsRepository
        $payrollId = $this->payrollsRepository->findPayrollId([
            'month' => $month,
            'year' => $year,
            'company_id' => $companyId
        ]);

        return [
            'month' => $month,
            'year' => $year,
            'payroll_id' => $payrollId,
            'company_id' => $companyId
        ];
    }


    /**
     * Get Arabic month name
     */
    private function getArabicMonth(int $monthNumber): string
    {
        $arabicMonths = [
            1 => 'يناير',
            2 => 'فبراير',
            3 => 'مارس',
            4 => 'أبريل',
            5 => 'مايو',
            6 => 'يونيو',
            7 => 'يوليو',
            8 => 'أغسطس',
            9 => 'سبتمبر',
            10 => 'أكتوبر',
            11 => 'نوفمبر',
            12 => 'ديسمبر'
        ];

        return $arabicMonths[$monthNumber] ?? date('F');
    }
}
