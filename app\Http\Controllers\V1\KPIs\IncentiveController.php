<?php

namespace App\Http\Controllers\V1\KPIs;

use App\Exports\V1\AddScoresPerEmployeeTemplate;
use App\Exports\V1\AddScoresPerTitleTemplate;
use App\Exports\V1\exportIncentivePerEmployee;
use App\Exports\V1\exportIncentivePerTitle;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\KPIs\AddScoresPerEmployeeRequest;
use App\Http\Requests\V1\KPIs\AddScoresPerTitleRequest;
use App\Http\Requests\V1\KPIs\CreateIncentiveRequest;
use App\Http\Requests\V1\KPIs\EditIncentiveRequest;
use App\Http\Requests\V1\KPIs\GetAllIncentivesSettingsRequest;
use App\Http\Requests\V1\KPIs\GetIncentiveRequest;
use App\Http\Requests\V1\KPIs\GetIncentivesSettingsRequest;
use App\Http\Requests\V1\KPIs\GetIncentiveTemplateRequest;
use App\Http\Requests\V1\KPIs\UploadIncentivePerEmployeeScoresRequest;
use App\Http\Requests\V1\KPIs\UploadIncentivePerTitleScoresRequest;
use App\Imports\V1\IncentivePerEmployeeScoresImport;
use App\Imports\V1\IncentivePerTitleScoresImport;
use App\Services\V1\KPIs\IncentiveService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class IncentiveController extends NewController
{
    public function __construct(IncentiveService $service)
    {
        $this->service = $service;
    }

    public function createIncentive(CreateIncentiveRequest $request)
    {
        try {
            $data = $request->validated();

            DB::beginTransaction();
            $incentive = $this->service->createIncentive($data);
            DB::commit();

            return getResponseStructure(['data' => $incentive], HttpStatusCodeUtil::OK, 'Incentive Added Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function editIncentive(int $id, EditIncentiveRequest $request)
    {
        try {
            $data = $request->validated();

            DB::beginTransaction();
            $this->service->editIncentive($id, $data);
            DB::commit();

            return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK, 'Incentive Edited Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function deleteIncentive(int $id)
    {
        try {

            DB::beginTransaction();
            $this->service->deleteIncentive($id);
            DB::commit();

            return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK, 'Incentive Deleted Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function getAllIncentivesSettings(GetAllIncentivesSettingsRequest $request)
    {
        try {
            $data = $request->validated();
            $incentivesSettings = $this->service->getAllIncentivesSettings($data);

            return getResponseStructure(['data' => $incentivesSettings], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            throw $e;
        }
    }

    public function getIncentiveSettings(int $id, GetIncentivesSettingsRequest $request)
    {
        try {
            $data = $request->validated();
            $incentiveSettings = $this->service->getIncentiveSettings($id, $data);

            return getResponseStructure(['data' => $incentiveSettings], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            throw $e;
        }
    }

    public function getIncentivesPerTitle(GetIncentiveRequest $request)
    {
        try {
            $data = $request->validated();
            $incentivesPerTitle = $this->service->getIncentivesPerTitle($data);

            return getResponseStructure($incentivesPerTitle, HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            throw $e;
        }
    }

    public function getIncentivesPerEmployee(GetIncentiveRequest $request)
    {
        try {
            $data = $request->validated();
            $incentivesPerEmployee = $this->service->getIncentivesPerEmployee($data);

            return getResponseStructure($incentivesPerEmployee, HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            throw $e;
        }
    }

    public function getIncentivesPerEmployeeWithScores(int $id, GetIncentivesSettingsRequest $request)
    {
        try {
            $data = $request->validated();
            $incentivesPerEmployeeWithScores = $this->service->getIncentivesPerEmployeeWithScores($id, $data);

            return getResponseStructure(['data' => $incentivesPerEmployeeWithScores], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            throw $e;
        }
    }

    public function addIncentivePerTitleScores($id, AddScoresPerTitleRequest $request)
    {
        try {
            $data = $request->validated();
            DB::beginTransaction();
            $this->service->addIncentivePerTitleScores($id, $data);
            DB::commit();

            return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK, 'Scores Added Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function addIncentivePerEmployeeScores($id, AddScoresPerEmployeeRequest $request)
    {
        try {
            $data = $request->validated();
            DB::beginTransaction();
            $this->service->addIncentivePerEmployeeScores($id, $data);
            DB::commit();

            return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK, 'Scores Added Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function exportIncentivePerTitle(GetIncentiveRequest $request)
    {
        $data = $request->validated();

        return Excel::download(new exportIncentivePerTitle($data, $this->service), 'Incentive_per_titles.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function exportAddScorePerTitleTemplate(GetIncentiveTemplateRequest $request)
    {
        $data = $request->validated();

        return Excel::download(new AddScoresPerTitleTemplate($data, $this->service), 'Incentive_per_title_scores_template.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function uploadIncentivePerTitleScores(UploadIncentivePerTitleScoresRequest $request)
    {
        $file = $request->file('Incentive_per_title_scores_template');
        $data = $request->validated();
        try {
            $errors = [];
            Excel::import(new IncentivePerTitleScoresImport($data, $this->service), $file);
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            foreach ($failures as $failure) {
                $errors[] = $failure->errors()[0].' at row '.$failure->row();
            }

            return $this->response(['errors' => $errors], 422, 'Errors in file');
        }

        return $this->response([], 200, 'Scores Added successfully');
    }

    public function exportIncentivePerEmployee(GetIncentiveRequest $request)
    {
        $data = $request->validated();

        return Excel::download(new exportIncentivePerEmployee($data, $this->service), 'Incentive_per_employees.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function exportAddScorePerEmployeeTemplate(GetIncentiveTemplateRequest $request)
    {
        $data = $request->validated();

        return Excel::download(new AddScoresPerEmployeeTemplate($data, $this->service), 'Incentive_per_employee_scores_template.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function uploadIncentivePerEmployeeScores(UploadIncentivePerEmployeeScoresRequest $request)
    {
        $file = $request->file('Incentive_per_employee_scores_template');
        $data = $request->validated();
        try {
            $errors = [];
            Excel::import(new IncentivePerEmployeeScoresImport($data, $this->service), $file);
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            foreach ($failures as $failure) {
                $errors[] = $failure->errors()[0].' at row '.$failure->row();
            }

            return $this->response(['errors' => $errors], 422, 'Errors in file');
        }

        return $this->response([], 200, 'Scores Added successfully');
    }
}
