<?php

namespace App\Http\Requests\V1\CostCenter;

use App\Enums\CostCenter\CalculationMethodEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateLocationCalculationMethodRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'method' => [
                'required',
                new Enum(CalculationMethodEnum::class),
            ],

        ];
    }
}
