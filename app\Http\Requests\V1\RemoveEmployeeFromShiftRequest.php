<?php

namespace App\Http\Requests\V1;

use App\Rules\BranchIdRule;
use App\Rules\EmployeeIdRule;
use Illuminate\Foundation\Http\FormRequest;

class RemoveEmployeeFromShiftRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'shift_id' => [
                'required',
                'integer',
                'exists:new_shifts,id',
            ],
            'employee_id' => [
                'required',
                'integer',
                new EmployeeIdRule,
            ],
            'branch_id' => [
                'required',
                'integer',
                new BranchIdRule,
            ],
            'date' => [
                'required',
                'date_format:Y-m-d',
            ],
        ];
    }
}
