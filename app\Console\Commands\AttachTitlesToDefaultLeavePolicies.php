<?php

namespace App\Console\Commands;

use App\Jobs\V1\FillEmployeeBaseBalancesJob;
use App\Models\CompanyDefaultLeaveType;
use App\Models\Employee;
use App\Models\Title;
use App\Util\EmployeeUtil;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AttachTitlesToDefaultLeavePolicies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:attach-titles-to-default-leave-policies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Attach titles to their company’s default leave type policies if not already attached';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $titles = Title::doesntHave('companyLeaveTypePolicies')->get();

        if ($titles->isEmpty()) {
            $this->info('✅ No titles found that need attaching.');

            return 0;
        }

        DB::beginTransaction();

        try {
            foreach ($titles as $title) {
                $this->info("🔄 Processing Title ID {$title->id} (“{$title->name_en}”) for Company ID {$title->company_id}");

                $company = $title->company;

                // 1) Rest / Annual / Sick
                $policies = [
                    'Rest Day' => optional($company->restDayLeaveType)->companyLeaveTypePolicy,
                    'Annual' => optional($company->annualLeaveType)->companyLeaveTypePolicy,
                    'Sick' => optional($company->sickLeaveType)->companyLeaveTypePolicy,
                ];

                foreach ($policies as $label => $policy) {
                    if ($policy) {
                        $policy->titles()->syncWithoutDetaching([$title->id]);
                        $this->info("   • Attached to {$label} Leave Policy (Policy ID: {$policy->id}).");
                    } else {
                        $this->line("   • Skipped {$label} Leave Policy (not defined).");
                    }
                }

                // 2) CompanyDefaultLeaveType entries
                $defaultLeaveTypes = CompanyDefaultLeaveType::with('companyLeaveType.companyLeaveTypePolicy')
                    ->where('company_id', $company->id)
                    ->get();

                foreach ($defaultLeaveTypes as $dlt) {
                    $policy = optional($dlt->companyLeaveType->companyLeaveTypePolicy);
                    if ($policy) {
                        $policy->titles()->syncWithoutDetaching([$title->id]);
                        $this->info("   • Attached to Default Leave Type “{$dlt->key}” (Policy ID: {$policy->id}).");
                    } else {
                        $this->line("   • Skipped Default Leave Type “{$dlt->key}” (policy not found).");
                    }
                }
            }
            $titleIds = $titles->pluck('id')->toArray();
            $nonTerminatedEmployees = Employee::where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                ->whereIn('title_id', $titleIds)
                ->with('employeeInfo', 'title.companyLeaveTypePolicies', 'company.maternityLeaveType', 'user', 'employeeLeaveBalances')
                ->get();

            dispatch(new FillEmployeeBaseBalancesJob($nonTerminatedEmployees));


            DB::commit();
            $this->info('🎉 All titles have been processed successfully.');

            return 0;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->error("🚨 Failed: {$e->getMessage()}");

            return 1;
        }
    }
}
