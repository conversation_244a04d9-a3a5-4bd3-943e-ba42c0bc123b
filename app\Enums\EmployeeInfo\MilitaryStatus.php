<?php

namespace App\Enums\EmployeeInfo;

enum MilitaryStatus: string
{
    case COMPLETED_SERVICE = 'completed_service';
    case EXEMPTED = 'exempted';
    case POSTPONED = 'postponed';
    case STUDENT = 'student';

    public static function all(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    public static function getTranslatedValues(): array
    {
        return array_map(fn ($case) => trans('employeeexport.' . $case->value), self::cases());
    }

    public static function getLabel(self $value): string
    {
        return trans('employeeexport.' . $value->value);
    }

    public static function fromLegacyValue(string $legacyValue): ?self
    {
        $legacyToNewMap = [
            'خدم الخدمة - Completed Service' => self::COMPLETED_SERVICE,
            'مستثنى - Exempted' => self::EXEMPTED,
            'مؤجل - Postponed' => self::POSTPONED,
            'طالب - Student' => self::STUDENT,
        ];

        return $legacyToNewMap[$legacyValue] ?? null;
    }

    public static function getLegacyValues(): array
    {
        return [
            'خدم الخدمة - Completed Service',
            'مستثنى - Exempted',
            'مؤجل - Postponed',
            'طالب - Student',
        ];
    }
}
