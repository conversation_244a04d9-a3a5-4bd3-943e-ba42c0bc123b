<?php

use App\Http\Controllers\V1\Employee\EmployeeController;
use App\Http\Controllers\V1\EmployeeProfile\EmployeeProfileController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'employee'], function () {
    Route::get('/', [EmployeeProfileController::class, 'list']);
    Route::get('weekly-schedule', [EmployeeController::class, 'employeeWeeklySchedule']);
    Route::get('requestsPendingOnMe', [EmployeeController::class, 'requestsPendingOnMe']);
    Route::get('requestsPendingOnMeCount', [EmployeeController::class, 'requestsPendingOnMeCount']);

    Route::get('requestsOnMe', [EmployeeController::class, 'getRequestsOnMe']);
    Route::get('requestsOnMe/count', [EmployeeController::class, 'requestsOnMeCount']);
    Route::get('requestsOnMe/export', [EmployeeController::class, 'exportRequestsOnMe']);

    Route::get('manager-monthly-view', [EmployeeController::class, 'getManagerMonthlyView'])->middleware(['permission:manage_payroll|manual_attendnace|manage_leaves']);
    Route::get('recent-code', [EmployeeController::class, 'getRecentEmployeeCode']);

    Route::get('my-permissions', [EmployeeController::class, 'getMyPermissions']);

    Route::get('transfer-logs', [EmployeeController::class, 'transferLogs'])->middleware(['permission:manage_company']);
    Route::get('transfer-logs/export', [EmployeeController::class, 'transferLogsExport'])->middleware(['permission:manage_company']);
    Route::post('enroll-face-id', [EmployeeController::class, 'enrollFaceId']);
    Route::get('generate-token-by-face-id', [EmployeeController::class, 'generateTokenFaceId']);

});
