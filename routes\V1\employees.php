<?php

use App\Http\Controllers\V1\EmployeeProfile\EmployeeProfileController;
use Illuminate\Support\Facades\Route;

Route::prefix('employees')->group(function () {

    Route::get('/', [EmployeeProfileController::class, 'list'])
        ->middleware(['permission:manage_company|lite_add|add_employee|view_employees']);

    Route::get('names', [EmployeeProfileController::class, 'getEmployeeNames'])
        ->middleware(['permission:manage_company']);

    Route::get('exports', [EmployeeProfileController::class, 'exportEmployees'])
        ->middleware(['permission:manage_company|add_employee']);
        
    Route::post('enhanced-exports', [EmployeeProfileController::class, 'enhancedExportEmployees'])
        ->middleware(['permission:manage_company|add_employee']);

    Route::post('bulk-template-download', [EmployeeProfileController::class, 'bulkTemplateDownload'])
        ->middleware(['permission:add_employee']);

    Route::post('bulk-upload', [EmployeeProfileController::class, 'bulkUpload'])
        ->middleware(['permission:add_employee']);

    Route::get('/info', [EmployeeProfileController::class, 'getEmployeeInfo']);

    Route::get('/{id}/info', [EmployeeProfileController::class, 'getEmployeeInfo']);

    Route::get('/{id}/salary', [EmployeeProfileController::class, 'getEmployeeSalaryInfo']);

    Route::get('/{id}/roles', [EmployeeProfileController::class, 'getEmployeeRoles']);

    Route::post('/{employee_id}/roles/{role_id}/assign', [EmployeeProfileController::class, 'assignRoleToEmployee']);

    Route::get('/{id}/payslip', [EmployeeProfileController::class, 'getEmployeePayslip'])
        ->middleware(['permission:manage_payroll|view_payroll|view_employee_salary_info|manage_employee_salary_info']);

    Route::get('/{id}/profile-picture', [EmployeeProfileController::class, 'getEmployeeProfilePicture']);

    Route::post('/profile-picture/upload', [EmployeeProfileController::class, 'uploadProfilePicture']);

    Route::post('/profile-picture/upload-by-manager', [EmployeeProfileController::class, 'uploadProfilePictureByManager'])
        ->middleware(['permission:lite_add|add_employee']);

    Route::post('/{id}/reset-device-id', [EmployeeProfileController::class, 'resetDeviceId'])
        ->middleware(['permission:reset_device_id']);

    Route::get('/scope-employees', [EmployeeProfileController::class, 'getScopeEmployees'])
        ->middleware(['permission:manage_company|lite_add|add_employee']);

    Route::get('/scope-employees-for-loans', [EmployeeProfileController::class, 'getScopeEmployeesForLoans'])
        ->middleware(['permission:add_loan']);

    Route::get('{id}/requests', [EmployeeProfileController::class, 'getEmployeeRequests'])
        ->middleware(['permission:manage_company|lite_add|add_employee']);

    Route::get('/employee-incentives', [EmployeeProfileController::class, 'getEmployeeIncentives']);

    Route::post('verify-Employee-pin', [EmployeeProfileController::class, 'verifyEmployeePin']);
    Route::middleware(['permission:manage_company|add_employee'])->group(function () {
        Route::prefix('export-template')->group(function () {
            Route::get('/getAll', [EmployeeProfileController::class, 'getEmployeeExportTemplates']);
            Route::post('/create', [EmployeeProfileController::class, 'createEmployeeExportTemplate']);
            Route::delete('/delete/{id}', [EmployeeProfileController::class, 'deleteEmployeeExportTemplate']);
        });
    });
    Route::middleware(['permission:add_employee|manage_company'])->group(function () {
        Route::prefix('profile')->group(function () {
            Route::post('/add', [EmployeeProfileController::class, 'addProfile']);
            Route::put('/{id}/update', [EmployeeProfileController::class, 'updateProfile']);
        });
    });
});
