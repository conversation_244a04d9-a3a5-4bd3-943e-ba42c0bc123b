<?php

namespace App\Console\Commands;

use App\Services\V1\Holidays\PublicHolidayAbsenceService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FillPublicHolidayAbsences extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill:public_holiday_absences';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = ' ';

    public function __construct(private PublicHolidayAbsenceService $publicHolidayAbsenceService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        DB::beginTransaction();
        try {
            $todaysDate = Carbon::now()->toDateString();
            $this->publicHolidayAbsenceService->fillPublicHolidayAbsencesOnDateForAllEmployees($todaysDate);
            DB::commit();
        } catch (Exception $e) {
            // \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
            // dd($e);
        }
    }
}
