<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class fixAbsnetTagDublication extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:absent_tag:duplicate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $duplicateIds = DB::select(DB::raw("
    SELECT t1.id
    FROM entity_tags AS t1
    JOIN entity_tags AS t2
    ON t1.entity_id = t2.entity_id
    AND t1.entity_type = t2.entity_type
    AND t1.tag = t2.tag
    WHERE t1.id < t2.id
    AND t1.tag = 'absent'
"));

        // Extract the IDs from the result
        $duplicateIds = array_map(function ($row) {
            return $row->id;
        }, $duplicateIds);

        // Delete the records with the duplicate IDs
        if (! empty($duplicateIds)) {
            DB::table('entity_tags')->whereIn('id', $duplicateIds)->delete();
        }
    }
}
