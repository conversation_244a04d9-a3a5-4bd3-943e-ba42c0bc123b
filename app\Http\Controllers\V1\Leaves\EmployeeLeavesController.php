<?php

namespace App\Http\Controllers\V1\Leaves;

use App\Exceptions\UnprocessableException;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\Leaves\AddEmergencyLeaveByManagerRequest;
use App\Http\Requests\V1\Leaves\RepayAnnualLeaveBalanceRequest;
use App\Http\Resources\V1\Leaves\LeaveBalancesOfTerminatedEmployeesCollection;
use App\Rules\EmployeeIdRule;
use App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService;
use App\Services\V1\LeaveManagement\EmployeeLeavesService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EmployeeLeavesController extends NewController
{
    public function __construct(
        private EmployeeLeavesService $leavesService,
        private EmployeeLeaveRequestCrudService $employeeLeaveRequestCrudService
    ) {
        parent::__construct($leavesService);
    }

    public function addEmergencyLeaveByManager(AddEmergencyLeaveByManagerRequest $request)
    {
        $data = $request->all(); // validation is done in the request class function prepare for validation
        $dummyOutput = new \stdClass;
        $dummyOutput->addLeaveByManager = true;
        DB::beginTransaction();
        $this->employeeLeaveRequestCrudService->create($data, $dummyOutput);
        if (isset($dummyOutput->Error)) {
            DB::rollBack();
            $lang = (config('globals.lang') ?? 'ar');
            throw new UnprocessableException($dummyOutput->Error[$lang == 'en' ? 0 : 1]);
        }

        DB::commit();

        return getResponseStructure(
            ['data' => $dummyOutput->employeeLeaveRequest],
            HttpStatusCodeUtil::CREATED,
            'Leave added successfully'
        );
    }

    public function bulkRepayAnnualLeaveBalance(RepayAnnualLeaveBalanceRequest $request)
    {
        $data = $request->validated();

        DB::beginTransaction();
        try {
            $this->leavesService->repayAnnualLeaveBalance($data);
            DB::commit();

            return getResponseStructure(
                [],
                HttpStatusCodeUtil::OK,
                'Annual leave balance repaid successfully'
            );
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function getEmployeeAnnualLeaveBalance($employeeId)
    {
        $this->validateEmployeeId($employeeId);

        $balanceEntity = $this->leavesService->getAnnualLeaveBalance($employeeId);

        return getResponseStructure(
            ['data' => $balanceEntity->balance],
            HttpStatusCodeUtil::OK,
            'Annual leave balance retrieved successfully'

        );
    }

    public function validateEmployeeId($employeeId)
    {
        $validation = new EmployeeIdRule;
        if (! $validation->passes('employee_id', $employeeId)) {
            throw new UnprocessableException($validation->message());
        }
    }

    public function getTerminatedEmployeesWithRemainingLeaveBalance()
    {

        $employeesWithBalances = $this->leavesService->getTerminatedEmployeesWithRemainingLeaveBalanceForTheLastDraftedPayroll();

        return getResponseStructure(
            ['data' => new LeaveBalancesOfTerminatedEmployeesCollection($employeesWithBalances)],
            HttpStatusCodeUtil::OK,
            'Terminated employees with remaining leave balance retrieved successfully'
        );
    }
}
