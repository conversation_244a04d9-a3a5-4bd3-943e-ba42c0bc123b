<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Enums\EntityTags\AttendanceTags;
use App\Models\Timecard;
use Illuminate\Support\Carbon;

class MissionTagHandler implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        // $absentTag = $employeeAttendance->entityTags->where('tag', 'absent')->first();
        return [
            'name' => $employeeAttendance->timecardType->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($employeeAttendance->from)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        return isset($tags[$employeeId]['tags'][AttendanceTags::MISSION->value]) ? $tags[$employeeId]['tags'][AttendanceTags::MISSION->value]['count'] + 1 : 1;
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return $employeeAttendance instanceof Timecard && $employeeAttendance->entityTags->pluck('tag')->contains($tag);
    }

    public function handleTagUnit(): string
    {
        return 'days';
    }
}
