<?php

namespace App\Console\Commands;

use App\Services\CompanySetup\CrudServices\EmployeeCrudService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AddEmployeesInfo extends Command
{
    public function __construct(private EmployeeCrudService $employeeCrudService)
    {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:employees:info';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $this->employeeCrudService->addEmployeesInfo();
            DB::commit();
        } catch (Exception $e) {
            Log::info('failed to add employees info');
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
