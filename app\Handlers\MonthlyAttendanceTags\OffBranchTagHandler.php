<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Models\Timecard;
use Illuminate\Support\Carbon;

class OffBranchTagHandler
{
    public function handleTagData($employeeAttendance): array
    {
        // $tag = $employeeAttendance->attendance->entityTags->where('tag', 'late')->first();
        return [
            'name' => $employeeAttendance->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($employeeAttendance->from)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        return isset($tags[$employeeId]['tags']['off_branch']) ? $tags[$employeeId]['tags']['off_branch']['count'] + 1 : 1;
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return $employeeAttendance instanceof Timecard &&
            $employeeAttendance->attendance &&
            $employeeAttendance->attendance->entityTags &&
            $employeeAttendance->attendance->entityTags->pluck('tag')->contains($tag);
    }

    public function handleTagUnit(): string
    {
        return 'incidents';
    }
}
