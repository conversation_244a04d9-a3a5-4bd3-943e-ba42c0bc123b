<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class BulkUniqueValueRule implements Rule
{
    protected array $existingValuesMap;
    protected string $validationMessageKey;
    protected string $attributeDisplayName;
    protected ?array $currentValuesMap;
    protected ?string $currentIdentifier;

    /**
     * Create a new rule instance for bulk operations.
     *
     * @param array $existingValuesMap Map of existing values (value => true/count)
     * @param string $validationMessageKey Translation key for validation message
     * @param string $attributeDisplayName Display name for the attribute in error messages
     * @param array|null $currentValuesMap Map of current values to exclude (identifier => value)
     * @param string|null $currentIdentifier Current row identifier (e.g., phone number)
     */
    public function __construct(
        array $existingValuesMap,
        string $validationMessageKey,
        string $attributeDisplayName,
        ?array $currentValuesMap = null,
        ?string $currentIdentifier = null
    ) {
        $this->existingValuesMap = $existingValuesMap;
        $this->validationMessageKey = $validationMessageKey;
        $this->attributeDisplayName = $attributeDisplayName;
        $this->currentValuesMap = $currentValuesMap;
        $this->currentIdentifier = $currentIdentifier;
    }

    /**
     * Determine if the validation rule passes.
     */
    public function passes($attribute, $value): bool
    {
        if (empty($value)) {
            return true;
        }

        if (!isset($this->existingValuesMap[$value])) {
            return true;
        }

        if ($this->currentValuesMap !== null && $this->currentIdentifier !== null) {
            $currentValue = $this->currentValuesMap[$this->currentIdentifier] ?? null;
            
            if ($value === $currentValue) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return __($this->validationMessageKey, ['attribute' => $this->attributeDisplayName]);
    }
}
