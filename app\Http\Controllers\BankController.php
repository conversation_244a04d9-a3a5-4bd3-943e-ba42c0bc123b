<?php

namespace App\Http\Controllers;

use App\DomainData\BankDto;
use App\DomainData\FilterDto;
use App\Services\PayrollSetup\CrudServices\BankCrudService;

class BankController extends Controller
{
    use BankDto, FilterDto;

    public function __construct(
        private BankCrudService $bankCrudService,
    ) {}

    public function getByFilter(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules(['page', 'filters', 'related_objects.*', 'related_objects_count.*',  'page_size']);

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['related_objects'])) {
            $request['related_objects'] = [];
        }
        if (! isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $this->bankCrudService->getByFilter($request, $output);

    }

    public function create(array $request, \stdClass &$output): void
    {

        $rules = $this->getRules(['name']);

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $this->bankCrudService->create($request, $output);

    }
}
