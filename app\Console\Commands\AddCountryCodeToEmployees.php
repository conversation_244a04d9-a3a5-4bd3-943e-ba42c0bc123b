<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddCountryCodeToEmployees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'employees:add-country-code';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Egypt country code (+20) to all employees phone numbers if not already formatted';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating employee phone numbers in bulk...');

        $updated = DB::update("
            UPDATE employees
            SET phone = CONCAT('+2', phone)
            WHERE phone NOT LIKE '+%';
        ");

        $this->info("Completed. Total phones updated: {$updated}");
    }
}