<?php

namespace App\Jobs;

use App\Services\V1\Missions\MissionService;
use App\Traits\V1\NotificationRedirection;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class HandleActionOnMissionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, NotificationRedirection, Queueable, SerializesModels;

    public $tries = 250;

    public $timeout = 120;

    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function handle()
    {
        try {
            Log::info('mission job is started');
            $attendanceData = Arr::get($this->data, 'attendance_data', null);
            $missionService = app(MissionService::class);
            if ($this->data['final_status'] == config('globals.REQUEST_STATUSES.APPROVED')) {
                $missionService->resolveForApprovedMission($this->data['mission_request'], $attendanceData);
            } elseif ($this->data['final_status'] == config('globals.REQUEST_STATUSES.CANCELLED')) {
                $missionService->resolveForCancelledMission($this->data['mission_request']);
            }
            $this->redirectNotificationsAfterRequestFinalized($this->data['mission_request'], $this->data['final_status']);

        } catch (\Exception $e) {
            Log::info('error in mission job');
            Log::info($e);
            // \Sentry\captureException($e);
        }
    }
}
