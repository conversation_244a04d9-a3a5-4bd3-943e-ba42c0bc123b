<?php

namespace App\Http\Requests\V1\KPIs;

use App\Rules\IncentivesLeaveTypeIdsRule;
use App\Rules\IsArabic;
use Illuminate\Foundation\Http\FormRequest;

class CreateIncentiveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name_ar' => ['required', 'string'], //, new IsArabic],
            'name_en' => ['string'],
            'type' => 'required|string|in:title,employee',
            'title_ids' => 'required|array',
            'title_ids.*' => 'required|integer|exists:titles,id',
            'branch_ids' => 'required|array',
            'branch_ids.*' => 'required|integer|exists:branches,id',
            'payout_frequency' => 'required|string|in:monthly,quarterly,semi_annually,annually',
            'calculation_method' => 'required|string|in:fixed,salary_percentage',
            'fixed_amount' => 'required_if:calculation_method,fixed|numeric',
            'salary_percentage' => 'required_if:calculation_method,salary_percentage|numeric',
            'all_titles' => 'boolean',
            'all_branches' => 'boolean',
            'leave_type_ids' => new IncentivesLeaveTypeIdsRule,
            'incentive_parameters' => 'required|array',
            'incentive_parameters.*.name_ar' => ['required', 'string'], //, new IsArabic],
            'incentive_parameters.*.name_en' => ['string'],
            'incentive_parameters.*.operator' => 'required|string|in:=',
            'incentive_parameters.*.weight' => 'required|integer',
        ];
    }
}
