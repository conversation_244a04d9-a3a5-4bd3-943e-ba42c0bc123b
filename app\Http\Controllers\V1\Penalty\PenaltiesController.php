<?php

namespace App\Http\Controllers\V1\Penalty;

use App\Exports\PenaltiesExport;
use App\Http\Controllers\NewController;
use App\Http\Requests\Penalties\AddPenaltyRequest;
use App\Http\Requests\Penalties\ApprovePenaltyRequest;
use App\Http\Requests\Penalties\GetPenaltiesByFilterRequest;
use App\Http\Requests\Penalties\GetPenaltyGroupsRequest;
use App\Http\Requests\Penalties\PenaltiesDateRequest;
use App\Http\Resources\GetEditHistoryCollection;
use App\Http\Resources\V1\Penalties\PenaltiesCollection;
use App\Http\Resources\V1\Penalties\PenaltiesResource;
use App\Services\V1\Penalties\PenaltiesService;
use App\Services\V1\Penalties\PenaltyGroupsService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class PenaltiesController extends NewController
{
    public function __construct(PenaltiesService $service, private PenaltyGroupsService $penaltyGroupsService)
    {
        parent::__construct($service);
    }

    public function filePenalty(AddPenaltyRequest $request)
    {
        $data = $request->validated();

        $penaltyRequest = [];
        DB::beginTransaction();
        try {
            $penaltyRequest = $this->service->add($data);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }

        return getResponseStructure(['data' => $penaltyRequest],
            HttpStatusCodeUtil::OK, 'Penalty Filed successfully');
    }

    public function getPenaltyGroups(GetPenaltyGroupsRequest $request)
    {
        $data = $request->validated();

        $penaltyGroups = [];

        if (isset($data['employee_id'])) {
            $penaltyGroups = $this->penaltyGroupsService->getPenaltyGroupsApplicableToEmployee($data['employee_id']);
        } else {
            $penaltyGroups = $this->penaltyGroupsService->getPenaltyGroupsNameAndDescription();
        }

        return getResponseStructure(['data' => $penaltyGroups],
            HttpStatusCodeUtil::OK, 'Penalty Groups fetched successfully');
    }

    public function getPenaltyGroupsForWorker()
    {

        $penaltyGroups = $this->penaltyGroupsService->getPenaltyGroupsApplicableToEmployee(auth()->user()->employee_id);

        return getResponseStructure(['data' => $penaltyGroups],
            HttpStatusCodeUtil::OK, 'Penalty Groups fetched successfully');
    }

    public function editPenalty($id, AddPenaltyRequest $request)
    {
        $data = $request->validated();

        $penaltyRequest = [];
        DB::beginTransaction();
        try {
            $penaltyRequest = $this->service->update($id, $data);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }

        return getResponseStructure(['data' => $penaltyRequest],
            HttpStatusCodeUtil::OK, 'Penalty Edited successfully');
    }

    public function cancelPenalty($id)
    {
        $this->service->cancel($id);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Penalty Cancelled successfully');
    }

    public function approvePenalty($id, ApprovePenaltyRequest $request)
    {
        $data = $request->validated();

        if (! isset($data['unit'])) {
            $data['unit'] = 'days';
        }

        DB::beginTransaction();
        try {
            $penaltyRequest = $this->service->approvePenalty($id, $data);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }

        return getResponseStructure(['data' => $penaltyRequest],
            HttpStatusCodeUtil::OK, 'Penalty Approved successfully');
    }

    public function rejectPenalty($id)
    {

        $penaltyRequest = [];
        DB::beginTransaction();
        try {
            $penaltyRequest = $this->service->rejectPenalty($id);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }

        return getResponseStructure(['data' => $penaltyRequest],
            HttpStatusCodeUtil::OK, 'Penalty rejected successfully');
    }

    public function getPenalty($id)
    {
        $penalty = $this->service->getPenalty($id);
        $penalty->{'edit_history'} = new GetEditHistoryCollection($this->service->getPenaltyEditHistory($id) ?? []);

        return getResponseStructure(['data' => new PenaltiesResource($penalty)],
            HttpStatusCodeUtil::OK, 'Penalty fetched successfully');
    }

    public function getWorkerPenalties(PenaltiesDateRequest $request)
    {
        $data = $request->validated();
        $penalties = new PenaltiesCollection($this->service->getWorkerPenalties($data));

        return getResponseStructure(['data' => $penalties],
            HttpStatusCodeUtil::OK, 'Penalties fetched successfully');
    }

    public function getPenaltiesByFilter(GetPenaltiesByFilterRequest $request)
    {
        $data = $request->validated();
        if (! isset($data['page_size'])) {
            $data['page_size'] = 10;
        }
        $penalties = $this->service->getPenaltiesByFilter($data);
        $response = new PenaltiesCollection($penalties);
        $peginatedData = $response->response()->getData();

        return getResponseStructure(['data' => $response, 'pagination' => $peginatedData->meta],
            HttpStatusCodeUtil::OK, 'Penalties fetched successfully');
    }

    public function exportPenalties(GetPenaltiesByFilterRequest $request)
    {
        $data = $request->validated();

        $penalties = $this->service->getPenaltiesByFilter($data);

        return Excel::download(new PenaltiesExport($penalties), 'Penalties.xlsx');
    }

    public function getPendingPenaltiesCount()
    {
        $count = $this->service->getPendingPenaltiesCount();

        return getResponseStructure(['data' => $count],
            HttpStatusCodeUtil::OK, 'Pending Penalties Count fetched successfully');
    }
}
