<?php

namespace App\Http\Controllers\Payroll\PayrollHub;

use App\Exceptions\UnprocessableException;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\PayrollHub\AbsentBulkActionsRequest;
use App\Http\Requests\V1\PayrollHub\CompanyPayrollEntriesRequest;
use App\Http\Requests\V1\PayrollHub\ConvertAbsenceToLeaveRequest;
use App\Http\Requests\V1\PayrollHub\EmployeePayrollEntriesRequest;
use App\Http\Resources\V1\PayrollHub\EmployeeResource;
use App\Http\Resources\V1\PayrollHub\GetCompanyAbsencesResource;
use App\Http\Resources\V1\PayrollHub\GetEmployeeAbsencesResource;
use App\Services\V1\PayrollHub\AbsencesService;
use App\Services\V1\PayrollHub\AttendanceBulkActionService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AbsenceController extends NewController
{
    public function __construct(
        protected AbsencesService $absencesService,
        protected AttendanceBulkActionService $attendanceBulkActionService
    ) {}

    public function getAllAbsencesForCompany(CompanyPayrollEntriesRequest $request)
    {
        $absences = $this->absencesService->getCompanyAbsences($request->validated());
        $data = GetCompanyAbsencesResource::collection($absences->companyAbsences);
        $paginatedData = $data->response()->getData();

        return getResponseStructure(
            [
                'data' => $data,
                'num_of_absences' => $absences->total_number_of_absences,
                'pagination' => $paginatedData->meta,
            ],
            HttpStatusCodeUtil::OK,
            'Absences fetched successfully'
        );
    }

    public function getEmployeeAbsences(EmployeePayrollEntriesRequest $request)
    {
        $data = $this->absencesService->getEmployeeAbsences($request->validated());

        return getResponseStructure(
            ['data' => $data->has_absences ? new GetEmployeeAbsencesResource($data) : new EmployeeResource($data)],
            HttpStatusCodeUtil::OK,
            'Absences fetched successfully'
        );
    }

    public function addBulkAttendance(AbsentBulkActionsRequest $request)
    {
        try {
            DB::transaction(function () use ($request) {
                $this->attendanceBulkActionService->bulkAddAttendanceForAbsent($request->validated());
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Attendance added successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function addBulkAbsentWithoutPermission(AbsentBulkActionsRequest $request)
    {
        try {
            DB::transaction(function () use ($request) {
                $this->attendanceBulkActionService->addBulkAbsentWithOutPermission($request->validated());
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Attendance added successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function addBulkRestDays(AbsentBulkActionsRequest $request)
    {
        try {
            DB::transaction(function () use ($request) {
                $this->attendanceBulkActionService->bulkAddRestDay($request->validated());
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Attendance added successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function addBulkLeave(AbsentBulkActionsRequest $request)
    {
        try {
            $this->attendanceBulkActionService->addBulkLeave($request->validated());

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Attendance added successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function convertAbsenceToLeave(ConvertAbsenceToLeaveRequest $request)
    {
        try {
            $output = null;
            DB::transaction(function () use ($request) {
                $output = $this->absencesService->convertAbsenceToLeave($request->validated());
            });

            return getResponseStructure(
                ['data' => [$output]],
                HttpStatusCodeUtil::OK,
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }
}
