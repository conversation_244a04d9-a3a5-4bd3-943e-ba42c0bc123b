<?php

namespace App\Http\Requests;

use App\Enums\Employee\EmployeeExportCategoryEnum;
use App\Enums\Employee\EmployeeExportFieldEnum;
use App\FeatureToggles\Unleash;
use App\Rules\ScopeSubDepartmentIdsRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ExportEmployeeProfileListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'page' => ['integer', 'min:1'],
            'page_size' => ['integer', 'min:0'],
            'branch_ids' => ['array'],
            'branch_ids.*' => ['integer'],
            'department_ids' => ['array'],
            'department_ids.*' => ['integer'],
            'title_ids' => ['array'],
            'title_ids.*' => ['integer'],
            'statuses' => ['array'],
            'statuses.*' => ['string', 'in:new_hire,on_probation,active,termination_pending,terminated'],
            'search_value' => ['min:1', 'max:30'],
            'missing_employee_info' => 'boolean',
            'sub_department_ids' => 'array',
            'sub_department_ids.*' => 'integer', new ScopeSubDepartmentIdsRule,
            'role_ids' => 'array',
            'role_ids.*' => ['integer', Rule::exists('spatie_roles', 'id')->where('company_id', auth()->user()->company_id)],
            'termination_from_date' => 'date_format:Y-m-d',
            'termination_to_date' => 'date_format:Y-m-d|after_or_equal:termination_from_date|required_with:termination_from_date',
            'is_trackable' => 'boolean',
            'is_central' => 'boolean',
            'is_unregistered_face_id' => ['boolean'],
        ];

        // Add export template fields validation if enhanced employee profile is enabled
        $unleash = app(Unleash::class);
        if ($unleash->isEnhancedEmployeeProfileEnabled()) {
            $categoryValues = EmployeeExportCategoryEnum::values();

            foreach ($categoryValues as $category) {
                if ($category === EmployeeExportCategoryEnum::ESSENTIAL->value) {
                    continue;
                }

                $categoryFields = EmployeeExportFieldEnum::getFieldsForCategory(EmployeeExportCategoryEnum::from($category));

                $rules[$category] = 'required|array';

                foreach ($categoryFields as $field) {
                    $rules["{$category}.{$field}"] = 'required|boolean';
                }
            }
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        $messages = [];

        // Add export template field messages if enhanced employee profile is enabled
        $unleash = app(Unleash::class);
        if ($unleash->isEnhancedEmployeeProfileEnabled()) {
            foreach (EmployeeExportCategoryEnum::values() as $category) {
                if ($category === EmployeeExportCategoryEnum::ESSENTIAL->value) {
                    continue;
                }

                $messages["{$category}.required"] = trans('validation.category_fields_required', ['category' => $category]);
                $messages["{$category}.array"] = trans('validation.category_fields_array', ['category' => $category]);

                $categoryFields = EmployeeExportFieldEnum::getFieldsForCategory(EmployeeExportCategoryEnum::from($category));

                foreach ($categoryFields as $field) {
                    $messages["{$category}.{$field}.required"] = trans('validation.field_required', [
                        'field' => $field,
                        'category' => $category
                    ]);
                    $messages["{$category}.{$field}.boolean"] = trans('validation.field_boolean', [
                        'field' => $field,
                        'category' => $category
                    ]);
                }
            }
        }

        return $messages;
    }
}
