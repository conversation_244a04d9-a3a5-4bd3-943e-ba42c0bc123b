<?php

namespace App\Http\Requests\V1\Holidays;

use App\Rules\ScopeBranchIdsRule;
use App\Rules\ScopeDepartmentIdsRule;
use App\Rules\ScopeSubDepartmentIdsRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;

class GetAllHolidayAbsencesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules['from_date'] = ['required', 'date_format:Y-m-d'];
        $rules['to_date'] = ['required', 'date_format:Y-m-d', 'after_or_equal:from_date'];
        $rules['branch_ids'] = ['array'];
        $rules['branch_ids.*'] = ['integer', new ScopeBranchIdsRule];
        $rules['title_ids'] = ['array'];
        $rules['title_ids.*'] = ['integer', (new Exists('titles', 'id'))->where(function ($query) {
            $query->where('company_id', auth()->user()->company_id) // company rule doesn't apply here
                ->whereNull('deleted_at');
        }), ];
        $rules['department_ids'] = ['array'];
        $rules['department_ids.*'] = ['required', new ScopeDepartmentIdsRule];
        $rules['sub_department_ids'] = ['array'];
        $rules['sub_department_ids.*'] = ['integer', new ScopeSubDepartmentIdsRule];
        $rules['page'] = ['integer', 'min:1'];
        $rules['page_size'] = ['integer', 'min:0']; // 0 means no pagination
        $rules['search_value'] = ['min:1', 'max:30'];
        $rules['holiday_ids'] = ['array'];
        $rules['holiday_ids.*'] = ['integer'];

        return $rules;
    }
}
