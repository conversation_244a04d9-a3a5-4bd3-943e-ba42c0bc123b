<?php

namespace App\Services\CompanySetup;

use App\Exceptions\UnprocessableException;
use App\Http\Resources\Employee\ChangeLogs\ChangeLogsCollection;
use App\Jobs\GenerateTimecardsForStaticTitles;
use App\Jobs\V1\FillEmployeeBaseBalancesJob;
use App\Repositories\NewEmployeeInfoRepository;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\NewRoleRepository;
use App\Repositories\NewTitleRepository;
use App\Repositories\PayrollRepositories\EmployeeSalaryRepository;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\Repository;
use App\Repositories\V1\BranchRepository;
use App\Repositories\V1\EmployeeRequestRepository;
use App\Repositories\V1\RequestViewRepository;
use App\Services\BaseService;
use App\Services\CompanySetup\CrudServices\RegisterationValidationCrudService;
use App\Services\CompanySetup\CrudServices\UserCrudService;
use App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService;
use App\Services\PayrollSetup\PayrollsService;
use App\Services\PayrollSetup\SystemSettingsService;
use App\Services\V1\Attendance\GenerateTimecardsService;
use App\Services\V1\KPIs\IncentiveService;
use App\Services\V1\LeaveManagement\FillEmployeeBalancesService;
use App\Traits\QueriesHelper;
use App\Traits\V2\WorkTypesTrait;
use App\Util\BasicInfoUtil;
use App\Util\EmployeeUtil;
use App\Util\ScopeUtil;
use Carbon\Carbon;
use Ramsey\Uuid\Uuid;

class EmployeesService extends BaseService
{
    use QueriesHelper, WorkTypesTrait;

    private \App\Repositories\IRepository $employeeChangeRepository;

    public function __construct(
        NewEmployeeRepository                      $newEmployeeRepository,
        private NewEmployeeInfoRepository          $employeeInfoRepository,
        private EmployeeSalaryRepository           $employeeSalaryRepository,
        private UserCrudService                    $userCrudService,
        private RegisterationValidationCrudService $registerationValidationCrudService,
        private EmployeeLeaveRequestCrudService    $employeeLeaveRequestCrudService,
        private FillEmployeeBalancesService        $fillEmployeeBalancesService,
        private FillEmployeeBalancesService        $oldFillEmployeeBalancesService,
        private BranchRepository                   $branchRepository,
        private PayrollsRepository                 $payrollRepository,
        private PayrollsService                    $payrollService,
        private SystemSettingsService              $systemSettingsService,
        private RequestViewRepository              $requestViewRepository,
        private EmployeeRequestRepository          $employeeRequestRepository,
        private NewRoleRepository                  $newRoleRepository,
        private NewTitleRepository                 $titleRepository,
        private GenerateTimecardsService           $generateTimecardsService,
        private IncentiveService                   $incentiveService,

    )
    {
        parent::__construct($newEmployeeRepository);

        $this->employeeChangeRepository = Repository::getRepository('EmployeeChange');

    }

    public function checkUniqueData($data, $id = -1)
    {

        $employeeWithSamePhone = $this->repository->findFirstEmployeeWithAttributesWithoutGlobalScopes(['phone' => $data['phone'] ?? null]);
        if (isset($employeeWithSamePhone) && $employeeWithSamePhone->id != $id) {
            throw new UnprocessableException(trans('messages.phone_number_exists') . ' ' . $data['phone']
                . $this->getProperErrorMessage($employeeWithSamePhone));
        }

        if (isset($data['national_id'])) {
            $employeeWithSameNationalId = $this->repository->findFirstEmployeeWithAttributesWithoutGlobalScopes(['national_id' => $data['national_id'] ?? null]);
            if (isset($employeeWithSameNationalId) && $employeeWithSameNationalId->id != $id) {
                throw new UnprocessableException(trans('messages.national_id_exists') . ' ' . $data['national_id']
                    . $this->getProperErrorMessage($employeeWithSameNationalId));
            }
        }
        $employeeWithSameEmployeeNumber = $this->repository->findFirstByKey('employee_number', $data['employee_number'] ?? null);
        if (isset($employeeWithSameEmployeeNumber) && $employeeWithSameEmployeeNumber->id != $id
            && $employeeWithSameEmployeeNumber->company_id == config('globals.company')->id) {
            throw new UnprocessableException(trans('messages.employee_number_exists') . ' ' . $data['employee_number']
                . $this->getProperErrorMessage($employeeWithSameEmployeeNumber));
        }

    }

    public function getRequestsRelatedToMe($data)
    {
        $isPaginated = isset($data['page_size']) && $data['page_size'];
        $requests = $this->employeeRequestRepository->getRequestsRelatedToMe($data);

        return [
            'data' => $requests,
            'pagination' => $isPaginated ? [
                'current_page' => $requests->currentPage(),
                'last_page' => $requests->lastPage(),
                'per_page' => $requests->perPage(),
                'total' => $requests->total(),
                'from' => $requests->firstItem(),
                'to' => $requests->lastItem(),
            ] : null,

        ];
    }

    public function getRequestsRelatedToMeCount()
    {

        $lastDraftedPayroll = $this->payrollRepository->getLastDraftedPayroll();

        $data = [
            'status' => 'pending',
            'pending_on_me' => 0,
            'from_date' => $lastDraftedPayroll->start,
            'to_date' => $lastDraftedPayroll->end,
        ];

        return $this->employeeRequestRepository->requestsRelatedToMeCount($data);
    }

    public function add($data)
    {
        $this->checkUniqueData($data);

        $infoKeys = ['join_date', 'gender', 'career_start_date', 'birth_date', 'number_of_years_of_experience',
            'number_kids', 'address', 'email', 'passport_number', 'nationality', 'secondary_phone', 'military_status', 'training_certification_status', 'notes'];
        $employeeData = array_filter($data, function ($key) use ($infoKeys) {
            return !in_array($key, $infoKeys);
        }, ARRAY_FILTER_USE_KEY);
        $payroll = config('globals.company')?->latestPayroll->first() ?? null;
        $employeeData['status'] = self::getEmployeeStatus($data['join_date'] ?? null, $payroll);

        $title = $this->titleRepository->findOrFail($data['title_id']);

        $uuid = Uuid::uuid7();
        $uuidString = str_replace('-', '', $uuid->toString());
        $employeeData['uuid'] = substr($uuidString, 0, 16);
        $endProbationDate = Carbon::parse($data['join_date'])
            ->addMonths($title->probation_period)
            ->startOfDay();

        $employeeData['on_probation'] = !($title->probation_period > 0 && isset($endProbationDate) && $endProbationDate < date('Y-m-d'));

        $employee = $this->repository->add($employeeData);

        $managedDepartments = $data['managed_department_ids'] ?? [];
        $employee->managedDepartments()->sync($managedDepartments);

        $managedSubDepartments = $data['managed_sub_department_ids'] ?? [];
        $employee->managedSubDepartments()->sync($managedSubDepartments);

        $userRequest = ['employee_id' => $employee->id, 'role_id' => $employee->title->role_id ?? null, 'name' => $employee->name_ar];
        $dummyOutput = new \stdClass;
        $this->userCrudService->create($userRequest, $dummyOutput);

        $registerationValidationRequest = ['name' => $employee->name, 'phone' => $employee->phone, 'user_id' => $dummyOutput->user->id];
        $dummyOutput = new \stdClass;
        $this->registerationValidationCrudService->create($registerationValidationRequest, $dummyOutput);

        $roleId = $employee->title->role_id;
        $employee->user->assignRole([$roleId]);

        if (!empty($employee->title->net_salary)) {
            $this->employeeSalaryRepository->add([
                'employee_id' => $employee->id,
                'basic_salary' => $employee->title->net_salary,
                'gross_salary' => $employee->title->gross_salary,
                'net_salary' => $employee->title->net_salary,
                'as_of_date' => Carbon::now()->format('Y-m-d'),
                'by_employee_id' => config('globals.user')->employee_id,
                'salary_disbursement_method' => 'cash',
            ]);
        }

        $highestScopeKey = $this->getUserHighestScopeKey($employee->user);

        if ($highestScopeKey == ScopeUtil::COMPANY_SCOPE) {
            $branches = $this->branchRepository->all()->pluck('id')->toArray();
            $employee->branches()->sync($branches);
        } else {
            $branches = $data['additional_branch_ids'] ?? [];
            $employee->branches()->sync($branches);
        }

        $employeeInfo = array_filter($data, function ($key) use ($infoKeys) {
            return in_array($key, $infoKeys);
        }, ARRAY_FILTER_USE_KEY);
        $employeeInfo['employee_id'] = $employee->id;

        $this->employeeInfoRepository->add($employeeInfo);
        if (isset($employee)) {
            $employee->load('employeeInfo');
            dispatch(new FillEmployeeBaseBalancesJob([$employee]));
        }

        $dummyOutput = new \stdClass;
        $this->employeeLeaveRequestCrudService->addProratedRequest(['employee_id' => $employee->id], $dummyOutput);

        $this->generateTimecardsIfStaticTitle($employee->id, $employee->title);

        $this->incentiveService->maintainIncentivePerEmployeeTable($employee->id, $data['branch_id'], $employee->title_id, config('globals.company')->id);

        return $employee;

    }

    public function generateTimecardsIfStaticTitle($employeeId, $title)
    {
        if (in_array($title->workTypePolicy->work_days_type, $this->getAssignableTypes($title->company_id))) {
            $workTypeData = [[
                'title_id' => $title->id,
                'work_type_policy_id' => $title->workTypePolicy->id,
                'start_time' => $title->workTypePolicy->start_time,
                'end_time' => $title->workTypePolicy->end_time,
                'rest_days' => $title->workTypePolicy->rest_days,
                'employee_id' => $employeeId, // this parameter is used to generate timecards for this employee only
                'work_days_type' => $title->workTypePolicy->work_days_type,
                'any_location_type' => $title->workTypePolicy->any_location_type,
            ]];

            dispatch(new GenerateTimecardsForStaticTitles($workTypeData));
        }

    }

    public function update(int $id, array $data)
    {
        $employee = $this->repository->findOrFail($id);
        $oldRole = $employee->title?->role;
        $managedBranches = $data['additional_branch_ids'] ?? null;
        unset($data['additional_branch_ids']);
        $managedDepartments = $data['managed_department_ids'] ?? null;
        unset($data['managed_department_ids']);
        $managedSubDepartments = $data['managed_sub_department_ids'] ?? null;
        unset($data['managed_sub_department_ids']);

        $data['branch_id'] = $data['main_branch_id'] ?? $employee->branch_id;
        unset($data['main_branch_id']);

        // $countryCode = $data['country_code'];
        // if(! str_starts_with($data['phone'], '+')){
        //     $data['phone'] = $countryCode.(str_starts_with($data['phone'], '0') && $countryCode == '+20' ? substr($data['phone'], 1) : $data['phone']);
        // }
        $countryCode = auth()->user()->company->country->dial_code;

        if (! str_starts_with($data['phone'], '+')) {
            $coreNumber = (str_starts_with($data['phone'], '0') && $countryCode === '+20')
                ? substr($data['phone'], 1)
                : $data['phone'];

            $data['phone'] = $countryCode.$coreNumber;
        }

        $infoKeys = ['join_date', 'gender', 'career_start_date', 'number_kids', 'address', 'birth_date',
            'number_of_years_of_experience', 'email', 'passport_number', 'nationality', 'military_status', 'secondary_phone', 'training_certification_status', 'notes'];
        $employeeKeys = [
            'first_name_ar', 'second_name_ar', 'third_name_ar', 'fourth_name_ar', 'fifth_name_ar', 'first_name_en', 'second_name_en',
            'third_name_en', 'fourth_name_en', 'fifth_name_en', 'employee_number', 'phone',
            'national_id', 'title_id', 'branch_id', 'is_trackable',
        ];

        $employeeData = array_filter($data, function ($key) use ($employeeKeys) {
            return in_array($key, $employeeKeys);
        }, ARRAY_FILTER_USE_KEY);

        if (isset($data['is_trackable'])) {
            $employeeData['is_trackable'] = $data['is_trackable'];
        }

        if (isset($data['first_name_ar']) || isset($data['second_name_ar']) || isset($data['third_name_ar'])) {
            $employeeData['name_ar'] = ($data['first_name_ar'] ?? $employee->first_name_ar) . ' ' . ($data['second_name_ar'] ?? $employee->second_name_ar)
                . ' ' . ($data['third_name_ar'] ?? $employee->third_name_ar);
        }
        if (isset($data['first_name_en']) || isset($data['second_name_en']) || isset($data['third_name_en'])) {
            $employeeData['name_en'] = ($data['first_name_en'] ?? $employee->first_name_en) . ' ' . ($data['second_name_en'] ?? $employee->second_name_en)
                . ' ' . ($data['third_name_en'] ?? $employee->third_name_en);
        }

        $this->checkUniqueData($employeeData, $id);

        $this->repository->update($id, $employeeData);

        $employee->name_ar = $employee->first_name_ar . ' ' . $employee->second_name_ar . ' ' . $employee->third_name_ar;
        $employee->name_en = $employee->first_name_en . ' ' . $employee->second_name_en . ' ' . $employee->third_name_en;
        $employee->name = $employee->name_ar; // TODO these line to be removed after we drop old name columns

        if (isset($managedDepartments)) {
            $employee->managedDepartments()->sync($managedDepartments);
        }

        if (isset($managedSubDepartments)) {
            $employee->managedSubDepartments()->sync($managedSubDepartments);
        }

        if (isset($employee->user)) {
            $employee->user->name = $employee->name_ar;
        } else {
            throw new UnprocessableException('Employee has no user');
        }

        $employee->user->save();
        $employee->save();

        $highestScopeKey = $this->getUserHighestScopeKey($employee->user);

        if ($highestScopeKey == ScopeUtil::COMPANY_SCOPE) {
            $branches = $this->branchRepository->all()->pluck('id')->toArray();
            $employee->branches()->sync($branches);
        } elseif (isset($managedBranches)) {
            $employee->branches()->sync($managedBranches);
        }

        if (isset($employee->employeeInfo)) {
            $employeeInfo = array_filter($data, function ($key) use ($infoKeys) {
                return in_array($key, $infoKeys);
            }, ARRAY_FILTER_USE_KEY);

            $employeeInfo['employee_id'] = $employee->id;

            $this->employeeInfoRepository->update($employee->employeeInfo->id, $employeeInfo);
            $employee->employeeInfo->save();
            $employee->refresh();

            $yearsOfExperience = $employee->employeeInfo->number_of_years_of_experience ?? null;
            $age = Carbon::parse($employee?->employeeInfo?->birth_date)->age ?? null;

            $companyAnnualLeaveId = auth()->user()->company->annual_leave_id;

            $employeeHasAddedHoursForAnnualLeave = $employee->employeeLeaveBalances()
                ->where('company_leave_type_id', $companyAnnualLeaveId)
                ->whereDate('start', '<=', now())
                ->whereDate('end', '>=', now())
                ->where('added_hours', '>', 0)
                ->exists();

            if ($yearsOfExperience > 10 || $age > 50) {

                if (!$employeeHasAddedHoursForAnnualLeave) {
                    $employeeLeaveBalance = $employee->employeeLeaveBalances()
                        ->where('company_leave_type_id', $companyAnnualLeaveId)
                        ->whereDate('start', '<=', now())
                        ->whereDate('end', '>=', now())
                        ->where('added_hours', '=', 0)
                        ->first();

                    if ($employeeLeaveBalance) {
                        $newBalance = $employeeLeaveBalance->balance + (9 * 8); // Assuming 8 hours per day for 9 days
                        $employeeLeaveBalance->update([
                            'balance' => $newBalance,
                            'added_hours' => 9 * 8, // Update added_hours as well
                        ]);
                        $employeeLeaveBalance->save();

                    }

                }

            } else {

                if ($employeeHasAddedHoursForAnnualLeave) {
                    $employeeLeaveBalance = $employee->employeeLeaveBalances()
                        ->where('company_leave_type_id', $companyAnnualLeaveId)
                        ->whereDate('start', '<=', now())
                        ->whereDate('end', '>=', now())
                        ->where('added_hours', '>', 0)
                        ->first();

                    if ($employeeLeaveBalance) {
                        $newBalance = $employeeLeaveBalance->balance - (9 * 8); // Assuming 8 hours per day for 9 days
                        $employeeLeaveBalance->update([
                            'balance' => $newBalance,
                            'added_hours' => 0, // Update added_hours as well
                        ]);
                    }

                }

            }

        }

    }

    public function liteCreate($data): void
    {

        $data['additional_branch_ids'] = $data['branch_ids'] ?? [];

        $data['join_date'] = date('Y-m-d');

        $employee_lite_code = $this->getRecentEmployeeCode();
        $employeeCodeType = $this->systemSettingsService->getCompanyEmployeeCodeType();
        if ($employeeCodeType === BasicInfoUtil::EMPLOYEE_CODE_TYPES['MANUAL']) {
            $uuid = uuid_create();
            $data['employee_number'] = ('new_' . substr($uuid, 0, 6));
        } else {
            $data['employee_number'] = $employee_lite_code;
        }
        $employee = $this->repository->findFirstByAnyKey($data);
        if (isset($employee) && $employee->branch_id != auth()->user()->employee->branch_id) {
            $message = request()->header('Accept-Language') === 'en' ?
                ($employee->name . 'is an employee at ' . $employee->branch->name . ' contact your manager to transfer him to your branch')
                :
                ($employee->name . ' هو موظف فى فرع ' . $employee->branch->name . ' برجاء التواصل مع مديرك لنقله إلى فرعك ');
            throw new UnprocessableException($message);
        }
        $payroll = config('globals.company')?->latestPayroll->first() ?? null;
        $data['status'] = self::getEmployeeStatus($data['join_date'] ?? null, $payroll);
        if (isset($data['country_code'])) {
            unset($data['country_code']);
        }
        $this->add($data);
    }

    public function getImportantDataByFilters($data)
    {
        return $this->repository->getImportantDataByFilters($data);
    }

    public function getEmployeesMissingSalaryInfoCount($data = null): int
    {
        if (!isset($data)) {
            $payroll = $this->payrollRepository->getLastDraftedPayroll();
            $data['payroll_from_date'] = $payroll->start;
        }

        return $this->repository->missingSalaryDataCounter($data);
    }

    public function getEmployeesMissingHiringDateCount(): int
    {
        return $this->repository->getEmployeesMissingHiringDateCount();
    }

    private function getProperErrorMessage($employee): string
    {
        if ($employee->company_id != auth()->user()->company_id) {
            return ', And this employee is on another company';
        }
        if (isset($employee->deleted_at)) {
            return ', And this employee has been deleted';
        }
        if (isset($employee->revoke_date) && $employee->revoke_date <= date('Y-m-d')) {
            return ', And this employee has been revoked system access';
        }
        if (isset($employee) && $employee->status == EmployeeUtil::STATUSES['TERMINATED']) {
            return ', And this employee has been terminated before';
        }

        return '';
    }

    public static function getEmployeeStatus($joinDate = null, $payroll = null)
    {
        if (is_null($joinDate) || is_null($payroll)) {
            return 'active';
        }

        // in case employee joined after last finalized payroll, so he is new hire
        // Or in case employee joined through a drafted payroll, so he is new hire
        if ((/* $probationMonths == 0 && */ $payroll->status == 'finalized' && $joinDate > $payroll->end) ||
            ($payroll->status == 'draft' && $joinDate <= $payroll->end && $joinDate >= $payroll->start)) {
            return 'new_hire';
        } else {
            return 'active';
        }
    }

    public function getRecentEmployeeCode()
    {
        $employeeCodeType = $this->systemSettingsService->getCompanyEmployeeCodeType() ?? BasicInfoUtil::EMPLOYEE_CODE_TYPES['MANUAL'];

        $lastEmployeeNumber = $this->repository->getLastEmployeeNumber();

        if (is_null($lastEmployeeNumber)) {
            return '00001';
        }

        if ($employeeCodeType == BasicInfoUtil::EMPLOYEE_CODE_TYPES['AUTO']) {
            return $this->produceNewEmployeeNumber($lastEmployeeNumber);
        }

        return $lastEmployeeNumber;
    }

    public function produceNewEmployeeNumber($oldNumber)
    {
        $incrementedNumber = str_pad((string)((int)$oldNumber + 1), strlen($oldNumber), '0', STR_PAD_LEFT);

        return $incrementedNumber;
    }

    public function syncAllBranchesToCompanyScopeEmployees(): void
    {
        $employeesWithCompanyScope = $this->repository->getEmployeesWithCompanyScope();
        $branchIds = $this->branchRepository->all()->pluck('id')->toArray();
        $employeesWithCompanyScope->each(function ($employee) use ($branchIds) {
            $employee->branches()->sync($branchIds);
        });
    }

    public function employeeChangeLogs($data)
    {
        return (new ChangeLogsCollection($this->employeeChangeRepository->employeeChangeLogs($data)))->response()->getData();
    }

    public function exportEmployeeChangeLogs($data)
    {
        return $this->employeeChangeRepository->exportEmployeeChangeLogs($data);
    }

    public function getCountOfTerminatedEmployeesWithOutstandingLoans()
    {
        return $this->repository->getCountOfTerminatedEmployeesWithOutstandingLoans();
    }

    public function changeTitle($employeeId, $titleId)
    {
        $employee = $this->repository->findOrFail($employeeId);
        $title = $this->titleRepository->findOrFail($titleId);
        $employee->title_id = $titleId;
        $employee->save();

        // title timecard
        /*
        $titles = [[
            'title_id' => $title->id,
            'work_type_policy_id' => $title->workTypePolicy->id,
            'start_time' => $title->workTypePolicy->start_time,
            'end_time' => $title->workTypePolicy->end_time,
            'rest_days' => $title->workTypePolicy->rest_days,
            'employee_id' => $employeeId,
            'work_days_type' => $title->workTypePolicy->work_days_type,
        ]];
        $nextDay = Carbon::tomorrow()->format('Y-m-d');

        $generateTimecardsService = new GenerateTimecardsService();
        $generateTimecardsService->generateTimecards($titles, $nextDay);
        */
    }
}
