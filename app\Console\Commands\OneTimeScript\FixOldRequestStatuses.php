<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\AttendanceDeduction;
use App\Models\AttendanceOvertime;
use App\Models\EmployeeLeaveRequest;
use App\Models\Penalty;
use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixOldRequestStatuses extends Command
{
    protected $requestWorkflowApprovals;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:old:pending_requests {type} {start_date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    public function handle()
    {
        DB::beginTransaction();
        try {
            $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
            $startDate = $this->argument('start_date');

            switch ($this->argument('type')) {

                case 'EmployeeLeaveRequest':
                    $leaves = EmployeeLeaveRequest::where('status', 'pending')->whereDate('from', '>=', $startDate)->get();
                    $data['requestable_type'] = config('globals.REQUEST_WORKFLOW_TYPES.LEAVE');
                    foreach ($leaves as $leave) {
                        $data['requestable_id'] = $leave->id;
                        $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle($data);
                        $this->requestWorkflowApprovals = $this->getApprovals($approvalCycle);
                        $status = $this->getFinalStatus();
                        if ($status != $leave->status) {
                            $leave->status = $status;
                            echo 'leave : '.$leave->id.' '.$leave->status."\n";
                            $leave->save();
                        }
                    }
                    break;

                case 'AttendanceDeduction':
                    $waiveDeductionRequests = AttendanceDeduction::where('status', 'applied')->whereDate('date', '>=', $startDate)->whereHas('workflowApprovalCycles')->get();
                    $data['requestable_type'] = config('globals.REQUEST_WORKFLOW_TYPES.WAIVE_DEDUCTION');
                    foreach ($waiveDeductionRequests as $waiveDeductionRequest) {
                        $data['requestable_id'] = $waiveDeductionRequest->id;
                        $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle($data);
                        $this->requestWorkflowApprovals = $this->getApprovals($approvalCycle);
                        $status = $this->getWaiveFinalStatus();
                        if ($status != $waiveDeductionRequest->status) {
                            $waiveDeductionRequest->status = $status;
                            echo 'waive deduction : '.$waiveDeductionRequest->id.' '.$waiveDeductionRequest->status."\n";
                            $waiveDeductionRequest->save();
                        }
                    }
                    break;

                case 'AttendanceOvertime':
                    $attendanceOvertimes = AttendanceOvertime::where('status', 'pending')->whereDate('date', '>=', $startDate)->get();
                    $data['requestable_type'] = config('globals.REQUEST_WORKFLOW_TYPES.ATTENDANCE_OVERTIME');
                    foreach ($attendanceOvertimes as $attendanceOvertime) {
                        $data['requestable_id'] = $attendanceOvertime->id;
                        $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle($data);
                        $this->requestWorkflowApprovals = $this->getApprovals($approvalCycle);
                        $status = $this->getFinalStatus();
                        if ($status != $attendanceOvertime->status) {
                            $attendanceOvertime->status = $status;
                            echo 'attendance overtime : '.$attendanceOvertime->id.' '.$attendanceOvertime->status."\n";
                            $attendanceOvertime->save();
                        }
                    }
                    break;

                case 'Penalty':
                    $penalties = Penalty::where('status', 'pending')->whereDate('date', '>=', $startDate)->get();
                    $data['requestable_type'] = config('globals.REQUEST_WORKFLOW_TYPES.PENALTY');
                    foreach ($penalties as $penalty) {
                        $data['requestable_id'] = $penalty->id;
                        $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle($data);
                        $this->requestWorkflowApprovals = $this->getApprovals($approvalCycle);
                        $status = $this->getFinalStatus();
                        if ($status != $penalty->status) {
                            $penalty->status = $status;
                            echo 'penalty : '.$penalty->id.' '.$penalty->status."\n";
                            $penalty->save();
                        }
                    }
                    break;

                default:
                    dd('uncorrected type');
                    break;
            }

            DB::commit();
        } catch (Exception $e) {
            // // dd($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }

    public function getApprovals($approvalCycle)
    {
        $approvals = [
            'then' => [],
            'or' => [],
        ];

        foreach ($approvalCycle as $approval) {
            if ($approval->operator == 'then') {
                $approvals['then'][] = $approval->status;
            } elseif ($approval->operator == 'or') {
                $approvals['or'][] = $approval->status;
            }
        }

        return $approvals;
    }

    public function getFinalStatus()
    {
        if (count($this->requestWorkflowApprovals['or']) > 0 && ! in_array('pending', $this->requestWorkflowApprovals['or'])) {
            return $this->requestWorkflowApprovals['or'][0];
        } elseif (count($this->requestWorkflowApprovals['then']) > 0 && in_array('rejected', $this->requestWorkflowApprovals['then'])) {
            return 'rejected';
        } elseif (count($this->requestWorkflowApprovals['then']) > 0 && in_array('pending', $this->requestWorkflowApprovals['then'])) {
            return 'pending';
        }

        return 'approved';

    }

    public function getWaiveFinalStatus()
    {
        if (count($this->requestWorkflowApprovals['or']) > 0 && ! in_array('pending', $this->requestWorkflowApprovals['or'])) {
            return $this->requestWorkflowApprovals['or'][0] == 'approved' ? 'waived' : 'applied';
        } elseif (count($this->requestWorkflowApprovals['then']) > 0 && in_array('rejected', $this->requestWorkflowApprovals['then'])) {
            return 'applied';
        }

        return 'waived';
    }
}
