<?php

namespace App\Http\Requests\V1\PayrollHub;

use App\Rules\BranchIdRule;
use Illuminate\Foundation\Http\FormRequest;

class UnaccountedBulkActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'employee_id' => [
                'required',
                'integer',
            ],
            'dates' => [
                'required',
                'array',
            ],
            'dates.*' => [
                'required',
                'date_format:Y-m-d',
            ],
            'company_leave_type_id' => [
                'nullable',
                'integer',
            ],
            'shift_id' => [
                'nullable',
                'integer',
            ],
            'branch_id' => [
                'nullable',
                'integer',
                new BranchIdRule,
            ],
        ];
    }
}
