<?php

namespace App\Console\Commands;

use App\Enums\V2\WorkTypes\WorkTypesEnum;
use App\Models\Title;
use App\Services\V1\Attendance\GenerateTimecardsService;
use App\Traits\V2\WorkTypesTrait;
use App\Util\UserWorkTypesUtil;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GenerateTimecardsToStaticTitles extends Command
{
    use WorkTypesTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-timecards-to-static-titles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Generating timecards to static titled');
            DB::beginTransaction();
            $titles = $this->prepareTitlesData();
            $this->info('Titles count is: '.count($titles));
            $date = Carbon::now()->format('Y-m-d');
            $titleIds = array_column($titles, 'title_id');
            $generateTimecardsService = new GenerateTimecardsService;
            $generateTimecardsService->generateTimecards($titles, $date, $titleIds);
            DB::commit();
            $this->info('Static timecards generated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e);
        }
    }

    public function prepareTitlesData()
    {
        $oldTypes = [UserWorkTypesUtil::FIXED_HYBRID, UserWorkTypesUtil::FIXED_ON_SITE, UserWorkTypesUtil::FLEXIBLE_HYBRID];

        $newTypes = WorkTypesEnum::all();

        $result = [];
        $titles = Title::withWhereHas('workTypePolicy', function ($q) use ($oldTypes, $newTypes) {
            $q->where(function ($q) use ($oldTypes, $newTypes) {
                $q->whereIn('work_days_type', $newTypes)
                    ->orWhereIn('work_days_type', $oldTypes);
            });

        })->get();
        foreach ($titles as $title) {
            $result[] =
            [
                'title_id' => $title->id,
                'work_days_type' => $title?->workTypePolicy?->work_days_type,
                'any_location_type' => $title?->workTypePolicy?->any_location_type,
                'start_time' => $title?->workTypePolicy?->start_time,
                'end_time' => $title?->workTypePolicy?->end_time,
                'rest_days' => $title?->workTypePolicy?->rest_days,
            ];
        }

        return $result;
    }
}
