<?php

return [

    'user' => null,
    'user_role' => null,
    'company' => null,
    'branchId' => null,
    'scope_keys' => [],
    'scope_branch_ids' => [],
    'scope_department_ids' => [],
    'scope_title_ids' => [],
    'APP_NAME' => env('APP_ENV_FCM'),
    'ATTENDANCE_TAGS' => [
        'ABSENT' => 'absent',
        'CONSIDERED_ABSENT' => 'considered_absent',
        'ON_TIME' => 'on_time',
        'LATE' => 'late',
        'EARLY_CLOCK_OUT' => 'early_clock_out',
        'OVERTIME' => 'overtime',
        'NO_CLOCK_OUT' => 'no_clock_out',
        'MANAGER_CLOCK_IN' => 'manager_clock_in',
        'MANAGER_APPROVED' => 'approved_by_manager',
        'MANAGER_CLOCK_OUT' => 'manager_clock_out',
        'ADDED_TIMECARD' => 'added_timecard',
        'SCHEDULED' => 'scheduled',
        'NOT_IN_YET' => 'not_in_yet',
        'ABSENT_WITHOUT_PERMISSION' => 'absent_without_permission',
        'PUBLIC_HOLIDAY' => 'public_holiday',
        'OFF_BRANCH' => 'off_branch',
        'OFF_SHIFT' => 'off_shift',
        'OUTSIDE_OFFICE' => 'outside_office',
        'ASSIGNED_TO_SHIFT' => 'assigned_to_shift',
        'FOODICS' => 'foodics',
        'WAIVED' => 'waived',
        'EXCUSED' => 'excused',
        'EXTRA_WORKDAY' => 'extra_workday',
        'MISSION' => 'mission',
    ],

    'ATTENDANCE_FILTERS' => [
        'ABSENT' => ['Absent', 'absent'],
        'LATE' => ['Late', 'late'],
        'EARLY_CLOCK_OUT' => ['Early Clock-Out', 'early_clock_out'],
        'ALL' => ['All', 'all'],
        'OVERTIME' => ['Overtime', 'overtime'],
        'NO_CLOCK_OUT' => ['No Clock-Out', 'no_clock_out'],
        'MANAGER_CLOCK_IN_AND_OUT' => ['Manager Clock-in/out', 'manual_clocked_in_and_out'],
        'MANUAL_TIMECARD' => ['Added Timecard', 'manual_time_card'],
        'PENDING_OVERTIME' => ['Pending Overtime', 'pending_overtime'],
    ],

    'OVERTIME_STATUSES' => [
        'PENDING' => 'pending',
        'APPROVED' => 'approved',
        'REJECTED' => 'rejected',
    ],

    'MAX_PAGE_SIZE' => 20,
    'DEFAULT_ORDER_BY' => 'date',
    'DEFAULT_ORDER_BY_TYPE' => 'desc',
    'DEFAULT_STATUS' => 'all',
    'MAX_EXCEL_FILE_SIZE_KB' => 15360,
    'MAX_FILE_SIZE_KB' => 16384,
    'MAX_EXCEL_ROWS' => 1000,
    'MAX_ATTACHMENTS_PER_REQUEST' => 5,
    'WORKED_HOURS_PER_DAY' => 8,
    'DEFAULT_REST_DAYS_BALANCE' => 40,
    'DEFAULT_ANNUAL_LEAVE_BALANCE' => 168,
    'DEFAULT_SICK_LEAVE_BALANCE' => 32,
    'DEFAULT_MATERNITY_LEAVE_BALANCE' => 720,
    'DEFAULT_EMERGENCY_LEAVE_BALANCE' => 48,
    'MAX_TIMES_TO_CHANGE_DEVICE_ID' => 5,
    'DEFAULT_COMPANY_RADIUS' => 300,

    'ATTENDANCE_SETTINGS' => [
        'ALLOWED_MINUTES_TO_CHECK_IN_BEFORE' => 'allowed_minutes_to_check_in_before',
        'APPLY_OVERTIME' => 'apply_overtime',
        'ALLOWED_MINUTES_TO_APPLY_OVERTIME' => 'allowed_minutes_to_apply_overtime',
        'ALLOWED_MINUTES_TO_CLOCK_OUT_EARLY' => 'allowed_minutes_to_clock_out_early',
        'APPLY_DEDUCTION' => 'apply_deduction',
        'APPLY_LATE_DEDUCTION' => 'apply_late_deduction',
        'APPLY_ABSENCE_DEDUCTION' => 'apply_absence_deduction',
    ],

    'EMPLOYEE_REQUEST_NAMES' => [
        'EDIT_OVERTIME' => 'edit_overtime',
        'SYSTEM_OVERTIME' => 'system_overtime',
        'WAIVE_DEDUCTION' => 'waive_deduction',
        'EMPLOYEE_LEAVE_REQUEST' => 'employee_leave_request',
    ],

    'ACTIONS_NAMES' => [
        'ADDED_CLOCK_OUT' => 'added_clock_out',
        'ADDED_TIME_CARD' => 'added_time_card',
        'ADDED_CLOCK_IN' => 'added_clock_in',
        'EDITED_OVERTIME' => 'edited_overtime',
        'CANCELLED_OVERTIME' => 'cancelled_overtime',
    ],

    'REQUEST_CYCLE_TYPES' => [
        'ATTENDANCE_OVERTIME' => 'attendance_overtime',
        'WAIVE_LATE_DEDUCTION' => 'waive_late_deduction',
        'EDIT_ATTENDANCE_DEDUCTION' => 'edit_attendance_deduction',
        'EDIT_ATTENDANCE_OVERTIME' => 'edit_attendance_overtime',
        'PENALTY' => 'penalty',
        'TERMINATION' => 'termination_request',
        'LOAN' => 'loan_request',
        'SALARY_ADVANCE' => 'loan_request',
        'EXTRA_WORKDAY' => 'extra_work_day_request',
        'OVERTIME_REQUEST' => 'overtime_request',
        'MISSIONS' => 'mission_request',
        'PROBATION' => 'probation_request',
    ],

    'REQUEST_WORKFLOW_TYPES' => [
        'ATTENDANCE_OVERTIME' => 'attendance_overtime',
        'WAIVE_LATE_DEDUCTION' => 'attendance_deduction',
        'EDIT_ATTENDANCE_DEDUCTION' => 'attendance_deduction',
        'EDIT_ATTENDANCE_OVERTIME' => 'attendance_overtime',
        'PENALTY' => 'penalty',
        'LEAVE' => 'employee_leave_request',
        'TERMINATION' => 'termination_request',
        'LOAN' => 'loan',
        'SALARY_ADVANCE' => 'salary_advance',
        'EXTRA_WORKDAY' => 'extra_work_day_request',
        'OVERTIME_REQUEST' => 'overtime_request',
        'MISSIONS' => 'mission_request',
        'PROBATION' => 'probation_request',
    ],
    'REQUEST_STATUSES' => [
        'PENDING' => 'pending',
        'REJECTED' => 'rejected',
        'APPROVED' => 'approved',
        'CANCELLED' => 'cancelled',
        'IN_PROCESS' => 'in_process',
    ],

    'LEAVE_TYPES' => [
        'REGULAR' => 'regular',
        'CARRYOVER' => 'carryover',
        'GRANT' => 'grant',
        'PRORATED' => 'prorated',
    ],
    'LEAVE_UNITS' => [
        'DAYS' => 'days',
        'HOURS' => 'hours',
        'PARTIALDAYS' => 'partialdays',
    ],
    'BALANCE_PERIODS' => [
        'PAYROLL_MONTH' => 'payroll_month',
        'CALENDAR_MONTH' => 'calendar_month',
        'CALENDAR_YEAR' => 'calendar_year',
        'COMPANY_YEAR' => 'company_year',
    ],

    'APP_DOMAIN' => env('APP_DOMAIN', 'localhost'),

    'PAYROLL_MONTHLY_CLOSING_DATE' => 25,

    'PAYROLL_DISBURSEMENT_DAY' => 22,

    'EMPLOYEE_STATUSES' => [
        'ACTIVE' => 'active',
        'NEW_HIRE' => 'new_hire',
        'ON_PROBATION' => 'on_probation',
        'TERMINATION_PENDING' => 'termination_pending',
        'TERMINATED' => 'terminated',
    ],

    'TESTING_USER_ID' => 3,

    'FINISHED_PAYROLL_SETUP' => 'finished_payroll_setup',

    'SCHEDULE_SLUG_AUTO_CLOCK_OUT' => env('SCHEDULE_SLUG_AUTO_CLOCK_OUT'),
    'SCHEDULE_SLUG_NOT_IN_YET_TIME_CARD' => env('SCHEDULE_SLUG_NOT_IN_YET_TIME_CARD'),
    'SCHEDULE_SLUG_LEAVE_BALANCE_FILL' => env('SCHEDULE_SLUG_LEAVE_BALANCE_FILL'),
    'SCHEDULE_SLUG_ABSENT_TIME_CARD' => env('SCHEDULE_SLUG_ABSENT_TIME_CARD'),
    'CLOCK_IN_PUSH_NOTIFICATION' => env('CLOCK_IN_PUSH_NOTIFICATION'),
    'CLOCK_OUT_PUSH_NOTIFICATION' => env('CLOCK_OUT_PUSH_NOTIFICATION'),
    'BRANCH_WEEK_START_DAY_PUSH_NOTIFICATION' => env('BRANCH_WEEK_START_DAY_PUSH_NOTIFICATION'),
    'SCHEDULE_SLUG_TRANSFERS' => env('SCHEDULE_SLUG_TRANSFERS'),
    'OTP_AUTO_APPLY_CODE' => env('OTP_AUTO_APPLY_CODE'),
    'SCHEDULE_SLUG_UPDATE_EMPLOYEE_STATUS' => env('SCHEDULE_SLUG_UPDATE_EMPLOYEE_STATUS'),

    'OVERTIME_JOB' => [
        'CONNECTION' => 'workflow',
        'QUEUE' => 'overtime_calculation',
    ],

    'DEDUCTION_JOB' => [
        'CONNECTION' => 'database',
        'QUEUE' => 'deduction_calculation',
    ],

    'HOLIDAY_PAYOUT_JOB' => [
        'CONNECTION' => 'database',
        'QUEUE' => 'default',
    ],

    'LOANS_UPDATE_STATUS_JOB' => [
        'CONNECTION' => 'database',
        'QUEUE' => 'default',
    ],

    'LEAVE_BALANCE_FILL' => [
        'CONNECTION' => 'long_retry',
        'QUEUE' => 'default',
    ],

    'WORK_TYPES' => [
        'FIXED_HYBRID' => 'fixed_hybrid',
        'FLEXIBLE_HYBRID' => 'flexible_hybrid',
        'DYNAMIC_ONSITE' => 'dynamic_onsite',
        'FIXED_ONSITE' => 'fixed_onsite',
    ],
    'BILL_STATUS' => [
        'PAID' => 'paid',
        'UNPAID' => 'unpaid',
    ],

    'TRAINING_CERTIFICATION_COMPANIES' => [
        4,
    ],
];
