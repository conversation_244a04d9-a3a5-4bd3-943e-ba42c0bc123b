<?php

namespace App\Http\Resources\V1\PayrollHub;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttendanceOvertimeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'overtime_id' => $this->attendanceOvertimes[0]?->id,
            'date' => $this->attendanceOvertimes[0]?->date,
            'clock_in_time' => $this->clockIn?->date,
            'clock_out_time' => $this->clockOut?->date,
            'base_overtime_minutes' => $this->attendanceOvertimes[0]?->base_overtime_minutes,
        ];
    }
}
