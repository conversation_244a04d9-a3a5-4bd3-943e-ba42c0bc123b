<?php

use Illuminate\Http\JsonResponse;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use Spatie\Activitylog\Models\Activity;
use ArPHP\I18N\Arabic;

if (!function_exists('set_impersonate_activity_causer')) {
    function set_impersonate_activity_causer(Activity $activity): void
    {
        $faceId = request()->header('face-id') ?? null;
        if (is_null($faceId) && env('APP_ENV') != 'testing' && request()->bearerToken() && $admin_id = JWTAuth::parseToken()->getPayload()->get('admin_id')) {
            $activity->admin_id = $admin_id;
        }
    }
}

if (!function_exists('getResponseStructure')) {

    function getResponseStructure(?array $payload, int $statusCode, ?string $message = null): JsonResponse
    {
        $request = Request()->segment(2);
        $version = str_contains($request, 'v') ? substr($request, 1) : 1;

        $response = [
            'version' => $version,
            'payload' => $payload,
        ];
        if ($message) {
            $response['message'] = $message;
        }

        return response()->json($response, $statusCode, [], JSON_INVALID_UTF8_IGNORE);
    }

    if (!function_exists('getErrorResponseStructure')) {
        /**
         * @param array|null $payload
         */
        function getErrorResponseStructure(int $statusCode, ?string $message = null): JsonResponse
        {
            $response = [];
            if ($message) {
                $response['payload']['errors']['message'] = $message;
            }

            return response()->json($response, $statusCode, [], JSON_INVALID_UTF8_IGNORE);
        }
    }

    if (!function_exists('escapeString')) {

        function escapeString(mixed $unsafeString): mixed
        {
            if (is_string($unsafeString)) {
                return addslashes(stripslashes($unsafeString));
            }

            return $unsafeString;
        }
    }

    if (!function_exists('str_replace_latest')) {

        function str_replace_latest(string $search, string $replace, string $subject): string
        {
            $pos = strrpos($subject, $search);
            if ($pos !== false) {
                $subject = substr_replace($subject, $replace, $pos, strlen($search));
            }

            return $subject;
        }
    }

    if (!function_exists('getTranslatedErrorMessage')) {

        function getTranslatedErrorMessage(array $error): string
        {
            $lang = (config('globals.lang') ?? 'ar');
            $index = $lang == 'ar' ? 1 : 0;

            return $error[$index];
        }
    }

    if (!function_exists('convertTimezoneToUTC')) {

        function convertTimezoneToUTC(string $dateTime, string $timezone = null): string
        {
            $timezone = $timezone ?? config('app.timezone');
            $convertedDate = Carbon\carbon::parse($dateTime, $timezone)->setTimezone('UTC');
            return $convertedDate->toDateTimeString();

        }
    }
}

/**
 * Checks if a string contains Arabic characters.
 *
 * @param string|null $string The string to check.
 * @return bool True if the string contains Arabic characters, false otherwise.
 */
if (!function_exists('containsArabic')) {
    function containsArabic(?string $string): bool
    {
        if (empty($string)) {
            return false;
        }
        // This regex checks for any characters in the Arabic Unicode block.
        return preg_match('/[\x{0600}-\x{06FF}]/u', $string) > 0;
    }
}

/**
 * Reshapes Arabic text for correct rendering in PDF.
 *
 * This helper uses the ar-php library to handle complex text shaping,
 * ensuring that Arabic text is displayed correctly from right to left with
 * proper character connections.
 *
 * @param string $text The Arabic text to be shaped.
 * @return string The shaped text.
 */
if (!function_exists('shapeArabic')) {
    function shapeArabic(string $text): string
    {
        $arabic = new Arabic('Glyphs');
        return $arabic->utf8Glyphs($text);
    }
}

