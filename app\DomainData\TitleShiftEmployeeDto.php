<?php

namespace App\DomainData;

trait TitleShiftEmployeeDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'title_shift_id' => 'required|numeric',
            'employee_id' => 'required|numeric',
            'branch_id' => 'required|numeric',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeTitleShiftEmployeeDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
