<?php

namespace App\DataTransferObject;

use Carbon\Carbon;

class UpdateTimeCardObject
{
    public string $name;

    public int $employeeId;

    public int $branchId;

    public ?int $shiftId;

    public string $from;

    public string $to;

    public ?int $timeCardTypeId;

    private int $requiredClockInBranchId;

    private int $requiredClockOutBranchId;

    private string $requiredClockInLong;

    private string $requiredClockInLat;

    private string $requiredClockOutLat;

    private string $requiredClockOutLong;

    private mixed $color;

    public function __construct(array $data)
    {
        $this->setName($data);
        $this->shiftId = $data['shift_id'] ?? null;
        $this->setFrom($data);
        $this->setTo($data);
        $this->setColor($data);
        $this->timeCardTypeId = $data['timeCardDefaultTypeId'];
        $this->setRequiredClockIn($data);
        $this->setRequiredClockOut($data);
    }

    public function getFrom(): string
    {
        return $this->from;
    }

    public function setFrom(array $data): void
    {
        if (isset($data['shift'])) {
            $shift = $data['shift'];
            $date = Carbon::parse($data['date']);
            $dayName = strtolower($date->format('D'));
            $shiftFromTime = $shift->{$dayName.'_from_time'};
            $this->from = Carbon::parse($data['date'].$shiftFromTime)->format('Y-m-d H:i:s');
        } else {
            $this->from = $data['from']; //dateTime
        }

    }

    public function getTo(): string
    {
        return $this->to;
    }

    public function setTo(array $data): void
    {
        if (isset($data['shift'])) {
            $shift = $data['shift'];
            $date = Carbon::parse($data['date']);
            $dayName = strtolower($date->format('D'));
            $shiftFromTime = $shift->{$dayName.'_from_time'};
            $shiftDuration = $shift->{$dayName.'_duration'};
            $this->to = Carbon::parse($data['date'].$shiftFromTime)->addHours($shiftDuration)->format('Y-m-d H:i:s');
        } else {
            $this->to = $data['to']; //dateTime
        }
    }

    public function setRequiredClockOut(array $data): void
    {
        if (isset($data['required_co_branch_id'])) {
            $this->requiredClockOutBranchId = $data['required_co_branch_id'];
        } elseif (isset($data['clock_out_lat']) && isset($data['clock_out_long'])) {
            $this->requiredClockOutLat = $data['clock_out_lat'];
            $this->requiredClockOutLong = $data['clock_out_long'];
        } else {
            $this->requiredClockOutBranchId = $data['branch_id'];
        }
    }

    public function setRequiredClockIn(array $data): void
    {
        if (isset($data['required_ci_branch_id'])) {
            $this->requiredClockInBranchId = $data['required_ci_branch_id'];
        } elseif (isset($data['clock_in_lat']) && isset($data['clock_in_long'])) {
            $this->requiredClockInLat = $data['clock_in_lat'];
            $this->requiredClockInLong = $data['clock_in_long'];
        } else {
            $this->requiredClockInBranchId = $data['branch_id'];
        }
    }

    public function getRequiredClockInLat(): ?string
    {
        return $this->requiredClockInLat ?? null;
    }

    public function getRequiredClockOutLat(): ?string
    {
        return $this->requiredClockOutLat ?? null;
    }

    public function getRequiredClockOutLong(): ?string
    {
        return $this->requiredClockOutLong ?? null;
    }

    public function getRequiredClockInLong(): ?string
    {
        return $this->requiredClockInLong ?? null;
    }

    public function getRequiredClockInBranchId(): ?int
    {
        return $this->requiredClockInBranchId ?? null;
    }

    public function getRequiredClockOutBranchId(): ?int
    {
        return $this->requiredClockOutBranchId ?? null;
    }

    private function setColor($data): void
    {
        if (isset($data['shift'])) {
            $this->color = $data['shift']->colorhex;
        } else {
            $this->color = $data['color'] ?? null;
        }
    }

    private function getColor(): ?string
    {
        return $this->color;
    }

    private function setName($data): void
    {
        if (isset($data['shift'])) {
            $this->name = $data['shift']->name;
        } else {
            $this->name = $data['name'];
        }
    }

    private function getName(): string
    {
        return $this->name;
    }

    public function toArray(): array
    {
        return [
            'shift_id' => $this->shiftId,
            'timecard_type_id' => $this->timeCardTypeId,
            'from' => $this->getFrom(),
            'to' => $this->getTo(),
            'name' => $this->getName(),
            'color' => $this->getColor(),
            'required_ci_branch_id' => $this->getRequiredClockInBranchId(),
            'required_co_branch_id' => $this->getRequiredClockOutBranchId(),
            'required_ci_lat' => $this->getRequiredClockInLat(),
            'required_ci_long' => $this->getRequiredClockInLong(),
            'required_co_lat' => $this->getRequiredClockOutLat(),
            'required_co_long' => $this->getRequiredClockOutLong(),
        ];
    }
}
