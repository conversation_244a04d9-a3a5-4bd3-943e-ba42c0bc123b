<?php

namespace App\Http\Requests\V1\PayrollHub;

use App\Rules\BranchIdRule;
use App\Traits\GetLastDraftedPayRollOrCreate;
use App\Rules\DepartmentIdRule;
use Illuminate\Foundation\Http\FormRequest;

class PayrollHubGetUnaccountedDaysRequest extends FormRequest
{
    use GetLastDraftedPayRollOrCreate;
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'department_ids' => [
                'nullable',
                'array'
            ],
            'department_ids.*' => [
                'integer',
                new DepartmentIdRule,
            ],
            'branch_ids' => [
                'nullable',
                'array'
            ],
            'branch_ids.*' => [
                'integer',
                new BranchIdRule,
            ],
            'payroll_from_date' => [
                'required',
            ],
            'payroll_to_date' => [
                'required',
            ],
            'company_id' => [
                'required',
                'integer',
            ],
            'page_size' => [
                'required',
                'integer',
            ],
            'search_value' => [
                'nullable',
                'string',
            ],
        ];
    }

    protected function prepareForValidation()
    {
        $payroll = $this->getCurrentPayroll();
        $this->merge([
            'company_id' => auth()->user()->employee->company_id,
            'payroll_from_date' => $payroll->start,
            'payroll_to_date' =>  $payroll->end,
            'page_size' => $this->input('page_size', 10),
        ]);
    }
}
