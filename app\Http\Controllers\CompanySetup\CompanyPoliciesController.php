<?php

namespace App\Http\Controllers\CompanySetup;

use App\DomainData\AttendanceGroupDto;
use App\DomainData\AttendanceSettingDto;
use App\DomainData\NationalHolidayGroupDto;
use App\DomainData\OvertimeGroupDto;
use App\DomainData\OvertimeMonthGroupDto;
use App\Http\Controllers\Controller;
use App\Services\CompanySetup\BusinessServices\CompanyPoliciesService;

class CompanyPoliciesController extends Controller
{
    use AttendanceGroupDto , AttendanceSettingDto, NationalHolidayGroupDto, OvertimeGroupDto, OvertimeMonthGroupDto
    {
        AttendanceSettingDto::getRules insteadof AttendanceGroupDto, OvertimeGroupDto, NationalHolidayGroupDto, OvertimeMonthGroupDto;
        AttendanceGroupDto::getRules as attendanceGroupDto;
        OvertimeGroupDto::getRules as overtimeGroupDto;
        NationalHolidayGroupDto::getRules as nationalHolidayGroupDto;
        OvertimeMonthGroupDto::getRules as overtimeMonthGroupDto;
    }

    public function __construct(
        private CompanyPoliciesService $service
    ) {}

    public function CreateUpdateMany(array $request, \stdClass &$output): void
    {
        $validator = \Validator::make($request, [
            'attendance_setting' => 'required',
            'attendance_group' => 'required',
            'overtime_group' => 'required',
            'national_holiday_group' => 'required',
            'overtime_month_group' => 'required',
        ]);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        //validate attendace settings
        $currentValidation = 'attendance_setting';
        $validator = \Validator::make($request[$currentValidation], [
            'entity_array' => 'required|array',
            'related_objects' => 'array',
            'related_objects.*' => 'in:attendanceSettings',
        ]);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request[$currentValidation] = $validator->validate();

        $rules = $this->getRules(['key', 'value', 'description', 'is_used']);
        $rules['attendance_setting_key'] = 'nullable|string';
        $rules['id'] = ['nullable', 'numeric'];

        foreach ($request[$currentValidation]['entity_array'] as $key => $entity) {
            $validator = \Validator::make($entity, $rules);
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }
        }

        //validate attendance group
        $currentValidation = 'attendance_group';
        $validator = \Validator::make($request[$currentValidation], [
            'entity_array' => 'required|array',
            'related_objects' => 'array',
            'related_objects.*' => 'in:titles,dailyAttendancePolicies',
        ]);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request[$currentValidation] = $validator->validate();

        $rules = $this->attendanceGroupDto(['name', 'deduct_days_monthly']);
        $rules['id'] = ['nullable', 'numeric'];
        $rules['titles'] = ['present', 'array'];
        $rules['titles.*'] = ['required', 'numeric'];
        $rules['daily_attendance_policies'] = ['array'];

        foreach ($request[$currentValidation]['entity_array'] as $key => $entity) {
            $validator = \Validator::make($entity, $rules);
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }
        }

        //validate overtime group
        $currentValidation = 'overtime_group';
        $validator = \Validator::make($request[$currentValidation], [
            'entity_array' => 'required|array',
            'related_objects' => 'array',
            'related_objects.*' => 'in:titles,dailyOvertimePolicies',
        ]);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request[$currentValidation] = $validator->validate();
        $rules = $this->overtimeGroupDto(['name']);
        $rules['id'] = ['nullable', 'numeric'];
        $rules['titles'] = ['present', 'array'];
        $rules['titles.*'] = ['required', 'numeric'];
        $rules['daily_overtime_policies'] = ['array'];

        foreach ($request[$currentValidation]['entity_array'] as $key => $entity) {
            $validator = \Validator::make($entity, $rules);
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }
        }

        //validate national holiday group
        $currentValidation = 'national_holiday_group';
        $validator = \Validator::make($request[$currentValidation], [
            'entity_array' => 'required|array',
            'related_objects' => 'array',
            'related_objects.*' => 'in:titles',
        ]);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request[$currentValidation] = $validator->validate();
        $rules = $this->nationalHolidayGroupDto(['name', 'type', 'percentage']);
        $rules['id'] = ['nullable', 'numeric'];
        $rules['titles'] = ['present', 'array'];
        $rules['titles.*'] = ['required', 'numeric'];

        foreach ($request[$currentValidation]['entity_array'] as $key => $entity) {
            $validator = \Validator::make($entity, $rules);
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }
        }

        //validate overtime month group
        $currentValidation = 'overtime_month_group';
        $validator = \Validator::make($request[$currentValidation], [
            'entity_array' => 'required|array',
            'related_objects' => 'array',
            'related_objects.*' => 'in:titles',
        ]);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request[$currentValidation] = $validator->validate();
        $rules = $this->overtimeMonthGroupDto(['name', 'type', 'percentage']);
        $rules['id'] = ['nullable', 'numeric'];
        $rules['titles'] = ['present', 'array'];
        $rules['titles.*'] = ['required', 'numeric'];

        foreach ($request[$currentValidation]['entity_array'] as $key => $entity) {
            $validator = \Validator::make($entity, $rules);
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }
        }

        $this->service->perform($request, $output);
    }
}
