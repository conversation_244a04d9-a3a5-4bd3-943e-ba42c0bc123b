<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Timecard;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use App\Traits\CICOHelper;
use App\Traits\V1\AttendancePoliciesTrait;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ApplyLateDeduction extends Command
{
    use AttendancePoliciesTrait, CICOHelper;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'apply-late-deduction {--company_id=company_id} {--start_date=start_date} {--end_date=end_date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $companyId = $this->option('company_id');
            $startDate = $this->option('start_date');
            $endDate = $this->option('end_date');
            $numLateCreated = 0;
            $timecards = Timecard::whereHas('employee', function ($query) use ($companyId) {
                $query->where('company_id', $companyId);
            })
                ->whereDate('from', '>=', $startDate)
                ->whereDate('from', '<=', $endDate)
                ->whereHas('attendance.entityTags', function ($q) {
                    $q->where('tag', 'late');
                })
                ->with('employee', 'childTimecard', 'attendance.clockIn')
                ->orderBy('timecards.from')
                ->get();

            if (! $timecards->count()) {
                $this->info('No timecards found');

                return;
            }
            config(['globals.user' => $timecards->first()->employee->user]);
            config(['globals.company' => $timecards->first()->employee->company]);
            foreach ($timecards as $timecard) {
                $existedDeductions = $timecard->attendance->attendanceDeductions;
                if (count($existedDeductions) == 0) {
                    $lateDeduction = $this->createAttendanceDeductionIfExist($timecard->employee, $this->getDifferenceBetweenClockInAndShiftTimeInMinutes($timecard), $timecard->attendance);
                }
                $numLateCreated += isset($lateDeduction);
            }
            $this->info("created late for $numLateCreated timecards");
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
            dd($e);
        }
    }

    public function getDifferenceBetweenClockInAndShiftTimeInMinutes($timecard)
    {
        $employeeLeaveRequestRepository = new EmployeeLeaveRequestRepository;
        $actualShiftStartTime = Carbon::parse($timecard->from);
        if (isset($timecard->childTimecard)) {
            $actualShiftStartTime = $actualShiftStartTime->max(Carbon::parse($timecard->childTimecard->to));
        }
        $partialLeave = $employeeLeaveRequestRepository->getEmployeeApprovedPartialLeaveAroundDate($timecard->employee->id, Carbon::parse($timecard->from)->toDateString());
        if (isset($partialLeave)) {
            $actualShiftStartTime = $actualShiftStartTime->max(Carbon::parse($partialLeave->to));
        }

        $clockInTime = Carbon::parse($timecard->attendance->clockIn->date);

        return $clockInTime->diffInMinutes($actualShiftStartTime);
    }
}
