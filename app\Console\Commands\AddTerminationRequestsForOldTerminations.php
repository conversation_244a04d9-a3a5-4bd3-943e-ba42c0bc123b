<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Models\TerminationRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddTerminationRequestsForOldTerminations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:adding-termination-requests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        DB::transaction(function () {
            $employeesWithoutTerminationRequests = Employee::whereDoesntHave('terminationRequest')
                ->whereHas('employeeInfo', function ($query) {
                    $query->whereNotNull('termination_date');
                })
                ->get();

            foreach ($employeesWithoutTerminationRequests as $employee) {
                $employeeInfo = $employee->employeeInfo;

                $terminationRequest = TerminationRequest::create([
                    'employee_id' => $employee->id,
                    'terminate_date' => $employeeInfo->termination_date,
                    'terminate_reason' => $employeeInfo->termination_reason,
                    'terminate_type' => 'Termination',
                    'workflow_id' => null,
                    'status' => 'approved',
                ]);

                $terminationRequest->employeeRequests()->create([
                    'employee_id' => $employee->id,
                    'company_id' => $employee->company_id,
                    'status' => 'approved',
                    'requestable_type' => 'termination_request',
                    'request_name' => 'termination_request',
                    'requestable_id' => $terminationRequest->id,
                    'date' => date('Y-m-d'),
                ]);
            }

            $this->info('Termination requests and employee requests added successfully.');
        });
    }
}
