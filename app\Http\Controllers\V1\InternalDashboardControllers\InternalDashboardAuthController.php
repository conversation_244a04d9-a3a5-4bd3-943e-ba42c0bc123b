<?php

namespace App\Http\Controllers\V1\InternalDashboardControllers;

use App\Http\Controllers\NewController;
use App\Http\Requests\InternalDashboardLoginRequest;
use App\Http\Requests\InternalDashboardRegisterRequest;
use App\Models\InternalDashboardAdmin;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\Log;

class InternalDashboardAuthController extends NewController
{
    public function __construct() {}

    public function register(InternalDashboardRegisterRequest $request)
    {
        $data = $request->validated();

        $admin = InternalDashboardAdmin::create($data);

        return getResponseStructure(['data' => $admin],
            HttpStatusCodeUtil::CREATED,
            'Admin created successfully');
    }

    public function login(InternalDashboardLoginRequest $request)
    {
        $data = $request->validated();

        $ttl = 1440;
        auth('admin-api')->factory()->setTTL($ttl);

        $admin = InternalDashboardAdmin::where('email', $data['email'])->first();
        Log::info('admin: '.$admin);
        if (! $admin || ! $admin->is_verified) {
            return getResponseStructure(
                ['error' => 'Your account is not verified. Please contact support.'],
                HttpStatusCodeUtil::FORBIDDEN,
                'Forbidden'
            );
        }

        if (! $token = auth('admin-api')->attempt(['email' => $data['email'], 'password' => $data['password']])) {
            return getResponseStructure(
                ['error' => 'Invalid credentials'],
                HttpStatusCodeUtil::UNAUTHORIZED,
                'Unauthorized'
            );
        }

        return getResponseStructure(
            [
                'data' => [
                    'token' => $token,
                    'expires_in' => $ttl * 60,
                    'admin' => auth('admin-api')->user(),
                ],
            ],
            HttpStatusCodeUtil::OK,
            'Login successful'
        );
    }
}
