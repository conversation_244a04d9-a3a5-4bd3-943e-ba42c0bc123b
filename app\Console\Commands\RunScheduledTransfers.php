<?php

namespace App\Console\Commands;

use App\Services\EmployeeChange\RunScheduledTransfersService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RunScheduledTransfers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'run:scheduled:transfers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = ' ';

    public function __construct(private RunScheduledTransfersService $runScheduledTransfersService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        // Log::info('Rest Days cron job is started at: ' . date('Y-m-d H:i:s'));
        $transferOutput = new \stdClass;
        $this->runScheduledTransfersService->run($transferOutput);
        if (isset($transferOutput->Error)) {
            Log::info($transferOutput->Error);
        }

    }
}
