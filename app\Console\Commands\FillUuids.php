<?php

namespace App\Console\Commands;

use App\Services\FillUuidsService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FillUuids extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill:uuid';

    private FillUuidsService $fillUuidsService;

    /**
     * Execute the console command.
     */
    public function handle(FillUuidsService $fillUuidsService): void
    {
        $this->fillUuidsService = $fillUuidsService;

        DB::beginTransaction();
        try {
            $this->fillUuidsService->fill();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
