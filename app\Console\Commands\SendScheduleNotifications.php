<?php

namespace App\Console\Commands;

use App\Jobs\SendShiftsPushNotification;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SendScheduleNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:schedule:notifications';

    /**
     * The console command description.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        DB::beginTransaction();
        try {
            $this->send();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }

    public function send()
    {
        $startingFromTime = Carbon::now()->subHours(6)->format('Y-m-d H:i:s');

        $users = User::whereHas('employee', function ($q) use ($startingFromTime) {
            $q->select('id')
                ->where(function ($q) use ($startingFromTime) {
                    $q->whereHas('timecards', function ($q) use ($startingFromTime) {
                        $q->where('created_at', '>=', $startingFromTime)
                            ->whereHas('entityTags', function ($q) {
                                $q->where('tag', config('globals.ATTENDANCE_TAGS.ABSENT'));
                            });
                    })
                        ->orWhereHas('employeeLeaveRequests', function ($q) use ($startingFromTime) {
                            $q
                                ->where('status', config('globals.REQUEST_STATUSES.APPROVED'))
                                ->where('created_at', '>=', $startingFromTime);
                        });
                });
        })
            ->get();

        if (count($users)) {
            dispatch(new SendShiftsPushNotification($users))->afterCommit();
        }

    }
}
