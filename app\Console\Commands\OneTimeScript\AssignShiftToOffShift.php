<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Timecard;
use App\Services\V1\Attendance\NewShiftsService;
use App\Services\V1\Attendance\TimeCardsService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AssignShiftToOffShift extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assign-shift-to-off-shift {--company_id=company_id} {--start_date=start_date} {--end_date=end_date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $newShiftsService = app(NewShiftsService::class);
            $timecardsService = app(TimeCardsService::class);
            $companyId = $this->option('company_id');
            $startDate = $this->option('start_date');
            $endDate = $this->option('end_date');
            $numAssigned = 0;
            $timecards = Timecard::whereHas('employee', function ($query) use ($companyId) {
                $query->where('company_id', $companyId);
            })
                ->whereNull('shift_id')
                ->whereDate('from', '>=', $startDate)
                ->whereDate('from', '<=', $endDate)
                ->withWhereHas('attendance.clockIn')
                ->withWhereHas('attendance.entityTags', function ($q) {
                    $q->where('tag', 'off_shift');
                })->get();

            if (! $timecards->count()) {
                $this->info('No timecards found');

                return;
            }
            config(['globals.user' => $timecards->first()->employee->user]);
            config(['globals.company' => $timecards->first()->employee->company]);
            foreach ($timecards as $timecard) {
                $clockIn = $timecard->attendance->clockIn;
                $data = [
                    'employee_id' => $timecard->employee_id,
                    'clock_in' => $clockIn->date,
                ];
                $shifts = $newShiftsService->getRunningShifts($data);
                $closestShiftDuration = PHP_INT_MAX;
                $closestShift = null;
                foreach ($shifts as $shift) {
                    $fromTime = Carbon::parse($shift->from_time);
                    $clockInTime = Carbon::parse($clockIn->date)->format('H:i:s');
                    $duration = $fromTime->diffInMinutes($clockInTime);
                    if ($duration < $closestShiftDuration) {
                        $closestShiftDuration = $duration;
                        $closestShift = $shift;
                    }
                }
                if (isset($closestShift)) {
                    $assignData['shift_id'] = $closestShift->id;
                    $assignData['timecard_id'] = $timecard->id;
                    $timecardsService->assignOffShiftTimecard($assignData);
                    $numAssigned++;
                }
            }
            $this->info("Assigned $numAssigned timecards");
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
            dd($e);
        }
    }
}
