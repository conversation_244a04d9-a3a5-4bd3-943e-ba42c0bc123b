<?php

namespace App\Console\Commands;

use App\Models\EmployeeLeaveRequest;
use App\Traits\V1\NotificationRedirection;
use App\Traits\WorkflowTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateRestDayLeaveRequests extends Command
{
    use NotificationRedirection, WorkflowTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-rest-day-leaves';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update status of rest day leave requests to approved';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting rest day leave requests update process...');

        try {
            DB::beginTransaction();

            $leaveRequests = EmployeeLeaveRequest::join('companies', 'employee_leave_requests.company_leave_type_id', '=', 'companies.rest_day_leave_id')
                ->whereIn('employee_leave_requests.status', ['pending', 'rejected'])
                ->whereDate('employee_leave_requests.from', '>=', '2025-01-01')
                ->select('employee_leave_requests.*')
                ->get();

            $this->info("Found {$leaveRequests->count()} rest day leave requests to update");

            $updatedCount = 0;
            $errorCount = 0;

            foreach ($leaveRequests as $request) {
                try {
                    // Update the leave request status
                    $request->update(['status' => 'approved']);

                    $updatedCount++;
                    // $this->info("✅ Updated leave request ID: {$request->id}");
                } catch (\Exception $e) {
                    $errorCount++;
                    Log::error("Error updating leave request ID {$request->id}: ".$e->getMessage());
                    $this->error("❌ Error updating leave request ID {$request->id}: ".$e->getMessage());
                }
            }

            DB::commit();

            $this->info('📊 Summary:');
            $this->info("✅ Successfully updated: {$updatedCount} requests");
            $this->info("❌ Failed to update: {$errorCount} requests");

            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in UpdateRestDayLeaveRequests: '.$e->getMessage());
            $this->error('❌ Error: '.$e->getMessage());

            return Command::FAILURE;
        }
    }
}
