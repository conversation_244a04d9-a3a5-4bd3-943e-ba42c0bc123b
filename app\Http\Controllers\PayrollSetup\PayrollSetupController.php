<?php

namespace App\Http\Controllers\PayrollSetup;

use App\Exceptions\UnprocessableException;
use App\Exports\ImportEmployeeSalarySample;
use App\Exports\V1\EmployeeSalariesExport;
use App\Http\Controllers\NewController;
use App\Http\Requests\AddCategoryRequest;
use App\Http\Requests\AddComponentPolicyRequest;
use App\Http\Requests\AddComponentRequest;
use App\Http\Requests\AddEmployeeSalaryRequest;
use App\Http\Requests\DeleteCategoryRequest;
use App\Http\Requests\DeleteComponentRequest;
use App\Http\Requests\DeletePolicyRequest;
use App\Http\Requests\DeleteSalaryRequest;
use App\Http\Requests\EditCategoryRequest;
use App\Http\Requests\EditComponentPolicyRequest;
use App\Http\Requests\EditComponentRequest;
use App\Http\Requests\GetCategoriesWithPoliciesRequest;
use App\Http\Requests\GetCategoryRequest;
use App\Http\Requests\GetEmployeesSalaryByFilterRequest;
use App\Http\Requests\GetPayrollEventsRequest;
use App\Http\Requests\PayrollBasicInfoRequest;
use App\Http\Requests\UploadBulkComponentsRequest;
use App\Http\Requests\UploadEmployeeSalariesRequest;
use App\Http\Requests\V1\AddCategoryWithComponentRequest;
use App\Http\Resources\Payroll\ComponentCategoryResource;
use App\Http\Resources\Payroll\ComponentCatgeoryCollection;
use App\Imports\EmployeeSalaryImport;
use App\Imports\EmployeeSalaryMultiSheetImport;
use App\Imports\VariableSalaryComponentImport;
use App\Models\CompanyLeaveType;
use App\Models\Employee;
use App\Models\Payroll;
use App\Models\PayRollPolicy;
use App\Models\TimecardType;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\V1\EmployeeRepository;
use App\Repositories\V1\PayRollPolicyRepository;
use App\Services\CompanySetup\BanksService;
use App\Services\CompanySetup\BusinessServices\GetEmployeesFiltersService;
use App\Services\CompanySetup\CrudServices\CompanyCrudService;
use App\Services\PayrollSetup\EmployeeInsurancesService;
use App\Services\PayrollSetup\EmployeeSalariesService;
use App\Services\PayrollSetup\PayrollsService;
use App\Services\PayrollSetup\SalaryComponentPoliciesService;
use App\Services\PayrollSetup\SalaryComponentsCategoriesService;
use App\Services\PayrollSetup\SalaryComponentsService;
use App\Services\PayrollSetup\SystemSettingsService;
use App\Services\V1\LeaveManagement\EmployeeLeavesService;
use App\Services\V1\Payroll\ConvertNetToGrossService;
use App\Traits\GetLastDraftedPayRollOrCreate;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Util\HttpStatusCodeUtil;
use App\Util\PayrollUtil;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use stdClass;

class PayrollSetupController extends NewController
{
    use GetLastDraftedPayRollOrCreate, PayrollHelper;

    public function __construct(private SystemSettingsService             $systemSettingsService,
                                private BanksService                      $banksService,
                                private SalaryComponentsCategoriesService $salaryComponentsCategoriesService,
                                private SalaryComponentsService           $salaryComponentsService,
                                private SalaryComponentPoliciesService    $salaryComponentPoliciesService,
                                private EmployeeSalariesService           $employeeSalariesService,
                                private GetEmployeesFiltersService        $getEmployeesFiltersService,
                                private EmployeeInsurancesService         $employeeInsurancesService,
                                private CompanyCrudService                $companyCrudService,
                                private EmployeeSalaryImport              $employeeSalaryImport,
                                private PayrollsRepository                $payrollsRepository,
                                private SystemSettingRepository           $systemSettingRepository,
                                private PayRollPolicyRepository           $payRollPolicyRepository,
                                private EmployeeRepository                $employeeRepository,
                                private ConvertNetToGrossService          $convertNetToGrossService,
                                private EmployeeLeavesService             $employeeLeavesService,
                                private PayrollsService                   $payrollsService
    )
    {
        parent::__construct($systemSettingsService);
    }

    public function setBasicInfo(PayrollBasicInfoRequest $request)
    {
        DB::beginTransaction();
        try {
            $payrollMonthlyClosingDateSetting = $this->systemSettingsService
                ->getFirstByAttributes(['key' => 'payroll_monthly_closing_day']);
            $oldDay = $payrollMonthlyClosingDateSetting->value ?? -1;
            if (isset($payrollMonthlyClosingDateSetting)) {
                $this->systemSettingsService->delete([$payrollMonthlyClosingDateSetting->id]);
            }
            $payrollMonthlyClosingDay = $request->input('payroll_monthly_closing_day');
            if (isset($payrollMonthlyClosingDay)) {
                $closingDateRequest['as_of_date'] = Carbon::now()->toDateString();
                $closingDateRequest['key'] = 'payroll_monthly_closing_day';
                $closingDateRequest['value'] = $payrollMonthlyClosingDay;
                $payrollMonthlyClosingDateSetting = $this->systemSettingsService->add($closingDateRequest);
                $this->payrollsService->updateDraftedPayrollsBasedOnClosthingDay($payrollMonthlyClosingDay);
                $this->employeeLeavesService->updateRestDayBalancesBasedOnClothingDay($payrollMonthlyClosingDay);
            }

            $disbursementDay = $this->systemSettingsService->getFirstByAttributes(['key' => 'payroll_disbursement_day']);
            if (isset($disbursementDay)) {
                $this->systemSettingsService->delete([$disbursementDay->id]);
            }

            $disbursementDay = $request->input('payroll_disbursement_day');
            if (isset($disbursementDay)) {
                $disbursementDayRequest['as_of_date'] = Carbon::now()->toDateString();
                $disbursementDayRequest['key'] = 'payroll_disbursement_day';
                $disbursementDayRequest['value'] = $request->input('payroll_disbursement_day');
                $disbursementDay = $this->systemSettingsService->add($disbursementDayRequest);
            }

            $policy = $request->input('tax_and_social_insurance_policy');
            $currentPayroll = $this->getCurrentPayroll();
            $currentTaxAndInsurancePolicy = $this->payRollPolicyRepository->getCompanyCurrentPayrollPolicy($currentPayroll->id);
            $this->addCompanyToPayrollPolicy(auth()->user()->company_id, $policy, $currentPayroll->id);
            if ($currentTaxAndInsurancePolicy != $policy) {
                $this->updateEmployeesSalary($policy);
            }

            if ($request->input('disbursement_bank_ids') && count($request->input('disbursement_bank_ids'))) {
                auth()->user()->company->banks()->sync($request->input('disbursement_bank_ids'));
            }
            DB::commit();

            return getResponseStructure(['data' => [
                'payroll_monthly_closing_day' => $payrollMonthlyClosingDateSetting,
                'payroll_disbursement_day' => $disbursementDay,
                'banks' => auth()->user()->company->banks()->get(),
            ],
            ], HttpStatusCodeUtil::OK, 'Settings was added successfully');

        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::error($e);

            return getErrorResponseStructure(HttpStatusCodeUtil::BAD_REQUEST, 'Could not update the settings');
        }
    }

    public function getBasicInfo()
    {
        $basicInfo = $this->systemSettingsService->getBasicInfo();
        $basicInfo['company_banks'] = auth()->user()->company->banks;
        $currentPayroll = $this->getCurrentPayroll();
        if (!is_null($currentPayroll)) {
            $basicInfo['payroll_policy'] = $this->payRollPolicyRepository->getCompanyCurrentPayrollPolicy($currentPayroll->id) ?? null;
            if (is_null($basicInfo['payroll_policy'])) {
                $lastPayrollId = Payroll::where('id', '!=', $currentPayroll->id)->orderBy('id', 'desc')->first()?->id ?? null;
                if (!is_null($lastPayrollId)) {
                    $lastCompanyPayrollPolicy = PayRollPolicy::where([
                        'payroll_id' => $lastPayrollId,
                        'payable_id' => auth()->user()->company_id,
                        'payable_type' => 'company',
                    ])->select('policy')->first();
                    $basicInfo['payroll_policy'] = $lastCompanyPayrollPolicy;
                }
            }
            $basicInfo['show_payroll_hub'] = $this->systemSettingsService->showPayrollHub();
        }

        return getResponseStructure(['data' => $basicInfo],
            HttpStatusCodeUtil::OK, 'Payroll setup basic information');
    }

    public function getCompanyBanks()
    {
        $banks = $this->banksService->getCompanyBanks();

        return getResponseStructure(['data' => $banks],
            HttpStatusCodeUtil::OK, 'Available banks');

    }

    public function getAllBanks()
    {
        $banks = $this->banksService->all();

        return getResponseStructure(['data' => $banks],
            HttpStatusCodeUtil::OK, 'Available banks');

    }

    public function addSalaryComponentCategory(AddCategoryRequest $request)
    {
        if ($this->salaryComponentsCategoriesService->companyHasCategory($request->input('name'))) {
            throw new UnprocessableException(trans('messages.company_has_key') . ' deleteComponent' . $request->input('name'));
        }
        $category = $this->salaryComponentsCategoriesService->add($request->all());

        return getResponseStructure(['data' => $category],
            HttpStatusCodeUtil::OK, 'Category was added successfully');
    }

    public function addSalaryComponent(AddComponentRequest $request)
    {
        if ($this->salaryComponentsService->companyHasComponent($request->input('name_ar'))) {
            throw new UnprocessableException(trans('messages.company_has_key') . ' ' . $request->input('name'));
        }
        $request['name'] = $request->input('name_ar');

        $component = $this->salaryComponentsService->add($request->all());

        return getResponseStructure(['data' => $component],
            HttpStatusCodeUtil::OK, 'Component was added successfully');
    }

    public function addSalaryComponentPolicy(AddComponentPolicyRequest $request)
    {
        $componentPolicy = $this->salaryComponentPoliciesService->add($request->all());

        return getResponseStructure(['data' => $componentPolicy],
            HttpStatusCodeUtil::OK, 'Component Policy was added successfully');
    }

    public function getCategoryWithPolicies(GetCategoryRequest $request)
    {
        $category = $this->salaryComponentsCategoriesService->getCategoryWithPolicies($request->all());

        return getResponseStructure(['data' => new ComponentCategoryResource($category)],
            HttpStatusCodeUtil::OK, 'Available Categories');

    }

    public function getCategoriesWithPolicies(GetCategoriesWithPoliciesRequest $request)
    {
        $categories = $this->salaryComponentsCategoriesService->getCategoriesWithPolicies($request->all());

        return getResponseStructure(['data' => new ComponentCatgeoryCollection($categories)],
            HttpStatusCodeUtil::OK, 'Available Categories');
    }

    public function addEmployeeSalaryAndInsurance(AddEmployeeSalaryRequest $request)
    {
        if (isset($request['bank_id'])) {
            $bank = $this->banksService->find($request->input('bank_id'));
            $bankHasCompanyRelation = $bank->companies()->where('company_id', auth()->user()->company_id)->exists();

            if (!$bankHasCompanyRelation) {
                throw new UnprocessableException(trans('messages.bank_not_linked_to_company') . ' ' . $request->input('name'));
            }
        }

        $data = $request->validated();

        $data['gross_salary'] = $this->calculateGrossSalary($data);

        if (!isset($data['salary_disbursement_method'])) {
            $data['salary_disbursement_method'] = Employee::find($data['employee_id'])?->employeeSalary?->salary_disbursement_method ?? 'cash';
        }

        $data['as_of_date'] = Carbon::now()->toDateString();
        $currentPayroll = $this->getCurrentPayroll();
        if (isset($data['policy']) && $data['policy']) {
            $policy = $data['policy'];
            $this->addEmployeeToPayrollPolicy($data['employee_id'], $policy, $currentPayroll->id);
        }
        $employeeInsuranceRequest = Arr::only($data, ['employee_id', 'social_insurance_date',
            'social_insurance_office', 'social_insurance_number', 'medical_insurance_number', 'government_insurance_number']);

        $data['basic_salary'] = Arr::get($data, 'basic_salary', Arr::get($data, 'net_salary')); // should be deleted after releasing new gross logic
        $data['net_salary'] = Arr::get($data, 'net_salary', Arr::get($data, 'basic_salary'));

        $employeeSalary = $this->employeeSalariesService->add($data);

        $employeeInsurance = $this->employeeInsurancesService->getFirstByAttributes(['employee_id' => $data['employee_id']]);

        if (isset($employeeInsurance)) {
            $this->employeeInsurancesService->update($employeeInsurance->id, $employeeInsuranceRequest);
        } else {
            $this->employeeInsurancesService->add($employeeInsuranceRequest);
        }

        return getResponseStructure(['data' => $employeeSalary],
            HttpStatusCodeUtil::OK, 'Employee salary was added successfully');
    }

    public function addEmployeeToPayrollPolicy(int $employeeId, string $policy, int $payrollId)
    {
        $this->payRollPolicyRepository->upsert(
            [
                'payable_id' => $employeeId,
                'payable_type' => 'employee',
                'policy' => $policy,
                'payroll_id' => $payrollId,
            ],
            [
                'payable_id' => $employeeId,
                'payable_type' => 'employee',
                // 'policy' => $policy,
                'payroll_id' => $payrollId,
            ],
            ['payable_id', 'payable_type', 'policy', 'payroll_id']
        );
    }

    public function addCompanyToPayrollPolicy(int $companyId, string $policy, int $payrollId)
    {
        $this->payRollPolicyRepository->upsert(
            [
                'payable_id' => $companyId,
                'payable_type' => 'company',
                'policy' => $policy,
                'payroll_id' => $payrollId,
            ],
            [
                'payable_id' => $companyId,
                'payable_type' => 'company',
                // 'policy' => $policy,
                'payroll_id' => $payrollId,
            ],
            ['payable_id', 'payable_type', 'policy', 'payroll_id']
        );

    }

    public function deleteCategory(DeleteCategoryRequest $request)
    {
        $ids = $request->input('ids');

        $count = $this->salaryComponentsCategoriesService->delete($ids);

        return getResponseStructure([],
            HttpStatusCodeUtil::OK, $count . ' Records were deleted');
    }

    public function deleteComponent(DeleteComponentRequest $request)
    {
        $ids = $request->input('ids');

        $count = $this->salaryComponentsService->delete($ids);

        return getResponseStructure([],
            HttpStatusCodeUtil::OK, $count . ' Records were deleted');
    }

    public function deletePolicy(DeletePolicyRequest $request)
    {
        $ids = $request->input('ids');

        $count = $this->salaryComponentPoliciesService->delete($ids);

        return getResponseStructure([],
            HttpStatusCodeUtil::OK, $count . ' Records were deleted');
    }

    public function deleteSalary(DeleteSalaryRequest $request)
    {
        $ids = $request->input('ids');

        $count = $this->employeeSalariesService->delete($ids);

        return getResponseStructure([],
            HttpStatusCodeUtil::OK, $count . ' Records were deleted');
    }

    public function getEmployeesSalaryByFilter(GetEmployeesSalaryByFilterRequest $request)
    {
        $request = $request->validated();

        $request['employee_salary'] = true;
        //        $request['status'] = config('globals.EMPLOYEE_STATUSES.ACTIVE');
        $output = new stdClass;
        $this->getEmployeesFiltersService->perform($request, $output);

        $employeesWithSalary = $output->Employees;

        return getResponseStructure($employeesWithSalary->toArray(),
            HttpStatusCodeUtil::OK, 'Available salaries');
    }

    public function editSalaryComponentCategory($id, EditCategoryRequest $request)
    {
        $data = $request->validated();
        $data['name'] = $data['name_ar'];
        $this->salaryComponentsCategoriesService->update($id, $data);

        return getResponseStructure([],
            HttpStatusCodeUtil::OK, ' Category has been edited');
    }

    public function editSalaryComponent($id, EditComponentRequest $request)
    {
        $data = $request->validated();
        $data['name'] = $data['name_ar'];
        $this->salaryComponentsService->update($id, $data);

        return getResponseStructure([],
            HttpStatusCodeUtil::OK, ' Salary Component has been edited');
    }

    public function editSalaryComponentPolicy($id, EditComponentPolicyRequest $request)
    {
        $data = $request->validated();

        if (isset($data['leave_type_ids'])) {
            if ($data['leave_type_ids'] === 'none') {
                $data['leave_type_ids'] = [];
            } elseif ($data['leave_type_ids'] === 'all') {
                $data['leave_type_ids'] = CompanyLeaveType::pluck('id')->toArray();
            }
        }

        if (isset($data['timecard_type_ids'])) {
            if ($data['timecard_type_ids'] === 'none') {
                $data['timecard_type_ids'] = [];
            } elseif ($data['timecard_type_ids'] === 'all') {
                $data['timecard_type_ids'] = TimecardType::pluck('id')->toArray();
            }
        }

        $this->salaryComponentPoliciesService->editWithLeaveTypesAndTimecardTypes($id, $data);

        return getResponseStructure([],
            HttpStatusCodeUtil::OK, ' Salary Component Policy has been edited');
    }

    public function editSalary($id, AddEmployeeSalaryRequest $request)
    {
        $this->employeeSalariesService->delete([$id]);
        $this->addEmployeeSalaryAndInsurance($request);

        return getResponseStructure([],
            HttpStatusCodeUtil::OK, ' Salary has been edited');
    }

    public function setPayrollSetupState(string $state)
    {
        $company = auth()->user()->company;
        $currentState = $company->payroll_setup_state;
        // if($currentState == config('globals.FINISHED_PAYROLL_SETUP')) ignore this validation for now #TODO
        // {
        //     throw new UnprocessableException(trans('messages.setup_is_finished_already'));
        // }

        $output = new stdClass;
        $this->companyCrudService->update(['id' => $company->id, 'payroll_setup_state' => $state], $output);

        if (isset($output->Error)) {
            return getResponseStructure([],
                HttpStatusCodeUtil::BAD_REQUEST, 'An Error has occured');
        }

        return getResponseStructure([],
            HttpStatusCodeUtil::OK, 'Payroll setup state has been updated');
    }

    public function downloadSalariesBulkUploadSample()
    {
        return Excel::download(new ImportEmployeeSalarySample, 'Salaries Import.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function uploadSalariesAndInsurances(UploadEmployeeSalariesRequest $request)
    {
        $file = $request->file('salary_import');

        $import = new EmployeeSalaryMultiSheetImport($this->employeeSalariesService, $this->employeeInsurancesService, $this->convertNetToGrossService);

        try {
            $import->onlySheets('Worksheet');
            $file = Excel::import($import, $file);
        } catch (Exception $e) {
            return $this->response(['errors' => array_filter(explode("\n", $e->getMessage()), 'strlen')], 422, 'error in the sheet'); // split error message by new line to return them as array and ignore empty array
        }

        return $this->response([], 200, 'Employees data was imported successfully');
    }

    public function uploadBulkSalaryComponents(UploadBulkComponentsRequest $request)
    {
        $file = $request->file('salary_components_file');
        $data = $request->all();
        try {
            $errors = [];
            Excel::import(new VariableSalaryComponentImport($data), $file);
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            foreach ($failures as $failure) {
                $errors[] = $failure->errors()[0] . ' at row ' . $failure->row();
            }

            return $this->response(['errors' => $errors], 422, 'Errors in file');
        } catch (UnprocessableException $e) {
            return $this->response(['errors' => [$e->getMessage()]], 422, 'error in payload');
        }

        return $this->response([], 200, 'Employees data was imported successfully');
    }

    /* old service charge
        public function setupServiceCharge($titleIds, $serviceChargeTaxable)
        {
            $serviceChargeCategoryRequest = [
                'name' => 'service_charge',
                'is_addition' => 1,
                'is_taxable' => $serviceChargeTaxable,
            ];

            $serviceChargeCategory = $this->salaryComponentsCategoriesService->findFirstByKey('name', 'service_charge');
            if (isset($serviceChargeCategory)) {
                $serviceChargeCategory->update($serviceChargeCategoryRequest);
            } else {
                $serviceChargeCategory = $this->salaryComponentsCategoriesService->add($serviceChargeCategoryRequest);
            }

            $serviceChargeComponent = $serviceChargeCategory->salaryComponent;
            if (! isset($serviceChargeComponent)) {
                $serviceChargeComponentRequest = [
                    'name' => 'service_charge',
                    'is_variable' => 0,
                    'is_automated' => 1,
                    'salary_components_category_id' => $serviceChargeCategory->id,
                ];
                $serviceChargeComponent = $this->salaryComponentsService->add($serviceChargeComponentRequest);
            }
            if (isset($serviceChargeComponent->salaryComponentPolicies)) {
                $serviceChargeComponent->salaryComponentPolicies()->delete();
            }

            $serviceChargeComponentPolicyRequest = [
                'salary_component_id' => $serviceChargeComponent->id,
                'calculation_frequency' => 'service_charge',
                'title_id' => null,
            ];
            $salaryComponentPolicy = $this->salaryComponentPoliciesService->add($serviceChargeComponentPolicyRequest);
            foreach ($titleIds as $titleId) {
                SalaryComponentPolicyTitle::create([
                    'policy_id' => $salaryComponentPolicy->id,
                    'title_id' => $titleId,
                ]);
            }
        }
    */
    public function exportEmployeeSalariesByFilter(GetEmployeesSalaryByFilterRequest $request)
    {

        $data = $request->validated();
        $data['employee_salary'] = true;
        $data['status'] = config('globals.EMPLOYEE_STATUSES.ACTIVE');
        $data['page_size'] = config('globals.MAX_EXCEL_ROWS');
        $data['page'] = 1;
        $output = new stdClass;
        $this->getEmployeesFiltersService->perform($data, $output);

        $employeesWithSalary = $output->Employees;

        return Excel::download(new EmployeeSalariesExport($employeesWithSalary), 'Employee_salaries.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function calculateGrossSalary($data)
    {
        $netSalary = Arr::get($data, 'net_salary');
        $taxAndInsurancePolicy = Arr::get($data, 'policy', $this->getTaxInsurancePolicy($data['employee_id'], config('globals.company')->id));
        $socialInsuranceSalary = Arr::get($data, 'social_insurance_salary');
        $result = $this->convertNetToGrossService->calculate($netSalary, $socialInsuranceSalary, $taxAndInsurancePolicy);

        return $result['grossSalary'];
    }

    public function updateEmployeesSalary($taxAndInsurancePolicy)
    {
        $employees = $this->employeeRepository->getEmployeesWithSalaryInfo();
        foreach ($employees as $employee) {
            $netSalary = $employee->employeeSalary?->net_salary ?? null;

            if (!isset($netSalary)) {
                continue;
            }

            if ($taxAndInsurancePolicy == PayrollUtil::TAX_AND_SOCIAL_INSURANCE_POLICIES['CUSTOM']) {
                $employeePolicy = $this->getTaxInsurancePolicy($employee->id, config('globals.company')->id);
                // if($employeePolicy == PayrollUtil::TAX_AND_SOCIAL_INSURANCE_POLICIES['CUSTOM'])
                //     $employeePolicy = PayrollUtil::TAX_AND_SOCIAL_INSURANCE_POLICIES['CALCULATION_NOT_INCLUDED'];
            } else {
                $employeePolicy = $taxAndInsurancePolicy;
            }

            $result = $this->convertNetToGrossService->calculate($netSalary, null, $employeePolicy);

            if (array_key_exists('grossSalary', $result)) {
                $employee->employeeSalary->gross_salary = $result['grossSalary'];
                $employee->employeeSalary->social_insurance_salary = $this->getSocialInsuranceSalary($employeePolicy, Arr::get($result, 'socialInsuranceAmount', $result['grossSalary']));
                $employee->employeeSalary->save();
            }

        }
    }

    public function getSocialInsuranceSalary($taxPolicy, $socialInsuranceSalary)
    {
        $customPolicy = PayrollUtil::TAX_AND_SOCIAL_INSURANCE_POLICIES['CUSTOM'];
        $calculationNotIncluded = PayrollUtil::TAX_AND_SOCIAL_INSURANCE_POLICIES['CALCULATION_NOT_INCLUDED'];

        switch ($taxPolicy) {
            case $customPolicy:
            case $calculationNotIncluded:
                return 0;
                break;
            default:
                return $socialInsuranceSalary;
                break;
        }
    }

    public function addCategoryWithComponents(AddCategoryWithComponentRequest $request)
    {
        try {
            DB::transaction(function () use ($request, &$category) {
                $data = $request->validated();
                $data['name'] = $data['name_ar'];
                $category = $this->salaryComponentsCategoriesService->add($data);
                $components = $request->input('components');
                foreach ($components as &$component) {
                    $component['name'] = $component['name_ar'];
                    $component['salary_components_category_id'] = $category->id;
                }
                $this->salaryComponentsService->createMany($components);
            });

            return getResponseStructure(
                ['data' => $category],
                HttpStatusCodeUtil::OK,
                'Category was added successfully'
            );

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function getPayrollEvents(GetPayrollEventsRequest $request)
    {
        $payrollEvents = $this->payrollsService->getPayrollEvents($request->validated());

        return getResponseStructure(
            ['data' => [
                'payroll_event_end' => $payrollEvents->payroll_event_end ?? null,
            ]],
            HttpStatusCodeUtil::OK,
            'Payroll events'
        );
    }
}
