<?php

namespace App\Http\Requests\V1\PayrollHub;

use App\Rules\EmployeeIdRule;
use App\Traits\GetLastDraftedPayRollOrCreate;
use Illuminate\Foundation\Http\FormRequest;

class EmployeePayrollEntriesRequest extends FormRequest
{
    use GetLastDraftedPayRollOrCreate;
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'employee_id' => [
                'required',
                'string',
                new EmployeeIdRule
            ],
            'from_date' => [
                'required',
                'date',
            ],
            'to_date' => [
                'required',
                'date',
            ],
        ];
    }

    protected function prepareForValidation()
    {
        $payroll = $this->getCurrentPayroll();
        $this->merge([
            'employee_id' => $this->route('id'),
            'from_date' => $payroll->start,
            'to_date' =>  $payroll->end,
        ]);
    }
}
