<?php

namespace App\Http\Requests\TimeCard;

use Illuminate\Foundation\Http\FormRequest;

class GetTimecardRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'with_mission_timecard_type' => 'boolean',
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'with_mission_timecard_type' => $this->with_mission_timecard_type ?? true,
        ]);
    }
}
