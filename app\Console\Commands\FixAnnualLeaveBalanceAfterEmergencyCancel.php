<?php

namespace App\Console\Commands;

use App\Models\CompanyDefaultLeaveType;
use App\Models\EmployeeLeaveBalance;
use App\Models\EmployeeLeaveRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixAnnualLeaveBalanceAfterEmergencyCancel extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-annual-leave-balance-after-emergency-cancel';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate annual leave balances for employees with cancelled emergency leaves';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting FixAnnualLeaveBalanceAfterEmergencyCancel command...');

        try {
            DB::beginTransaction();

            // Get emergency leave types
            $emergencyLeaveTypes = CompanyDefaultLeaveType::where('key', 'emergency_leave_type_id')->get();

            if ($emergencyLeaveTypes->isEmpty()) {
                $this->warn('No emergency leave types found.');
                DB::rollBack();

                return;
            }

            $this->info('Emergency leave types found: ' . $emergencyLeaveTypes->count());

            // Get employees with cancelled emergency leaves this year
            $employeeIds = EmployeeLeaveRequest::where('status', 'cancelled')
                ->whereIn('company_leave_type_id', $emergencyLeaveTypes->pluck('company_leave_type_id'))
                ->where('from', '>=', now()->startOfYear())
                ->pluck('employee_id')
                ->unique();

            if ($employeeIds->isEmpty()) {
                $this->warn('No employees found with cancelled emergency leaves.');
                DB::rollBack();

                return;
            }

            $this->info('Found employees with cancelled emergency leaves: ' . $employeeIds->count());

            // Process each affected employee
            foreach ($employeeIds as $employeeId) {
                try {
                    // Fetch the employee's company and annual leave type
                    $leaveRequest = EmployeeLeaveRequest::where('employee_id', $employeeId)
                        ->with('companyLeaveType.company')
                        ->first();

                    $annualLeaveType = $leaveRequest->companyLeaveType->company->annualLeaveType;

                    if (!$annualLeaveType) {
                        $this->warn("Annual leave type not found for employee ID {$employeeId}");
                        throw new \Exception("Annual leave type not found for employee ID {$employeeId}");
                    }

                    $annualLeaveTypeId = $annualLeaveType->id;

                    // Base annual balance (21 days * 8 hours per day)
                    $baseAnnualLeaveBalance = 21 * 8;

                    // Sum up approved annual and emergency leaves for this employee this year
                    $approvedLeaveQuantity = EmployeeLeaveRequest::where('employee_id', $employeeId)
                        ->where('status', 'approved')
                        ->where('from', '>=', now()->startOfYear())
                        ->whereIn('company_leave_type_id', function ($query) use ($annualLeaveTypeId, $emergencyLeaveTypes) {
                            $query->select('id')
                                ->from('company_leave_types')
                                ->whereIn('id', $emergencyLeaveTypes->pluck('company_leave_type_id'))
                                ->orWhere('id', $annualLeaveTypeId);
                        })
                        ->sum('net_quantity');

                    // Recalculate annual leave balance
                    $newAnnualBalance = $baseAnnualLeaveBalance - $approvedLeaveQuantity;

                    // Update employee's annual leave balance
                    $leaveBalance = EmployeeLeaveBalance::where('employee_id', $employeeId)
                        ->where('company_leave_type_id', $annualLeaveTypeId)
                        ->where('start', '<=', now()->startOfYear())
                        ->where('end', '>=', now()->startOfYear())
                        ->first();
                    if (!$leaveBalance) {
                        continue;
                    }

                    $oldBalance = $leaveBalance->balance;
                    $leaveBalance->balance = $newAnnualBalance + $leaveBalance->transferred_balance;
                    $leaveBalance->save();

                    $this->info("Updated leave balance for employee ID {$employeeId}:");
                    $this->info("  - Old Balance: {$oldBalance}");
                    $this->info("  - Approved Leave Quantity: {$approvedLeaveQuantity}");
                    $this->info("  - New Balance: {$newAnnualBalance}");

                } catch (\Exception $e) {
                    $this->error("Error recalculating leave balance for employee ID {$employeeId}: " . $e->getMessage());
                    throw $e; // Ensure transaction rollback
                }
            }

            DB::commit();
            $this->info('FixAnnualLeaveBalanceAfterEmergencyCancel command completed successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('FixAnnualLeaveBalanceAfterEmergencyCancel command failed: ' . $e->getMessage());
        }
    }
}
