<?php

namespace App\Jobs;

use App\Models\EmployeeLeaveRequest;
use App\Models\User;
use App\Notifications\PushFcmNotification;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ApprovedLeaveRequestPushNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private EmployeeLeaveRequest $leaveRequest;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(EmployeeLeaveRequest $leaveRequest)
    {
        $this->leaveRequest = $leaveRequest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // $user = $this->leaveRequest->employee->user;
        $employeeId = $this->leaveRequest->employee->id;
        $user = User::where('employee_id', $employeeId)->select('id', 'lang')->first();
        Log::info('user from inside push notification job: '. json_encode($user). 'with size: '. strlen(json_encode($user)));
        $fromDate = Carbon::parse($this->leaveRequest->from)->format('D, d M Y');
        $toDate = Carbon::parse($this->leaveRequest->to)->format('D, d M Y');

        $user->notify(new PushFcmNotification(
            [
                'en' => 'Your leave has been approved',
                'ar' => 'تم قبول طلب الإجازة',
            ],
            [
                'en' => "Your {$this->leaveRequest->type} request between dates $fromDate and $toDate has been approved.",
                'ar' => "طلب الاجازة السنوية الخاص بك من يوم $fromDate إلى $toDate  تم قبوله.",

            ],
            ['URL' => 'leaves', 'DATE' => $fromDate],
            $user->lang
        ));
    }
}
