<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdatePayslipStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payslip:update-status 
                            {--employee_ids=* : Employee IDs to update (comma-separated or multiple --employee_ids)}
                            {--payroll_id= : Payroll ID}
                            {--month= : Month (1-12)}
                            {--year= : Year}
                            {--status=downloaded : Status to set (pending, downloaded)}
                            {--all : Update all employees for the given payroll period}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update payslip status for selected employees';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // Get parameters
            $employeeIds = $this->option('employee_ids');
            $payrollId = $this->option('payroll_id');
            $month = $this->option('month');
            $year = $this->option('year');
            $status = $this->option('status');
            $updateAll = $this->option('all');

            // Validate status
            if (!in_array($status, ['pending', 'downloaded'])) {
                $this->error('Status must be either "pending" or "downloaded"');
                return 1;
            }

            // Validate required parameters
            if (!$payrollId || !$month || !$year) {
                $this->error('payroll_id, month, and year are required');
                $this->info('Usage: php artisan payslip:update-status --payroll_id=1 --month=12 --year=2024 --employee_ids=1,2,3');
                return 1;
            }

            // Process employee IDs
            $processedEmployeeIds = [];
            if (!$updateAll) {
                if (empty($employeeIds)) {
                    $this->error('Either provide --employee_ids or use --all flag');
                    return 1;
                }

                // Handle comma-separated IDs
                foreach ($employeeIds as $idString) {
                    $ids = explode(',', $idString);
                    foreach ($ids as $id) {
                        $id = trim($id);
                        if (is_numeric($id)) {
                            $processedEmployeeIds[] = (int) $id;
                        }
                    }
                }

                if (empty($processedEmployeeIds)) {
                    $this->error('No valid employee IDs provided');
                    return 1;
                }

                $processedEmployeeIds = array_unique($processedEmployeeIds);
            }

            // Show confirmation
            $this->info('Update Parameters:');
            $this->info("Payroll ID: {$payrollId}");
            $this->info("Month: {$month}");
            $this->info("Year: {$year}");
            $this->info("Status: {$status}");

            if ($updateAll) {
                $this->info("Target: ALL employees");
            } else {
                $this->info("Employee IDs: " . implode(', ', $processedEmployeeIds));
            }

            if (!$this->confirm('Do you want to proceed with this update?')) {
                $this->info('Operation cancelled');
                return 0;
            }

            // Build query
            $query = DB::table('employee_payroll_summaries')
                ->where('payroll_id', $payrollId)
                ->where('month', $month)
                ->where('year', $year);

            // Add employee filter if not updating all
            if (!$updateAll) {
                $query->whereIn('employee_id', $processedEmployeeIds);
            }

            // Show current records before update
            $currentRecords = $query->get(['employee_id', 'payslip_status']);

            if ($currentRecords->isEmpty()) {
                $this->error('No records found matching the criteria');
                return 1;
            }

            $this->info("\nFound {$currentRecords->count()} records:");
            $this->table(
                ['Employee ID', 'Current Status'],
                $currentRecords->map(function ($record) {
                    return [$record->employee_id, $record->payslip_status ?? 'null'];
                })->toArray()
            );

            // Perform update
            DB::beginTransaction();

            $updatedRows = $query->update([
                'payslip_status' => $status,
                'updated_at' => now()
            ]);

            DB::commit();

            // Show results
            $this->info("\n✅ Successfully updated {$updatedRows} records");

            // Show updated records
            $updatedRecords = DB::table('employee_payroll_summaries')
                ->where('payroll_id', $payrollId)
                ->where('month', $month)
                ->where('year', $year);

            if (!$updateAll) {
                $updatedRecords->whereIn('employee_id', $processedEmployeeIds);
            }

            $finalRecords = $updatedRecords->get(['employee_id', 'payslip_status']);

            $this->info("\nUpdated records:");
            $this->table(
                ['Employee ID', 'New Status'],
                $finalRecords->map(function ($record) {
                    return [$record->employee_id, $record->payslip_status];
                })->toArray()
            );

            // Log the operation
            Log::info('Payslip status updated via command', [
                'payroll_id' => $payrollId,
                'month' => $month,
                'year' => $year,
                'status' => $status,
                'employee_ids' => $updateAll ? 'all' : $processedEmployeeIds,
                'updated_rows' => $updatedRows,
                'command_user' => get_current_user()
            ]);

            return 0;

        } catch (\Exception $e) {
            DB::rollBack();

            $this->error('Error updating payslip status: ' . $e->getMessage());

            Log::error('Payslip status update command failed', [
                'error' => $e->getMessage(),
                'options' => $this->options()
            ]);

            return 1;
        }
    }
}