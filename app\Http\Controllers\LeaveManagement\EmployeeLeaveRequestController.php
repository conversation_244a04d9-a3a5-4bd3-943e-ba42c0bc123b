<?php

namespace App\Http\Controllers\LeaveManagement;

use App\DomainData\EmployeeLeaveRequestDto;
use App\Exports\EmployeeLeaveBalanceExport;
use App\Exports\LeaveExport;
use App\Http\Controllers\Controller;
use App\Services\LeaveManagement\BusinessServices\CancelLeavesService;
use App\Services\LeaveManagement\BusinessServices\GetBalancesFilterService;
use App\Services\LeaveManagement\BusinessServices\GetLeavesFiltersService;
use App\Services\LeaveManagement\BusinessServices\LeaveActionsService;
use App\Services\LeaveManagement\CrudServices\CompanyLeaveTypeCrudService;
use App\Services\LeaveManagement\CrudServices\CompanyLeaveTypePolicyCrudService;
use App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService;
use Illuminate\Validation\Rules\Exists;
use Maatwebsite\Excel\Facades\Excel;
use stdClass;

class EmployeeLeaveRequestController extends Controller
{
    use EmployeeLeaveRequestDto;

    public function __construct(private EmployeeLeaveRequestCrudService   $service,
                                private GetLeavesFiltersService           $getLeavesFiltersService,
                                private CompanyLeaveTypePolicyCrudService $companyLeaveTypePolicyCrudService,
                                private CancelLeavesService               $cancelLeavesService,
                                private LeaveActionsService               $leaveActionsService,
                                private CompanyLeaveTypeCrudService       $companyLeaveTypeCrudService,
                                private GetBalancesFilterService          $getBalancesFiltersService,

    )
    {
    }

    private function isValidFilters(array &$request, stdClass &$output): bool
    {
        $rules = $this->getLeavesFilterRules();

        if (!isset($request['page_size'])) {
            $request['page_size'] = config('globals.MAX_PAGE_SIZE');
        }

        if (!isset($request['order_by'])) {
            $request['order_by'] = 'created_at';
        }

        if (!isset($request['order_by_type'])) {
            $request['order_by_type'] = 'asc';
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return false;
        }

        $request = $validator->validate();

        return true;
    }

    public function getByFilters(array $request, stdClass &$output): void
    {
        if (!$this->isValidFilters($request, $output)) {
            return;
        }
        $this->getLeavesFiltersService->perform($request, $output);
        unset($output->leavesCollection);
    }

    public function exportLeaves(array $request, stdClass &$output): void
    {
        if (!$this->isValidFilters($request, $output)) {
            return;
        }
        $request['page_size'] = 0;
        $leaves = $this->getLeavesFiltersService->getLeaves($request);
        $output->return_link = Excel::download(new LeaveExport($leaves), 'leaves.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function exportLeavesBalances(array $request, stdClass &$output): void
    {
        if (!$this->isValidFilters($request, $output)) {
            return;
        }

        $request['page_size'] = 0;
        $request['with_restdays'] = (isset($request['with_restdays']) && $request['with_restdays']) ? true : false;

        $this->getBalancesFiltersService->perform($request, $output);
        $output->return_link = Excel::download(new EmployeeLeaveBalanceExport($output->balances->data), 'leaves.xlsx', \Maatwebsite\Excel\Excel::XLSX);

    }

    public function getLeavesForWorker(array $request, stdClass &$output): void
    {
        if (!$this->isValidFilters($request, $output)) {
            return;
        }
        $request['employee_id'] = config('globals.user')->employee_id;
        $request['with_restdays'] = false;

        $this->getLeavesFiltersService->perform($request, $output);
        unset($output->leavesCollection);
    }

    public function create(array $request, stdClass &$output): void
    {
        $rules = $this->getRules(['from', 'to', 'note']);
        $rules['attachments'] = 'array|max:5';
        $rules['attachments.*'] = 'mimes:pdf,doc,docx,png,jpg,jpeg';
        $rules['balance_id'] = ['required', 'integer'];
        $rules['partial_leave_type'] = ['nullable', 'string', 'in:before,after'];
        $rules['duration'] = ['nullable', 'integer', 'in:2,4'];

        if (isset($request['partial_leave_type'])) {
            $request['to'] = $request['from'];
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        if (!isset($request['employee_id'])) {
            $request['employee_id'] = config('globals.user')->employee_id;
        }

        if (!isset($request['type'])) {
            $request['type'] = config('globals.LEAVE_TYPES.REGULAR');
        }

        if ($request['type'] == config('globals.LEAVE_TYPES.REGULAR')) {
            $this->service->create($request, $output);
        }
    }

    public function update(array $request, stdClass &$output): void
    {
        $rules = $this->getRules(['from', 'to', 'note']);
        $rules['id'] = ['required', 'integer'];
        $rules['balance_id'] = ['required', 'integer'];
        $rules['attachments'] = 'array|max:5';
        $rules['attachments.*'] = 'mimes:pdf,doc,docx,png,jpg,jpeg';
        $rules['existing_attachment_ids'] = 'array';
        $rules['existing_attachment_ids.*'] = [
            'integer',
            (new Exists('attachments', 'id'))->where(function ($query) {
                $query
                    ->whereNull('deleted_at');
            }),
        ];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        $request['employee_id'] = config('globals.user')->employee_id;

        $this->service->update($request, $output);
    }

    public function cancel(array $request, stdClass &$output): void
    {
        $rules['id'] = ['required', 'integer'];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $request['action'] = 'cancel';

        $this->leaveActionsService->perform($request, $output);
    }

    public function getLeaveTypes(array $request, stdClass &$output): void
    {
        $request['company_id'] = config('globals.user')->company_id;
        $request['with_restdays'] = (isset($request['with_restdays']) && $request['with_restdays'] == 'true') ? true : false;
        $this->companyLeaveTypeCrudService->getLeaveTypes($request, $output);

    }

    public function approve(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|integer';
        $rules['check_warning'] = 'boolean';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (!isset($request['check_warning'])) {
            $request['check_warning'] = true;
        }

        $request['action'] = 'approve';

        $this->leaveActionsService->perform($request, $output);

        if (isset($request['check_warning']) && $request['check_warning'] && isset($output->Warning) && count($output->Warning)) {
            $output->no_need_commit = true;
            $output->request_body = $request;
        }
    }

    public function reject(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|integer';
        $rules['check_warning'] = 'boolean';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request['action'] = 'reject';

        $this->leaveActionsService->perform($request, $output);

    }

    public function getEditHistory(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|integer';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->getEditHistory($request, $output);

    }

    public function addRestDay(array $request, stdClass &$output): void
    {
        $rules['date'] = ['required', 'date'];
        $rules['employee_id'] = ['required', 'integer'];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->addRestDay($request, $output);

    }

    public function addManyRestDays(array $request, stdClass &$output): void
    {
        $validator = \Validator::make($request, [
            'entity_array' => 'required|array',
        ]);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        $rules['date'] = ['required', 'date'];
        $rules['employee_id'] = ['required', 'integer'];

        foreach ($request['entity_array'] as $key => $entity) {
            $validator = \Validator::make($entity, $rules);
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }
        }
        $this->service->addManyRestDays($request, $output);
    }

    public function deleteRestDays($request, $output): void
    {
        $rules['ids'] = ['required', 'array'];
        $rules['ids.*'] = ['required', 'integer'];
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->deleteRestDays($request, $output);
    }

    public function netQuantityInRange($request, $output): void
    {
        $rules = [
            'start_date' => 'required|date_format:Y-m-d',
            'end_date' => 'required|date_format:Y-m-d|after_or_equal:start_date',
            'employee_id' => 'integer',
        ];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->netQuantityInRange($request, $output);
    }

    public function addLeaveByManager($request, $output): void
    {
        $rules = ['employee_id' => 'required|integer'];
        $rules = ['check_warning' => 'boolean'];
        $rules['attachments'] = 'array|max:5';
        $rules['attachments.*'] = 'mimes:pdf,doc,docx,png,jpg,jpeg';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request['type'] = config('globals.LEAVE_TYPES.REGULAR');

        $output->addLeaveByManager = true;
        $this->service->create($request, $output);

        if (isset($output->Error) && count($output->Error)) {
            return;
        }

        $leave_id = $output->employee_leave_request->id;
        $request['id'] = $leave_id;
        $request['check_warning'] = false;

        // $this->approve($request, $output);

        if (isset($output->Error) && count($output->Error)) {
            return;
        }

        // dispatch(new CreateLeaveRequestPushNotificationJob($output->employeeLeaveRequest))->afterCommit();
    }
}
