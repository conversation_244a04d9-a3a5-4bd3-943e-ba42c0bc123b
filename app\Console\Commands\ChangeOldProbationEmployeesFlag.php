<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Models\Title;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ChangeOldProbationEmployeesFlag extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:change-old-probation-employees-flag';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $employees = Employee::where('on_probation', true)
            ->where('created_at', '>', '2024-01-02')
            ->whereHas('title', function ($query) {
                $query->where('probation_period', '>', 0);
            })
            ->with(['title', 'employeeInfo'])
            ->get();

        $titles = Title::get();

        foreach ($employees as $employee) {
            $joinDate = $employee->employeeInfo->join_date ?? null;
            $endProbationDate = Carbon::parse($joinDate)
                ->addMonths($employee->title->probation_period)
                ->startOfDay();

            if ($employee->title->probation_period > 0 && isset($endProbationDate) && $endProbationDate < date('Y-m-d') && $employee->on_probation) {
                $employee->on_probation = false;
                $employee->save();
            }
        }

        foreach ($titles as $title) {
            $title->probation_period = 3;
            $title->save();
        }
    }
}
