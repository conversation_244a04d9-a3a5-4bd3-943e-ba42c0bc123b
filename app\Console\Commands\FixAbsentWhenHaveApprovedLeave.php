<?php

namespace App\Console\Commands;

use App\Enums\EntityTags\AttendanceTags;
use App\Models\EmployeeLeaveRequest;
use App\Models\Timecard;
use App\Services\LeaveManagement\FixBalancesService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixAbsentWhenHaveApprovedLeave extends Command
{
    public function __construct(private FixBalancesService $service)
    {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:absents-by-leaves {date} {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove absent timecards when there is an approved leave covering the absence.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $fromDate = $this->argument('date');

        $this->info("Starting process to fix absents from date: {$fromDate}");

        $absentTimecards = Timecard::whereDate('from', '>=', $fromDate)
            ->whereHas('entityTags', function ($q) {
                $q->where('tag', AttendanceTags::ABSENT)
                    ->orWhere('tag', AttendanceTags::ABSENT_WITHOUT_PERMISSION);
            })
            ->with('EntityTags', function ($q) {
                $q->where('tag', AttendanceTags::ABSENT)
                    ->orWhere('tag', AttendanceTags::ABSENT_WITHOUT_PERMISSION);
            })
            ->get();

        $totalTimecards = $absentTimecards->count();

        if ($totalTimecards === 0) {
            $this->warn('No absent timecards found from the specified date.');

            return;
        }

        $this->info("Found {$totalTimecards} absent timecards. Checking each for approved leave...");

        $deletedCount = 0;

        DB::beginTransaction();

        try {
            foreach ($absentTimecards as $timecard) {
                $approvedLeave = EmployeeLeaveRequest::where('employee_id', $timecard->employee_id)
                    ->where('status', 'approved')
                    ->where('from', '<=', $timecard->from)
                    ->where('to', '>=', $timecard->to)
                    ->first();

                if (! empty($approvedLeave)) {
                    if ($this->option('force') || $this->confirm("Delete absent timecard ID {$timecard->id} for employee ID {$timecard->employee_id}?")) {
                        $timecard->delete();
                        $deletedCount++;
                        $this->info("Deleted timecard ID {$timecard->id}.");
                    } else {
                        $this->warn("Skipped deletion for timecard ID {$timecard->id}.");
                    }
                } else {
                    $this->line("No approved leave found for timecard ID {$timecard->id}.");
                }

            }

            DB::commit();

            $this->info("Process completed. Deleted {$deletedCount} absent timecard(s) out of {$totalTimecards} scanned.");
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->error('An error occurred: '.$e->getMessage());
        }
    }
}
