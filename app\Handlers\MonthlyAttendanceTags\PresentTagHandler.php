<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Models\Timecard;
use Illuminate\Support\Carbon;

class PresentTagH<PERSON>ler implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        return [
            'name' => $employeeAttendance->name ?? $employeeAttendance->shift?->name ?? $employeeAttendance->timecardType->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($employeeAttendance->from)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        if (isset($tags[$employeeId]['tags']['present'])) {
            $attendanceRecords = $tags[$employeeId]['tags']['present']['data'];
            $dates = array_column($attendanceRecords, 'date');
            $uniqueDates = array_unique($dates);

            return count($uniqueDates);
        } else {
            return 1;
        }

    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return $employeeAttendance instanceof Timecard && ! is_null($employeeAttendance->attendance);
    }

    public function handleTagUnit(): string
    {
        return 'days';
    }
}
