<?php

namespace App\DomainData;

trait RegisterationValidationDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'name' => 'required|string',
            'phone' => 'required|string',
            'verification_code' => 'required|string',
            'is_verified_phone' => 'boolean',
            'is_verified_email' => 'boolean',
            'expire_date' => 'date',
            'account_verified_at' => 'date',
            'user_id' => 'required|numeric',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeRegisterationValidationDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
