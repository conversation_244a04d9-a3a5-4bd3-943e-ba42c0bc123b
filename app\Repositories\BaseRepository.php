<?php

namespace App\Repositories;

use App\Exceptions\InvalidParameter;
use App\Filters\FiltersCriteria\FiltersCriteria;
use Closure;
use Exception;
use Illuminate\Database\ConnectionInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

abstract class BaseRepository
{
    protected null|Builder|Model $model = null;

    /**
     * BaseRepository constructor.
     */
    public function __construct()
    {
        $this->makeModel();
    }

    abstract public function model(): string;

    public function builder(): \Illuminate\Database\Query\Builder
    {
        return DB::table($this->model->getTable());
    }

    /**
     * @return mixed
     */
    public function add(array $data)
    {
        return $this->model->create($data);
    }

    /**
     * @return mixed
     */
    public function insert(array $data)
    {
        return $this->model->insert($data);
    }

    /**
     * Upsert a record based on unique constraints.
     *
     * @param  array  $updateColumns
     * @return mixed
     */
    public function upsert(array $data, array $uniqueKeys)
    {
        return $this->model->updateOrInsert($uniqueKeys, $data);
    }

    /**
     * @return mixed
     */
    public function insertGetId(array $data)
    {
        return $this->model->insertGetId($data);
    }

    /**
     * @return mixed
     */
    public function insertOrIgnore(array $data)
    {
        return $this->model->insertOrIgnore($data);
    }

    /**
     * @return mixed
     */
    public function update($id, array $attributes)
    {
        // $attributes = array_filter($attributes); #TODO validate why this method were called here
        return $this->updateWithoutFilter($id, $attributes);
    }

    /**
     * @return mixed
     */
    public function updateWithoutFilter($id, array $attributes)
    {
        $model = $this->model->find($id);
        if ($model) {
            $model->fill($attributes);
            $model->save();

            return $model;
        }

        return false;
    }

    /**
     * @return bool|int
     */
    public function multipleUpdate(array $IDs, array $attributes)
    {
        return $this->model->whereIn($this->getPrimaryKeyName(), $IDs)->update($attributes);
    }

    /**
     * @return mixed
     */
    public function find($id)
    {
        return $this->model->find($id);
    }

    /**
     * @return mixed
     */
    public function findOrFail($id)
    {
        return $this->model->findOrFail($id);
    }

    /**
     * @return mixed
     */
    public function get()
    {
        return $this->model->get();
    }

    /**
     * @return mixed
     */
    public function findWithTrashed($id)
    {
        if (in_array(SoftDeletes::class, class_uses($this->model))) {
            return $this->model->withTrashed()->find($id);
        }

        return $this->find($id);
    }

    /**
     * @return bool|null
     *
     * @throws Exception
     */
    public function delete(array $ids)
    {
        return $this->model->whereIn($this->getPrimaryKeyName(), $ids)->delete();
    }

    public function deleteByKeys(array $data)
    {
        $query = $this->model;
        foreach ($data as $key => $value) {
            $query = $query->where($key, $value);
        }

        return $query->delete();
    }

    /**
     * @return Model|null
     */
    protected function makeModel()
    {
        if (is_null($this->model)) {
            $this->model = app($this->model());
        }

        return $this->model;
    }

    public function firstOrCreate(array $search, array $create = []): object
    {
        return $this->model->firstOrCreate($search, $create);
    }

    /**
     * @return mixed
     */
    public function updateOrCreate(array $attributes, array $values = [])
    {
        return $this->model->updateOrCreate($attributes, $values);
    }

    public function updateOrCreateMany(array $attributes, array $values = []): array
    {
        $rows = [];
        foreach ($attributes as $attribute) {
            $rows[] = $this->model->updateOrCreate([$attribute], $values);
        }

        return $rows;
    }

    public function createMany(array $records): array
    {
        $rows = [];
        foreach ($records as $singleRecord) {
            $rows[] = $this->add($singleRecord);
        }

        return $rows;
    }



    public function findByIds(array $ids, ?Closure $queryCallBack = null): Collection
    {
        $query = $this->model->newQuery()
            ->whereIn($this->getPrimaryKeyName(), $ids);

        if (is_callable($queryCallBack)) {
            call_user_func($queryCallBack, $query);
        }

        return $query->get();
    }

    public function findByIdsNotIn(array $ids, ?Closure $queryCallBack = null): Collection
    {
        $query = $this->model->newQuery()
            ->whereNotIn($this->getPrimaryKeyName(), $ids);

        if (is_callable($queryCallBack)) {
            call_user_func($queryCallBack, $query);
        }

        return $query->get();
    }

    /**
     * @return void
     */
    public function allowTrashed(bool $allowTrashed)
    {
        $this->model = $this->model->withTrashed($allowTrashed);
    }

    /**
     * @return Builder
     */
    public function withoutAllRelations()
    {
        return $this->model = $this->model->withOnly([]);
    }

    /**
     * @return Builder|Model|object|null
     */
    public function getFirstByAttributes(array $attributes)
    {
        return $this->model->where($attributes)->first();
    }

    protected function getPrimaryKeyName(): string
    {
        $key = 'id';
        try {
            $key = $this->model->getKeyName();
        } catch (Exception $e) {
            //
        }

        return $key;
    }

    public function getDBConnection(?string $connection = null): ConnectionInterface
    {
        return DB::connection($connection ?? config('database.default'));
    }

    /**
     * @throws InvalidParameter
     */
    public function applyFiltersCriteriaOnBuilder(Builder|\Illuminate\Database\Query\Builder $builder, FiltersCriteria $filtersCriteria): Builder|\Illuminate\Database\Query\Builder
    {
        $this->validateBuilderInstance($builder);

        $aggregationConditions = [];
        foreach ($filtersCriteria->getConditions() as $condition) {
            if ($condition->isAggregation()) {
                $aggregationConditions[] = $condition;
            }
            $builder = $builder->where(DB::raw($condition->getField()), $condition->getOperation(), $condition->getValue());
        }

        $aggregationInConditions = [];
        foreach ($filtersCriteria->getInConditions() as $inCondition) {
            if ($inCondition->isAggregation()) {
                $aggregationInConditions[] = $inCondition;
            }
            $builder = $builder->whereIn($inCondition->getField(), $inCondition->getValues());
        }

        foreach ($filtersCriteria->getRawConditions() as $rawCondition) {
            if ($rawCondition->isAggregation()) {
                $builder = $builder->havingRaw($rawCondition->getExpression(), $rawCondition->getBinding());
            } else {
                $builder = $builder->whereRaw($rawCondition->getExpression(), $rawCondition->getBinding());
            }
        }

        if ($aggregationQueryString = $this->handleAggregationConditions($aggregationConditions, $aggregationInConditions)) {
            $builder->havingRaw($aggregationQueryString);
        }

        $this->applyFiltersCriteriaSortsOnBuilder($builder, $filtersCriteria);

        return $builder;
    }

    /**
     * @throws InvalidParameter
     */
    protected function applyFiltersCriteriaSortsOnBuilder(Builder|\Illuminate\Database\Query\Builder $builder, FiltersCriteria $filtersCriteria): Builder|\Illuminate\Database\Query\Builder
    {
        $this->validateBuilderInstance($builder);
        foreach ($filtersCriteria->getSorts() as $sort) {
            if (! empty($filtersCriteria->getSortableFields()) && ! in_array($sort->getField(), $filtersCriteria->getSortableFields())) {
                continue;
            }
            $builder->orderBy($sort->getField(), strtolower($sort->getDirection()));
        }

        return $builder;
    }

    private function handleAggregationConditions(array $conditions, array $inConditions): string
    {
        if (empty($conditions) && empty($orConditions) && empty($inConditions)) {
            return '';
        }
        $andQueryString = $this->buildAggregationQueryString($conditions);
        $inConditionsQueryString = $this->buildAggregationQueryStringForInConditions($inConditions);
        $conditionsArray = [$andQueryString, $inConditionsQueryString];
        $result = '';
        foreach ($conditionsArray as $string) {
            if ($string) {
                $result .= "$string and  ";
            }
        }

        return str_replace_latest('and', '', $result);
    }

    private function buildAggregationQueryString(array $conditions, bool $isOr = false): string
    {
        if (count($conditions) == 0) {
            return '';
        }
        $queryString = $isOr ? '(' : '';
        foreach ($conditions as $condition) {
            $field = $condition->getField();
            $operation = $condition->getOperation();
            $value = $condition->getValue();
            $logicalExpression = $isOr ? ' or ' : ' and ';
            if (is_numeric($value)) {
                $queryString .= "$field $operation $value $logicalExpression ";
            } else {
                $value = preg_replace('/[\@\.\;\\\"]+/', '', $value);
                $queryString .= "$field $operation \"$value\" $logicalExpression ";
            }
        }
        $queryString = str_replace_latest($isOr ? 'or ' : 'and ', '', $queryString);

        if ($isOr) {
            $queryString .= ')';
        }

        return $queryString;
    }

    private function buildAggregationQueryStringForInConditions(array $inConditions): string
    {
        if (empty($inConditions)) {
            return '';
        }

        $queryString = '';
        foreach ($inConditions as $inCondition) {
            $logicalExpression = ' and ';
            $field = $inCondition->getField();
            $values = [];
            foreach ($inCondition->getValues() as $value) {
                $values[] = "'$value'";
            }
            $values = implode(',', $values);
            $operation = ' in ';
            $queryString .= "$field $operation ($values) $logicalExpression";
        }

        return str_replace_latest('and', '', $queryString);
    }

    /**
     * @throws InvalidParameter
     */
    private function validateBuilderInstance($builder): void
    {
        if (! $builder instanceof Builder && ! $builder instanceof \Illuminate\Database\Query\Builder && ! $builder instanceof Relation) {
            throw new InvalidParameter('Invalid Builder Passed');
        }
    }

    public function all()
    {
        return $this->model->all();
    }

    public function findByKey($key, $value, $relations = [])
    {
        return $this->model->with($relations)->where($key, $value)->get();
    }

    public function getWithRelations($relations = [])
    {
        return $this->model->with($relations)->get();
    }

    public function findByKeys(array $attributes)
    {
        return $this->model->where($attributes)->get();
    }

    public function findFirstByKeys(array $attributes, $selectedColumns = null)
    {
        return $this->model
            ->when(isset($selectedColumns), function ($q) use ($selectedColumns) {
                $q->select($selectedColumns);
            })
            ->where($attributes)->first();
    }

    public function selectColumns(array $selectedColumns)
    {
        return $this->model
            ->select($selectedColumns)
            ->get();
    }

    public function findFirstByKey($key, $value, $relations = [])
    {
        return $this->model->with($relations)->where($key, $value)->first();
    }

    public function createOrUpdate(array $attributes, array $values)
    {
        return $this->model->updateOrCreate($attributes, $values);
    }
}
