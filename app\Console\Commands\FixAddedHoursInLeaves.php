<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\Employee;
use Illuminate\Console\Command;

class FixAddedHoursInLeaves extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-added-hours-in-leaves {company_id : The ID of the company}     {--year= : Calendar year to recalculate (YYYY, defaults to current)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $year = (int) ($this->option('year') ?: now()->year);
        $company = Company::find($this->argument('company_id'));

        $employees = Employee::where('company_id', $company->id)->whereHas('employeeInfo', function ($query) {
            $query->where('number_of_years_of_experience', '>', 10)
                ->orWhere('birth_date', '<', now()->subYears(50));
        })
            ->with('company', 'employeeLeaveBalances')
            ->get();

        foreach ($employees as $employee) {
            $companyAnnualLeaveId = $employee->company->annual_leave_id;

            $employeeHasAddedHoursForAnnualLeave = $employee->employeeLeaveBalances()
                ->where('company_leave_type_id', $companyAnnualLeaveId)
                ->whereYear('end', $year)
                ->where('added_hours', '>', 0)
                ->exists();

            if (! $employeeHasAddedHoursForAnnualLeave) {
                $employeeLeaveBalance = $employee->employeeLeaveBalances()
                    ->where('company_leave_type_id', $companyAnnualLeaveId)
                    ->whereYear('end', $year)
                    ->where('added_hours', '=', 0)
                    ->first();

                if ($employeeLeaveBalance) {
                    $employeeLeaveBalance->update([
                        'added_hours' => 9 * 8,
                    ]);
                    $employeeLeaveBalance->save();
                    $this->info("Added hours for employee {$employee->id} have been fixed.");

                }
            }
        }

    }
}
