<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Employee;
use App\Traits\GenerateUuid;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixTerminationDates extends Command
{
    use GenerateUuid;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:termination_dates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $employeesWithTerminationDate = Employee::with('employeeInfo')->whereHas('employeeInfo', function ($query) {
                $query->whereNotNull('termination_date');
            })->get();
            // deduct one day from the termination date
            foreach ($employeesWithTerminationDate as $employee) {
                $employee->employeeInfo->termination_date = date('Y-m-d', strtotime($employee->employeeInfo->termination_date.' -1 day'));
                $employee->employeeInfo->save();
            }
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
