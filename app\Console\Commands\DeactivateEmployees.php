<?php

namespace App\Console\Commands;

use App\Models\Employee;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeactivateEmployees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deactivate:employees';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'one time script to add revoke date to deactivated employees';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        DB::beginTransaction();
        try {
            $employees = Employee::where('status', 'deactivated')->with('employeeInfo')->get();

            foreach ($employees as $employee) {
                $employee->employeeInfo->termination_date = '2023-10-30';
                $employee->employeeInfo->termination_date = '2023-01-01';
                $employee->employeeInfo->save();

                $employee->revoke_date = '2023-01-01';
                $employee->save();
            }
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }

    }
}
