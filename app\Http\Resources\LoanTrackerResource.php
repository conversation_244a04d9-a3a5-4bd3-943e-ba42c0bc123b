<?php

namespace App\Http\Resources;

use App\Http\Resources\V1\EmployeeFiles\AttachmentCollection;
use App\Http\Resources\V1\WorkFlows\ApprovalCycleCollection;
use App\Traits\QueriesHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LoanTrackerResource extends JsonResource
{
    use QueriesHelper;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $lang = (config('globals.lang') ?? 'ar');

        return [
            'id' => $this->id,
            'status' => $this->status,
            'loan_amount' => $this->amount,
            'request_date' => \Carbon\Carbon::parse($this->created_at)->format('Y-m-d'),
            'paid_amount' => $this->paid_amount,
            'remaining_amount' => $this->remaining_amount,
            'payout_date' => $this->disbursement_date,
            'installment_count' => $this->installment_count,
            'remaining_installments_count' => $this->remaining_installments_count,
            'remaining_installments' => InstallmentResource::collection(collect($this->remaining_installments)),
            'paid_installments_count' => $this->paid_installments_count,
            'paid_installments' => InstallmentResource::collection(collect($this->paid_installments)),
            'can_disburse' => $this->disbursable ?? false,
            'can_edit' => $this->editable ?? false,
            'can_cancel' => $this->can_cancel ?? false,
            'actionable' => $this->actionable ?? false,
            'comment' => $this->comment,
            'final_payment_date' => $this->last_installment_date,
            'installments' => InstallmentResource::collection($this->whenLoaded('installments')),
            'employee' => [
                'id' => $this->employee->id,
                'status' => $this->employee->status,
                'profile_image' => $this->employee->profilePicture->attachment_url ?? null,
                'name' => $this->employee->name,
                'employee_number' => $this->employee->employee_number,
                'first_name' => $this->employee->first_name,
                'second_name' => $this->employee->second_name,
                'first_name_ar' => $this->employee->first_name_ar,
                'second_name_ar' => $this->employee->second_name_ar,
                'name_ar' => $this->employee->name_ar,
                'title' => [
                    'id' => $this->employee->title->id ?? null,
                    //                    'name' => $this->employee->title->name ?? null,
                    //                    'name_ar' => $this->employee->title->name_ar ?? null,
                    'name' => $lang == 'ar' ? $this->employee->title->name_ar : $this->employee->title->name_en,
                    'color' => $this->employee->title->color ?? null,
                ],
                'branch' => [
                    'id' => $this->employee->branch->id ?? null,
                    //                    'name' => $this->employee->branch->name ?? null,
                    //                    'name_ar' => $this->employee->branch->name_ar ?? null,
                    'name' => $lang == 'ar' ? $this->employee->branch->name_ar : $this->employee->branch->name_en,
                ],
                'employee_info' => [
                    'termination_date' => $this->employee->employeeInfo->termination_date ?? null,
                ],
            ],
            'approval_cycle' => new ApprovalCycleCollection($this->getApprovalCycle($this, $this->employee)),
            'attachments' => $this?->attachments ? new AttachmentCollection($this->attachments) : [],

        ];
    }
}
