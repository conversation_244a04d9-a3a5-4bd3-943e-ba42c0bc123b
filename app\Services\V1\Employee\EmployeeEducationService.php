<?php

namespace App\Services\V1\Employee;

use App\Exceptions\UnprocessableException;
use App\Repositories\V1\Employee\EmployeeEducationRepository;
use App\Services\BaseService;

class EmployeeEducationService extends BaseService
{

    public function __construct(EmployeeEducationRepository $repository)
    {
        $this->repository = $repository;
    }

    public function getByEmployeeId(int $employeeId)
    {
        return $this->repository->getByEmployeeId($employeeId);
    }
}