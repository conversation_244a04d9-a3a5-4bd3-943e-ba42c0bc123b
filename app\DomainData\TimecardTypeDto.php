<?php

namespace App\DomainData;

trait TimecardTypeDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'name' => 'required|string',
            'is_default' => 'required|bool',
            'company_id' => 'integer',
        ];
        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeTimecardTypeDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
