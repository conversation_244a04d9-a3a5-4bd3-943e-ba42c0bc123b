<?php

namespace App\DomainData;

trait OvertimeMonthGroupTitleDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'title_id' => 'numeric',
            'overtime_month_group_id' => 'numeric',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeOvertimeMonthGroupTitleDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
