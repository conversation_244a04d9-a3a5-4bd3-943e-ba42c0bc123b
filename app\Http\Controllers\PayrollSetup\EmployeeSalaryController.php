<?php

namespace App\Http\Controllers\PayrollSetup;

use App\DomainData\EmployeeSalaryDto;
use App\DomainData\FilterDto;
use App\Http\Controllers\Controller;
use App\Services\PayrollSetup\BusinessServices\EmployeeSalaryCsvService;
use App\Services\PayrollSetup\CrudServices\EmployeeSalaryCrudService;

class EmployeeSalaryController extends Controller
{
    use EmployeeSalaryDto, FilterDto;

    public function __construct(
        private EmployeeSalaryCrudService $employeeSalaryCrudService,
        private EmployeeSalaryCsvService $employeeSalaryCsvService
    ) {}

    public function create(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules([]);

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $this->employeeSalaryCrudService->create($request, $output);
    }

    public function update(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules([]);
        $rules['id'] = 'required|numeric';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $this->employeeSalaryCrudService->update($request, $output);
    }

    public function getById(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules(['related_objects.*', 'related_objects_count.*']);
        $rules['id'] = 'required|numeric';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['related_objects'])) {
            $request['related_objects'] = [];
        }
        if (! isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $this->employeeSalaryCrudService->getById($request, $output);
    }

    public function employeeSalaryCsvDownload(array $request, \stdClass &$output): void
    {
        // $this->employeeSalaryCsvService->download($output);
    }

    public function employeeSalaryCsvUpload(array $request, \stdClass &$output): void
    {
        $rules['csv_file'] = 'required|mimes:xlsx,txt';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $fileSize = request()->file('csv_file')->getSize() / (1024);

        if ($fileSize > config('globals.MAX_EXCEL_FILE_SIZE_KB')) {
            $output->Error = ['File size is too large, max file size is 15M', 'حجم الملف كبير جدًا ، الحد الأقصى لحجم الملف هو 15 ميجا بايت'];

            return;
        }

        //  $this->employeeSalaryCsvService->perform($request , $output);
    }
}
