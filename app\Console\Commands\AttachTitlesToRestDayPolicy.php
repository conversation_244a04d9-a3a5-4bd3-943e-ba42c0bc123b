<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\Title;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AttachTitlesToRestDayPolicy extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:attach-titles-to-rest-day-policy';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Attach all Dyafa company titles to the Rest Day leave policy if not already attached';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companyId = 271;

        // Fetch titles that aren't yet attached to any leave policy
        $titles = Title::where('company_id', $companyId)
            ->doesntHave('companyLeaveTypePolicies')
            ->get();

        $count = $titles->count();
        if ($count === 0) {
            $this->info("✅ No titles found for Company ID {$companyId} that need attaching.");
            return 0;
        }

        $this->info("🔍 Found {$count} titles for Company ID {$companyId} to attach to Rest Day policy.");

        // Load the Rest Day leave-type policy
        $restDayType = Company::find($companyId)->restDayLeaveType;
        if (!$restDayType || !$restDayType->companyLeaveTypePolicy) {
            $this->error("🚨 Rest Day leave policy not defined for Company ID {$companyId}.");
            return 1;
        }

        $policy = $restDayType->companyLeaveTypePolicy;

        DB::beginTransaction();

        try {
            foreach ($titles as $title) {
                $policy->titles()->syncWithoutDetaching([$title->id]);
                $this->info("   • Attached Title ID {$title->id} (“{$title->name_en}”).");
            }

            DB::commit();
            $this->info('🎉 All titles have been attached to the Rest Day policy successfully.');
            return 0;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->error("🚨 Failed to attach titles: {$e->getMessage()}");
            return 1;
        }
    }
}
