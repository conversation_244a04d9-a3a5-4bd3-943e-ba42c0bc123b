<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Models\Timecard;
use Illuminate\Support\Carbon;

class NoClockOutTagHandler implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        $tag = $employeeAttendance->attendance->entityTags->where('tag', 'no_clock_out')->first();

        return [
            'name' => $employeeAttendance->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($tag->created_at)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        return isset($tags[$employeeId]['tags']['no_clockout']) ? $tags[$employeeId]['tags']['no_clockout']['count'] + 1 : 1;
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return $employeeAttendance instanceof Timecard &&
            $employeeAttendance->attendance &&
            $employeeAttendance->attendance->entityTags &&
            $employeeAttendance->attendance->entityTags->pluck('tag')->contains('no_clock_out');
    }

    public function handleTagUnit(): string
    {
        return 'incidents';
    }
}
