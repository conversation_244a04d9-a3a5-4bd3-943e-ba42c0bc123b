<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Timecard;
use App\Repositories\Repository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RemoveDuplicateTimecardsWithoutAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:duplicate:tc_without_attendance';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $timecardRepository = Repository::getRepository('Timecard');
            $count = 0;
            $timecardIds = [];

            $timecards = Timecard::whereDoesntHave('attendance')->with('employee')->get();
            ////dd($timecards->pluck('id'));
            foreach ($timecards as $timecard) {
                if ($timecardRepository->checkOverlappingTimeCardsInUpdate($timecard, $timecard->from, $timecard->to)) {

                    if ($timecard->branch_id != $timecard->employee->branch_id) {

                        $timecardIds[] = $timecard->id;
                        $count + 1;
                        $timecard->delete();
                    }
                }
            }
            ////dd($timecardIds);
            echo 'Total timecards deleted: '.$count;
            // Log::info('Total timecards deleted ids: ' . $timecardIds);

            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::error($e);
            DB::rollBack();
        }
    }
}
