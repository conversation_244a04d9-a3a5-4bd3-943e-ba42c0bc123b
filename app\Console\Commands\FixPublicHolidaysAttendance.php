<?php

namespace App\Console\Commands;

use App\Enums\EntityTags\AttendanceTags;
use App\Enums\V2\WorkTypes\RestDaysTypesEnum;
use App\FeatureToggles\Unleash;
use App\Models\PublicHoliday;
use App\Models\Timecard;
use App\Util\UserWorkTypesUtil;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixPublicHolidaysAttendance extends Command
{
    protected $signature = 'attendance:fix-public-holidays';

    protected $description = 'Removes absences during public holidays for employees with fixed onsite work type';

    public function handle(): void
    {
        $publicHolidays = PublicHoliday::whereYear('start', '>=', 2025)->get();
        $this->info('[Init] Starting to process public holidays. Found '.count($publicHolidays).' holidays.');

        DB::beginTransaction();
        try {
            $totalDeleted = 0;

            foreach ($publicHolidays as $holiday) {
                $this->line("[Processing] Holiday: {$holiday->name_en} ({$holiday->start} to {$holiday->end})");

                $timecards = Timecard::whereDate('from', '>=', $holiday->start)
                    ->whereDate('from', '<=', $holiday->end)
                    ->whereHas('entityTags', function ($q) {
                        $q->where('tag', AttendanceTags::ABSENT)
                            ->orWhere('tag', AttendanceTags::ABSENT_WITHOUT_PERMISSION);
                    })
                    ->with(['entityTags', 'employee.title.workTypePolicy'])
                    ->get();

                $this->info("[Progress] Found {$timecards->count()} timecards with absences for {$holiday->name_en}");

                $deletedCount = 0;
                foreach ($timecards as $timecard) {
                   // $this->info("[info] Progress timecard ID: {$timecard->id} (Employee: {$timecard->employee_id})");


                    if ($this->doesEmployeeHaveFixedRestdays($timecard->employee)) {
                        Log::info("Deleted timecard ID {$timecard->id} during {$holiday->name_en}");
                        $this->info("[Action] Deleted timecard ID: {$timecard->id} (Employee: {$timecard->employee_id})");
                        $timecard->delete();
                        $deletedCount++;

                    }
                }

                $this->info("[Summary] Holiday {$holiday->name_en}: {$deletedCount} timecards deleted");
                $totalDeleted += $deletedCount;
            }

            DB::commit();
            $this->info("[Final] Completed successfully. Total timecards deleted: {$totalDeleted}");

        } catch (Exception $e) {
            DB::rollBack();
            Log::error("Error in attendance:fix-public-holidays: {$e}");
            $this->error("[Error] Operation failed: {$e->getMessage()}");
        }
    }

    private function doesEmployeeHaveFixedRestdays($employee): bool
    {
        $workTypePolicy = $employee?->title?->workTypePolicy;
        $unleash = app(Unleash::class);

        return $unleash->getNewWorkTypesFeatureFlag($employee?->company_id)

            ? $workTypePolicy->rest_days_type === RestDaysTypesEnum::FIXED->value
            : $workTypePolicy->work_days_type !== UserWorkTypesUtil::DYNAMIC_ON_SITE;
    }
}
