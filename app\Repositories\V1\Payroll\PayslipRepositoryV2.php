<?php

namespace App\Repositories\V1\Payroll;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * @author: @ahmed-zaki
 * @date: 11/06/2025
 * @description: This repository is used to get the payslips for the employees
 * 
 */

class PayslipRepositoryV2
{
    public function getPayslips(array $data)
    {
        try {
            $query = DB::table('employee_salary_component_month as escm')
                ->join('employee_payroll_summaries as eps', function ($join) {
                    $join->on('escm.payroll_id', '=', 'eps.payroll_id')
                        ->on('escm.employee_id', '=', 'eps.employee_id');
                })
                ->join('employees as e', 'escm.employee_id', '=', 'e.id')
                ->join('titles as t', 'e.title_id', '=', 't.id')
                ->join('salary_components as sc', 'escm.salary_component_id', '=', 'sc.id')
                ->join('salary_components_categories as scc', 'sc.salary_components_category_id', '=', 'scc.id')
                ->where('escm.payroll_id', $data['payroll_id'])
                ->where('escm.company_id', $data['company_id'])
                ->where('eps.month', $data['month'])
                ->where('eps.year', $data['year'])
                ->select([
                    'e.id as employee_id',
                    'e.name as employee_name',
                    'e.employee_number as code',
                    't.name as title',
                    'scc.id as category_id',
                    'scc.name as category_name',
                    'scc.is_addition as category_is_addition',
                    'sc.name as component_name',
                    'sc.id as component_id',
                    'escm.amount',
                    'eps.final_net_salary as net_salary',
                    'eps.tax_amount as tax_amount',
                    'eps.insurance_amount as insurance_amount',
                ]);

            return $query->orderBy('employee_id')->limit(10)->get();

        } catch (\Exception $e) {
            Log::error('Error in getPayslips: ' . $e->getMessage());
            throw new \Exception('Failed to retrieve payslip data: ' . $e->getMessage());
        }
    }
}
