<?php

namespace App\Console\Commands;

use App\Enums\EntityTags\AttendanceTags;
use App\Models\ExtraWorkDayRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RemoveExtraWorkdayRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-extra-workday-requests {company_id} {from_date} {to_date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove one invalid extra workday tag from attendances';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $companyId = $this->argument('company_id');
            $fromDate = $this->argument('from_date');
            $toDate = $this->argument('to_date');

            $this->info('Starting rejecting extra workday requests...');

            $extraWorkDayRequests = ExtraWorkDayRequest::whereDate('extra_work_day_date', '>=', $fromDate)
                ->whereDate('extra_work_day_date', '<=', $toDate)
                ->whereHas('employee', function ($query) use ($companyId) {
                    $query->where('company_id', $companyId);
                })->with('attendance.entityTags', 'employeeRequest')->get();

            $this->info('Extra workday requests count: '.$extraWorkDayRequests->count());

            foreach ($extraWorkDayRequests as $extraWorkDayRequest) {
                $attendance = $extraWorkDayRequest->attendance;
                if ($attendance) {
                    foreach ($attendance->entityTags as $entityTag) {
                        if ($entityTag->tag == AttendanceTags::EXTRA_WORKDAY->value) {
                            $entityTag->delete();
                            break;
                        }
                    }
                }
                $extraWorkDayRequest->update([
                    'status' => 'rejected',
                ]);
                $extraWorkDayRequest->employeeRequest->update([
                    'status' => 'rejected',
                ]);
            }

            $this->info('Rejected extra workday requests   completed.');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
        }
    }
}
