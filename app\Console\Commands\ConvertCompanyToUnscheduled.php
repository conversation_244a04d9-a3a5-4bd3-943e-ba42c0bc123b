<?php

namespace App\Console\Commands;

use App\Models\Cico;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ConvertCompanyToUnscheduled extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:convert-company-to-unscheduled {company_id} {date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $companyId = $this->argument('company_id');
            $date = $this->argument('date');
            Cico::whereHas('employee', function ($query) use ($companyId) {
                $query->where('company_id', $companyId);
            })
                ->whereDate('date', '<', $date)
                ->where('status', 'unverified')
                ->update(['status' => 'rejected']);

            Cico::whereHas('employee', function ($query) use ($companyId) {
                $query->where('company_id', $companyId);
            })
                ->whereDate('date', '>=', $date)
                ->where('status', 'unverified')
                ->update(['is_unscheduled' => 1]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Company not found');
        }
    }
}
