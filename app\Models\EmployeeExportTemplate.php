<?php

namespace App\Models;

use App\Models\Employee;
use App\Traits\CompanyRule;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmployeeExportTemplate extends BaseModel
{
    use CompanyRule, HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'is_default',
        'fields',
        'created_by',
        'company_id',
    ];

    protected $casts = [
        'fields' => 'array',
        'is_default' => 'boolean',
    ];

    public function creator()
    {
        return $this->belongsTo(Employee::class, 'created_by');
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
