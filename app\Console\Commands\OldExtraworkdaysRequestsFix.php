<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use App\Models\EmployeeRequest;
use App\Models\EntityTag;
use App\Models\ExtraWorkdayRequest;
use App\Models\Title;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Traits\WorkflowTrait;
use App\Util\PayrollUtil;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class OldExtraworkdaysRequestsFix extends Command
{
    use WorkflowTrait;

    protected $signature = 'app:old-extraworkdays-requests-fix';

    protected $description = 'Fix old extra workdays requests.';

    public function handle()
    {
        $this->info('Starting OldExtraworkdaysRequestsFix Command...');
        DB::beginTransaction();

        try {

            $payrollRepository = new PayrollsRepository;

            $titles = Title::where('extra_workday_policy_id', '!=', null)
                ->with('workTypePolicy', 'extraWorkdayPolicy')->get();

            $companyIds = $titles->pluck('company_id')->unique();

            // Preload the last drafted payrolls for all companies
            $draftedPayrollsByCompany = [];
            foreach ($companyIds as $companyId) {
                $draftedPayroll = $payrollRepository->getLastDraftedPayroll($companyId);
                if ($draftedPayroll) {
                    $draftedPayrollsByCompany[$companyId] = $draftedPayroll;
                }
            }

            // Clear all pending extra workday requests at the start
            ExtraWorkdayRequest::where('status', 'pending')->delete();

            foreach ($titles as $title) {
                $this->info("Processing Title ID: {$title->id}");

                // Get the drafted payroll for the current title's company
                $draftedPayroll = $draftedPayrollsByCompany[$title->company_id] ?? null;

                if (!$draftedPayroll) {
                    $this->info("Skipping Title ID: {$title->id} - No Drafted Payroll Found.");
                    continue;
                }

                $payrollStart = $draftedPayroll->start;
                $payrollEnd = $draftedPayroll->end;


                if (!isset($title->requestGroup)) {
                    $this->info("Skipping Title ID: {$title->id} - No Request Group.");

                    continue;
                }

                $approvalCycleExists = $title
                    ->requestGroup
                    ->requestWorkflows()
                    ->where('type', 'extra_work_day_request')
                    ->whereNull('deleted_at')
                    ->exists();

                if (!$approvalCycleExists) {
                    $this->info("Skipping Title ID: {$title->id} - No Approval Cycle Exists.");

                    continue;
                }

                $workTypePolicy = $title->workTypePolicy;
                $extraWorkdayPolicy = $title->extraWorkdayPolicy;

                if ($workTypePolicy->work_days_type != 'dynamic_onsite') {
                    $restDays = $workTypePolicy->rest_days;
                    $restDaysArray = explode(',', $restDays);
                    $restDaysDates = $this->getRestDaysDates($payrollStart, $payrollEnd, $restDaysArray);
                    $employees = $title->employees;

                    foreach ($employees as $employee) {
                        $excludedRequests = ExtraWorkdayRequest::whereIn('status', ['approved', 'rejected'])
                            ->where('employee_id', $employee->id)
                            ->pluck('extra_work_day_date')
                            ->toArray();

                        $extraWorkDays = Attendance::where('employee_id', $employee->id)
                            ->whereIn('date', $restDaysDates)
                            ->whereNotIn('date', $excludedRequests)
                            ->get();

                        $this->addExtraWorkDayRequests($employee, $extraWorkDays, $extraWorkdayPolicy->compensation_rate);
                    }
                } else {
                    $employees = $title->employees;
                    $payrollMonthDays = PayrollUtil::PAYROLL_MONTH_DAYS;
                    $restDaysCount = $workTypePolicy->rest_days_count;
                    $thresholdCount = $payrollMonthDays - $restDaysCount;

                    foreach ($employees as $employee) {
                        $excludedRequests = ExtraWorkdayRequest::whereIn('status', ['approved', 'rejected'])
                            ->orWhere('status', 'rejected')
                            ->where('employee_id', $employee->id)
                            ->pluck('extra_work_day_date')
                            ->toArray();

                        $attendances = Attendance::where('employee_id', $employee->id)
                            ->whereBetween('date', [$payrollStart, $payrollEnd])
                            ->whereNotIn('date', $excludedRequests)
                            ->orderBy('date', 'asc')
                            ->when($thresholdCount > 0, function ($query) use ($thresholdCount) {
                                return $query->skip($thresholdCount)->take(PHP_INT_MAX); // Adding a large limit to accompany the skip
                            })
                            ->get();

                        $this->addExtraWorkDayRequests($employee, $attendances, $extraWorkdayPolicy->compensation_rate);
                    }
                }

            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("Error processing Title ID: {$title->id}. Error: {$e->getMessage()}");
        }

        $this->info('OldExtraworkdaysRequestsFix Command Completed.');
    }

    /**
     * Get an array of dates corresponding to the given rest days within a date range.
     *
     * @param string $startDate
     * @param string $endDate
     * @param array $restDaysArray
     * @return array
     */
    private function getRestDaysDates($startDate, $endDate, $restDaysArray)
    {
        // Map abbreviated days to full day names
        $dayMap = [
            'sat' => 'Saturday',
            'sun' => 'Sunday',
            'mon' => 'Monday',
            'tue' => 'Tuesday',
            'wed' => 'Wednesday',
            'thu' => 'Thursday',
            'fri' => 'Friday',
        ];

        // Convert abbreviations to full names
        $fullRestDaysArray = array_map(fn($day) => $dayMap[strtolower($day)] ?? null, $restDaysArray);

        $restDaysDates = [];
        $currentDate = Carbon::parse($startDate);

        while ($currentDate->lte(Carbon::parse($endDate))) {
            if (in_array($currentDate->format('l'), $fullRestDaysArray)) {
                $restDaysDates[] = $currentDate->toDateString();
            }
            $currentDate->addDay();
        }

        return $restDaysDates;
    }

    /**
     * Add extra workday requests in bulk for the given employee and attendances.
     *
     * @param \App\Models\Employee $employee
     * @param \Illuminate\Support\Collection $attendances
     * @param float $compensationRate
     */
    private function addExtraWorkDayRequests($employee, $attendances, $compensationRate)
    {
        $this->info("Processing Extra Workday Requests for Employee ID: {$employee->id}...");

        $employeeRequests = [];
        $entityTags = [];
        $requesterRoleIds = $employee->user->roles->pluck('id')->toArray();

        if ($attendances->isNotEmpty() && $attendances->count() > 0) {
            foreach ($attendances as $attendance) {
                $extraWorkdayRequest = ExtraWorkdayRequest::create([
                    'employee_id' => $employee->id,
                    'extra_work_day_date' => $attendance->date,
                    'status' => 'pending',
                    'compensation_rate' => $compensationRate,
                ]);

                $extraWorkdayRequestId = $extraWorkdayRequest->id;

                $employeeRequests[] = [
                    'company_id' => $employee->company_id,
                    'requested_by' => $employee->id,
                    'requestable_type' => 'extra_work_day_request',
                    'request_name' => 'extra_work_day_request',
                    'comment' => null,
                    'status' => 'pending',
                    'employee_id' => $employee->id,
                    'date' => $attendance->date,
                    'requestable_id' => $extraWorkdayRequestId,
                ];
                $attendance->extra_work_day_status = 'pending';
                $attendance->save();

                $entityTags[] = [
                    'tag' => config('globals.ATTENDANCE_TAGS.EXTRA_WORKDAY'),
                    'entity_id' => $attendance->id,
                    'entity_type' => 'attendance',
                    'company' => $employee->company_id,
                ];

                $this->initializeRequestWorkflow(
                    $extraWorkdayRequest,
                    $employee,
                    config('globals.REQUEST_CYCLE_TYPES.EXTRA_WORKDAY'),
                    config('globals.REQUEST_WORKFLOW_TYPES.EXTRA_WORKDAY'),
                    $requesterRoleIds
                );
            }

            EmployeeRequest::insert($employeeRequests);
            EntityTag::insert($entityTags);
        } else {
            $this->error("No valid attendance data found for Employee ID: {$employee->id}");
        }
    }
}
