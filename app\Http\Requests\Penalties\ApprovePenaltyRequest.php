<?php

namespace App\Http\Requests\Penalties;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;

class ApprovePenaltyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'penalty_group_id' => [
                'integer',
                (new Exists('penalty_groups', 'id'))->where(function ($query) {
                    $query->where('company_id', auth()->user()->company_id) // company rule doesn't apply here
                        ->whereNull('deleted_at');
                }),
            ],
            'amount' => 'required_with:penalty_group_id|numeric|min:0',
            'unit' => 'required|string|in:days,cash',
        ];
    }
}
