<?php

namespace App\Console\Commands;

use App\Jobs\NotClockInPushNotification;
use App\Models\User;
use App\Services\TimeTracking\CrudServices\EntityTagCrudService;
use Illuminate\Console\Command;

class clockInPushNotification extends Command
{
    public function __construct(private EntityTagCrudService $entityTagCrudService)
    {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clock:in:push:notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'send notification In the time of shift and 10 mins after shift start time in case the user did not CICO';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (env('APP_ENV') != 'local') {
            $users = User::query()->whereIn('employee_id', $this->entityTagCrudService->getEmployeeIdsWithoutClockInYet())->get();
            dispatch(new NotClockInPushNotification($users));
        }
    }
}
