<?php

namespace App\Http\Resources\V1\EmployeeProfile;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class EnhancedProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $result = [];

        $result['personal_details'] = [
            'employee_id' => $this->id,
            'nationality' => $this->employeeInfo->nationality ?? null,
            'national_id' => $this->national_id,
            'birth_date' => $this->employeeInfo->birth_date ?? null,
            'age' => $this->employeeInfo->birth_date ? Carbon::parse($this->employeeInfo->birth_date)->age : null,
            'gender' => $this->employeeInfo->gender ?? null,
            'address' => $this->employeeInfo->address ?? null,
            'place_of_birth' => $this->employeeInfo->place_of_birth ?? null,
            'religion' => $this->employeeInfo->religion ?? null,
            'marital_status' => $this->employeeInfo->marital_status ?? null,
            'military_status' => $this->employeeInfo->military_status ?? null,
            'number_kids' => $this->employeeInfo->number_kids ?? null,
            'is_trackable' => $this->is_trackable ?? false,
        ];

        $result['contact_details'] = [
            'employee_id' => $this->id,
            'phone' => $this->secondary,
            'secondary_phone' => $this->employeeInfo->secondary_phone,
            'work_email' => $this->employeeInfo->email ?? null,
            'personal_email' => $this->employeeInfo->personal_email ?? null,
        ];

        if ($this->relationLoaded('emergencyContacts') && $this->emergencyContacts && $this->emergencyContacts->count() > 0) {
            $result['contact_details']['emergency_contacts'] = EmployeeEmergencyContactResource::collection($this->emergencyContacts);
        }

        $result['contract_details'] = [
            'employee_id' => $this->id,
            'join_date' => $this->employeeInfo->join_date ?? null,
            'employment_type' => $this->employeeInfo->employment_type ?? null,
            'length_of_service' => $this->employeeInfo->getLengthOfServiceAttribute() ?? null,
        ];

        if ($this->relationLoaded('contracts') && $this->contracts && $this->contracts->count() > 0) {
            $result['contract_details']['contracts'] = EmployeeContractResource::collection($this->contracts);
        }

        if ($this->relationLoaded('education') && $this->education && $this->education->count() > 0) {
            $result['education'] = [
                'items' => EmployeeEducationResource::collection($this->education)
            ];
        }

        return $result;
    }
}
