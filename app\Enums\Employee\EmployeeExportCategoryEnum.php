<?php

namespace App\Enums\Employee;

use App\Traits\EnumToArray;

enum EmployeeExportCategoryEnum: string
{
    use EnumToArray;

    case ESSENTIAL = 'essential';
    case BASIC = 'basic';
    case CONTACT = 'contact';
    case EDUCATION = 'education';
    case EMPLOYMENT = 'employment';
    /*case SALARY = 'salary';
    case SOCIAL_INSURANCE = 'social_insurance';
    case MEDICAL_INSURANCE = 'medical_insurance';*/

    public static function getLabel(self $value): string
    {
        return trans('employeeexport.' . $value->value);
    }
    public static function getPresetTemplateName(self $value): string
    {
        return match ($value) {
            self::BASIC => 'Basic Details',
            self::EMPLOYMENT => 'Employment Details',
            /*self::SALARY => 'Employee salary info',
            self::SOCIAL_INSURANCE => 'Employee social insurance info',
            self::MEDICAL_INSURANCE => 'Employee medical insurance info',*/
        };
    }
}
