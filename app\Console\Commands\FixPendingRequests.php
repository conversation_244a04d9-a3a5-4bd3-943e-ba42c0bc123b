<?php

namespace App\Console\Commands;

use App\FeatureToggles\Unleash;
use App\Models\AttendanceOvertime;
use App\Models\EmployeeLeaveRequest;
use App\Models\EmployeeRequest;
use App\Models\ExtraWorkDayRequest;
use App\Models\Loan;
use App\Models\MissionRequest;
use App\Models\Penalty;
use App\Models\ProbationRequest;
use App\Models\SalaryAdvance;
use App\Models\StateMachines\RequestApproved;
use App\Models\StateMachines\RequestCancelled;
use App\Models\StateMachines\RequestRejected;
use App\Models\TerminationRequest;
use App\Repositories\Repository;
use App\Repositories\TimecardRepository;
use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use App\Services\V1\Holidays\PublicHolidaysAttendanceService;
use App\Services\V1\LeaveManagement\EmployeeLeavesService;
use App\Services\V1\LeaveManagement\LeavesSplitterService;
use App\Services\V1\Missions\MissionService;
use App\Traits\V1\NotificationRedirection;
use App\Traits\WorkflowTrait;
use App\Util\HolidaysUtil;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixPendingRequests extends Command
{
    use NotificationRedirection, WorkflowTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-pending-requests {type}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update status of pending requests based on their approval cycles';

    public $is_completed = false;

    public $finalStatus = 'pending';

    public $requestWorkflowApprovals;

    public $timecards = [];

    public $statusMap = [
        'approve' => RequestApproved::class,
        'reject' => RequestRejected::class,
        'cancel' => RequestCancelled::class,
    ];

    public $types = [
        'attendance_overtime' => [AttendanceOvertime::class, 'attendance_overtime', 'attendance_overtime'],
        'employee_leave_request' => [EmployeeLeaveRequest::class, 'employee_leave_request', 'employee_leave_request'],
        'penalty' => [Penalty::class, 'penalty', 'penalty'],
        'mission' => [MissionRequest::class, 'mission_request', 'mission_request'],
        'loan' => [Loan::class, 'loan_request', 'loan'],
        'salary_advance' => [SalaryAdvance::class, 'loan_request', 'salary_advance'],
        'probation' => [ProbationRequest::class, 'probation_request', 'probation_request'],
        'extra_work_day' => [ExtraWorkDayRequest::class, 'extra_work_day_request', 'extra_work_day_request'],
        'termination' => [TerminationRequest::class, 'termination_request', 'termination_request'],
    ];

    /**
     * Map request types to their corresponding models
     */
    protected function getModelClass(): array
    {

        return $this->argument('type') == 'all' ? $this->types : [$this->types[$this->argument('type')]];
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting pending requests fix process...');
        $this->finalizePendingEmployeeRequestWithAdminAction();

        try {

            $modelClasses = $this->getModelClass();
            foreach ($modelClasses as $modelClass) {
                $this->line('-------------------------------------');
                $this->info('👤 currently on '.$modelClass[1]);

                $requests = $modelClass[0]::where('created_at', '>=', '2025-04-01')->where('status', 'pending')
                    ->with('employee', 'workflow', 'employeeRequest')->get();

                $pendingCounter = 0;
                $missingCounter = 0;
                $missingWorkflowCounter = 0;

                foreach ($requests as $model) {
                    DB::beginTransaction();

                    if (! isset($model->employeeRequest)) {
                        $missingCounter++;
                        DB::rollBack();

                        continue;
                    }

                    if (empty($model->workflow)) {
                        if ($modelClass[2] != 'employee_leave_request') {

                            $this->initializeRequestWorkflow($model->withoutRelations(), $model->employee, $modelClass[1], $modelClass[2]);
                            //  Log::info('model with missing employee request model_id:'.$model->id);
                        } else {
                            $this->initializeRequestWorkflow($model->withoutRelations(), $model->employee, $model->companyLeaveType->uuid, 'employee_leave_request');
                        }
                        $missingWorkflowCounter++;

                        DB::commit();

                        continue;
                    }

                    $this->is_completed = false;
                    $this->finalStatus = 'pending';

                    $request = [
                        'requestable_id' => $model->id,
                        'requestable_type' => $modelClass[2],
                    ];

                    $isSuperAdmin = ! empty($model->employeeRequest->decider_admin_id);
                    $this->checkApprovalCycleStatus($request, $isSuperAdmin);
                    if (! $this->is_completed) {
                        $pendingCounter++;
                        $workflow = $model->workflow ?? null;
                        if (isset($workflow) && $workflow->status != 'waiting') {
                            $model->workflowApprovalCycles()->delete();
                            if ($modelClass[2] != 'employee_leave_request') {
                                $this->initializeRequestWorkflow($model->withoutRelations(), $model->employee, $modelClass[1], $modelClass[2]);
                            } else {
                                $this->initializeRequestWorkflow($model->withoutRelations(), $model->employee, $model->companyLeaveType->uuid, 'employee_leave_request');
                            }
                        }
                        DB::commit();

                        continue;
                    }

                    $this->getFinalStatus($model);

                    $this->info('model id:'.$model->id.'the status is '.$this->finalStatus);

                    if ($modelClass[2] == 'loan' || $modelClass[2] == 'salary_advance') {
                        $this->updateLoansRequestStatus($model);

                    } elseif ($modelClass[2] == 'mission_request') {
                        $this->updateMissionsRequestStatus($model);

                    } elseif ($modelClass[2] == 'employee_leave_request') {
                        $this->updateLeaveRequestStatus($model);
                    } else {
                        $this->updateRequestStatus($model); // fit to all other requests

                    }
                    $this->updateEmployeeRequestStatus($model->employeeRequest);

                    DB::commit();

                }

                $updatedCounter = $requests->count() - $pendingCounter - $missingCounter - $missingWorkflowCounter;
                Log::info("📦 total {$requests->count()} requests");
                Log::info("✅ updated { $updatedCounter } requests");
                Log::info("🟡 pending {$pendingCounter} requests");
                Log::info("🚫 missing {$missingCounter} requests");
                $this->info("📦 total {$requests->count()} requests");
                $this->info("✅ updated {$updatedCounter} requests");
                $this->info("🟡 pending {$pendingCounter} requests");
                $this->info("🚫 missing workflow {$missingWorkflowCounter} requests");
                $this->info("🚫 missing {$missingCounter} requests");

            }

        } catch (\Exception $e) {
            Log::error("\n❌ Error encountered: ".$e);
            DB::rollBack();

            Log::error('FixPendingRequests failed: '.$e);

            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    private function checkApprovalCycleStatus($data, $isSuperAdmin = false)
    {

        if ($isSuperAdmin) {
            $this->is_completed = true;

        } else {

            $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
            $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle($data);

            if (empty($approvalCycle)) {
                $this->info('No approval cycle found for request');
                $this->info('No approval cycle for requestable_id: '.$data['requestable_id'].', type: '.$data['requestable_type']);
                $this->is_completed = false;

                return;
            }

            $this->requestWorkflowApprovals = $this->getApprovals($approvalCycle);
            $this->checkIsCompleted();
        }
    }

    private function getApprovals($approvalCycle)
    {
        $approvals = [
            'then' => [],
            'or' => [],
        ];

        foreach ($approvalCycle as $approval) {
            if ($approval->operator == 'then') {
                $approvals['then'][] = $approval->status;
            } elseif ($approval->operator == 'or') {
                $approvals['or'][] = $approval->status;
            }
        }

        return $approvals;
    }

    private function checkIsCompleted()
    {
        $orExists = count($this->requestWorkflowApprovals['or']) > 0;
        $thenExists = count($this->requestWorkflowApprovals['then']) > 0;

        if (($orExists && ! in_array(config('globals.REQUEST_STATUSES.PENDING'), $this->requestWorkflowApprovals['or'])) ||
        ($thenExists && in_array(config('globals.REQUEST_STATUSES.REJECTED'), $this->requestWorkflowApprovals['then'])) ||
        ($thenExists && in_array(config('globals.REQUEST_STATUSES.CANCELLED'), $this->requestWorkflowApprovals['then'])) ||
        ($thenExists && ! in_array(config('globals.REQUEST_STATUSES.PENDING'), $this->requestWorkflowApprovals['then']))) { // ||
            // ($thenExists && in_array(config('globals.REQUEST_STATUSES.APPROVED'), $this->requestWorkflowApprovals['then']))) {  this will serve if you need to approve request if any one of the cycle approved

            $this->info('checkIsCompleted true');

            $this->is_completed = true;
        }
    }

    private function getFinalStatus($data)
    {

        // superAdmin
        if (! empty($data->employeeRequest->decider_admin_id)) {
            $this->finalStatus = isset($this->statusMap[$data->employeeRequest->decider_admin_action]) ? $this->statusMap[$data->employeeRequest->decider_admin_action] : $data->employeeRequest->status;

            return;
        }

        if (empty($this->requestWorkflowApprovals['then']) && empty($this->requestWorkflowApprovals['or'])) {
            $this->finalStatus = 'pending';

            return;
        }

        if (in_array(config('globals.REQUEST_STATUSES.CANCELLED'), $this->requestWorkflowApprovals['then'])
        || in_array(config('globals.REQUEST_STATUSES.CANCELLED'), $this->requestWorkflowApprovals['or'])) {
            $this->finalStatus = config('globals.REQUEST_STATUSES.CANCELLED');
        } elseif (count($this->requestWorkflowApprovals['or']) > 0 && ! in_array('pending', $this->requestWorkflowApprovals['or'])) {
            $this->finalStatus = $this->requestWorkflowApprovals['or'][0];
        } elseif (count($this->requestWorkflowApprovals['then']) > 0
        && in_array('rejected', $this->requestWorkflowApprovals['then'])) {
            $this->finalStatus = 'rejected';
        } else {
            $this->finalStatus = 'approved';
        }
    }

    private function updateRequestStatus($requestObj)
    {
        if ($this->finalStatus == 'approved') {
            $requestObj->update(['status' => 'approved']);
            // $this->redirectNotificationsAfterRequestFinalized($requestObj, 'approved');
        } elseif ($this->finalStatus == config('globals.REQUEST_STATUSES.CANCELLED')) {
            $requestObj->update(['status' => 'cancelled']);
            //  $this->redirectNotificationsAfterRequestFinalized($requestObj, 'cancelled');
        } else {
            $requestObj->update(['status' => 'rejected']);
            //   $this->redirectNotificationsAfterRequestFinalized($requestObj, 'rejected');
        }
    }

    private function updateEmployeeRequestStatus($employeeRequestObj)
    {
        if ($this->finalStatus == 'approved') {
            $employeeRequestObj->update(['status' => 'approved']);
        } elseif ($this->finalStatus == config('globals.REQUEST_STATUSES.CANCELLED')) {
            $employeeRequestObj->update(['status' => 'cancelled']);
        } else {
            $employeeRequestObj->update(['status' => 'rejected']);
        }
    }

    public function updateLoansRequestStatus($requestObj)
    {
        if ($this->finalStatus == 'ready_to_disburse') {
            $requestObj->update(['status' => 'ready_to_disburse']);
            // $this->redirectNotificationsAfterRequestFinalized($requestObj, 'approved');

        } elseif ($this->finalStatus == config('globals.REQUEST_STATUSES.CANCELLED')) {
            $requestObj->update(['status' => 'cancelled']);
            $requestObj->installments()?->delete();
            // $this->redirectNotificationsAfterRequestFinalized($requestObj, 'cancelled');

        } else {
            $requestObj->update(['status' => 'rejected']);
            if (isset($requestObj->installments)) {
                $requestObj->installments()->each(function ($installment) {
                    $installment->delete();
                });
            }
            //  $this->redirectNotificationsAfterRequestFinalized($requestObj, 'rejected');
        }
    }

    public function updateMissionsRequestStatus($requestObj)
    {
        $missionService = app(MissionService::class);
        if ($this->finalStatus == 'approved') {
            $requestObj->update(['status' => 'approved']);
            // $requestObj->refresh();
            $missionService->resolveForApprovedMission($requestObj);
            //     $this->redirectNotificationsAfterRequestFinalized($requestObj, 'approved');
        } elseif ($this->finalStatus == config('globals.REQUEST_STATUSES.CANCELLED')) {
            $requestObj->update(['status' => 'cancelled']);
            $missionService->resolveForCancelledMission($requestObj);
            //    $this->redirectNotificationsAfterRequestFinalized($requestObj, 'cancelled');
        } else {
            $requestObj->update(['status' => 'rejected']);
            //  $this->redirectNotificationsAfterRequestFinalized($requestObj, 'rejected');
        }
    }

    public function updateLeaveRequestStatus($requestObj)
    {
        $timecardRepository = new TimecardRepository('Timecard');

        $employeeLeavesService = app(EmployeeLeavesService::class);

        if ($this->finalStatus == 'approved') {
            $requestObj->update(['status' => 'approved']);
            //  $requestObj->refresh();
            $this->splitLeavesBasedOnRestdaysAndHolidays($requestObj); // split then update leave balance for each leave
            $employeeLeavesService->handleApprovedPartialLeaveRequest($requestObj);
            if (! isset($requestObj->partial_leave_type)) {
                $timecardsInLeaveRange = $this->getTimecardIdsInLeaveRange($requestObj, $timecardRepository);
                if (count($timecardsInLeaveRange) > 0) {
                    $timecardRepository->delete($timecardsInLeaveRange);
                }
            }
            // $this->redirectNotificationsAfterRequestFinalized($requestObj, 'approved');
        } elseif ($this->finalStatus == config('globals.REQUEST_STATUSES.CANCELLED')) {
            $requestObj->update(['status' => 'cancelled']);
            // $this->redirectNotificationsAfterRequestFinalized($requestObj, 'cancelled');
        } else {
            $requestObj->update(['status' => 'rejected']);
            // $this->redirectNotificationsAfterRequestFinalized($requestObj, 'rejected');
        }
    }

    public function setTimecards($timecards = [])
    {
        $this->timecards = $timecards;
    }

    public function setIsCompleted(bool $action)
    {
        $this->is_completed = $action;
    }

    // public function deleteTimecardsIfExists()
    // {
    //     if ($this->finalStatus == 'approved' && count($this->timecards)) {
    //         $timecardRepository = new TimecardRepository;
    //         $timecardRepository->delete($this->timecards);
    //     }
    // }

    public function splitLeavesBasedOnRestdaysAndHolidays($leaveObj)
    {
        $leavesSplitterService = app(LeavesSplitterService::class);
        $leaves = $leavesSplitterService->splitLeaveBasedOnHolidaysAndRestdays($leaveObj);
        foreach ($leaves as $leave) {
            $this->updateLeaveBalance($leave);
        }
    }

    public function updateLeaveBalance($leaveObj)
    {
        $leaveObj->load('employee.title.PublicHolidaysPolicy');

        $this->handlePublicHolidayCompensation($leaveObj);
        $employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');

        $leaveBalance = $employeeLeaveBalanceRepository->leaveBalanceForEmployee(
            $leaveObj->employee_id, $leaveObj->from, $leaveObj->company_leave_type_policy_id
        );

        if ($leaveBalance) {
            $this->updateRelatedBalances($leaveObj, $leaveBalance, $employeeLeaveBalanceRepository);
            $leaveBalance->balance = max(0, $leaveBalance->balance - $leaveObj->net_quantity);
            $leaveBalance->save();
        }
    }

    private function handlePublicHolidayCompensation($leaveObj)
    {
        if (isset($leaveObj->employee->title->PublicHolidaysPolicy) &&
            $leaveObj->employee->title->PublicHolidaysPolicy->compensation_method != HolidaysUtil::POLICIES['EXTRA_WORK_DAY']
        ) {
            $publicHolidaysAttendanceService = new PublicHolidaysAttendanceService;
            $publicHolidaysAttendanceService->updateHolidayBalanceAfterLeaveApproved($leaveObj);
        }
    }

    private function updateRelatedBalances($leaveObj, $leaveBalance, $employeeLeaveBalanceRepository)
    {
        $unleash = app(Unleash::class);
        if ($unleash->isLeaveBalanceChangesFlagEnabled()) {
            $this->adjustDebitBalance($leaveObj, $leaveBalance);
            $this->adjustTransferredBalance($leaveObj, $leaveBalance);
            $this->adjustParentLeaveBalance($leaveObj, $employeeLeaveBalanceRepository);
            $leaveObj->save();
        }
    }

    private function adjustDebitBalance($leaveObj, $leaveBalance)
    {
        if ($leaveObj->net_quantity > $leaveBalance->balance) {
            $debitBalance = $leaveObj->net_quantity - $leaveBalance->balance;
            $leaveBalance->debit_balance += $debitBalance;
            $leaveObj->debit_net_quantity = $debitBalance;
        }
    }

    private function adjustTransferredBalance($leaveObj, $leaveBalance)
    {
        if ($leaveBalance->used_transferred_balance < $leaveBalance->transferred_balance) {
            $leaveBalance->used_transferred_balance = min($leaveBalance->transferred_balance, $leaveBalance->used_transferred_balance + $leaveObj->net_quantity
            );
        }
    }

    private function adjustParentLeaveBalance($leaveObj, $employeeLeaveBalanceRepository)
    {
        $parentLeaveType = $leaveObj->companyLeaveType->parentCompanyLeaveType;
        if ($parentLeaveType) {
            $parentLeaveBalance = $employeeLeaveBalanceRepository->leaveBalanceForEmployee(
                $leaveObj->employee_id, $leaveObj->from, $parentLeaveType->companyLeaveTypePolicy->id
            );
            if ($parentLeaveBalance && $parentLeaveBalance->balance > 0) {
                if ($leaveObj->net_quantity > $parentLeaveBalance->balance) {
                    $debitBalance = $leaveObj->net_quantity - $parentLeaveBalance->balance;
                    $parentLeaveBalance->debit_balance += $debitBalance;
                    $leaveObj->debit_net_quantity = $debitBalance;
                }
                $parentLeaveBalance->balance = max(0, $parentLeaveBalance->balance - $leaveObj->net_quantity);
                $parentLeaveBalance->save();
            }
        }
    }

    public function removeDuplicateApprovals($data)
    {
        $uniqueApprovals = [];
        $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle($data);
        foreach ($approvalCycle as $approval) {
            if (! in_array($approval->order, $uniqueApprovals)) {
                $uniqueApprovals[] = $approval->order;

                continue;
            }
            $approval->delete();
        }
    }

    public function getTimecardIdsInLeaveRange($leaveEntity, $timecardRepository)
    {
        $fromDate = Carbon::parse($leaveEntity->from)->toDateString();
        $toDate = Carbon::parse($leaveEntity->to)->toDateString();

        return $timecardRepository->timecardsInRange($leaveEntity->employee_id, $fromDate, $toDate)
            ->pluck('id')->toArray() ?? [];
    }

    public function finalizePendingEmployeeRequestWithAdminAction()
    {
        try {
            DB::beginTransaction();
            $this->info('finalizing pending employee requests with admin action');
            $employeeRequests = EmployeeRequest::where('status', 'pending')->where('decider_admin_id', '!=', null)
                ->where('decider_admin_action', '!=', null)->get();
            $this->info('found '.$employeeRequests->count().' requests to finalize');
            foreach ($employeeRequests as $request) {
                $request->update(['status' => $this->statusMap[$request->decider_admin_action]]);
            }
            $this->info('Done finalizePendingEmployeeRequestWithAdminAction');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->info('error finalizing pending employee requests with admin action: '.$e);
        }
    }
}
