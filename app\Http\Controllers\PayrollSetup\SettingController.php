<?php

namespace App\Http\Controllers\PayrollSetup;

use App\DomainData\FilterDto;
use App\DomainData\PayrollSettingDto;
use App\Http\Controllers\Controller;
use App\Services\PayrollSetup\CrudServices\SettingCrudService;

class SettingController extends Controller
{
    use FilterDto, PayrollSettingDto;

    public function __construct(
        private SettingCrudService $settingCrudService
    ) {}

    public function create(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules([]);
        $rules = $this->getAdditionalRules($rules);

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $this->settingCrudService->create($request, $output);
    }

    public function update(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules([]);
        $rules = $this->getAdditionalRules($rules);

        $rules['id'] = 'required|numeric';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $this->settingCrudService->update($request, $output);
    }

    public function get(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules(['related_objects.*', 'related_objects_count.*']);

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['related_objects'])) {
            $request['related_objects'] = [];
        }
        if (! isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $this->settingCrudService->getByKey($request, $output);
    }

    public function delete(array $request, \stdClass &$output): void
    {
        $rules['ids'] = ['required', 'array'];
        $rules['ids.*'] = ['required', 'numeric'];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $this->failMessages($validator->messages());

            return;
        }

        $request = $validator->validate();
        $this->settingCrudService->delete($request, $output);
    }
}
