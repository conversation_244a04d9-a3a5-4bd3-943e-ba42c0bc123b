<?php

namespace App\Http\Controllers\Payroll\PayrollHub;

use App\Http\Controllers\NewController;
use App\Http\Requests\V1\PayrollHub\CompanyPayrollEntriesRequest;
use App\Http\Requests\V1\PayrollHub\EmployeePayrollEntriesRequest;
use App\Http\Requests\V1\PayrollHub\ExtraWorkDaysBulkActionRequest;
use App\Http\Resources\V1\ExtraWorkDayCompanyRequestsResource;
use App\Http\Resources\V1\PayrollHub\ExtraWorkDayAnalyticsResource;
use App\Http\Resources\V1\PayrollHub\ExtraWorkEmployeeRequestsResource;
use App\Services\V1\Attendance\ExtraWorkdayRequestsService;
use App\Util\HttpStatusCodeUtil;
use App\Util\ScopeUtil;
use App\Traits\QueriesHelper;
use Illuminate\Support\Facades\Log;

class ExtraWorkDayController extends NewController
{
    use QueriesHelper;
    public function __construct(
        protected ExtraWorkdayRequestsService $extraWorkdayRequestsService
    )
    {
    }

    public function getExtraWorkDaysAnalytics(CompanyPayrollEntriesRequest $request)
    {
        $data = null;
        // Check if the user has the company scope
        if($this->getUserHighestScopeKey(auth()->user()) == ScopeUtil::COMPANY_SCOPE)
        {
            $data = $this->extraWorkdayRequestsService->getExtraWorkDaysStatistics($request->validated());
        }
        return getResponseStructure(
            ['data' => $data ? new ExtraWorkDayAnalyticsResource($data) : null],
            HttpStatusCodeUtil::OK,
            'Extra work days statistics fetched successfully'
        );
    }

    public function getAllExtraWorkDaysForCompany(CompanyPayrollEntriesRequest $request)
    {
        Log::info('getAllExtraWorkDaysForCompany: '.json_encode($request->validated()));
        $extraWorkDays = $this->extraWorkdayRequestsService->getAllExtraWorkDaysForCompany($request->validated());
        $paginatedDate = ExtraWorkDayCompanyRequestsResource::collection($extraWorkDays);
        return getResponseStructure(
            [
                'data' => $paginatedDate,
                'meta' => $paginatedDate->response()->getData()->meta,
            ],
            HttpStatusCodeUtil::OK,
            'Extra work days fetched successfully'
        );
    }

    public function getEmployeeExtraWorkDays(EmployeePayrollEntriesRequest $request)
    {
        $data = $this->extraWorkdayRequestsService->getAllExtraWorkDaysForEmployee($request->validated());
        return getResponseStructure(
            ['data' => new ExtraWorkEmployeeRequestsResource($data)],
            HttpStatusCodeUtil::OK,
            'Extra work days fetched successfully'
        );
    }

    public function bulkApprove(ExtraWorkDaysBulkActionRequest $request)
    {
        $this->extraWorkdayRequestsService->bulkApproveExtraWorkdayRequests($request->validated());
        return getResponseStructure(
            ['data' => []],
            HttpStatusCodeUtil::OK,
            'Extra work days approved successfully'
        );
    }

    public function bulkReject(ExtraWorkDaysBulkActionRequest $request)
    {
        $this->extraWorkdayRequestsService->bulkRejectExtraWorkdayRequests($request->validated());
        return getResponseStructure(
            ['data' => []],
            HttpStatusCodeUtil::OK,
            'Extra work days rejected successfully'
        );
    }

    public function bulkCancel(ExtraWorkDaysBulkActionRequest $request)
    {
        $this->extraWorkdayRequestsService->bulkCancelExtraWorkdayRequests($request->validated());
        return getResponseStructure(
            ['data' => []],
            HttpStatusCodeUtil::OK,
            'Extra work days deleted successfully'
        );
    }
}