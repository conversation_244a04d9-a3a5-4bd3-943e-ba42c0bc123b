<?php

namespace App\DataTransferObject;

use Carbon\Carbon;
use Carbon\CarbonInterval;
use Illuminate\Support\Arr;

class CreateTimeCardObject
{
    public ?string $name;

    public int $employeeId;

    public ?int $branchId;

    public ?int $shiftId;

    public string $from;

    public string $to;

    public ?int $timeCardTypeId;

    private ?int $requiredClockInBranchId;

    private ?int $requiredClockOutBranchId;

    private string $requiredClockInLong;

    private string $requiredClockInLat;

    private string $requiredClockOutLat;

    private string $requiredClockOutLong;

    private ?string $color;

    private mixed $createById;

    private ?int $ciSavedLocationId;

    private ?int $coSavedLocationId;

    private ?bool $anyLocation;

    public function __construct(array $data)
    {
        $this->setName($data);
        $this->employeeId = $data['employee_id'];
        $this->branchId = $data['branch_id'] ?? null;
        $this->shiftId = $data['shift_id'] ?? null;
        $this->setFrom($data);
        $this->setTo($data);
        $this->createById = isset($data['by_create_manual_card']) && $data['by_create_manual_card'] ? auth()->user()->employee_id : null;
        $this->timeCardTypeId = $data['timeCardDefaultTypeId'];
        $this->setColor($data);
        $this->setRequiredClockIn($data);
        $this->setRequiredClockOut($data);
        $this->ciSavedLocationId = $data['ci_saved_location_id'] ?? null;
        $this->coSavedLocationId = $data['co_saved_location_id'] ?? null;
        $this->anyLocation = $data['any_location'] ?? 0;
    }

    public function getFrom(): string
    {
        return $this->from;
    }

    public function setFrom(array $data): void
    {
        if (isset($data['shift'])) {
            $shift = $data['shift'];
            $date = Carbon::parse($data['date']);
            $dayName = strtolower($date->format('D'));
            $shiftFromTime = $shift->{$dayName.'_from_time'};
            $fromDate = isset($data['from']) ? Carbon::parse($data['from'])->format('Y-m-d') : $data['date'];
            $this->from = Carbon::parse($fromDate.' '.$shiftFromTime)->format('Y-m-d H:i:s');
        } else {
            $this->from = $data['from']; // dateTime
        }

    }

    public function getTo(): string
    {
        return $this->to;
    }

    public function setTo(array $data): void
    {
        if (isset($data['shift'])) {
            $shift = $data['shift'];
            $date = Carbon::parse($data['date']);
            $dayName = strtolower($date->format('D'));
            $shiftFromTime = $shift->sun_from_time;
            $shiftDuration = explode('.', $shift->sun_duration);
            $hours = Arr::get($shiftDuration, 0, 0);
            $minutes = Arr::get($shiftDuration, 1, 0);
            $interval = CarbonInterval::hours($hours)->minutes($minutes);
            $totalMinutes = $interval->totalMinutes;
            $shiftToTime = Carbon::parse($shiftFromTime)->addMinutes($totalMinutes)->format('H:i:s');
            if ($shiftToTime < $shiftFromTime) {
                $data['date'] = Carbon::parse($data['date'])->addDay(1)->format('Y-m-d');
            }
            $toDate = isset($data['to']) ? Carbon::parse($data['to'])->format('Y-m-d') : $data['date'];
            $this->to = Carbon::parse($toDate.' '.$shiftToTime)->format('Y-m-d H:i:s');

        } else {
            $this->to = $data['to']; // dateTime
        }
    }

    public function setRequiredClockOut(array $data): void
    {
        if (isset($data['required_co_branch_id'])) {
            $this->requiredClockOutBranchId = $data['required_co_branch_id'];
        } elseif ((isset($data['clock_out_lat']) || isset($data['required_co_lat'])) && (isset($data['clock_out_long']) || isset($data['required_co_long']))) {
            $this->requiredClockOutLat = $data['clock_out_lat'] ?? $data['required_co_lat'];
            $this->requiredClockOutLong = $data['clock_out_long'] ?? $data['required_co_long'];
        } elseif (isset($data['co_saved_location_id']) && isset($data['ci_saved_location_lat']) && isset($data['ci_saved_location_long'])) {
            $this->coSavedLocationId = $data['co_saved_location_id'];
            $this->requiredClockOutLat = $data['co_saved_location_lat'];
            $this->requiredClockOutLong = $data['co_saved_location_long'];
        } elseif (isset($data['any_location']) && $data['any_location'] == 1) {
            $this->requiredClockOutBranchId = null;
        } else {
            $this->requiredClockOutBranchId = $data['branch_id'];
        }
    }

    public function setRequiredClockIn(array $data): void
    {
        if (isset($data['required_ci_branch_id'])) {
            $this->requiredClockInBranchId = $data['required_ci_branch_id'];
        } elseif ((isset($data['clock_in_lat']) || isset($data['required_ci_lat'])) && (isset($data['clock_in_long']) || isset($data['required_ci_long']))) {
            $this->requiredClockInLat = $data['clock_in_lat'] ?? $data['required_ci_lat'];
            $this->requiredClockInLong = $data['clock_in_long'] ?? $data['required_ci_long'];
        } elseif (isset($data['ci_saved_location_id']) && isset($data['ci_saved_location_lat']) && isset($data['ci_saved_location_long'])) {
            $this->ciSavedLocationId = $data['ci_saved_location_id'];
            $this->requiredClockInLat = $data['ci_saved_location_lat'];
            $this->requiredClockInLong = $data['ci_saved_location_long'];

        } elseif (isset($data['any_location']) && $data['any_location'] == 1) {
            $this->requiredClockInBranchId = null;
        } else {
            $this->requiredClockInBranchId = $data['branch_id'];
        }
    }

    public function getRequiredClockInLat(): ?string
    {
        return $this->requiredClockInLat ?? null;
    }

    public function getRequiredClockOutLat(): ?string
    {
        return $this->requiredClockOutLat ?? null;
    }

    public function getRequiredClockOutLong(): ?string
    {
        return $this->requiredClockOutLong ?? null;
    }

    public function getRequiredClockInLong(): ?string
    {
        return $this->requiredClockInLong ?? null;
    }

    public function getRequiredClockInBranchId(): ?int
    {
        return $this->requiredClockInBranchId ?? null;
    }

    public function getRequiredClockOutBranchId(): ?int
    {
        return $this->requiredClockOutBranchId ?? null;
    }

    private function setName($data): void
    {
        if (isset($data['shift'])) {
            $this->name = $data['shift']->name;
        } else {
            $this->name = $data['name'] ?? null;
        }
    }

    private function setColor($data): void
    {
        if (isset($data['shift'])) {
            $this->color = $data['shift']->colorhex;
        } else {
            $this->color = $data['color'] ?? null;
        }
    }

    private function getColor(): ?string
    {
        return $this->color;
    }

    private function getName(): ?string
    {
        return $this->name;
    }

    public function toArray(): array
    {
        return [
            'employee_id' => $this->employeeId,
            'branch_id' => $this->branchId,
            'shift_id' => $this->shiftId,
            'timecard_type_id' => $this->timeCardTypeId,
            'created_by_id' => $this->createById,
            'from' => $this->getFrom(),
            'to' => $this->getTo(),
            'name' => $this->getName(),
            'color' => $this->getColor(),
            'required_ci_branch_id' => $this->getRequiredClockInBranchId(),
            'required_co_branch_id' => $this->getRequiredClockOutBranchId(),
            'required_ci_lat' => $this->getRequiredClockInLat(),
            'required_ci_long' => $this->getRequiredClockInLong(),
            'required_co_lat' => $this->getRequiredClockOutLat(),
            'required_co_long' => $this->getRequiredClockOutLong(),
            'ci_saved_location_id' => $this->ciSavedLocationId,
            'co_saved_location_id' => $this->coSavedLocationId,
            'any_location' => $this->anyLocation,
        ];
    }
}
