<?php

namespace App\Http\Controllers\TimeTracking;

use App\Http\Controllers\Controller;
use App\Services\TimeTracking\BusinessServices\DeductionActionsService;
use stdClass;

class DeductionController extends Controller
{
    public function __construct(
        private DeductionActionsService $deductionActionsService,
    ) {}

    public function approve(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|integer';
        $rules['deduction_value'] = 'string';
        $rules['reason'] = 'in:issue_with_phone,forgot_clock_in,has_permission,other';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request['action'] = 'approve';

        $request['related_objects'] = ['employeeApproves'];
        $request['related_objects_count'] = [];

        if (isset($request['deduction_value'])) {
            $output->deduction_value = $request['deduction_value'];
        }

        if (isset($request['reason'])) {
            $output->reason = $request['reason'];
        }

        $this->deductionActionsService->perform($request, $output);
    }

    public function reject(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|integer';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request['action'] = 'reject';
        $request['related_objects'] = ['employeeApproves'];
        $request['related_objects_count'] = [];

        $this->deductionActionsService->perform($request, $output);

    }

    public function waive(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|integer';
        $rules['comment'] = 'string';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request['action'] = 'waive';
        $request['related_objects'] = ['employeeApproves'];
        $request['related_objects_count'] = [];

        $this->deductionActionsService->perform($request, $output);

    }

    public function cancel(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|integer';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request['action'] = 'cancel';

        $request['related_objects'] = ['employeeApproves'];
        $request['related_objects_count'] = [];

        $this->deductionActionsService->perform($request, $output);
    }
}
