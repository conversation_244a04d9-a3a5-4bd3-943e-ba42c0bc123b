<?php

namespace App\Http\Requests\V1\EmplyoeeProfile;

use App\Rules\EmployeeIdRule;
use Illuminate\Foundation\Http\FormRequest;

class UploadProfilePictureByManagerRequest extends FormRequest
{
    public function authorize()
    {
        return true; // Adjust as needed for your authorization logic
    }

    public function rules()
    {
        return [
            'profile_picture' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg,gif',
                'max:5948'
            ],
            'employee_id' => [
                'required',
                new EmployeeIdRule
            ]
        ];
    }
}
