<?php

namespace App\Services\V1\Probation;

use App\Exceptions\UnprocessableException;
use App\Repositories\V1\Probation\ProbationRepository;
use App\Services\BaseService;
use App\Services\TerminationService;
use App\Traits\V1\EmployeeRequestsTrait;
use App\Traits\WorkflowTrait;
use Workflow\WorkflowStub;
use App\Jobs\AssignApprovalsJob;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;
use App\Util\EmployeeUtil;

class ProbationService extends BaseService
{
    use WorkflowTrait, EmployeeRequestsTrait, PrepareAssignRequestCycleDataTrait;

    public function __construct(
        protected ProbationRepository $probationRepository,
        protected TerminationService $terminationService,
    ) {}

    public function createRequest($employee, $date)
    {
        $probationRequest = $this->probationRepository->add([
            'employee_id' => $employee->id,
            'company_id' => $employee->company_id,
            'probation_end_date' => $date,
            'status' => 'pending',
        ]);


        AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('probation', $probationRequest));

        return $probationRequest;
    }

    /**
     * @throws UnprocessableException
     */
    public function actionOnProbation($probationId, $actionType, $data = null)
    {
        $probationRequest = $this->probationRepository->findOrFail($probationId);
        $roleIds = config('globals.user')->roles->pluck('id')->toArray();

        $userCanTakeAnAction = $this->canTakeAnAction($roleIds, $probationRequest->id, config('globals.REQUEST_WORKFLOW_TYPES.PROBATION'), $actionType);
        if (! $userCanTakeAnAction) {
            throw new UnprocessableException(trans('messages.can_not_take_this_action'));
        }

        if ($probationRequest->status != 'pending' && $actionType != 'cancel') {
            throw new UnprocessableException(trans('messages.workflow_is_completed'));
        }

        $this->doAnAction($actionType);
        
        if($this->checkRequestIsCompleted($probationRequest->employeeRequest)) {
            $finalStatus = $this->getFinalStatus($probationRequest->employeeRequest, $actionType);
            $this->updateEntity($probationRequest, $finalStatus);
            $this->updateRequest($probationRequest->employeeRequest, $finalStatus);
            if($finalStatus == 'approved') {
                $probationRequest->employee->on_probation = false;
                $probationRequest->employee->status = EmployeeUtil::STATUSES['ACTIVE'];
                $probationRequest->employee->save();
            }
            elseif($finalStatus == 'rejected') {
                $terminationDate = [
                    'terminate_date' => $data['terminate_date'] ?? null,
                    'terminate_reason' => $data['terminate_reason'] ?? null,
                    'terminate_type' => $data['terminate_type'] ?? null,
                    'employee_id' => $probationRequest->employee_id,
                ];
                if (auth()->user()->hasPermissionTo('terminate_employee', 'user-api')) {
                    $this->terminationService->directTerminate($terminationDate);
                } else {
                    $this->terminationService->requestTerminate($terminationDate);
                }
            }
        }
    }

    public function getPendingProbationRequestsCount($data)
    {
        return $this->probationRepository->getPendingProbationRequestsCount($data);
    }
}
