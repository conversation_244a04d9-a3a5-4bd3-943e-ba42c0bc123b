<?php

namespace App\Http\Controllers\Payroll\PayrollHub;

use App\Http\Controllers\NewController;
use App\Http\Requests\V1\PayrollHub\CompanyPayrollEntriesRequest;
use App\Http\Requests\V1\PayrollHub\EmployeePayrollEntriesRequest;
use App\Http\Resources\V1\PayrollHub\GetCompanyAnalyticsResource;
use App\Http\Resources\V1\PayrollHub\GetCompanyDeductionsResource;
use App\Http\Resources\V1\PayrollHub\GetEmployeeDeductionsResource;
use App\Services\V1\PayrollHub\DeductionsService;
use App\Traits\QueriesHelper;
use App\Util\HttpStatusCodeUtil;
use App\Util\ScopeUtil;

class DeductionController extends NewController
{
    use QueriesHelper;
    public function __construct(
        protected DeductionsService          $deductionsService,
    )
    {
    }

    public function getAllDeductionsForCompany(CompanyPayrollEntriesRequest $request)
    {
        $deductions = $this->deductionsService->getAllDeductionsForCompany($request->validated());
        $data = GetCompanyDeductionsResource::collection($deductions);
        $paginatedData = $data->response()->getData();
        return getResponseStructure(
            [
                'data' => $data,
                'pagination' => $paginatedData->meta,
            ],
            HttpStatusCodeUtil::OK,
            'Deductions fetched successfully'
        );
    }

    public function getEmployeeDeductions(EmployeePayrollEntriesRequest $request)
    {
        $data = $this->deductionsService->getAllDeductionsForEmployee($request->validated());
        return getResponseStructure(
            ['data' => new GetEmployeeDeductionsResource($data)],
            HttpStatusCodeUtil::OK,
            'Deductions fetched successfully'
        );
    }

    public function getMonthlyDeductionAnalytics(CompanyPayrollEntriesRequest $request){
        $data = null;
        if($this->getUserHighestScopeKey(auth()->user()) == ScopeUtil::COMPANY_SCOPE){
            $data = $this->deductionsService->getMonthlyAnalytics($request->validated());
        }
        return getResponseStructure(
            ['data' => $data ? GetCompanyAnalyticsResource::collection($data) : null],
            HttpStatusCodeUtil::OK,
            'Analytics fetched successfully'
        );
    }
}