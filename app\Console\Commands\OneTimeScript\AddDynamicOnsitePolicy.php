<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Company;
use App\Models\Role;
use App\Models\Title;
use App\Models\WorkTypePolicy;
use App\Services\V1\Attendance\GenerateTimecardsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddDynamicOnsitePolicy extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:add-dynamic-onsite-policy';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            // $data = [];
            $this->info('Adding Dynamic Onsite Policies started at: '.date('Y-m-d H:i:s'));
            $companies = Company::all();
            foreach ($companies as $company) {
                $dynamicOnsitePolicy = WorkTypePolicy::create([
                    'company_id' => $company->id,
                    'name' => 'Dynamic On-Site',
                    'description' => 'Dynamic on-site',
                    'work_days_type' => 'dynamic_onsite',
                    'rest_days_count' => $company->restDayLeaveType?->companyLeaveTypePolicy?->base_balance / 8 ?? 5,
                    'apply_any_branch' => 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $dynamicOnsiteAnyBranchPolicy = WorkTypePolicy::create([
                    'company_id' => $company->id,
                    'name' => 'Dynamic On-Site Any Branch',
                    'description' => 'Dynamic on-site at any branch',
                    'work_days_type' => 'dynamic_onsite',
                    'rest_days_count' => $company->restDayLeaveType?->companyLeaveTypePolicy?->base_balance / 8 ?? 5,
                    'apply_any_branch' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $this->info('Dynamic Onsite Policies created for company: '.$company->id);

                $roles = Role::with('titles')->whereHas('permissions', function ($query) {
                    $query->where('name', 'any_branch');
                })->where('company_id', $company->id)->get();

                $titlesWithAnyBranchIds = [];
                foreach ($roles as $role) {
                    foreach ($role->titles as $title) {
                        $titlesWithAnyBranchIds[] = $title->id;
                        // $data[] = [
                        //     'title_id' => $title->id,
                        //     'work_type_policy_id' => $dynamicOnsiteAnyBranchPolicy->id,
                        //     'start_time' => $dynamicOnsiteAnyBranchPolicy->start_time,
                        //     'end_time' => $dynamicOnsiteAnyBranchPolicy->end_time,
                        //     'rest_days' => $dynamicOnsiteAnyBranchPolicy->rest_days,
                        // ];
                    }
                }
                $titles = Title::whereNotIn('id', $titlesWithAnyBranchIds)
                    ->where('company_id', $company->id)->get();

                $titleIds = $titles->pluck('id')->toArray();

                Title::whereIn('id', $titlesWithAnyBranchIds)->update(['work_type_policy_id' => $dynamicOnsiteAnyBranchPolicy->id]);

                Title::whereIn('id', $titleIds)->update(['work_type_policy_id' => $dynamicOnsitePolicy->id]);

                // foreach($titles as $title){
                //     $data[] = [
                //         'title_id' => $title->id,
                //         'work_type_policy_id' => $dynamicOnsitePolicy->id,
                //         'start_time' => $dynamicOnsitePolicy->start_time,
                //         'end_time' => $dynamicOnsitePolicy->end_time,
                //         'rest_days' => $dynamicOnsitePolicy->rest_days,
                //     ];
                // }

            }

            // $startingDate = date('Y-m-d', strtotime('+1 day'));

            // $generateTimecardsService = new GenerateTimecardsService();

            // $generateTimecardsService->generateTimecards($data, $startingDate);

            $this->info('Adding Dynamic Onsite Policies completed at: '.date('Y-m-d H:i:s'));
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            $this->info('Adding Dynamic Onsite Policies failed at: '.date('Y-m-d H:i:s'));
            $this->error($e);
        }
    }
}
