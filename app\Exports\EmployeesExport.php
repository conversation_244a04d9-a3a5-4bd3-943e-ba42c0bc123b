<?php

namespace App\Exports;

use App\Enums\Employee\EmployeeExportCategoryEnum;
use App\Enums\Employee\EmployeeExportFieldEnum;
use App\Enums\EmployeeInfo\Nationalities;
use App\Enums\EmployeeInfo\ReligionEnum;
use App\FeatureToggles\Unleash;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class EmployeesExport implements FromArray, ShouldAutoSize, WithColumnFormatting, WithEvents, WithHeadings
{
    private $unleash;
    private $templateFields;
    private $isEnhancedProfileEnabled;
    private $userLanguage;

    public function __construct(private $data, private $request = null)
    {
        $this->unleash = app(Unleash::class);
        $this->isEnhancedProfileEnabled = $this->unleash->isEnhancedEmployeeProfileEnabled();

        $this->userLanguage = config('globals.lang') ?? app()->getLocale() ?? 'en';

        if ($this->isEnhancedProfileEnabled) {
            $this->templateFields = $this->extractTemplateFields($this->request);
        }
    }

    private function extractTemplateFields($request): array
    {
        $fields = [];
        foreach (EmployeeExportCategoryEnum::values() as $category) {
            if ($category === EmployeeExportCategoryEnum::ESSENTIAL->value) {
                continue;
            }

            if (isset($request[$category]) && is_array($request[$category])) {
                $fields[$category] = $request[$category];
            }
        }

        return $fields;
    }

    public function collection()
    {
        return collect($this->data);
    }

    public function headings(): array
    {
        if (!$this->isEnhancedProfileEnabled || empty($this->templateFields)) {
            return [
                [
                    'Full Name',
                    'Code',
                    'Phone Number',
                    'National ID',
                    'Title',
                    'Department',
                    'Sub Department',
                    'Main Branch',
                    'Other Branches',
                    'Status',
                    'Termination Date',
                    'Employment Date',
                    'Birth Date',
                    'Experience Start Date',
                    'Years of Experience',
                    'Address',
                    '# of Dependents',
                    'Gender',
                    'Email',
                    'Passport Number',
                    'Secondary Phone',
                    'Nationality',
                    'Military Status',
                    'Notes',
                    'Training Certification Status',
                ]
            ];
        }

        $columnNames = [];

        $essentialFields = EmployeeExportFieldEnum::getFieldsForCategory(EmployeeExportCategoryEnum::ESSENTIAL);
        foreach($essentialFields as $field) {
            $columnNames[] = EmployeeExportFieldEnum::getLabel(EmployeeExportFieldEnum::from($field));
        }

        foreach (EmployeeExportCategoryEnum::cases() as $category) {
            if ($category === EmployeeExportCategoryEnum::ESSENTIAL) {
                continue;
            }

            $categoryValue = $category->value;
            $categoryFields = EmployeeExportFieldEnum::getFieldsForCategory($category);

            foreach ($categoryFields as $field) {
                if ($this->templateFields[$categoryValue][$field]) {
                    $fieldEnum = EmployeeExportFieldEnum::from($field);
                    $columnNames[] = EmployeeExportFieldEnum::getLabel($fieldEnum);
                }
            }
        }

        return [$columnNames];
    }

    public function array(): array
    {
        $employees = $this->data;
        $data = [];

        if (!$this->isEnhancedProfileEnabled || empty($this->templateFields)) {
            foreach ($employees as $employee) {
                $data[] = [
                    'Full Name' => "{$employee?->first_name} {$employee?->second_name} {$employee?->third_name} {$employee?->fourth_name} {$employee?->fifth_name}",
                    'Code' => $employee->employee_number,
                    'Phone Number' => $this->formatAsText($employee->phone),
                    'National ID' => $this->formatAsText($employee->national_id),
                    'Title' => $employee->title?->name,
                    'Department' => $employee->title?->department?->name,
                    'Sub Department' => $employee->title?->subDepartment?->name,
                    'Main Branch' => $employee->branch?->name,
                    'Other Branches' => $employee->branches?->pluck('name')->join(', '),
                    'Status' => $employee->status,
                    'Termination Date' => $employee->employeeInfo?->termination_date,
                    'Employment Date' => $employee->employeeInfo?->join_date,
                    'Birth Date' => $employee->employeeInfo?->birth_date ?? null,
                    'Experience Start Date' => $employee->employeeInfo?->career_start_date,
                    'Years of Experience' => $employee->employeeInfo?->number_of_years_of_experience ?? 0,
                    'Address' => $employee->employeeInfo?->address,
                    '# of Dependents' => $employee->employeeInfo?->number_kids,
                    'Gender' => $employee->employeeInfo?->gender,
                    'Email' => $employee->employeeInfo?->email,
                    'Passport Number' => $this->formatAsText($employee->employeeInfo?->passport_number),
                    'Secondary Phone' => $this->formatAsText($employee->employeeInfo?->secondary_phone),
                    'Nationality' => $employee->employeeInfo?->nationality,
                    'Military Status' => $employee->employeeInfo?->military_status,
                    'Notes' => $employee->employeeInfo?->notes,
                    'Training Certification Status' => $employee->employeeInfo?->training_certification_status,
                ];
            }

            return [array_values($data)];
        }

        foreach ($employees as $employee) {
            $row = [];

            // Essential fields with proper formatting
            $row[] = $this->formatEmployeeName($employee, 'ar');
            $row[] = $this->formatEmployeeName($employee, 'en');
            $row[] = $employee->employee_number;
            $row[] = $this->formatNames($employee->title);
            $row[] = $this->formatNames($employee->title?->department);
            $row[] = $this->formatNames($employee->branch);
            $row[] = $this->formatAsText($employee->phone);

            foreach (EmployeeExportCategoryEnum::cases() as $category) {
                if ($category === EmployeeExportCategoryEnum::ESSENTIAL) {
                    continue;
                }

                $categoryValue = $category->value;
                $categoryFields = EmployeeExportFieldEnum::getFieldsForCategory($category);

                foreach ($categoryFields as $field) {
                    if ($this->templateFields[$categoryValue][$field]) {
                        $row[] = $this->getFieldValue($employee, $field);
                    }
                }
            }

            $data[] = $row;
        }

        return $data;
    }

    private function getFieldValue($employee, $field)
    {
        switch ($field) {
            case EmployeeExportFieldEnum::NATIONALITY->value:
                return $this->translateEnumValue($employee->employeeInfo?->nationality);

            case EmployeeExportFieldEnum::NATIONAL_ID->value:
                return $this->formatAsText($employee->national_id);

            case EmployeeExportFieldEnum::PASSPORT_NUMBER->value:
                return $this->formatAsText($employee->employeeInfo?->passport_number);

            case EmployeeExportFieldEnum::GENDER->value:
                return $this->translateEnumValue($employee->employeeInfo?->gender);

            case EmployeeExportFieldEnum::PLACE_OF_BIRTH->value:
                return $employee->employeeInfo?->place_of_birth;

            case EmployeeExportFieldEnum::ADDRESS->value:
                return $employee->employeeInfo?->address;

            case EmployeeExportFieldEnum::MILITARY_STATUS->value:
                return $this->translateEnumValue($employee->employeeInfo?->military_status);

            case EmployeeExportFieldEnum::DATE_OF_BIRTH->value:
                return $this->formatDate($employee->employeeInfo?->birth_date);

            case EmployeeExportFieldEnum::RELIGION->value:
                return $employee->employeeInfo?->religion
                ? ( $employee->employeeInfo?->religion != ReligionEnum::OTHER->value
                ? $this->translateEnumValue($employee->employeeInfo?->religion)
                 : $employee->employeeInfo?->other_religion) : null;

            case EmployeeExportFieldEnum::MARITAL_STATUS->value:
                return $this->translateEnumValue($employee->employeeInfo?->marital_status);

            case EmployeeExportFieldEnum::NUMBER_OF_CHILDREN->value:
                return $employee->employeeInfo?->number_kids;

            case EmployeeExportFieldEnum::YEARS_OF_EXPERIENCE->value:
                return $employee->employeeInfo?->number_of_years_of_experience;

            case EmployeeExportFieldEnum::PRIMARY_PHONE->value:
                return $this->formatAsText($employee->phone);

            case EmployeeExportFieldEnum::SECONDARY_PHONE->value:
                return $this->formatAsText($employee->employeeInfo?->secondary_phone);

            case EmployeeExportFieldEnum::WORK_EMAIL->value:
                return $employee->employeeInfo?->email;

            case EmployeeExportFieldEnum::PERSONAL_EMAIL->value:
                return $employee->employeeInfo?->personal_email;

            // Emergency contact details
            case EmployeeExportFieldEnum::EMERGENCY_CONTACT_NAME->value:
                return $employee->latestEmergencyContact?->name ?? '';

            case EmployeeExportFieldEnum::EMERGENCY_CONTACT_PHONE->value:
                return $this->formatAsText($employee->latestEmergencyContact?->phone ?? '');

            case EmployeeExportFieldEnum::EMERGENCY_CONTACT_RELATION->value:
                return $employee->latestEmergencyContact?->relation ?? '';

            // Education details
            case EmployeeExportFieldEnum::DEGREE_TYPE->value:
                return $this->translateEnumValue($employee->latestEducation?->degree_type ?? '');

            case EmployeeExportFieldEnum::DEGREE_NAME->value:
                return $employee->latestEducation?->degree_name ?? '';

            case EmployeeExportFieldEnum::INSTITUTION_NAME->value:
                return $employee->latestEducation?->institution_name ?? '';

            case EmployeeExportFieldEnum::GRADUATION_YEAR->value:
                return $employee->latestEducation?->graduation_year ?? '';

            // Employment details
            case EmployeeExportFieldEnum::HIRING_DATE->value:
                return $this->formatDate($employee->employeeInfo?->join_date);

            case EmployeeExportFieldEnum::EMPLOYMENT_TYPE->value:
                return $this->translateEnumValue($employee->employeeInfo?->employment_type);

            // Contract details
            case EmployeeExportFieldEnum::LATEST_CONTRACT_START_DATE->value:
                return $this->formatDate($employee->latestContract?->contract_start_date ?? '');

            case EmployeeExportFieldEnum::LATEST_CONTRACT_DURATION->value:
                return $this->translateEnumValue($employee->latestContract?->contract_duration ?? '');

            case EmployeeExportFieldEnum::LATEST_CONTRACT_END_DATE->value:
                return $this->formatDate($employee->latestContract?->contract_end_date ?? '');

            // Salary details
            /*case EmployeeExportFieldEnum::NET_SALARY->value:
                return $employee->employeeSalary?->net_salary;

            case EmployeeExportFieldEnum::GROSS_SALARY->value:
                return $employee->employeeSalary?->gross_salary;

            case EmployeeExportFieldEnum::SOCIAL_INSURANCE_SALARY->value:
                return $employee->employeeSalary?->social_insurance_salary;

            case EmployeeExportFieldEnum::APPLIED_ALLOWANCES->value:
                // This would need to be implemented based on how allowances are stored
                return '';

            // Social insurance data
            case EmployeeExportFieldEnum::SOCIAL_INSURANCE_STATUS->value:
                return $employee->employeeInsurance?->is_socially_insured ? 'Insured' : 'Not Insured';

            case EmployeeExportFieldEnum::SOCIAL_INSURANCE_NUMBER->value:
                return $employee->employeeInsurance?->social_insurance_number;

            case EmployeeExportFieldEnum::SOCIAL_INSURANCE_START_DATE->value:
                return $employee->employeeInsurance?->social_insurance_date;

            case EmployeeExportFieldEnum::SOCIAL_INSURANCE_END_DATE->value:
                return $employee->employeeInsurance?->social_insurance_end_date;

            case EmployeeExportFieldEnum::SOCIAL_INSURANCE_OFFICE->value:
                return $employee->employeeInsurance?->social_insurance_office;

            // Medical insurance data
            case EmployeeExportFieldEnum::MEDICAL_INSURANCE_STATUS->value:
                return $employee->employeeInsurance?->is_medically_insured ? 'Insured' : 'Not Insured';

            case EmployeeExportFieldEnum::MEDICAL_INSURANCE_NUMBER->value:
                return $employee->employeeInsurance?->medical_insurance_number;

            case EmployeeExportFieldEnum::MEDICAL_INSURANCE_PROVIDER->value:
                return $employee->employeeInsurance?->medical_insurance_provider;

            case EmployeeExportFieldEnum::MEDICAL_INSURANCE_START_DATE->value:
                return $employee->employeeInsurance?->medical_insurance_start_date;

            case EmployeeExportFieldEnum::MEDICAL_INSURANCE_END_DATE->value:
                return $employee->employeeInsurance?->medical_insurance_end_date;*/

            default:
                return '';
        }
    }
    private function formatEmployeeName($employee, $language = 'both'): string
    {
        if ($language === 'ar') {
            $nameAr = $employee->name_ar ?? trim("{$employee->first_name_ar} {$employee->second_name_ar} {$employee->third_name_ar} {$employee->fourth_name_ar} {$employee->fifth_name_ar}");
            return $nameAr;
        } else {
            $nameEn = $employee->name_en ?? trim("{$employee->first_name_en} {$employee->second_name_en} {$employee->third_name_en} {$employee->fourth_name_en} {$employee->fifth_name_en}");
            return $nameEn;
        }
    }

    private function formatNames($field): string
    {
        if (!$field) {
            return '';
        }

        if (isset($field->name_ar) && isset($field->name_en)) {
            if ($this->userLanguage === 'ar') {
                return $field->name_ar ?: $field->name_en ?: '';
            } else {
                return $field->name_en ?: $field->name_ar ?: '';
            }
        }

        return $field->name ?? '';
    }

    private function translateEnumValue($value): string
    {
        if (empty($value)) {
            return '';
        }

        $translation = trans("employeeexport.{$value}");

        if ($translation === "employeeexport.{$value}") {
            return $value;
        }

        return $translation;
    }


    private function formatNationality($nationality): string
    {
        return Nationalities::getFormattedValue($nationality);
    }

    private function formatDate($date): string
    {
        if (empty($date)) {
            return '';
        }
        try {
            $dateObj = is_string($date) ? Carbon::parse($date) : $date;
            return $dateObj->format('Y-m-d');
        } catch (\Exception) {
            return $date;
        }
    }

    /*
     This function is used to add a zero-width space to the beginning of a string
     to prevent Excel from converting it to scientific notation.
    */
    private function formatAsText($value): string
    {
        if (empty($value)) {
            return '';
        }
        return (string) $value;
        //return "\u{200B}" . (string) $value; // uncomment this to add the zero wide space and force the excel to see the numbers as text
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $headings = $this->headings()[0];
                $highestRow = count($this->data) + 1; 

                for ($index = 0; $index < count($headings); $index++) {
                    $columnLetter = Coordinate::stringFromColumnIndex($index + 1);
                    for ($row = 1; $row <= $highestRow; $row++) {
                        $cellValue = $event->sheet->getDelegate()->getCell("{$columnLetter}{$row}")->getValue();
                        $event->sheet->getDelegate()->getCell("{$columnLetter}{$row}")
                            ->setValueExplicit($cellValue, DataType::TYPE_STRING);
                    }
                }
            },
        ];
    }

    public function columnFormats(): array
    {
        $headings = $this->headings()[0];
        $highestRow = count($this->data) + 1; // +1 for header row
        $formats = [];

        for ($i = 1; $i <= count($headings); $i++) {
            $colLetter = Coordinate::stringFromColumnIndex($i);
            $formats[$colLetter . '1:' . $colLetter . $highestRow] = NumberFormat::FORMAT_TEXT;
        }

        return $formats;
    }
}
