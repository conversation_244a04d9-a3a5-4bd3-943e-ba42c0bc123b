<?php

namespace App\Repositories\V1\Payroll;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * @author: @ahmed-zaki
 * @date: 11/06/2025
 * @description: This repository is used to get the payslips for the employees
 * 
 */

class PayslipRepository
{
    public function getPayslips(array $data)
    {
        try {
            $query = DB::table('employee_salary_component_month as escm')
                ->join('employee_payroll_summaries as eps', function ($join) {
                    $join->on('escm.payroll_id', '=', 'eps.payroll_id')
                        ->on('escm.employee_id', '=', 'eps.employee_id');
                })
                ->join('employees as e', 'escm.employee_id', '=', 'e.id')
                ->join('titles as t', 'e.title_id', '=', 't.id')
                ->join('salary_components as sc', 'escm.salary_component_id', '=', 'sc.id')
                ->join('salary_components_categories as scc', 'sc.salary_components_category_id', '=', 'scc.id')
                ->where('escm.company_id', $data['company_id'])
                ->where('escm.payroll_id', $data['payroll_id'])
                ->where('eps.month', $data['month'])
                ->where('eps.year', $data['year']);

            // Filter by specific employee IDs if provided
            if (!empty($data['employee_ids'])) {
                $query->whereIn('escm.employee_id', $data['employee_ids']);
            }

            return $query->select([
                'e.id as employee_id',
                'e.name as employee_name',
                'e.employee_number as code',
                't.name as title',
                'scc.id as category_id',
                'scc.name as category_name',
                'scc.is_addition as category_is_addition',
                'sc.name as component_name',
                'sc.id as component_id',
                'escm.amount',
                'eps.final_net_salary as net_salary',
                'eps.tax_amount as tax_amount',
                'eps.insurance_amount as insurance_amount',
            ])
                ->orderBy('employee_id')
                ->get();

        } catch (\Exception $e) {
            Log::error('Error in getPayslips: ' . $e->getMessage());
            throw new \Exception('Failed to retrieve payslip data: ' . $e->getMessage());
        }
    }

    public function getPayslip($id)
    {
        return DB::table('employee_salary_component_month as escm')
            ->join('employee_payroll_summaries as eps', function ($join) {
                $join->on('escm.payroll_id', '=', 'eps.payroll_id')
                    ->on('escm.employee_id', '=', 'eps.employee_id');
            })
            ->join('employees as e', 'escm.employee_id', '=', 'e.id')
            ->join('titles as t', 'e.title_id', '=', 't.id')
            ->join('salary_components as sc', 'escm.salary_component_id', '=', 'sc.id')
            ->join('salary_components_categories as scc', 'sc.salary_components_category_id', '=', 'scc.id')
            ->where('escm.id', $id)
            ->first();
    }

    /**
     * Get payslip data for a specific employee
     */
    public function getPayslipByEmployeeId(int $employeeId)
    {
        try {
            return DB::table('employee_salary_component_month as escm')
                ->join('employee_payroll_summaries as eps', function ($join) {
                    $join->on('escm.payroll_id', '=', 'eps.payroll_id')
                        ->on('escm.employee_id', '=', 'eps.employee_id');
                })
                ->join('employees as e', 'escm.employee_id', '=', 'e.id')
                ->join('titles as t', 'e.title_id', '=', 't.id')
                ->join('salary_components as sc', 'escm.salary_component_id', '=', 'sc.id')
                ->join('salary_components_categories as scc', 'sc.salary_components_category_id', '=', 'scc.id')
                ->where('escm.employee_id', $employeeId)
                ->select([
                    'e.id as employee_id',
                    'e.name as employee_name',
                    'e.employee_number as code',
                    't.name as title',
                    'scc.id as category_id',
                    'scc.name as category_name',
                    'scc.is_addition as category_is_addition',
                    'sc.name as component_name',
                    'sc.id as component_id',
                    'escm.amount',
                    'eps.final_net_salary as net_salary',
                    'eps.tax_amount as tax_amount',
                    'eps.insurance_amount as insurance_amount',
                ])
                ->orderBy('category_id')
                ->get();

        } catch (\Exception $e) {
            Log::error('Error in getPayslipByEmployeeId: ' . $e->getMessage());
            throw new \Exception('Failed to retrieve payslip data for employee: ' . $e->getMessage());
        }
    }

    /**
     * Get employees with payslip data for filtering
     * Accepts filters: branches, departments, cash_only, is_downloaded, search
     * Returns: emp_id, emp_name, emp_title, payslip_status
     * Search: Searches by employee name or employee number/code
     */
    public function getEmployees(array $data, array $filters = [])
    {
        try {
            $query = DB::table('employee_payroll_summaries as eps')
                ->join('employees as e', 'eps.employee_id', '=', 'e.id')
                ->join('titles as t', 'e.title_id', '=', 't.id')
                ->join('departments as d', 't.department_id', '=', 'd.id')
                ->join('branches as b', 'e.branch_id', '=', 'b.id')
                ->leftJoin('employee_salaries as es', function ($join) {
                    $join->on('e.id', '=', 'es.employee_id')
                        ->whereNull('es.deleted_at'); // Handle soft deletes
                })
                ->where('eps.month', $data['month'])
                ->where('eps.year', $data['year'])
                ->where('eps.payroll_id', $data['payroll_id'])
                ->where('e.company_id', $data['company_id']);

            // Apply branch filter
            if (!empty($filters['branches'])) {
                $query->whereIn('e.branch_id', $filters['branches']);
            }

            // Apply department filter
            if (!empty($filters['departments'])) {
                $query->whereIn('d.id', $filters['departments']);
            }

            // Apply cash_only filter
            if (isset($filters['cash_only']) && $filters['cash_only']) {
                $query->where('es.salary_disbursement_method', 'cash');
            }

            // Apply is_downloaded filter
            if (isset($filters['is_downloaded'])) {
                if ($filters['is_downloaded']) {
                    $query->where('eps.payslip_status', 'downloaded');
                } else {
                    $query->where('eps.payslip_status', '!=', 'downloaded');
                }
            }

            // Apply search filter (employee name or code)
            if (!empty($filters['search'])) {
                $search = $filters['search'];
                $query->where(function ($q) use ($search) {
                    $q->where('e.name', 'LIKE', "%{$search}%")
                        ->orWhere('e.employee_number', 'LIKE', "%{$search}%");
                });
            }

            return $query->select([
                'e.id as emp_id',
                'e.employee_number as emp_code',
                'e.name as emp_name',
                'e.first_name as first_name',
                'e.second_name as second_name',
                't.id as title_id',
                't.name as title_name',
                't.color as title_color',
                'eps.payslip_status',
                'b.id as branch_id',
                'b.name as branch_name',
                'es.salary_disbursement_method as pay_method'
            ])
                ->distinct()
                ->orderBy('e.name')
                ->get();

        } catch (\Exception $e) {
            Log::error('Error in getEmployees: ' . $e->getMessage());
            throw new \Exception('Failed to retrieve employees data: ' . $e->getMessage());
        }
    }

    /**
     * Update payslip status for successful downloads
     */
    public function markEmployeesAsDownloaded(array $employeeIds, array $payrollData): bool
    {
        try {
            if (empty($employeeIds)) {
                return true;
            }

            $updatedRows = DB::table('employee_payroll_summaries')
                ->where('payroll_id', $payrollData['payroll_id'])
                ->where('month', $payrollData['month'])
                ->where('year', $payrollData['year'])
                ->whereIn('employee_id', $employeeIds)
                ->update([
                    'payslip_status' => 'downloaded',
                    'updated_at' => now()
                ]);

            Log::info('Marked employees as downloaded in database', [
                'employee_count' => count($employeeIds),
                'employee_ids' => $employeeIds,
                'updated_rows' => $updatedRows,
                'payroll_id' => $payrollData['payroll_id'],
                'month' => $payrollData['month'],
                'year' => $payrollData['year']
            ]);

            return $updatedRows > 0;

        } catch (\Exception $e) {
            Log::error('Failed to mark employees as downloaded', [
                'error' => $e->getMessage(),
                'employee_ids' => $employeeIds,
                'payroll_data' => $payrollData
            ]);
            throw new \Exception('Failed to update download status: ' . $e->getMessage());
        }
    }
}

