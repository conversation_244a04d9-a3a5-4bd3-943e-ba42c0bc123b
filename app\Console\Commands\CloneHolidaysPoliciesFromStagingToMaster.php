<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CloneHolidaysPoliciesFromStagingToMaster extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:clone-holidays-policies-from-staging-to-master';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        try {
            // $public_holiday_policies_staging = DB::connection('staging_database')->table('public_holidays_policies')->get();

            // foreach ($public_holiday_policies_staging as $policy) {
            //     DB::table('public_holidays_policies')->insert([
            //         'id' => $policy->id,
            //         'policy_name' => $policy->policy_name,
            //         'compensation_method' => $policy->compensation_method,
            //         'compensation_pay_rate' => $policy->compensation_pay_rate,
            //         'compensation_holidays_rate' => $policy->compensation_holidays_rate,
            //         'expiration_months' => $policy->expiration_months,
            //         'company_id' => $policy->company_id,
            //     ]);
            //     Log::info("Inserted policy '{$policy->policy_name}' into production database.");
            // }

            // // Retrieve mapping of title IDs to public holiday policy IDs from staging database
            // $titlesPublicHolidayPolicyMap_staging = DB::connection('staging_database')->table('titles')->pluck('public_holidays_policy_id', 'id');

            // // Update titles table in production database
            // foreach ($titlesPublicHolidayPolicyMap_staging as $title_id => $policy_id) {
            //     DB::table('titles')->where('id', $title_id)->update([
            //         'public_holidays_policy_id' => $policy_id,
            //     ]);

            //     Log::info("Updated title with ID '{$title_id}' with public holiday policy ID");

            // }
            DB::beginTransaction();
            $publicHolidaysToggle = DB::connection('staging_database')->table('system_settings')->where('key', 'public_holidays_policy')->get();
            echo 'count of public holidays toggle '.count($publicHolidaysToggle);
            foreach ($publicHolidaysToggle as $toggle) {
                echo "Inserting toggle '{$toggle->key}' for company  '{$toggle->company_id}' into production database.";
                DB::table('system_settings')->insert([
                    'key' => $toggle->key,
                    'value' => $toggle->value,
                    'company_id' => $toggle->company_id,
                    'as_of_date' => $toggle->as_of_date,
                    'deleted_at' => $toggle->deleted_at ?? null,
                    'display_name' => $toggle->display_name ?? null,
                    'description' => $toggle->description ?? null,
                ]);
                echo "Inserted toggle '{$toggle->key}' into production database.";
                Log::info("Inserted toggle '{$toggle->key}' into production database.");
            }
            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());

        }
    }
}
