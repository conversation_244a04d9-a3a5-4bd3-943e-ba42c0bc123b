<?php

namespace App\Http\Middleware;

use App\Util\HttpStatusCodeUtil;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class ThrottleRequests
{
    public function handle(Request $request, Closure $next)
    {
        $currentMethod = $request->method();
        $userId = $request->user()?->id;
        $allowedMethods = ['GET', 'HEAD', 'OPTIONS'];

        // Skip GET requests
        if (! isset($userId) || in_array($currentMethod, $allowedMethods)) {
            return $next($request);
        }

        // Get the last request data from cache
        $lastRequest = Cache::get('last_request_'.$userId);

        // Check if the last request was non-GET and within 3 seconds
        if ($lastRequest &&
             ! in_array($lastRequest['method'], $allowedMethods) &&
            (microtime(true) - $lastRequest['time']) < 1) { // to prevent idempotent requests
            return response()->json([
                'message' => 'Too many requests. Please wait a moment and try again.',
            ], HttpStatusCodeUtil::TOO_MANY_ATTEMPTS);
        }

        // Store the current request data in cache
        Cache::put('last_request_'.$userId, [
            'time' => microtime(true),
            'method' => $currentMethod,
        ], 60); // Cache for 60 seconds

        return $next($request);
    }
}
