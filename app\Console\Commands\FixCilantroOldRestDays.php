<?php

namespace App\Console\Commands;

use App\Services\CompanySetup\FixCilantroOldRestDaysService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixCilantroOldRestDays extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:cilantro:rests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = ' ';

    public function __construct(private FixCilantroOldRestDaysService $service)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        // Log::info('Rest Days cron job is started at: ' . date('Y-m-d H:i:s'));

        DB::beginTransaction();
        try {
            $this->service->fix();
            DB::commit();
        } catch (Exception $e) {
            // \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
