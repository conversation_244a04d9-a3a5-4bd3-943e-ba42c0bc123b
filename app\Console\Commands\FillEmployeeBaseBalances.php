<?php

namespace App\Console\Commands;

use App\FeatureToggles\Unleash;
use App\Jobs\V1\FillEmployeeBaseBalancesJob;
use App\Models\Company;
use App\Services\LeaveManagement\FillEmployeeBalancesService as OldFillEmployeeBalancesService;
use App\Services\V1\LeaveManagement\FillEmployeeBalancesService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FillEmployeeBaseBalances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    // make companyId optional parameter which is an array
    protected $signature = 'leave:balances:fill {companyIds?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'create all balances of every leave type to every employee ';

    public function __construct(private OldFillEmployeeBalancesService $oldFillEmployeeBalances, private FillEmployeeBalancesService $fillEmployeeBalancesService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        ini_set('max_execution_time', 18000);
        $argument = $this->argument('companyIds');
        $companyIds = isset($argument) ? explode(',', $this->argument('companyIds')) : null;
        try {
            $unleash = app(Unleash::class);

            if ($unleash->isLeaveBalanceChangesFlagEnabled()) {
                // Get active companies
                $query = Company::where('status', 'active');
                if ($companyIds) {
                    $query->whereIn('id', $companyIds);
                }
                
                $companies = $query->get();
                foreach ($companies as $company) {
                    FillEmployeeBaseBalancesJob::dispatch(null, [$company->id]);
                }
                
                $this->info('Jobs have been dispatched for processing employee balances.');
            } else {
                $this->oldFillEmployeeBalances->fill();
            }
        } catch (Exception $e) {
            Log::error('Error filling employee balances', ['error' => $e->getMessage()]);
            \Sentry\captureException($e);
            dd($e);
        }
    }
}
