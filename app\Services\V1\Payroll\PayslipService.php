<?php

namespace App\Services\V1\Payroll;

use App\Repositories\NewCompanyRepository;
use App\Repositories\V1\Payroll\PayslipRepository;
use Spatie\Browsershot\Browsershot;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Collection;
use ZipArchive;
use Exception;

/**
 * PayslipService - Generate PDFs using Browsershot and create ZIP files
 * 
 * Core functionality:
 * 1. Load payslip PDF template
 * 2. Store temp PDF for each payslip
 * 3. Collect all PDFs in one ZIP folder
 */
class PayslipService
{
    private PayslipRepository $payslipRepository;
    private NewCompanyRepository $newCompanyRepository;
    private array $config;

    public function __construct(PayslipRepository $payslipRepository, NewCompanyRepository $newCompanyRepository)
    {
        $this->payslipRepository = $payslipRepository;
        $this->newCompanyRepository = $newCompanyRepository;
        $this->config = config('payslips', []);

        Log::info('PayslipService initialized', [
            'browsershot_enabled' => class_exists(Browsershot::class),
            'temp_dir' => $this->config['browsershot_options']['temp_dir'] ?? 'not_set'
        ]);
    }

    /**
     * Main method: Generate and store payslips as ZIP
     * 
     * @param array $data
     * @return array
     */
    public function generateAndStorePayslips(array $data): array
    {
        try {
            Log::info('Starting payslip generation', $data);

            // Step 1: Get payslip data
            $rawPayslips = $this->payslipRepository->getPayslips($data);
            if ($rawPayslips->isEmpty()) {
                throw new Exception('No payslip data found');
            }

            $payslips = $this->processPayslipsData($rawPayslips);
            $company = $this->newCompanyRepository->getCompanyInfo($data['company_id']);

            // Step 2: Create ZIP file path
            $zipInfo = $this->createZipPath($company, $data['month'], $data['year']);

            // Step 3: Generate PDFs and create ZIP
            $language = $data['print_lang'] ?? 'en';
            $this->generatePDFsAndCreateZip($payslips, $zipInfo, $company->name, $language);

            Log::info('Payslip generation completed', [
                'total_employees' => $payslips->count(),
                'zip_path' => $zipInfo['full_path']
            ]);

            return [
                'path' => $zipInfo['full_path'],
                'total_employees' => $payslips->count()
            ];

        } catch (Exception $e) {
            Log::error('Payslip generation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Get payslips data
     */
    public function getPayslips(array $data): Collection
    {
        return $this->payslipRepository->getPayslips($data);
    }

    /**
     * Get single payslip data by employee ID
     */
    public function getPayslip(int $employeeId): ?array
    {
        try {
            $rawPayslips = $this->payslipRepository->getPayslipByEmployeeId($employeeId);

            if ($rawPayslips->isEmpty()) {
                return null;
            }

            $employeeComponents = $rawPayslips;
            $firstRecord = $employeeComponents->first();

            $categories = $employeeComponents->groupBy('category_name')->map(function ($components) {
                return $components->map(function ($component) {
                    return [
                        'name' => $component->component_name,
                        'amount' => number_format($component->amount, 2)
                    ];
                })->toArray();
            })->toArray();

            return [
                'employee' => [
                    'id' => $firstRecord->employee_id,
                    'name' => $firstRecord->employee_name,
                    'code' => $firstRecord->code,
                    'title' => $firstRecord->title,
                    'net_salary' => number_format($firstRecord->net_salary, 2)
                ],
                'categories' => $categories,
                'tax_amount' => number_format($firstRecord->tax_amount, 2),
                'insurance_amount' => number_format($firstRecord->insurance_amount, 2),
            ];

        } catch (\Exception $e) {
            Log::error('Error getting single payslip', [
                'employee_id' => $employeeId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get stored payslips path
     */
    public function getStoredPayslipsPath(array $data): string
    {
        $company = $this->newCompanyRepository->getCompanyInfo($data['company_id']);
        $zipInfo = $this->createZipPath($company, $data['month'], $data['year']);
        return 'public/' . $zipInfo['full_path'];
    }

    /**
     * Process raw payslip data into structured format
     */
    private function processPayslipsData(Collection $rawPayslips): Collection
    {
        return $rawPayslips->groupBy('employee_id')->map(function ($employeeComponents) {
            $firstRecord = $employeeComponents->first();

            $categories = $employeeComponents->groupBy('category_name')->map(function ($components) {
                return $components->map(function ($component) {
                    return [
                        'name' => $component->component_name,
                        'amount' => number_format($component->amount, 2)
                    ];
                })->toArray();
            })->toArray();

            return [
                'employee' => [
                    'id' => $firstRecord->employee_id,
                    'name' => $firstRecord->employee_name,
                    'code' => $firstRecord->code,
                    'title' => $firstRecord->title,
                    'net_salary' => number_format($firstRecord->net_salary, 2)
                ],
                'categories' => $categories,
                'tax_amount' => number_format($firstRecord->tax_amount, 2),
                'insurance_amount' => number_format($firstRecord->insurance_amount, 2)
            ];
        })->values();
    }

    /**
     * Create ZIP file path structure
     */
    private function createZipPath(object $company, int $month, int $year): array
    {
        $companyCode = preg_replace('/[^a-zA-Z0-9_-]/', '_', $company->code);
        $baseDir = "companies/{$companyCode}/payslips";
        $fileName = sprintf('%02d_%d_payslips.zip', $month, $year);
        $fullPath = "{$baseDir}/{$fileName}";

        return [
            'base_dir' => $baseDir,
            'file_name' => $fileName,
            'full_path' => $fullPath,
            'absolute_path' => storage_path("app/public/{$fullPath}")
        ];
    }

    /**
     * Core method: Generate PDFs and create ZIP
     * This implements the 3 main requirements:
     * 1. Load payslip PDF template
     * 2. Store temp PDF for each payslip
     * 3. Collect all PDFs in one ZIP folder
     */
    private function generatePDFsAndCreateZip(Collection $payslips, array $zipInfo, string $companyName, string $language): void
    {
        // Ensure ZIP directory exists
        $zipDir = dirname($zipInfo['absolute_path']);
        if (!File::exists($zipDir)) {
            File::makeDirectory($zipDir, 0755, true);
        }

        // Remove existing ZIP file
        if (File::exists($zipInfo['absolute_path'])) {
            File::delete($zipInfo['absolute_path']);
        }

        // Create ZIP
        $zip = new ZipArchive();
        $result = $zip->open($zipInfo['absolute_path'], ZipArchive::CREATE);

        if ($result !== true) {
            throw new Exception("Cannot create ZIP file. Error code: {$result}");
        }

        // Create temp directory for individual PDFs
        $tempDir = $this->createTempDirectory();

        try {
            $tempPdfFiles = [];

            // Step 1 & 2: Generate temp PDF for each employee
            foreach ($payslips as $index => $payslip) {
                Log::info("Processing payslip {$index}/{$payslips->count()}", [
                    'employee_code' => $payslip['employee']['code']
                ]);

                // 1. Load payslip PDF template and generate PDF
                $pdfContent = $this->generateEmployeePDF($payslip, $companyName, $language);

                // 2. Store temp PDF for each payslip
                $tempPdfPath = $tempDir . "/payslip_{$payslip['employee']['code']}.pdf";
                file_put_contents($tempPdfPath, $pdfContent);
                $tempPdfFiles[] = $tempPdfPath;

                Log::info("Generated temp PDF", [
                    'file' => $tempPdfPath,
                    'size' => strlen($pdfContent) . ' bytes'
                ]);
            }

            // Step 3: Collect all PDFs in one ZIP folder
            foreach ($tempPdfFiles as $tempPdfPath) {
                $fileName = basename($tempPdfPath);
                $zip->addFile($tempPdfPath, $fileName);
                Log::debug("Added PDF to ZIP: {$fileName}");
            }

            $zip->close();

            // Clean up temp PDF files
            foreach ($tempPdfFiles as $tempPdfPath) {
                if (file_exists($tempPdfPath)) {
                    unlink($tempPdfPath);
                }
            }

            // Clean up temp directory
            if (is_dir($tempDir)) {
                rmdir($tempDir);
            }

            // Verify ZIP was created
            if (!File::exists($zipInfo['absolute_path'])) {
                throw new Exception('ZIP file was not created');
            }

            Log::info('ZIP file created successfully', [
                'path' => $zipInfo['absolute_path'],
                'size' => File::size($zipInfo['absolute_path']) . ' bytes',
                'total_pdfs' => count($tempPdfFiles)
            ]);

        } catch (Exception $e) {
            $zip->close();

            // Clean up temp files on error
            foreach ($tempPdfFiles ?? [] as $tempPdfPath) {
                if (file_exists($tempPdfPath)) {
                    unlink($tempPdfPath);
                }
            }

            if (isset($tempDir) && is_dir($tempDir)) {
                rmdir($tempDir);
            }

            if (File::exists($zipInfo['absolute_path'])) {
                File::delete($zipInfo['absolute_path']);
            }

            throw $e;
        }
    }

    /**
     * Generate PDF for single employee using Browsershot
     * This implements requirement 1: Load payslip PDF template
     */
    private function generateEmployeePDF(array $payslip, string $companyName, string $language): string
    {
        // Set the application locale for translations
        $previousLocale = app()->getLocale();
        app()->setLocale($language);

        try {
            // Get language configuration
            $langConfig = $this->config['supported_languages'][$language] ?? $this->config['supported_languages']['en'];

            // Prepare data for the template
            $data = [
                'employee' => $payslip['employee'],
                'categories' => $payslip['categories'],
                'tax_amount' => $payslip['tax_amount'],
                'insurance_amount' => $payslip['insurance_amount'],
                'company_name' => $companyName,
                'direction' => $langConfig['direction'] ?? 'ltr',
                'language' => $language,
                'month' => $language === 'ar' ? $this->getArabicMonth(date('n')) : date('F'),
                'year' => date('Y')
            ];

            // 1. Load payslip PDF template - Generate HTML from the view
            $html = view('admin.pdf-templates.payslip', $data)->render();

            Log::info('Generated HTML template', [
                'employee_code' => $payslip['employee']['code'],
                'html_length' => strlen($html),
                'language' => $language
            ]);

            // CRITICAL: Set environment variables for temp directory BEFORE creating Browsershot
            $options = $this->config['browsershot_options'] ?? [];
            $customTempDir = $options['temp_dir'] ?? null;

            if ($customTempDir && is_dir($customTempDir)) {
                // Store original environment variables
                $originalTmp = getenv('TMP');
                $originalTemp = getenv('TEMP');
                $originalTmpDir = getenv('TMPDIR');

                // Set environment variables to our custom temp directory
                putenv('TMP=' . $customTempDir);
                putenv('TEMP=' . $customTempDir);
                putenv('TMPDIR=' . $customTempDir);

                Log::info('Set environment temp variables', [
                    'custom_temp_dir' => $customTempDir,
                    'TMP' => getenv('TMP'),
                    'TEMP' => getenv('TEMP'),
                    'TMPDIR' => getenv('TMPDIR')
                ]);
            }

            // Generate PDF using Browsershot
            $browsershot = Browsershot::html($html);

            // Apply configuration from config file
            if ($customTempDir && is_dir($customTempDir)) {
                Log::info('Setting custom temp path for Browsershot', [
                    'temp_dir' => $customTempDir,
                    'exists' => is_dir($customTempDir),
                    'writable' => is_writable($customTempDir)
                ]);

                try {
                    $browsershot->setCustomTempPath($customTempDir);
                    Log::info('Custom temp path set successfully');
                } catch (\Exception $e) {
                    Log::error('Failed to set custom temp path', [
                        'error' => $e->getMessage(),
                        'temp_dir' => $customTempDir
                    ]);
                    // Continue without custom temp path
                }
            } else {
                Log::warning('No temp_dir configured in browsershot_options or directory does not exist');
            }

            if (isset($options['format'])) {
                $browsershot->format($options['format']);
            }

            if (isset($options['margins'])) {
                $browsershot->margins(...$options['margins']);
            }

            if (isset($options['timeout'])) {
                $browsershot->timeout($options['timeout']);
            }

            if (isset($options['show_background']) && $options['show_background']) {
                $browsershot->showBackground();
            }

            if (isset($options['wait_until_network_idle']) && $options['wait_until_network_idle']) {
                $browsershot->waitUntilNetworkIdle();
            }

            if (isset($options['chrome_args']) && is_array($options['chrome_args'])) {
                $browsershot->addChromiumArguments($options['chrome_args']);
            }

            // Debug: Log Browsershot configuration before PDF generation
            Log::info('About to generate PDF with Browsershot', [
                'employee_code' => $payslip['employee']['code'],
                'temp_dir_config' => $options['temp_dir'] ?? 'not_set',
                'chrome_args_count' => count($options['chrome_args'] ?? []),
                'browsershot_class' => get_class($browsershot)
            ]);

            try {
                $pdfContent = $browsershot->pdf();
                Log::info('PDF generation successful');
            } catch (\Exception $e) {
                Log::error('Browsershot PDF generation failed', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'employee_code' => $payslip['employee']['code'],
                    'temp_dir' => $customTempDir ?? 'not_set'
                ]);

                // Restore original environment variables on error
                if (isset($originalTmp, $originalTemp, $originalTmpDir)) {
                    putenv('TMP=' . ($originalTmp ?: ''));
                    putenv('TEMP=' . ($originalTemp ?: ''));
                    putenv('TMPDIR=' . ($originalTmpDir ?: ''));
                }

                // Try fallback approach with minimal configuration
                Log::info('Attempting fallback PDF generation with minimal config');
                try {
                    $fallbackBrowsershot = Browsershot::html($html);
                    $fallbackBrowsershot->format('A4')
                        ->margins(20, 20, 20, 20)
                        ->addChromiumArguments(['--no-sandbox', '--disable-gpu'])
                        ->timeout(30);

                    $pdfContent = $fallbackBrowsershot->pdf();
                    Log::info('Fallback PDF generation successful');
                } catch (\Exception $fallbackError) {
                    Log::error('Fallback PDF generation also failed', [
                        'fallback_error' => $fallbackError->getMessage()
                    ]);
                    throw $e; // Throw original error
                }
            }

            // Restore original environment variables after successful generation
            if (isset($originalTmp, $originalTemp, $originalTmpDir)) {
                putenv('TMP=' . ($originalTmp ?: ''));
                putenv('TEMP=' . ($originalTemp ?: ''));
                putenv('TMPDIR=' . ($originalTmpDir ?: ''));

                Log::info('Restored original environment variables');
            }

            Log::info('PDF generated successfully', [
                'employee_code' => $payslip['employee']['code'],
                'pdf_size' => strlen($pdfContent) . ' bytes'
            ]);

            return $pdfContent;

        } catch (\Exception $e) {
            Log::error('PDF generation failed', [
                'employee_code' => $payslip['employee']['code'],
                'error' => $e->getMessage(),
                'language' => $language
            ]);
            throw new Exception("Failed to generate PDF for employee {$payslip['employee']['code']}: " . $e->getMessage());
        } finally {
            // Restore previous locale
            app()->setLocale($previousLocale);
        }
    }

    /**
     * Create a temporary directory for PDF files
     */
    private function createTempDirectory(): string
    {
        $tempBase = $this->config['browsershot_options']['temp_dir'] ?? sys_get_temp_dir();

        // Ensure base temp directory exists with proper permissions
        if (!is_dir($tempBase)) {
            mkdir($tempBase, 0755, true);
        }

        $tempDir = $tempBase . '/payslips_' . uniqid();

        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        Log::info('Created temp directory for payslips', [
            'temp_dir' => $tempDir,
            'base_dir' => $tempBase,
            'writable' => is_writable($tempDir)
        ]);

        return $tempDir;
    }

    /**
     * Get Arabic month name
     */
    private function getArabicMonth(int $monthNumber): string
    {
        $arabicMonths = [
            1 => 'يناير',
            2 => 'فبراير',
            3 => 'مارس',
            4 => 'أبريل',
            5 => 'مايو',
            6 => 'يونيو',
            7 => 'يوليو',
            8 => 'أغسطس',
            9 => 'سبتمبر',
            10 => 'أكتوبر',
            11 => 'نوفمبر',
            12 => 'ديسمبر'
        ];

        return $arabicMonths[$monthNumber] ?? date('F');
    }
}
