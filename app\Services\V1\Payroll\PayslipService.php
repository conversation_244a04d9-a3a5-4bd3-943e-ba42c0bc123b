<?php

namespace App\Services\V1\Payroll;

use App\Repositories\NewCompanyRepository;
use App\Repositories\V1\Payroll\PayslipRepository;
use Spatie\Browsershot\Browsershot;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Collection;
use ZipArchive;
use Exception;

/**
 * Simple PayslipService - Generate PDFs and create ZIP
 */
class PayslipService
{
    private PayslipRepository $payslipRepository;
    private NewCompanyRepository $newCompanyRepository;
    private array $config;

    public function __construct(PayslipRepository $payslipRepository, NewCompanyRepository $newCompanyRepository)
    {
        $this->payslipRepository = $payslipRepository;
        $this->newCompanyRepository = $newCompanyRepository;

        // Load configuration from config/payslips.php
        $this->config = config('payslips');

        // Ensure we have the required configuration structure
        if (!isset($this->config['supported_languages'])) {
            $this->config['supported_languages'] = [
                'en' => ['direction' => 'ltr'],
                'ar' => ['direction' => 'rtl']
            ];
        }

        // Log configuration for debugging
        Log::info('PayslipService initialized with Browsershot', [
            'supported_languages' => array_keys($this->config['supported_languages'] ?? []),
            'browsershot_enabled' => isset($this->config['browsershot_options'])
        ]);
    }



    /**
     * Get payslips data
     */
    public function getPayslips(array $data): Collection
    {
        return $this->payslipRepository->getPayslips($data);
    }

    /**
     * Get single payslip data by employee ID
     */
    public function getPayslip(int $employeeId): ?array
    {
        try {
            // Get raw payslip data for specific employee
            $rawPayslips = $this->payslipRepository->getPayslipByEmployeeId($employeeId);

            if ($rawPayslips->isEmpty()) {
                return null;
            }

            // Process the data similar to processPayslipsData but for single employee
            $employeeComponents = $rawPayslips;
            $firstRecord = $employeeComponents->first();

            $categories = $employeeComponents->groupBy('category_name')->map(function ($components) {
                return $components->map(function ($component) {
                    return [
                        'name' => $component->component_name,
                        'amount' => number_format($component->amount, 2)
                    ];
                })->toArray();
            })->toArray();

            return [
                'employee' => [
                    'id' => $firstRecord->employee_id,
                    'name' => $firstRecord->employee_name,
                    'code' => $firstRecord->code,
                    'title' => $firstRecord->title,
                    'net_salary' => number_format($firstRecord->net_salary, 2)
                ],
                'categories' => $categories,
                'tax_amount' => number_format($firstRecord->tax_amount, 2),
                'insurance_amount' => number_format($firstRecord->insurance_amount, 2),
            ];

        } catch (\Exception $e) {
            Log::error('Error getting single payslip', [
                'employee_id' => $employeeId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Main method: Generate and store payslips as ZIP
     */
    public function generateAndStorePayslips(array $data): array
    {
        try {

            Log::info('Starting payslip generation', $data);

            // 1. Get data
            $rawPayslips = $this->payslipRepository->getPayslips($data);
            if ($rawPayslips->isEmpty()) {
                throw new Exception('No payslip data found');
            }

            $payslips = $this->processPayslipsData($rawPayslips);
            $company = $this->newCompanyRepository->getCompanyInfo($data['company_id']);

            // 2. Create ZIP file path
            $zipInfo = $this->createZipPath($company, $data['month'], $data['year']);

            // 3. Generate PDFs and create ZIP
            $language = $data['print_lang'] ?? 'en';
            Log::info('Payslip generation language', [
                'requested_language' => $data['print_lang'] ?? 'not_set',
                'final_language' => $language,
                'data_keys' => array_keys($data)
            ]);

            $this->generatePDFsAndCreateZip($payslips, $zipInfo, $company->name, $language);

            Log::info('Payslip generation completed', [
                'total_employees' => $payslips->count(),
                'zip_path' => $zipInfo['full_path']
            ]);

            return [
                'path' => $zipInfo['full_path'],
                'total_employees' => $payslips->count()
            ];

        } catch (Exception $e) {
            Log::error('Payslip generation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Process raw payslip data
     */
    private function processPayslipsData(Collection $rawPayslips): Collection
    {
        return $rawPayslips->groupBy('employee_id')->map(function ($employeeComponents) {
            $firstRecord = $employeeComponents->first();

            $categories = $employeeComponents->groupBy('category_name')->map(function ($components) {
                return $components->map(function ($component) {
                    return [
                        'name' => $component->component_name,
                        'amount' => number_format($component->amount, 2)
                    ];
                })->toArray();
            })->toArray();

            return [
                'employee' => [
                    'id' => $firstRecord->employee_id,
                    'name' => $firstRecord->employee_name,
                    'code' => $firstRecord->code,
                    'title' => $firstRecord->title,
                    'net_salary' => number_format($firstRecord->net_salary, 2)
                ],
                'categories' => $categories,
                'tax_amount' => number_format($firstRecord->tax_amount, 2),
                'insurance_amount' => number_format($firstRecord->insurance_amount, 2)
            ];
        })->values();
    }

    /**
     * Create ZIP file path structure
     */
    private function createZipPath(object $company, int $month, int $year): array
    {
        $companyCode = preg_replace('/[^a-zA-Z0-9_-]/', '_', $company->code);
        $baseDir = "companies/{$companyCode}/payslips";
        $fileName = sprintf('%02d_%d_payslips.zip', $month, $year);
        $fullPath = "{$baseDir}/{$fileName}";

        return [
            'base_dir' => $baseDir,
            'file_name' => $fileName,
            'full_path' => $fullPath,
            'absolute_path' => storage_path("app/public/{$fullPath}")
        ];
    }

    /**
     * Core method: Generate PDFs and create ZIP
     */
    private function generatePDFsAndCreateZip(Collection $payslips, array $zipInfo, string $companyName, string $language): void
    {
        // Ensure ZIP directory exists
        $zipDir = dirname($zipInfo['absolute_path']);
        if (!File::exists($zipDir)) {
            File::makeDirectory($zipDir, 0755, true);
        }

        // Remove existing ZIP file
        if (File::exists($zipInfo['absolute_path'])) {
            File::delete($zipInfo['absolute_path']);
        }

        // Create ZIP
        $zip = new ZipArchive();
        $result = $zip->open($zipInfo['absolute_path'], ZipArchive::CREATE);

        if ($result !== true) {
            throw new Exception("Cannot create ZIP file. Error code: {$result}");
        }

        try {
            // Generate PDF for each employee and add to ZIP
            foreach ($payslips as $payslip) {
                // 1. Generate PDF for employee
                $pdfContent = $this->generateEmployeePDF($payslip, $companyName, $language);

                // 2. Add PDF to ZIP
                $fileName = "payslip_{$payslip['employee']['code']}.pdf";

                $zip->addFromString($fileName, $pdfContent);

                Log::debug("Added PDF to ZIP: {$fileName}");
            }

            // 3. Store ZIP
            $zip->close();

            // Verify ZIP was created
            if (!File::exists($zipInfo['absolute_path'])) {
                throw new Exception('ZIP file was not created');
            }

            Log::info('ZIP file created successfully', [
                'path' => $zipInfo['absolute_path'],
                'size' => File::size($zipInfo['absolute_path']) . ' bytes'
            ]);

        } catch (Exception $e) {
            $zip->close();
            if (File::exists($zipInfo['absolute_path'])) {
                File::delete($zipInfo['absolute_path']);
            }
            throw $e;
        }
    }

    /**
     * Generate PDF for single employee
     */
    private function generateEmployeePDF(array $payslip, string $companyName, string $language): string
    {
        // Set the application locale for translations
        $previousLocale = app()->getLocale();
        app()->setLocale($language);

        $langConfig = $this->config['supported_languages'][$language] ?? $this->config['supported_languages']['en'];

        // Debug logging
        Log::info('Language configuration debug', [
            'language' => $language,
            'langConfig' => $langConfig,
            'direction' => $langConfig['direction'] ?? 'not_set',
            'full_config' => $this->config
        ]);

        $data = [
            'employee' => $payslip['employee'],
            'categories' => $payslip['categories'],
            'tax_amount' => $payslip['tax_amount'],
            'insurance_amount' => $payslip['insurance_amount'],
            'company_name' => $companyName,
            'direction' => $langConfig['direction'] ?? 'ltr',
            'language' => $language,
            'month' => $language === 'ar' ? $this->getArabicMonth(date('n')) : date('F'),
            'year' => date('Y')
        ];

        // Generate HTML from the view
        $html = view('admin.pdf-templates.payslip', $data)->render();

        // --- FIX FOR WINDOWS PERMISSION ERROR ---
        // Set custom temp directory to avoid system temp directory permission issues

        // 1. Create our main temp directory
        $tempDir = storage_path('app/temp');
        if (!File::isDirectory($tempDir)) {
            File::makeDirectory($tempDir, 0777, true, true);
        }

        // 2. Set environment variables BEFORE creating any Process instances
        // This affects both Browsershot and Symfony Process component
        $originalTmpDir = getenv('TMPDIR');
        $originalTmp = getenv('TMP');
        $originalTemp = getenv('TEMP');

        putenv('TMPDIR=' . $tempDir);
        putenv('TMP=' . $tempDir);
        putenv('TEMP=' . $tempDir);

        // Also set PHP's sys_get_temp_dir by updating $_ENV
        $_ENV['TMPDIR'] = $tempDir;
        $_ENV['TMP'] = $tempDir;
        $_ENV['TEMP'] = $tempDir;

        // Log the temp directory setup for debugging
        Log::info('Custom temp directory setup', [
            'custom_temp_dir' => $tempDir,
            'sys_get_temp_dir_before' => sys_get_temp_dir(),
        ]);

        // 3. Create Chrome-specific subdirectories for Chrome args
        $chromeDataDir = $tempDir . '/chrome-data';
        $chromeCacheDir = $tempDir . '/chrome-cache';

        foreach ([$chromeDataDir, $chromeCacheDir] as $dir) {
            if (!File::isDirectory($dir)) {
                File::makeDirectory($dir, 0777, true, true);
            }
        }

        try {
            // 4. Create Browsershot instance
            $browsershot = Browsershot::html($html);

            // 5. Set the custom temp path to prevent Windows permission issues
            $browsershot->setCustomTempPath($tempDir);

            // Apply configuration from config/payslips.php
            $options = $this->config['browsershot_options'] ?? [];

            if (isset($options['format'])) {
                $browsershot->format($options['format']);
            }
            if (isset($options['margins']) && is_array($options['margins'])) {
                $browsershot->margins(...$options['margins']);
            }
            if (isset($options['timeout'])) {
                $browsershot->timeout($options['timeout']);
            }
            if (isset($options['wait_until_network_idle']) && $options['wait_until_network_idle']) {
                $browsershot->waitUntilNetworkIdle();
            }
            if (isset($options['show_background']) && $options['show_background']) {
                $browsershot->showBackground();
            }

            // 6. Set Chrome args to use our directories
            $chromeArgs = $options['chrome_args'] ?? [];
            $chromeArgs[] = '--user-data-dir=' . $chromeDataDir;
            $chromeArgs[] = '--data-path=' . $chromeDataDir;
            $chromeArgs[] = '--disk-cache-dir=' . $chromeCacheDir;
            $chromeArgs[] = '--temp-dir=' . $tempDir;

            $browsershot->setOption('args', $chromeArgs);

            // 7. Generate the PDF
            $pdfContent = $browsershot->pdf();

        } catch (\Exception $e) {
            Log::error('Browsershot PDF generation failed with custom temp path', [
                'error' => $e->getMessage(),
                'temp_dir' => $tempDir,
                'temp_dir_exists' => File::exists($tempDir),
                'temp_dir_writable' => is_writable($tempDir),
                'chrome_data_dir' => $chromeDataDir,
                'chrome_data_dir_exists' => File::exists($chromeDataDir),
            ]);

            // Restore original environment variables even on error
            $this->restoreEnvironmentVariables($originalTmpDir, $originalTmp, $originalTemp);
            throw $e;
        }

        // Restore original environment variables
        $this->restoreEnvironmentVariables($originalTmpDir, $originalTmp, $originalTemp);

        // Restore the previous locale
        app()->setLocale($previousLocale);

        return $pdfContent;
    }

    /**
     * Get stored payslips path
     */
    public function getStoredPayslipsPath(array $data): string
    {
        $company = $this->newCompanyRepository->getCompanyInfo($data['company_id']);
        $zipInfo = $this->createZipPath($company, $data['month'], $data['year']);
        return 'public/' . $zipInfo['full_path'];
    }

    /**
     * Restore original environment variables
     */
    private function restoreEnvironmentVariables($originalTmpDir, $originalTmp, $originalTemp): void
    {
        // Restore environment variables
        if ($originalTmpDir !== false) {
            putenv('TMPDIR=' . $originalTmpDir);
            $_ENV['TMPDIR'] = $originalTmpDir;
        } else {
            putenv('TMPDIR');
            unset($_ENV['TMPDIR']);
        }

        if ($originalTmp !== false) {
            putenv('TMP=' . $originalTmp);
            $_ENV['TMP'] = $originalTmp;
        } else {
            putenv('TMP');
            unset($_ENV['TMP']);
        }

        if ($originalTemp !== false) {
            putenv('TEMP=' . $originalTemp);
            $_ENV['TEMP'] = $originalTemp;
        } else {
            putenv('TEMP');
            unset($_ENV['TEMP']);
        }
    }

    /**
     * Get Arabic month name
     */
    private function getArabicMonth(int $monthNumber): string
    {
        $arabicMonths = [
            1 => 'يناير',
            2 => 'فبراير',
            3 => 'مارس',
            4 => 'أبريل',
            5 => 'مايو',
            6 => 'يونيو',
            7 => 'يوليو',
            8 => 'أغسطس',
            9 => 'سبتمبر',
            10 => 'أكتوبر',
            11 => 'نوفمبر',
            12 => 'ديسمبر'
        ];

        return $arabicMonths[$monthNumber] ?? date('F');
    }
}
