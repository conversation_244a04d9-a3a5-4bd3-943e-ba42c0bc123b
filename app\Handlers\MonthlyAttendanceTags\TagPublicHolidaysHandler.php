<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Models\Timecard;
use Illuminate\Support\Carbon;

class TagPublicHolidaysH<PERSON><PERSON> implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        return [
            'name' => $employeeAttendance->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($employeeAttendance->from)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        $count = isset($tags[$employeeId]['tags']['public_holiday']) ?
            $tags[$employeeId]['tags']['public_holiday']['count'] + 1 : 1;

        return $count;
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return $employeeAttendance instanceof Timecard &&
            $employeeAttendance->attendance &&
            $employeeAttendance->attendance->entityTags &&
            $employeeAttendance->attendance->entityTags->pluck('tag')->contains('public_holiday');
    }

    public function handleTagUnit(): string
    {
        return 'days';
    }
}
