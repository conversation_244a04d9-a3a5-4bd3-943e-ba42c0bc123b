<?php

namespace App\Http\Requests\V1\Attendance;

use App\Enums\Attendance\AttendanceDeductionEnum;
use Illuminate\Foundation\Http\FormRequest;

class ToggleDeductionMethodRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'deduction_method' => 'required|in:'.AttendanceDeductionEnum::DEDUCT_MONTHLY->value.','.AttendanceDeductionEnum::DEDUCT_FROM_ANNUAL_LEAVE_BALANCE->value,
        ];
    }
}
