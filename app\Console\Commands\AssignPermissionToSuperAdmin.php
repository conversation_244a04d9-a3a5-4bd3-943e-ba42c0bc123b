<?php

namespace App\Console\Commands;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AssignPermissionToSuperAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:assign-permission-to-super-admin';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign permission to all Super Admin roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $permissionName = 'add_loan';

            $this->info("Starting to assign {$permissionName} permission to Super Admin roles...");

            DB::beginTransaction();

            // Find the permission
            $permission = Permission::where('name', $permissionName)
                ->where('guard_name', 'user-api')
                ->first();

            if (!$permission) {
                $this->error("{$permissionName} permission not found. Please run the migration first.");
                return 1;
            }

            $this->info("Found {$permissionName} permission with ID: " . $permission->id);

            // Find all roles with name 'Super Admin'
            $superAdminRoles = Role::where('name', 'Super Admin')->get();

            if ($superAdminRoles->isEmpty()) {
                $this->warn('No Super Admin roles found in the system.');
                DB::commit();
                return 0;
            }

            $this->info('Found ' . $superAdminRoles->count() . ' Super Admin roles');

            $assignedCount = 0;
            $alreadyHadCount = 0;

            foreach ($superAdminRoles as $role) {
                // Check if the role already has this permission
                if ($role->hasPermissionTo($permissionName, 'user-api')) {
                    // $this->info("Role '{$role->name}' (ID: {$role->id}, Company: {$role->company_id}) already has {$permissionName} permission");
                    $alreadyHadCount++;
                } else {
                    // Assign the permission to the role
                    $role->givePermissionTo($permission);
                    $this->info("Assigned {$permissionName} permission to role '{$role->name}' (ID: {$role->id}, Company: {$role->company_id})");
                    $assignedCount++;
                }
            }

            DB::commit();

            $this->info('');
            $this->info('Summary:');
            $this->info("- Total Super Admin roles found: {$superAdminRoles->count()}");
            $this->info("- Roles that received the permission: {$assignedCount}");
            $this->info("- Roles that already had the permission: {$alreadyHadCount}");
            $this->info('');
            $this->info("Successfully completed assigning {$permissionName} permission to Super Admin roles!");

            return 0;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error occurred while assigning permissions: ' . $e);
            Log::error('AssignPermissionToSuperAdmin command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }
}
