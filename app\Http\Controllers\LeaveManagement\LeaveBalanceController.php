<?php

namespace App\Http\Controllers\LeaveManagement;

use App\DomainData\EmployeeLeaveBalanceDto;
use App\Http\Controllers\Controller;
use App\Services\LeaveManagement\BusinessServices\GetBalancesFilterService;
use stdClass;

class LeaveBalanceController extends Controller
{
    use EmployeeLeaveBalanceDto;

    public function __construct(
        private GetBalancesFilterService $getBalancesFiltersService,
    ) {}

    public function getBalance(array $request, stdClass &$output): void
    {

        if (! isset($request['employee_id']) && ! is_null(config('globals.user')) && isset(config('globals.user')->employee_id)) {
            $request['employee_id'] = config('globals.user')->employee_id;
        }
        $request['page_size'] = 0;
        $request['related_objects'] = ['companyLeaveType', 'companyLeaveTypePolicy'];

        $request['with_restdays'] = false;
        $this->getBalancesFiltersService->perform($request, $output);
        $output->balance = $output->balances->data[0]->balances ?? [];
        unset($output->balances);
    }

    public function getAllBalances(array $request, stdClass &$output): void
    {
        $rules = $this->getBalancesFilterRules();

        if (! isset($request['page_size'])) {
            $request['page_size'] = config('globals.MAX_PAGE_SIZE');
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        $request['with_restdays'] = (isset($request['with_restdays']) && $request['with_restdays']) ? true : false;

        $this->getBalancesFiltersService->perform($request, $output);
    }

    public function getAllBalancesForWorker(array $request, stdClass &$output): void
    {
        $rules = $this->getBalancesFilterRules();

        if (! isset($request['page_size'])) {
            $request['page_size'] = config('globals.MAX_PAGE_SIZE');
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $request['employee_id'] = auth()->user()->employee_id;

        $request['with_restdays'] = false;
        $this->getBalancesFiltersService->perform($request, $output);
    }
}
