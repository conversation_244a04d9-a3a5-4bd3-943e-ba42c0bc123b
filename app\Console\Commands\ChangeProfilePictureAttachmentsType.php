<?php

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ChangeProfilePictureAttachmentsType extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'change:profile_picture_attachments_type';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = ' ';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        DB::beginTransaction();
        try {
            DB::table('attachments')
                ->where('attachable_type', 'profile-picture')
                ->update(['attachable_type' => 'employee']);

            DB::commit();
        } catch (Exception $e) {
            // \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
