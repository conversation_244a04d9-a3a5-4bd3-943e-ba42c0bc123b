<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employees_info', function (Blueprint $table) {
            $table->string('employment_type')->nullable();
            $table->string('place_of_birth')->nullable();
            $table->string('religion')->nullable();
            $table->string('personal_email')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employees_info', function (Blueprint $table) {
            $table->dropColumn('employment_type');
            $table->dropColumn('place_of_birth');
            $table->dropColumn('religion');
            $table->dropColumn('personal_email');
        });
    }
};
