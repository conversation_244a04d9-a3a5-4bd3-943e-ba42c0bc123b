<?php

namespace App\Http\Requests\V1\WorkerApp;

use App\Exceptions\UnprocessableException;
use App\FeatureToggles\Unleash;
use Illuminate\Foundation\Http\FormRequest;

class RequestOvertimeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $unleash = app(Unleash::class);

        if (! $unleash->isRequestOvertimeHoursEnabled()) {
            throw new UnprocessableException('overtime requests is not enabled');
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'reason' => [
                'required',
                'string',
            ],
            'date' => [
                'required',
                'date_format:Y-m-d',
                'after_or_equal:today',
            ],
            'employee_id' => [
                'required',
                'integer',
            ],
            'status' => [
                'string',
            ],
        ];

    }

    public function prepareForValidation()
    {
        $this->merge([
            'status' => 'pending',
            'employee_id' => $this->input('employee_id', auth()->user()->employee_id),
        ]);
    }
}
