<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\DB;

class UniqueNationalIdRule implements Rule
{
    protected ?int $exceptId;

    public function __construct(?int $exceptId = null)
    {
        $this->exceptId = $exceptId;
    }

    public function passes($attribute, $value): bool
    {
        if (empty($value)) {
            return true;
        }

        $query = DB::table('employees')
            ->where('national_id', $value)
            ->whereNull('deleted_at');

        if ($this->exceptId) {
            $query->where('id', '!=', $this->exceptId);
        }

        return !$query->exists();
    }

    public function message(): string
    {
        return __('validation.unique_national_id', ['attribute' => 'national ID']);
    }
}
