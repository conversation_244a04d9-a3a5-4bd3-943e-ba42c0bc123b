<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employee_payroll_summaries', function (Blueprint $table) {
            $table->enum('payslip_status', ['pending', 'downloaded'])->default('pending');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employee_payroll_summaries', function (Blueprint $table) {
            $table->dropColumn('payslip_status');
        });
    }
};
