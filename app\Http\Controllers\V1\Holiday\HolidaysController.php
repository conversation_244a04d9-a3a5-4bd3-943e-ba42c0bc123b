<?php

namespace App\Http\Controllers\V1\Holiday;

use App\Exports\HolidaysAttendanceBalanceExport;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\Holidays\GetAllHolidayAbsencesRequest;
use App\Http\Requests\V1\Holidays\GetAllHolidaysRequest;
use App\Http\Requests\V1\Holidays\GetHolidaysBalancesRequest;
use App\Http\Requests\V1\Holidays\GetHolidaysMonthlyViewRequest;
use App\Http\Requests\V1\Holidays\PayoutHolidayBalanceCompensationRequest;
use App\Http\Resources\V1\Holidays\PublicHolidaysAbsencesCollection;
use App\Http\Resources\V1\Holidays\PublicHolidaysBalancesCollection;
use App\Http\Resources\V1\Holidays\PublicHolidaysBalancesResource;
use App\Http\Resources\V1\Holidays\PublicHolidaysCollection;
use App\Services\V1\Holidays\PublicHolidayAbsenceService;
use App\Services\V1\Holidays\PublicHolidaysService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class HolidaysController extends NewController
{
    public function __construct(PublicHolidaysService $service, private PublicHolidayAbsenceService $publicHolidayAbsenceService)
    {
        parent::__construct($service);
    }

    public function getHolidayBalance(int $id)
    {
        $holidayBalance = $this->service->getHolidayBalance($id);

        return getResponseStructure([
            'data' => new PublicHolidaysBalancesResource($holidayBalance),
        ], HttpStatusCodeUtil::OK, 'Public Holidays Balance');
    }

    public function getAllHolidays(GetAllHolidaysRequest $request)
    {
        $data = $request->validated();
        if (! isset($data['year'])) {
            $data['year'] = date('Y');
        }
        if (! isset($data['page'])) {
            $data['page'] = 1;
        }
        // if (!isset($data['page_size'])) {
        //     $data['page_size'] = 10;
        // }
        $holidays = $this->service->getAllHolidays($data);

        $paginatedHolidays = (new PublicHolidaysCollection($holidays))->response()->getData();

        return getResponseStructure(['data' => $paginatedHolidays->data, 'pagination' => $paginatedHolidays?->meta ?? null],
            HttpStatusCodeUtil::OK, 'Available  Public Holidays');
    }

    public function getHolidaysMonthlyView(GetHolidaysMonthlyViewRequest $request)
    {
        $data = $request->validated();
        $holidays = $this->service->getHolidaysMonthlyView($data);

        return getResponseStructure([
            'data' => new PublicHolidaysCollection($holidays),
        ], HttpStatusCodeUtil::OK, 'Available Monthly Public Holidays');
    }

    public function getHolidaysBalances(GetHolidaysBalancesRequest $request)
    {

        $data = $request->validated();

        if (! isset($data['page'])) {
            $data['page'] = 1;
        }
        // if (!isset($data['page_size'])) {
        //     $data['page_size'] = 10;
        // }

        $balances = $this->service->getHolidaysBalances($data);

        $paginatedBalances = (new PublicHolidaysBalancesCollection($balances))->response()->getData();

        return getResponseStructure([
            'data' => $paginatedBalances->data,
            'pagination' => $paginatedBalances?->meta ?? null,
        ], HttpStatusCodeUtil::OK, 'Available Public Holidays Balances');
    }

    public function getHolidaysBalancesExport(GetHolidaysBalancesRequest $request)
    {

        $data = $request->validated();

        $balances = $this->service->getHolidaysBalances($data);

        return Excel::download(new HolidaysAttendanceBalanceExport($balances), 'HolidaysAttendanceBalance.xlsx', \Maatwebsite\Excel\Excel::XLSX);

    }

    public function getHolidaysBalancesCount()
    {
        $holidaysBalancesCount = $this->service->getHolidaysBalancesCount();

        return getResponseStructure([
            'data' => $holidaysBalancesCount,
        ], HttpStatusCodeUtil::OK, 'Public Holidays Balances Count');
    }

    public function cancelHolidayBalanceCompensation(int $id)
    {
        $this->service->cancelHolidayBalanceCompensation($id);

        return getResponseStructure([], HttpStatusCodeUtil::OK, 'Public Holidays Balance Compensation Cancelled Successfully');
    }

    public function payoutHolidayBalanceCompensation(int $id, PayoutHolidayBalanceCompensationRequest $request)
    {
        $data = $request->validated();

        DB::beginTransaction();
        try {
            $this->service->payoutHolidayBalanceCompensation($id, $data);
            DB::commit();

            return getResponseStructure([], HttpStatusCodeUtil::OK, 'Public Holidays Balance Compensation Paid Out Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function bulkPayoutHolidayBalanceCompensation()
    {
        DB::beginTransaction();
        try {
            $this->service->bulkPayoutHolidayBalanceCompensation();
            DB::commit();

            return getResponseStructure([], HttpStatusCodeUtil::OK, 'Bulk Public Holidays Balance Compensation Paid Out Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function getExpiringHolidaysAttendanceCounter(GetHolidaysMonthlyViewRequest $request)
    {
        $expiringHolidaysCounter = $this->service->getExpiringHolidaysAttendanceCounter($request->validated());

        return getResponseStructure([
            'data' => $expiringHolidaysCounter,
        ], HttpStatusCodeUtil::OK, 'Expiring Public Holidays Counter');
    }

    public function checkIfHolidaysComingSoon()
    {
        $endDate = date('Y-m-d', strtotime('+1 week'));
        $comingSoonHolidays = $this->service->checkIfHolidaysComingSoon($endDate);

        return getResponseStructure([
            'data' => $comingSoonHolidays,
        ], HttpStatusCodeUtil::OK, 'Public Holidays Coming Soon');

    }

    public function getAllHolidayAbsences(GetAllHolidayAbsencesRequest $request)
    {
        $data = $request->validated();

        if (! isset($data['page'])) {
            $data['page'] = 1;
        }

        $holidayAbsences = $this->publicHolidayAbsenceService->list($data);

        $paginatedHolidays = (new PublicHolidaysAbsencesCollection($holidayAbsences))->response()->getData();

        return getResponseStructure(['data' => $paginatedHolidays->data, 'pagination' => $paginatedHolidays?->meta ?? null],
            HttpStatusCodeUtil::OK, 'Public Holidays Absences :)');
    }
}
