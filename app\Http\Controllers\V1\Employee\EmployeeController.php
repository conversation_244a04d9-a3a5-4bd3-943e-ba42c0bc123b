<?php

namespace App\Http\Controllers\V1\Employee;

use App\Exceptions\UnauthorizedException;
use App\Exceptions\UnprocessableException;
use App\Exports\V1\EmployeeChangeLogsExport;
use App\Exports\V1\EmployeeRequestsExport;
use App\Handlers\WeeklySchedule\WeeklyScheduleEntriesHandler;
use App\Http\Controllers\NewController;
use App\Http\Requests\EnrollFaceIdRequest;
use App\Http\Requests\ListManagerMonthlyRequest;
use App\Http\Requests\V1\Employee\ChangeTitleRequest;
use App\Http\Requests\V1\EmployeeWeeklyScheduleRequest;
use App\Http\Requests\V1\ExportRequestsOnMeRequest;
use App\Http\Requests\V1\GetPendingOneMeRequest;
use App\Http\Requests\V1\GetRequestsOnMeRequest;
use App\Http\Resources\EmployeeRequestResource;
use App\Http\Resources\V1\ManagerMonthlyViewCollection;
use App\Models\User;
use App\Services\CompanySetup\BranchesService;
use App\Services\CompanySetup\BusinessServices\GetUserPermissionsService;
use App\Services\CompanySetup\CrudServices\EmployeeCrudService;
use App\Services\CompanySetup\EmployeeAggregationService;
use App\Services\CompanySetup\EmployeesService;
use App\Services\V1\Employee\ChangeTitleService;
use App\Services\V1\EmployeeProfile\EmployeeProfileService;
use App\Traits\RolesAndPermissionsTrait;
use App\Util\HttpStatusCodeUtil;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;

class EmployeeController extends NewController
{
    use RolesAndPermissionsTrait;

    public function __construct(
        private EmployeeCrudService $employeeCrudService,
        private BranchesService $branchesService,
        private EmployeesService $employeesService,
        private EmployeeAggregationService $employeeAggregationService,
        private GetUserPermissionsService $getUserPermissionsService,
        private ChangeTitleService $changeTitleService,
        private EmployeeProfileService $employeeProfileService
    ) {}

    public function employeeWeeklySchedule(EmployeeWeeklyScheduleRequest $request)
    {
        $output = new \stdClass;
        $this->employeeCrudService->timecardsAndLeaves($request->toArray(), $output);
        $employeeTimeCardAndLeaves = $output->employeesCollection;
        $handler = new WeeklyScheduleEntriesHandler;
        $result = $handler->handleEntries($employeeTimeCardAndLeaves, $request->toArray());

        return $this->response(['data' => $result, 'show_public_holidays' => $this->getIsEgyptianFlag()], 200, 'done');
    }

    public function requestsPendingOnMe(GetPendingOneMeRequest $request)
    {
        $data = $request->validated();

        if (! isset($data['filter'])) {
            $data['filter'] = 'all';
        }

        $result = $this->branchesService->requestsPendingOnEmployeeGroupedByBranches($data)[0] ?? [];

        // dd(sizeof($result[0]->toArray()), array_column($result[0]->toArray()['cicos'], 'id'));
        return $this->response(['data' => $result], 200, 'requests pending one me');

    }

    public function requestsPendingOnMeCount(GetPendingOneMeRequest $request)
    {
        $data = $request->validated();

        if (! isset($data['filter'])) {
            $data['filter'] = 'all';
        }

        $count = $this->branchesService->requestsPendingOnEmployeeGroupedByBranches($data)[1] ?? 0;

        return $this->response(['data' => $count], 200, 'count of requests pending one me');
    }

    public function getRequestsOnMe(GetRequestsOnMeRequest $request)
    {
        // overtimes,waive_deductions,unverified_attendances,employee_leave_requests,missing_info,penalties
        $data = $request->validated();
        if ($data['pending_on_me'] && $data['status'] != 'pending') {
            throw new UnprocessableException('status should be pending if pending_on_me is true');
        }

        $requests = $this->employeesService->getRequestsRelatedToMe($data);

        return $this->response([
            'data' => new EmployeeRequestResource($requests['data']->groupBy('requestable_type')),
            'pagination' => $requests['pagination'],

        ], 200, 'done');
    }

    public function requestsOnMeCount()
    {

        $count = $this->employeesService->getRequestsRelatedToMeCount();

        return $this->response(['data' => $count], 200, 'done');
    }

    public function exportRequestsOnMe(ExportRequestsOnMeRequest $request)
    {
        $data = $request->validated();
        $requests = $this->employeesService->getRequestsRelatedToMe($data);

        return Excel::download(new EmployeeRequestsExport($requests['data']), 'Requests.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function getManagerMonthlyView(ListManagerMonthlyRequest $request)
    {
        $data = $request->validated();
        $result = $this->employeeAggregationService->getManagerMonthlyView($data);

        return getResponseStructure(['data' => new ManagerMonthlyViewCollection($result)], HttpStatusCodeUtil::OK);
    }

    public function getRecentEmployeeCode()
    {
        $recentEmployeeCode = $this->employeesService->getRecentEmployeeCode();

        return $this->response(['data' => ['employee_code' => $recentEmployeeCode]], 200, 'done');
    }

    public function getMyPermissions()
    {
        $output = new \stdClass;
        $this->getUserPermissionsService->perform(['user_id' => auth()->user()->id], $output);

        return getResponseStructure(['data' => $output->permissions], 200, 'Current User Permissions');
    }

    public function transferLogs(Request $request): \Illuminate\Http\JsonResponse
    {
        $changeLogs = $this->employeesService->employeeChangeLogs($request->all());

        return getResponseStructure(['data' => $changeLogs->data, 'pagination' => $changeLogs->meta], 200, '');

    }

    public function transferLogsExport(Request $request): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $changeLogs = $this->employeesService->exportEmployeeChangeLogs($request->all());

        return Excel::download(new EmployeeChangeLogsExport($changeLogs), 'employee-change-logs-export.xlsx', \Maatwebsite\Excel\Excel::XLSX);

    }

    public function enrollFaceId(EnrollFaceIdRequest $request)
    {
        $data = $request->validated();
        $user = User::where('employee_id', $data['employee_id'])->first();
        if (! $user) {
            throw new UnprocessableException('User not found');
        }
        $user->face_id = $data['face_id'];
        $user->templates = $data['templates'];
        $user->save();
        $this->employeeProfileService->registerEmployeeFaceId($data);

        return $this->response(['data' => $user], 200, 'done');
    }

    public function changeTitle($employeeId, ChangeTitleRequest $request)
    {
        try {
            DB::transaction(function () use ($employeeId, $request) {
                $this->changeTitleService->changeTitleForEmployee($employeeId, $request->validated());
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Titles changed successfully'
            );
        } catch (Exception $e) {
            throw $e;
        }
    }

    public function generateTokenFaceId(Request $request)
    {
        $faceId = $request->input('face_id');

        if (! $faceId) {
            throw new UnauthorizedException('No face_id provided.');
        }

        $user = User::where('face_id', $faceId)->first();
        if (! $user) {
            throw new UnauthorizedException('Invalid face_id.');
        }

        // Optionally invalidate any existing token:
        // if ($currentToken = JWTAuth::getToken()) {
        //     JWTAuth::invalidate($currentToken);
        // }

        // Generate a new token for this user
        $expirationTime = 60 * 24 * 700; // e.g. 700 days
        JWTAuth::factory()->setTTL($expirationTime);
        $token = JWTAuth::fromUser($user);

        return $this->response(['data' => ['token' => $token]], 200, 'done');
    }
}
