<?php

namespace App\Console\Commands;

use App\Models\EmployeeSalary;
use App\Services\V1\Payroll\ConvertGrossToNetService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixNetSalaryIssue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-net-salary-issue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'copy net salary to gross salary then calculate the net salary for company id 4';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $employeesSalary = EmployeeSalary::where('company_id', 4)->get();
            $grossToNetService = new ConvertGrossToNetService;
            foreach ($employeesSalary as $employeeSalary) {
                if (! isset($employeeSalary->net_salary) || ! isset($employeeSalary->gross_salary)) {
                    $this->info('Employee ID: '.$employeeSalary->employee_id.'Net Salary or Gross Salary is null');

                    continue;
                }
                $socialInsurance = $employeeSalary?->social_insurance_salary ?? null;

                $newNetSalary = $grossToNetService->calculate($employeeSalary->gross_salary, $socialInsurance)['netSalary'] ?? null;
                if (is_null($newNetSalary)) {
                    $this->info('Employee ID: '.$employeeSalary->employee_id.'New Net Salary is null');

                    continue;
                }
                $this->info('Employee ID: '.$employeeSalary->employee_id.'Social Insurance: '.$socialInsurance.' New Net Salary: '.$newNetSalary.' Old Net Salary: '.$employeeSalary->net_salary.' Old Gross Salary: '.$employeeSalary->gross_salary);

                // $employeeSalary->gross_salary = $employeeSalary->net_salary;
                $employeeSalary->net_salary = $newNetSalary;
                $employeeSalary->save();
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
        }
    }
}
