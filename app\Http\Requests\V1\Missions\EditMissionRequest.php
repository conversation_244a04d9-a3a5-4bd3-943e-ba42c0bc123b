<?php

namespace App\Http\Requests\V1\Missions;

use App\Enums\Missions\MissionsEnum;
use App\Rules\V1\Missions\ValidateMissionTimeRange;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class EditMissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'mission_type' => [
                'required',
                'string',
                new Enum(MissionsEnum::class),
            ],
            'reason' => [
                'required',
                'string',
            ],
            'from' => [
                'required',
                'date_format:Y-m-d H:i:s',
            ],
            'to' => [
                'required',
                'date_format:Y-m-d H:i:s',
                'after:from',
                new ValidateMissionTimeRange($this->request->all()),
            ],
            'location' => [
                'required',
                'string',
            ],
        ];

    }
}
