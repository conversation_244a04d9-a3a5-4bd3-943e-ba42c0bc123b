<?php

namespace Tests\Feature\V1\Leaves;

use App\Jobs\V1\FillEmployeeBaseBalancesJob;
use App\Models\Company;
use App\Models\CompanyLeaveType;
use App\Models\CompanyLeaveTypePolicy;
use App\Models\Country;
use App\Models\Employee;
use App\Models\Title;
use App\Models\User;
use App\Repositories\NewCompanyLeaveTypeRepository;
use App\Traits\RefreshOnlyBluworksDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Bus;
use Tests\TestCase;
use Tests\Traits\DatabaseSetupTrait;
use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;

uses(TestCase::class, RefreshOnlyBluworksDatabase::class, DatabaseSetupTrait::class, WithFaker::class);

beforeEach(function () {
    // Setup database for tests
    $this->setUpDatabase();

    // Create Egypt as the test country with all required attributes
    $this->country = Country::factory()->create([
        'name_ar' => 'مصر',
        'name_en' => 'Egypt',
        'timezone' => 'Africa/Cairo',
        'dial_code' => '+20',
        'currency' => 'EGP',
    ]);

    // Create a test company with the Egypt country
    $this->company = Company::factory()->create([
        'country_id' => $this->country->id,
    ]);

    // Create a company admin user for testing
    $this->user = User::factory()->create([
        'company_id' => $this->company->id,
        'is_admin' => 1,
    ]);

    // Create titles for the company
    $this->titles = Title::factory()->count(3)->create([
        'company_id' => $this->company->id,
    ]);

    // Create default leave types for the company with unique names
    $this->annualLeave = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية',
        'name_en' => 'Annual Leave',
        'name' => 'annual_leave_' . uniqid(), // Ensure unique name
        'is_primary' => true,
        'balance_period' => 'calendar_year',
        'gender' => 'all',
    ]);

    $this->sickLeave = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة مرضية',
        'name_en' => 'Sick Leave',
        'name' => 'sick_leave_' . uniqid(), // Ensure unique name
        'is_primary' => true,
        'balance_period' => 'calendar_year',
        'gender' => 'all',
    ]);

    $this->restDay = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'يوم راحة',
        'name_en' => 'Rest Day',
        'name' => 'rest_day_' . uniqid(), // Ensure unique name
        'is_primary' => true,
        'balance_period' => 'calendar_month',
        'gender' => 'all',
    ]);

    // Link leave types to company through specific columns
    $this->company->update([
        'annual_leave_id' => $this->annualLeave->id,
        'sick_leave_id' => $this->sickLeave->id,
        'rest_day_leave_id' => $this->restDay->id,
    ]);

    // Create policies for each leave type
    CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $this->annualLeave->id,
        'base_balance' => 168, // 21 days * 8 hours
        'unit' => 'hours',
    ]);

    CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $this->sickLeave->id,
        'base_balance' => 240, // 30 days * 8 hours
        'unit' => 'hours',
    ]);

    CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $this->restDay->id,
        'base_balance' => 8, // 1 day * 8 hours
        'unit' => 'hours',
    ]);
});

afterEach(function () {
    $this->tearDownDatabase();
});

// Test getting all leave types
test('can get all company leave types', function () {
    // Create additional leave types for the company
    $additionalLeaveTypes = CompanyLeaveType::factory()->count(2)
        ->create([
            'company_id' => $this->company->id,
            'balance_period' => 'calendar_year',
        ]);

    foreach ($additionalLeaveTypes as $leaveType) {
        CompanyLeaveTypePolicy::factory()->create([
            'company_id' => $this->company->id,
            'company_leave_type_id' => $leaveType->id,
            'base_balance' => 168, // 21 days * 8 hours
            'unit' => 'hours',
        ]);
    }

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->get('/api/v1/admin/company/leaves');

    $response->assertStatus(200)
        ->assertJsonStructure([
            'version',
            'payload' => [
                'data' => [
                    '*' => [
                        'id',
                        'uuid',
                        'name' => [
                            'en',
                            'ar',
                        ],
                        'balance_period',
                        'base_balance',
                        'unit',
                        'gender',
                        'is_probation_allowed',
                        'min_requester_years',
                        'titles',
                        'all_titles',
                        'leave_deduction_percentage',
                    ],
                ],
            ],
            'message',
        ]);

    // Assert the response contains all leave types with correct data
    $response->assertJsonFragment([
        'name' => [
            'en' => 'Annual Leave',
            'ar' => 'إجازة سنوية',
        ],
        'balance_period' => 'calendar_year',
        'gender' => 'all',
    ]);

    $response->assertJsonFragment([
        'name' => [
            'en' => 'Sick Leave',
            'ar' => 'إجازة مرضية',
        ],
        'balance_period' => 'calendar_year',
        'gender' => 'all',
    ]);

    $response->assertJsonFragment([
        'name' => [
            'en' => 'Rest Day',
            'ar' => 'يوم راحة',
        ],
        'balance_period' => 'calendar_month',
        'gender' => 'all',
    ]);
});

// Test creating a new leave type
test('can create a new company leave type', function () {
    Bus::fake();

    // Prepare data for creating a new leave type
    $leaveTypeData = [
        'name_ar' => 'إجازة خاصة',
        'name_en' => 'Special Leave',
        'balance_period' => 'calendar_year',
        'base_balance' => 168, // 21 days * 8 hours
        'unit' => 'hours',
        'gender' => 'all',
        'is_probation_allowed' => true,
        'titles' => $this->titles->pluck('id')->toArray(),
        'min_requester_years' => 0,
        'leave_deduction_percentage' => 0,
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->post('/api/v1/admin/company/leaves', $leaveTypeData);

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Item has been set successfully',
        ]);

    // Find the newly created leave type
    $leaveType = CompanyLeaveType::where('company_id', $this->company->id)
        ->where('name_ar', 'إجازة خاصة')
        ->first();

    // Assert the FillEmployeeBaseBalancesJob was dispatched
    Bus::assertDispatched(FillEmployeeBaseBalancesJob::class);

    // Assert the leave type was created in the database
    assertDatabaseHas('company_leave_types', [
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة خاصة',
        'name_en' => 'Special Leave',
        'balance_period' => 'calendar_year',
    ]);

    // Assert the leave type policy was created
    assertDatabaseHas('company_leave_type_policies', [
        'company_id' => $this->company->id,
        'company_leave_type_id' => $leaveType->id,
        'base_balance' => 168, // 21 days * 8 hours
        'unit' => 'hours',
    ]);

    // Assert titles were associated with the policy
    $policy = CompanyLeaveTypePolicy::where('company_leave_type_id', $leaveType->id)->first();
    expect($policy->titles()->count())->toBe(count($this->titles));
});

// Test creating a leave type with invalid data
test('cannot create a company leave type with invalid data', function () {
    // Prepare invalid data for creating a new leave type (missing required fields)
    $leaveTypeData = [
        'name_ar' => 'إجازة خاصة',
        // Missing required fields
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->post('/api/v1/admin/company/leaves', $leaveTypeData);

    $response->assertStatus(422);

    // Assert no leave type was created
    assertDatabaseMissing('company_leave_types', [
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة خاصة',
    ]);
});

// Test updating a leave type
test('can update an existing company leave type', function () {
    Bus::fake();

    // Create a leave type to update
    $leaveType = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة اعتيادية',
        'name_en' => 'Regular Leave',
        'balance_period' => 'calendar_year',
    ]);

    $policy = CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $leaveType->id,
        'base_balance' => 168, // 21 days * 8 hours
        'unit' => 'hours',
    ]);

    // Associate titles with the policy
    $policy->titles()->attach($this->titles->pluck('id'));

    // Create employees with the titles
    $employees = Employee::factory()->count(2)->create([
        'company_id' => $this->company->id,
        'title_id' => $this->titles[0]->id,
    ]);

    // Prepare update data
    $updateData = [
        'name_ar' => 'إجازة سنوية معدلة',
        'name_en' => 'Modified Annual Leave',
        'base_balance' => 192, // 24 days * 8 hours
        'unit' => 'hours',
        'gender' => 'all',
        'titles' => [$this->titles[0]->id, $this->titles[1]->id],
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->put("/api/v1/admin/company/leaves/{$leaveType->id}", $updateData);

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Item has been set successfully',
        ]);

    // Debug: Check if any jobs were dispatched
    Bus::dispatched(FillEmployeeBaseBalancesJob::class);

    // Assert the FillEmployeeBaseBalancesJob was dispatched
    Bus::assertDispatched(FillEmployeeBaseBalancesJob::class);

    // Assert the job was dispatched with the correct queue and connection
    Bus::assertDispatched(FillEmployeeBaseBalancesJob::class, function ($job) {
        return $job->onQueue(config('globals.LEAVE_BALANCE_FILL.QUEUE')) &&
            $job->onConnection(config('globals.LEAVE_BALANCE_FILL.CONNECTION'));
    });

    // Assert the leave type was updated in the database
    assertDatabaseHas('company_leave_types', [
        'id' => $leaveType->id,
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية معدلة',
        'name_en' => 'Modified Annual Leave',
    ]);

    // Assert the policy was updated
    assertDatabaseHas('company_leave_type_policies', [
        'company_leave_type_id' => $leaveType->id,
        'base_balance' => 192, // 24 days * 8 hours
        'unit' => 'hours',
    ]);

    // Assert titles were updated correctly
    $policy->refresh();
    expect($policy->titles()->count())->toBe(2);
});

// Test updating a leave type with invalid data
test('cannot update a company leave type with invalid data', function () {
    // Create a leave type to update
    $leaveType = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة اعتيادية',
        'name_en' => 'Regular Leave',
        'balance_period' => 'calendar_year',
    ]);

    // Prepare invalid update data
    $updateData = [
        'balance_period' => 'invalid_period', // Invalid balance period
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->put("/api/v1/admin/company/leaves/{$leaveType->id}", $updateData);

    $response->assertStatus(422);

    // Assert the leave type was not updated
    assertDatabaseHas('company_leave_types', [
        'id' => $leaveType->id,
        'balance_period' => 'calendar_year',
    ]);
});

// Test deleting a leave type
test('can delete a company leave type', function () {
    // Create a leave type to delete
    $leaveType = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
    ]);

    $policy = CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $leaveType->id,
    ]);

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->delete("/api/v1/admin/company/leaves/{$leaveType->id}");

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Item deleted successfully',
        ]);

    // Since we're using soft deletes, check that the record is soft deleted
    expect(CompanyLeaveType::find($leaveType->id))->toBeNull();
    expect(CompanyLeaveType::withTrashed()->find($leaveType->id)->deleted_at)->not->toBeNull();

    // Check the associated policy is also soft deleted
    expect(CompanyLeaveTypePolicy::find($policy->id))->toBeNull();
    expect(CompanyLeaveTypePolicy::withTrashed()->find($policy->id)->deleted_at)->not->toBeNull();
});

// Test creating a leave type with special gender requirements
test('can create a leave type specific to a gender', function () {
    // Prepare data for creating a gender-specific leave type
    $leaveTypeData = [
        'name_ar' => 'إجازة أمومة',
        'name_en' => 'Maternity Leave',
        'balance_period' => 'calendar_year',
        'base_balance' => 720, // 90 days * 8 hours
        'unit' => 'hours',
        'gender' => 'female',
        'is_probation_allowed' => true,
        'titles' => $this->titles->pluck('id')->toArray(),
        'min_requester_years' => 0,
        'leave_deduction_percentage' => 0,
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->post('/api/v1/admin/company/leaves', $leaveTypeData);

    $response->assertStatus(200);

    // Assert the leave type was created with the correct gender setting
    $leaveType = CompanyLeaveType::where('company_id', $this->company->id)
        ->where('name_en', 'Maternity Leave')
        ->first();

    expect($leaveType->gender)->toBe('female');
});

test('can create annual leave type', function () {
    Bus::fake();

    // Prepare data for creating annual leave type
    $leaveTypeData = [
        'name_ar' => 'إجازة سنوية جديدة',
        'name_en' => 'New Annual Leave',
        'balance_period' => 'calendar_year',
        'base_balance' => 168, // 21 days * 8 hours
        'unit' => 'hours',
        'gender' => 'all',
        'is_probation_allowed' => true,
        'titles' => $this->titles->pluck('id')->toArray(),
        'min_requester_years' => 0,
        'leave_deduction_percentage' => 0,
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->post('/api/v1/admin/company/leaves', $leaveTypeData);

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Item has been set successfully',
            'version' => '1',
            'payload' => [
                'data' => []
            ]
        ]);

    // Find the newly created leave type
    $leaveType = CompanyLeaveType::where('company_id', $this->company->id)
        ->where('name_ar', 'إجازة سنوية جديدة')
        ->first();

    // Assert the leave type was created in the database
    assertDatabaseHas('company_leave_types', [
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية جديدة',
        'name_en' => 'New Annual Leave',
        'balance_period' => 'calendar_year',
    ]);

    // Assert the leave type policy was created
    assertDatabaseHas('company_leave_type_policies', [
        'company_id' => $this->company->id,
        'company_leave_type_id' => $leaveType->id,
        'base_balance' => 168, // 21 days * 8 hours
        'unit' => 'hours',
    ]);

    // Assert titles were associated with the policy
    $policy = CompanyLeaveTypePolicy::where('company_leave_type_id', $leaveType->id)->first();
    expect($policy->titles()->count())->toBe(count($this->titles));
});

test('can update annual leave type', function () {
    Bus::fake();

    // Create annual leave type to update (using a different name from the preset annual leave)
    $leaveType = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية إضافية',
        'name_en' => 'Additional Annual Leave',
        'balance_period' => 'calendar_year',
    ]);

    // Make sure this is not one of the preset leave types
    expect($leaveType->id)->not->toBe($this->annualLeave->id);
    expect($leaveType->id)->not->toBe($this->sickLeave->id);
    expect($leaveType->id)->not->toBe($this->restDay->id);

    $policy = CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $leaveType->id,
        'base_balance' => 168, // 21 days * 8 hours
        'unit' => 'hours',
    ]);

    // Associate titles with the policy
    $policy->titles()->attach($this->titles->pluck('id'));

    // Create employees with the titles
    $employees = Employee::factory()->count(2)->create([
        'company_id' => $this->company->id,
        'title_id' => $this->titles[0]->id,
    ]);

    // Prepare update data
    $updateData = [
        'name_ar' => 'إجازة سنوية إضافية معدلة',
        'name_en' => 'Modified Additional Annual Leave',
        'base_balance' => 192, // 24 days * 8 hours
        'unit' => 'hours',
        'gender' => 'all',
        'titles' => [$this->titles[0]->id, $this->titles[1]->id],
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->put("/api/v1/admin/company/leaves/{$leaveType->id}", $updateData);

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Item has been set successfully',
        ]);

    // Assert the FillEmployeeBaseBalancesJob was dispatched
    Bus::assertDispatched(FillEmployeeBaseBalancesJob::class);

    // Assert the job was dispatched with the correct queue and connection
    Bus::assertDispatched(FillEmployeeBaseBalancesJob::class, function ($job) {
        return $job->onQueue(config('globals.LEAVE_BALANCE_FILL.QUEUE')) &&
            $job->onConnection(config('globals.LEAVE_BALANCE_FILL.CONNECTION'));
    });

    // Assert the leave type was updated in the database
    assertDatabaseHas('company_leave_types', [
        'id' => $leaveType->id,
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية إضافية معدلة',
        'name_en' => 'Modified Additional Annual Leave',
    ]);

    // Assert the policy was updated
    assertDatabaseHas('company_leave_type_policies', [
        'company_leave_type_id' => $leaveType->id,
        'base_balance' => 192, // 24 days * 8 hours
        'unit' => 'hours',
    ]);

    // Assert titles were updated correctly
    $policy->refresh();
    expect($policy->titles()->count())->toBe(2);
});

test('cannot create annual leave type with invalid data', function () {
    // Prepare invalid data for creating annual leave type
    $leaveTypeData = [
        'name_ar' => 'إجازة سنوية غير صالحة',
        'balance_period' => 'invalid_period',
        'base_balance' => -1, // Invalid negative balance
        'unit' => 'invalid_unit',
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->post('/api/v1/admin/company/leaves', $leaveTypeData);

    $response->assertStatus(422);

    // Assert no leave type was created
    assertDatabaseMissing('company_leave_types', [
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية غير صالحة',
    ]);
});

test('can delete annual leave type', function () {
    // Create annual leave type to delete
    $leaveType = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية',
        'name_en' => 'Annual Leave',
        'balance_period' => 'calendar_year',
    ]);

    $policy = CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $leaveType->id,
        'base_balance' => 168, // 21 days * 8 hours
        'unit' => 'hours',
    ]);

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->delete("/api/v1/admin/company/leaves/{$leaveType->id}");

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Item deleted successfully',
        ]);

    // Since we're using soft deletes, check that the record is soft deleted
    expect(CompanyLeaveType::find($leaveType->id))->toBeNull();
    expect(CompanyLeaveType::withTrashed()->find($leaveType->id)->deleted_at)->not->toBeNull();

    // Check the associated policy is also soft deleted
    expect(CompanyLeaveTypePolicy::find($policy->id))->toBeNull();
    expect(CompanyLeaveTypePolicy::withTrashed()->find($policy->id)->deleted_at)->not->toBeNull();
});

test('can add titles to annual leave type', function () {
    Bus::fake();

    // Create annual leave type
    $leaveType = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية جديدة', // Changed name to differentiate from preset annual leave
        'name_en' => 'New Annual Leave', // Changed name to differentiate from preset annual leave
        'balance_period' => 'calendar_year',
    ]);

    $policy = CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $leaveType->id,
        'base_balance' => 168, // 21 days * 8 hours
        'unit' => 'hours',
    ]);

    // Initially attach only one title
    $policy->titles()->attach($this->titles[0]->id);

    // Create additional titles
    $newTitles = Title::factory()->count(2)->create([
        'company_id' => $this->company->id,
    ]);

    // Create employees with the new titles
    $employees = collect();
    foreach ($newTitles as $title) {
        $employees->push(
            Employee::factory()->create([
                'company_id' => $this->company->id,
                'title_id' => $title->id,
            ])
        );
    }

    // Prepare update data to add new titles
    $updateData = [
        'name_ar' => 'إجازة سنوية جديدة', // Keep same name as created
        'name_en' => 'New Annual Leave', // Keep same name as created
        'base_balance' => 168,
        'unit' => 'hours',
        'gender' => 'all',
        'titles' => array_merge(
            [$this->titles[0]->id], // Keep existing title
            $newTitles->pluck('id')->toArray() // Add new titles
        ),
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->put("/api/v1/admin/company/leaves/{$leaveType->id}", $updateData);

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Item has been set successfully',
        ]);

    // Assert the FillEmployeeBaseBalancesJob was dispatched
    Bus::assertDispatched(FillEmployeeBaseBalancesJob::class, function ($job) {
        return $job->onQueue(config('globals.LEAVE_BALANCE_FILL.QUEUE')) &&
            $job->onConnection(config('globals.LEAVE_BALANCE_FILL.CONNECTION'));
    });

    // Assert titles were updated correctly
    $policy->refresh();
    expect($policy->titles()->count())->toBe(3); // Original + 2 new titles

    // Assert specific titles are attached
    foreach ($newTitles as $title) {
        expect($policy->titles->contains($title->id))->toBeTrue();
    }
});

test('can remove titles from annual leave type', function () {
    Bus::fake();

    // Create annual leave type
    $leaveType = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية مخصصة', // Changed name
        'name_en' => 'Custom Annual Leave', // Changed name
        'balance_period' => 'calendar_year',
    ]);

    $policy = CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $leaveType->id,
        'base_balance' => 168, // 21 days * 8 hours
        'unit' => 'hours',
    ]);

    // Initially attach all titles
    $policy->titles()->attach($this->titles->pluck('id'));

    // Create employees with the titles
    $employees = Employee::factory()->count(2)->create([
        'company_id' => $this->company->id,
        'title_id' => $this->titles[0]->id,
    ]);

    // Prepare update data to remove some titles
    $updateData = [
        'name_ar' => 'إجازة سنوية مخصصة', // Keep same name
        'name_en' => 'Custom Annual Leave', // Keep same name
        'base_balance' => 168,
        'unit' => 'hours',
        'gender' => 'all',
        'titles' => [$this->titles[0]->id], // Keep only first title
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->put("/api/v1/admin/company/leaves/{$leaveType->id}", $updateData);

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Item has been set successfully',
        ]);

    // Assert the FillEmployeeBaseBalancesJob was dispatched
    Bus::assertDispatched(FillEmployeeBaseBalancesJob::class);

    // Assert titles were updated correctly
    $policy->refresh();
    expect($policy->titles()->count())->toBe(1); // Only one title remains

    // Assert specific titles are removed
    expect($policy->titles->contains($this->titles[1]->id))->toBeFalse();
    expect($policy->titles->contains($this->titles[2]->id))->toBeFalse();
});

test('can update titles for annual leave type with existing employees', function () {
    Bus::fake();

    // Create annual leave type
    $leaveType = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية عادية', // Changed name
        'name_en' => 'Regular Annual Leave', // Changed name
        'balance_period' => 'calendar_year',
    ]);

    $policy = CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $leaveType->id,
        'base_balance' => 168, // 21 days * 8 hours
        'unit' => 'hours',
    ]);

    // Initially attach all titles
    $policy->titles()->attach($this->titles->pluck('id'));

    // Create employees with different titles
    $employees = collect([
        Employee::factory()->create([
            'company_id' => $this->company->id,
            'title_id' => $this->titles[0]->id,
        ]),
        Employee::factory()->create([
            'company_id' => $this->company->id,
            'title_id' => $this->titles[1]->id,
        ]),
    ]);

    // Create new title
    $newTitle = Title::factory()->create([
        'company_id' => $this->company->id,
    ]);

    // Prepare update data to modify titles
    $updateData = [
        'name_ar' => 'إجازة سنوية عادية', // Keep same name
        'name_en' => 'Regular Annual Leave', // Keep same name
        'base_balance' => 168,
        'unit' => 'hours',
        'gender' => 'all',
        'titles' => array_merge(
            [$this->titles[0]->id], // Keep existing title
            [$newTitle->id], // Add new title
        ),
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->put("/api/v1/admin/company/leaves/{$leaveType->id}", $updateData);

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Item has been set successfully',
        ]);

    // Assert the FillEmployeeBaseBalancesJob was dispatched
    Bus::assertDispatched(FillEmployeeBaseBalancesJob::class, function ($job) {
        return $job->onQueue(config('globals.LEAVE_BALANCE_FILL.QUEUE')) &&
            $job->onConnection(config('globals.LEAVE_BALANCE_FILL.CONNECTION'));
    });

    // Assert titles were updated correctly
    $policy->refresh();
    expect($policy->titles()->count())->toBe(2); // Original + 2 new titles

    // Assert specific titles are attached/detached
    expect($policy->titles->contains($this->titles[0]->id))->toBeTrue(); // Kept
    expect($policy->titles->contains($this->titles[1]->id))->toBeFalse(); // Removed
    expect($policy->titles->contains($newTitle->id))->toBeTrue(); // Added
});

test('can create secondary leave type linked to primary leave type', function () {
    Bus::fake();

    // Create primary leave type
    $primaryLeaveType = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية رئيسية',
        'name_en' => 'Primary Annual Leave',
        'name' => 'primary_annual_leave_' . uniqid(),
        'balance_period' => 'calendar_year',
        'is_primary' => 1,
        'uuid' => (string)\Str::uuid(),
    ]);

    // Create policy for primary leave type
    $primaryPolicy = CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $primaryLeaveType->id,
        'base_balance' => 168,
        'unit' => 'hours',
    ]);

    // Attach titles to primary policy
    $primaryPolicy->titles()->attach($this->titles->pluck('id'));

    // Create secondary leave type directly using repository
    $secondaryLeaveType = app(NewCompanyLeaveTypeRepository::class)->add([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية ثانوية',
        'name_en' => 'Secondary Annual Leave',
        'name' => 'secondary_annual_leave_' . uniqid(),
        'balance_period' => 'calendar_year',
        'is_primary' => 0,
        'secondary_company_leave_type_id' => $primaryLeaveType->id,
        'gender' => 'all',
        'uuid' => (string)\Str::uuid(),
    ]);

    // Create policy for secondary leave type
    $secondaryPolicy = CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $secondaryLeaveType->id,
        'base_balance' => 168,
        'unit' => 'hours',
    ]);

    // Attach titles to secondary policy
    $secondaryPolicy->titles()->attach($this->titles->pluck('id'));

    // Assert the secondary leave type was created
    assertDatabaseHas('company_leave_types', [
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية ثانوية',
        'name_en' => 'Secondary Annual Leave',
        'is_primary' => 0,
        'secondary_company_leave_type_id' => $primaryLeaveType->id,
    ]);

    // Assert the policy was created
    assertDatabaseHas('company_leave_type_policies', [
        'company_id' => $this->company->id,
        'company_leave_type_id' => $secondaryLeaveType->id,
        'base_balance' => 168,
        'unit' => 'hours',
    ]);

    // Assert titles were attached to the policy
    expect($secondaryPolicy->titles()->count())->toBe(count($this->titles));
});

test('cannot create leave type with invalid gender value', function () {
    // Prepare data with invalid gender
    $leaveTypeData = [
        'name_ar' => 'إجازة سنوية غير صالحة',
        'name_en' => 'Invalid Annual Leave',
        'name' => 'invalid_annual_leave_' . uniqid(),
        'balance_period' => 'calendar_year',
        'base_balance' => 168,
        'unit' => 'hours',
        'gender' => 'invalid_gender', // Invalid gender value
        'titles' => $this->titles->pluck('id')->toArray(),
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->post('/api/v1/admin/company/leaves', $leaveTypeData);

    $response->assertStatus(422);

    // Assert no leave type was created
    assertDatabaseMissing('company_leave_types', [
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية غير صالحة',
    ]);
});

test('can update leave type with all fields', function () {
    Bus::fake();

    // Create initial leave type
    $leaveType = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة عارضة',
        'name_en' => 'Casual Leave',
        'balance_period' => 'calendar_year',
        'is_primary' => 1,
        'gender' => 'all',
        'leave_deduction_percentage' => '0.00',
    ]);

    // Create initial policy
    $policy = CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $leaveType->id,
        'base_balance' => 168,
        'unit' => 'hours',
    ]);

    // Attach titles to policy
    $policy->titles()->attach($this->titles->pluck('id'));

    // Prepare update data
    $updateData = [
        'name_ar' => 'إجازة عارضة معدلة',
        'name_en' => 'Modified Casual Leave',
        'balance_period' => 'calendar_year',
        'base_balance' => 192,
        'unit' => 'hours',
        'gender' => 'male',
        'is_primary' => 1,
        'leave_deduction_percentage' => 25.50, // Send as percentage (25.50%)
        'titles' => $this->titles->pluck('id')->toArray(),
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->put("/api/v1/admin/company/leaves/{$leaveType->id}", $updateData);

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Item has been set successfully',
        ]);

    // Assert the leave type was updated
    assertDatabaseHas('company_leave_types', [
        'id' => $leaveType->id,
        'name_ar' => 'إجازة عارضة معدلة',
        'name_en' => 'Modified Casual Leave',
        'balance_period' => 'calendar_year',
        'is_primary' => 1,
        'gender' => 'male',
        'leave_deduction_percentage' => '0.26', // After division by 100
    ]);

    // Assert the policy was updated
    assertDatabaseHas('company_leave_type_policies', [
        'company_leave_type_id' => $leaveType->id,
        'base_balance' => 192,
        'unit' => 'hours',
    ]);

    // Assert timestamps are updated
    $leaveType->refresh();
    expect($leaveType->updated_at)->toBeGreaterThanOrEqual($leaveType->created_at);
});

// Test that preset leave type names cannot be changed
test('cannot update names of preset leave types', function () {
    Bus::fake();

    // Try to update annual leave name (which is a preset leave type)
    $updateData = [
        'name_ar' => 'إجازة سنوية معدلة',
        'name_en' => 'Modified Annual Leave',
        'base_balance' => 192, // 24 days * 8 hours
        'unit' => 'hours',
        'gender' => 'all',
    ];

    // Make the request as the company admin
    $response = actingAs($this->user, 'user-api')
        ->put("/api/v1/admin/company/leaves/{$this->annualLeave->id}", $updateData);

    $response->assertStatus(200);

    // Assert the leave type name was NOT updated in the database
    assertDatabaseHas('company_leave_types', [
        'id' => $this->annualLeave->id,
        'name_ar' => 'إجازة سنوية', // Original name
        'name_en' => 'Annual Leave', // Original name
    ]);

    // Assert other properties were updated
    assertDatabaseHas('company_leave_type_policies', [
        'company_leave_type_id' => $this->annualLeave->id,
        'base_balance' => 192, // Updated base balance
    ]);

});
