<?php

namespace App\Jobs;

use App\Exceptions\UnprocessableException;
use App\Models\Employee;
use App\Models\Role;
use App\Models\Title;
use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use App\Traits\V1\AutoApproveTrait;
use App\Traits\V1\EmployeeRequestsTrait;
use App\Traits\V1\NotificationRedirection;
use App\Traits\WorkflowTrait;
use App\Util\EmployeeUtil;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AssignApprovalsJob implements ShouldQueue
{
    use AutoApproveTrait, Dispatchable, EmployeeRequestsTrait, InteractsWithQueue, NotificationRedirection, Queueable, SerializesModels, WorkflowTrait;

    public $tries = 250;

    public $timeout = 120;

    protected $data;

    protected $autoApprovedRequests;

    public function __construct($data)
    {
        $this->data = $data;
        $this->autoApprovedRequests = [
            config('globals.REQUEST_WORKFLOW_TYPES.LEAVE'),
            config('globals.REQUEST_WORKFLOW_TYPES.MISSIONS'),
            config('globals.REQUEST_WORKFLOW_TYPES.WAIVE_LATE_DEDUCTION'),
            config('globals.REQUEST_WORKFLOW_TYPES.EDIT_ATTENDANCE_DEDUCTION'),
        ];
    }

    public function handle()
    {
        try {
            Log::info('Assign Approvals Job started');
            $this->data['title'] = Title::find($this->data['title_id']);
            $this->data['employee'] = Employee::find($this->data['employee_id']);

            $requestWorkflows = $this->getRequestGroupWithWorkflows($this->data['title'], $this->data['request_type']);

            if (is_null($requestWorkflows)) {
                Log::info('Request Group not found for this title id: '.$this->data['title_id'].
                    ' and request type: '.$this->data['request_type'].' and request id: '.$this->data['request']->id);
                $this->assignRequestToSuperAdmin($this->data);
            } else {
                $this->assignRequestToWorkflowApprovals($requestWorkflows, $this->data);
            }

            if (! isset($this->data['is_auto_approve_applied']) || $this->data['is_auto_approve_applied'] == 0) {
                Log::info('inside auto approve condition');

                $this->data['request']->load(['employeeApproves.users.employee' => function ($q) {
                    $q->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                        ->with('branches', function ($q) {
                            $q->select('branch_employee.id', 'branch_id');
                        });
                }]);
                $this->redirectNotificationsAfterRequestCreated($this->data['request'], $this->data['employee']);
                Log::info('finished assign approvals job');
            }
            

            if(in_array($this->data['workflow_type'], $this->autoApprovedRequests)){
                $this->handleAutoApproveIfExist($this->data['request']);
            }
        } catch (\Exception $e) {
            Log::info('Error in AssignApprovalsJob');
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function getRequestGroupWithWorkflows($title, $requestType)
    {
        $requestGroup = $title->requestGroup;
        if (! $requestGroup) {
            return null;
        }
        Log::info('requestGroup: '.$requestGroup->id);

        $approvalCycles = $requestGroup->requestWorkflows()->where('type', $requestType)->whereNull('deleted_at')->orderBy('order', 'desc')->get();
        Log::info('approvalCycles: '.$approvalCycles->count());

        if ($approvalCycles->count() == 0) {
            return null;
        }

        return $approvalCycles;
    }

    public function assignRequestToWorkflowApprovals($requestWorkflows, &$data)
    {
        $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $data['auto_approve'] = in_array($data['workflow_type'], $this->autoApprovedRequests) && $this->canAutoApprove($data['employee']);
        $data['is_auto_approve_applied'] = 0;
        Log::info('inside assign request to workflow approvals');
        foreach ($requestWorkflows as $requestWorkflow) {
            $approvalData = $this->prepareApprovalData($requestWorkflow, $data);
            if (! $workflowApprovalCycleRepository->checkIfApprovalExists($approvalData)) {
                $workflowApprovalCycleRepository->add($approvalData);
            }
        }
    }

    public function prepareApprovalData($workflow, &$data)
    {
        Log::info($workflow->role_id);

        $date = $this->getApprovalDate($data);

        return [
            'status' => $this->getApprovalStatus($workflow, $data),
            'date' => $date,
            'role_id' => $workflow->role_id,
            'requestable_id' => $data['request']->id,
            'requestable_type' => $data['workflow_type'],
            'request_workflow_id' => $workflow->id,
            'employee_id' => $data['employee_id'],
            'company_id' => $workflow->company_id,
            'branch_id' => $data['employee']->branch_id,
            'department_id' => $data['title']->department_id,
            'order' => $workflow->order,
            'operator' => $workflow->operator,
            'sub_department_id' => $data['title']->sub_department_id,
        ];
    }

    public function getApprovalStatus($workflow, &$data)
    {
        if (isset($data['auto_approve']) && $data['auto_approve'] == true && in_array($workflow->role_id, $data['requester_role_ids'])) {
            $data['is_auto_approve_applied'] = $data['is_auto_approve_applied'] + 1;

            return 'approved';
        } elseif (isset($data['auto_approve']) && isset($data['requester_role_ids']) && ! in_array($workflow->role_id, $data['requester_role_ids']) && $data['is_auto_approve_applied'] > 0) {
            return 'skipped';
        } else {
            return 'pending';
        }
    }

    public function assignRequestToSuperAdmin(&$data)
    {
        $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $data['is_auto_approve_applied'] = 0;
        $superAdminRole = Role::where('name', 'Super Admin')
            ->where('company_id', $data['employee']->company_id)
            ->first();
        Log::info('superAdminRoleId: '.$superAdminRole->id);
        $approvalData = $this->prepareSuperAdminApprovalData($superAdminRole, $data);
        Log::info('approvalData: '.json_encode($approvalData));
        $workflowApprovalCycleRepository->add($approvalData);
    }

    public function prepareSuperAdminApprovalData($superAdminRole, &$data)
    {
        $date = $this->getApprovalDate($data);
        // Log::info('super admin data: '.json_encode($data));
        return [
            'status' => isset($data['requester_role_ids']) && in_array($data['workflow_type'], $this->autoApprovedRequests) ? $this->checkSuperAdminActionStatus($superAdminRole->id, $data['requester_role_ids']) : 'pending',
            'date' => $date,
            'role_id' => $superAdminRole->id,
            'requestable_id' => $data['request']->id,
            'requestable_type' => $data['workflow_type'],
            'request_workflow_id' => null,
            'employee_id' => $data['employee_id'],
            'company_id' => $superAdminRole->company_id,
            'branch_id' => $data['employee']->branch_id,
            'department_id' => $data['title']->department_id,
            'order' => 1,
            'operator' => 'then',
            'sub_department_id' => $data['title']->sub_department_id,
        ];
    }

    public function getApprovalDate($data)
    {
        $date = null;

        if ($data['workflow_type'] == config('globals.REQUEST_WORKFLOW_TYPES.LEAVE')
            ||
            $data['workflow_type'] == config('globals.REQUEST_WORKFLOW_TYPES.MISSIONS')
        ) {
            $date = Carbon::parse($data['request']->from)->format('Y-m-d');
        } elseif (
            $data['workflow_type'] == config('globals.REQUEST_WORKFLOW_TYPES.TERMINATION')
            ||
            $data['workflow_type'] == config('globals.REQUEST_WORKFLOW_TYPES.LOAN')
            ||
            $data['workflow_type'] == config('globals.REQUEST_WORKFLOW_TYPES.SALARY_ADVANCE')
        ) {
            $date = Carbon::parse($data['request']->created_at)->format('Y-m-d');
        } elseif ($data['workflow_type'] == config('globals.REQUEST_WORKFLOW_TYPES.EXTRA_WORKDAY')) {
            $date = Carbon::parse($data['request']->extra_work_day_date)->format('Y-m-d');
        } elseif ($data['workflow_type'] == config('globals.REQUEST_WORKFLOW_TYPES.PROBATION')) {
            $date = $data['request']->probation_end_date;
        } else {
            $date = $data['request']->date;
        }

        return $date;
    }

    public function checkSuperAdminActionStatus($superAdminRoleId, $requesterRoleIds)
    {
        if (in_array($superAdminRoleId, $requesterRoleIds)) {
            return 'approved';
        }

        return 'pending';
    }
}
