<?php

namespace App\Http\Controllers\V1\CostCenter;

use App\Exceptions\UnprocessableException;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\CostCenter\AddCentralDepartmentsRequest;
use App\Http\Resources\V1\Department\DepartmentCollection;
use App\Services\V1\CostCenter\CentralDepartmentService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\Log;

class CentralDepartmentController extends NewController
{
    public function __construct(private CentralDepartmentService $centralDepartmentService) {}

    public function list()
    {
        try {
            $data = $this->centralDepartmentService->list();

            return getResponseStructure(
                ['data' => new DepartmentCollection($data)],
                HttpStatusCodeUtil::OK,
                'Central departments retrieved successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function syncCentralDepartments(AddCentralDepartmentsRequest $request)
    {

        try {
            $this->centralDepartmentService->syncCentralDepartments($request->validated());

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Departments updated successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }
}
