<?php

namespace App\Http\Controllers\Payroll\PayrollHub;

use App\Http\Controllers\NewController;
use App\Http\Requests\V1\PayrollHub\PayrollHubGetUnaccountedDaysRequest;
use App\Http\Resources\V1\PayrollHub\GetUnaccountedDaysResource;
use App\Services\PayrollSetup\MonthlySummariesService;
use App\Util\HttpStatusCodeUtil;

class UnaccountedDaysController extends NewController
{
    public function __construct(
        protected MonthlySummariesService $monthlySummariesService,

    )
    {
    }

    public function getUnaccountedDays(PayrollHubGetUnaccountedDaysRequest $request)
    {
        $unaccountedDays = $this->monthlySummariesService->allUnaccountedDays($request->validated());
        return getResponseStructure(
            ['data' => new GetUnaccountedDaysResource($unaccountedDays)],
            HttpStatusCodeUtil::OK,
            'Unaccounted days fetched successfully'
        );
    }

}