<?php

namespace App\Http\Requests\V1\Leaves;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;

class EditCompanyLeaveTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name_ar' => 'string',
            'name_en' => 'string',
            'balance_period' => 'string|in:calendar_year,payroll_month,calendar_month',
            'prorated_monthly' => 'boolean',
            'allow_balance_transfer' => 'boolean',
            'max_transfer_balance' => 'integer|required_if:balance_transfer,true',
            'transferred_balance_usable_until' => 'integer|required_if:balance_transfer,true|max:12|min:0',
            'allow_cash_compensation' => 'boolean',
            'request_before_days' => 'integer',
            'partial_leave_allowed' => 'boolean|nullable',
            'base_balance' => 'numeric',
            'unit' => 'string|in:days,hours',
            'gender' => 'string|in:all,male,female',
            'is_probation_allowed' => 'boolean',
            'titles' => 'array',
            'titles.*' => [
                'required',
                'integer',
                (new Exists('titles', 'id'))->where(function ($query) {
                    $query->where('company_id', auth()->user()->company_id)
                        ->whereNull('deleted_at');
                }),
            ],
            'min_requester_years' => 'integer',
            'leave_deduction_percentage' => 'numeric|min:0|max:100',
        ];
    }

    public function prepareForValidation()
    {
        // divide 'leave_deduction_percentage' by 100 if exists
        if ($this->has('leave_deduction_percentage')) {
            $this->merge([
                'leave_deduction_percentage' => $this->leave_deduction_percentage / 100,
            ]);
        }
    }

    public function getOnlyEditRequestData()
    {
        // return all except 'allow_cash_compensation'
        return $this->except('allow_cash_compensation');
    }

    public function getAllowCashCompensation()
    {
        // return 'allow_cash_compensation' if exists
        return $this->allow_cash_compensation;
    }
}
