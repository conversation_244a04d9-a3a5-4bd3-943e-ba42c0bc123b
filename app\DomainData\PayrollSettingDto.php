<?php

namespace App\DomainData;

trait PayrollSettingDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'monthly_closing_date' => 'required|integer|between:1,31',
            'payroll_disbursement_date' => 'required|integer|between:1,31',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializePayrollSettingDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }

    public function getAdditionalRules(array $rules = []): array
    {
        $rules['shift_policies'] = ['array'];
        $rules['shift_policies.*.type'] = ['required', 'in:start_shift,end_shift'];
        $rules['shift_policies.*.titles'] = ['required', 'array'];
        $rules['shift_policies.*.titles.*'] = ['required', 'numeric'];
        $rules['bank_ids'] = ['required', 'array'];
        $rules['bank_ids.*'] = ['numeric'];

        return $rules;
    }
}
