<!DOCTYPE html>
<html dir="{{ $direction ?? 'ltr' }}" lang="{{ $language ?? 'en' }}" class="{{ $direction == 'rtl' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ shapeArabic(__('salary_components.payslip')) }} -
        {{ isset($employee['name']) ? shapeArabic($employee['name']) : '' }}
    </title>
    <style>
        /* Modern CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Amiri', 'DejaVu Sans', sans-serif;
            direction:
                {{ $direction ?? 'ltr' }}
            ;
            text-align:
                {{ $direction == 'rtl' ? 'right' : 'left' }}
            ;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #2c3e50;
            background: #ffffff;
        }

        /* Container with modern styling */
        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
        }

        /* Modern Header Section */
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            margin: -20px -20px 30px -20px;
        }

        .header-table {
            width: 100%;
            border-collapse: collapse;
        }

        .header-table td {
            padding: 0;
            vertical-align: middle;
        }

        .company-name {
            font-size: 28px;
            font-weight: 700;
            color: white;
            text-align:
                {{ $direction == 'rtl' ? 'right' : 'left' }}
            ;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .company-logo {
            font-size: 24px;
            font-weight: 600;
            color: #ffd700;
            text-align:
                {{ $direction == 'rtl' ? 'left' : 'right' }}
            ;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* Modern Section Styling */
        .section {
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }

        .rtl .section {
            border-left: none;
            border-right: 4px solid #667eea;
        }

        /* Employee Info Section */
        .employee-info {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-left: 4px solid #f5576c;
        }

        .rtl .employee-info {
            border-left: none;
            border-right: 4px solid #f5576c;
        }

        .payslip-month {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 15px;
            color: white;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .employee-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            color: white;
        }

        .employee-details-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .employee-details-table td {
            padding: 8px 0;
            vertical-align: top;
            color: white;
            font-weight: 500;
        }

        /* Category Section Styling */
        .category-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        }

        .category-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 20px;
        }

        .category-header-table {
            width: 100%;
            border-collapse: collapse;
        }

        .category-header-table td {
            padding: 0;
            vertical-align: middle;
        }

        .category-name {
            font-weight: 700;
            font-size: 18px;
            text-align:
                {{ $direction == 'rtl' ? 'right' : 'left' }}
            ;
            color: white;
        }

        .amount-keyword {
            text-align:
                {{ $direction == 'rtl' ? 'left' : 'right' }}
            ;
            font-weight: 600;
            color: white;
        }

        .category-content {
            padding: 20px;
        }

        .component-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f3f4;
            transition: background-color 0.2s ease;
        }

        .component-row:hover {
            background-color: #f8f9fa;
        }

        .component-row:last-child {
            border-bottom: none;
        }

        .component-name {
            font-weight: 600;
            color: #2c3e50;
            flex: 1;
        }

        .component-amount {
            font-weight: 600;
            color: #27ae60;
            text-align:
                {{ $direction == 'rtl' ? 'left' : 'right' }}
            ;
            min-width: 120px;
        }

        .total-row {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            margin: 15px -20px -20px -20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 700;
            font-size: 16px;
        }

        /* Tax Section Styling */
        .tax-section {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: #2c3e50;
            border-left: 4px solid #fa709a;
        }

        .rtl .tax-section {
            border-left: none;
            border-right: 4px solid #fa709a;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #2c3e50;
            text-align:
                {{ $direction == 'rtl' ? 'right' : 'left' }}
            ;
        }

        .tax-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            font-weight: 600;
        }

        .tax-item:last-child {
            border-bottom: none;
        }

        .tax-amount {
            color: #e74c3c;
            font-weight: 700;
        }

        /* Net Pay Section */
        .net-pay-section {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            text-align: center;
            padding: 25px;
            border-radius: 12px;
            margin: 30px 0;
            box-shadow: 0 4px 20px rgba(17, 153, 142, 0.3);
        }

        .net-pay-label {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .net-pay-amount {
            font-size: 32px;
            font-weight: 800;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* Signature Section */
        .signature-section {
            margin-top: 50px;
            padding: 20px 0;
        }

        .signature-container {
            text-align:
                {{ $direction == 'rtl' ? 'left' : 'right' }}
            ;
        }

        .signature-label {
            margin-bottom: 15px;
            font-weight: 600;
            color: #2c3e50;
        }

        .signature-box {
            border: 2px solid #bdc3c7;
            width: 200px;
            height: 80px;
            margin:
                {{ $direction == 'rtl' ? '0 auto 0 0' : '0 0 0 auto' }}
            ;
            border-radius: 4px;
            background: #f8f9fa;
            position: relative;
        }

        .signature-box::after {
            content: '';
            position: absolute;
            bottom: 10px;
            left: 20px;
            right: 20px;
            height: 1px;
            background: #bdc3c7;
        }

        /* Arabic Text Support with Amiri Font */
        .arabic-text {
            font-family: 'Amiri', 'DejaVu Sans', sans-serif;
            direction: rtl;
            unicode-bidi: bidi-override;
            text-align: right;
            font-weight: 500;
        }

        .normal-text {
            font-family: 'DejaVu Sans', sans-serif;
            direction: ltr;
        }

        .number {
            font-family: 'DejaVu Sans', sans-serif !important;
            direction: ltr !important;
            font-weight: 600;
        }

        /* RTL Layout Adjustments */
        .rtl .company-name {
            text-align: right;
        }

        .rtl .company-logo {
            text-align: left;
        }

        .rtl .category-name,
        .rtl .section-title {
            text-align: right;
        }

        .rtl .amount-keyword,
        .rtl .component-amount {
            text-align: left;
        }

        .rtl .signature-container {
            text-align: left;
        }

        .rtl .component-row {
            flex-direction: row-reverse;
        }

        .rtl .total-row {
            flex-direction: row-reverse;
        }

        .rtl .tax-item {
            flex-direction: row-reverse;
        }

        /* Page Break */
        .new-page {
            page-break-after: always;
        }

        /* Responsive Design for PDF */
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }

            .container {
                max-width: none;
            }

            .header-section {
                margin: -15px -15px 20px -15px;
            }
        }

        /* Enhanced Visual Elements */
        .divider {
            height: 2px;
            background: linear-gradient(90deg, transparent, #667eea, transparent);
            margin: 20px 0;
            border: none;
        }

        .highlight-box {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.3);
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    @php
        // Global translation variables with Arabic reshaping
        $amountText = __('salary_components.amount');
        if (containsArabic($amountText)) {
            $amountText = shapeArabic($amountText);
        }

        $currencyText = __('salary_components.currency');
        if (containsArabic($currencyText)) {
            $currencyText = shapeArabic($currencyText);
        }

        $totalText = __('salary_components.total');
        if (containsArabic($totalText)) {
            $totalText = shapeArabic($totalText);
        }

        // Determine font class based on language
        $fontClass = $language == 'ar' ? 'arabic-text' : 'normal-text';
        $isRTL = $direction == 'rtl';
    @endphp

    <div class="container">
        {{-- Modern Header Section --}}
        <div class="header-section">
            <table class="header-table">
                <tr>
                    <td class="company-name">
                        @php
                            $companyName = $company_name ?? 'Company Name';
                            $companyNameDisplay = containsArabic($companyName) ? shapeArabic($companyName) : $companyName;
                        @endphp
                        <span class="{{ $fontClass }}">
                            {{ $companyNameDisplay }}
                        </span>
                    </td>
                    <td class="company-logo">blueworks.</td>
                </tr>
            </table>
        </div>

        {{-- Employee Information Section --}}
        <div class="section employee-info">
            <div class="payslip-month">
                @php
                    $payslipForText = __('salary_components.payslip_for');
                    if (containsArabic($payslipForText)) {
                        $payslipForText = shapeArabic($payslipForText);
                    }
                @endphp
                <span class="{{ $fontClass }}">
                    {{ $payslipForText }}
                </span>
                <span class="normal-text">{{ containsArabic($month ?? '') ? shapeArabic($month ?? '') : $month ?? '' }}
                    {{ $year ?? '' }}</span>
            </div>

            <div class="employee-name">
                @php
                    $employeeNameText = __('salary_components.employee_name');
                    if (containsArabic($employeeNameText)) {
                        $employeeNameText = shapeArabic($employeeNameText);
                    }

                    $employeeName = $employee['name'] ?? '';
                    if (containsArabic($employeeName)) {
                        $employeeName = shapeArabic($employeeName);
                    }
                @endphp
                <span class="{{ $fontClass }}">
                    {{ $employeeNameText }}:
                </span>
                <span class="{{ $fontClass }}">
                    {{ $employeeName }}
                </span>
            </div>

            <table class="employee-details-table">
                <tr>
                    <td>
                        @php
                            $codeText = __('salary_components.employee_code');
                            if (containsArabic($codeText)) {
                                $codeText = shapeArabic($codeText);
                            }
                        @endphp
                        <span class="{{ $fontClass }}">
                            {{ $codeText }}:
                        </span>
                        <span class="number">#{{ $employee['code'] ?? '' }}</span>
                    </td>
                    <td style="text-align: {{ $isRTL ? 'left' : 'right' }};">
                        @php
                            $titleText = __('salary_components.job_title');
                            if (containsArabic($titleText)) {
                                $titleText = shapeArabic($titleText);
                            }

                            $employeeTitle = $employee['title'] ?? '';
                            if (containsArabic($employeeTitle)) {
                                $employeeTitle = shapeArabic($employeeTitle);
                            }
                        @endphp
                        <span class="{{ $fontClass }}">
                            {{ $titleText }}:
                        </span>
                        <span class="{{ $fontClass }}">
                            {{ $employeeTitle }}
                        </span>
                    </td>
                </tr>
            </table>
        </div>

        <div class="divider"></div>

        {{-- Salary Categories Section --}}
        @if(isset($categories) && is_array($categories))
            @foreach($categories as $category_name => $components)
                <div class="category-section">
                    <div class="category-header">
                        <table class="category-header-table">
                            <tr>
                                <td class="category-name">
                                    @php
                                        $categoryKey = 'salary_components.' . trim($category_name) ?? '';
                                        $translatedCategoryName = __($categoryKey) ?: $category_name;
                                        if (containsArabic($translatedCategoryName)) {
                                            $translatedCategoryName = shapeArabic($translatedCategoryName);
                                        }
                                    @endphp
                                    <span class="{{ $fontClass }}">
                                        {{ $translatedCategoryName }}
                                    </span>
                                </td>
                                <td class="amount-keyword">
                                    <span class="{{ $fontClass }}">
                                        {{ $amountText }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div class="category-content">
                        @php $categoryTotal = 0; @endphp
                        @if(is_array($components))
                            @foreach($components as $component)
                                @php
                                    $componentKey = 'salary_components.' . $component['name'] ?? '';
                                    $translatedComponentName = __($componentKey) ?: ($component['name'] ?? '');
                                    if (containsArabic($translatedComponentName)) {
                                        $translatedComponentName = shapeArabic($translatedComponentName);
                                    }
                                    $amount = floatval(str_replace(',', '', $component['amount'] ?? '0'));
                                    $categoryTotal += $amount;
                                @endphp
                                <div class="component-row">
                                    <div class="component-name">
                                        <span class="{{ $fontClass }}">
                                            {{ $translatedComponentName }}
                                        </span>
                                    </div>
                                    <div class="component-amount">
                                        <span class="number">
                                            {{ $component['amount'] ?? '0' }}
                                            <span class="{{ $fontClass }}">
                                                {{ $currencyText }}
                                            </span>
                                        </span>
                                    </div>
                                </div>
                            @endforeach
                        @endif

                        <div class="total-row">
                            <div>
                                <span class="{{ $fontClass }}">
                                    {{ $totalText }} {{ $translatedCategoryName }}
                                </span>
                            </div>
                            <div>
                                <span class="number">
                                    {{ number_format($categoryTotal, 2) }}
                                    <span class="{{ $fontClass }}">
                                        {{ $currencyText }}
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        @endif

        {{-- Page Break --}}
        <div class="new-page"></div>

        {{-- Taxes & Social Insurance Section --}}
        <div class="section tax-section">
            <div class="section-title">
                @php
                    $taxInsuranceText = __('salary_components.taxes_social_insurance');
                    if (containsArabic($taxInsuranceText)) {
                        $taxInsuranceText = shapeArabic($taxInsuranceText);
                    }
                @endphp
                <span class="{{ $fontClass }}">
                    {{ $taxInsuranceText }}
                </span>
            </div>

            <div class="tax-item">
                <div>
                    @php
                        $taxesText = __('salary_components.taxes');
                        if (containsArabic($taxesText)) {
                            $taxesText = shapeArabic($taxesText);
                        }
                    @endphp
                    <span class="{{ $fontClass }}">
                        {{ $taxesText }}
                    </span>
                </div>
                <div class="tax-amount">
                    <span class="number">
                        -{{ $tax_amount ?? '0' }}
                        <span class="{{ $fontClass }}">
                            {{ $currencyText }}
                        </span>
                    </span>
                </div>
            </div>

            <div class="tax-item">
                <div>
                    @php
                        $socialInsuranceText = __('salary_components.social_insurance');
                        if (containsArabic($socialInsuranceText)) {
                            $socialInsuranceText = shapeArabic($socialInsuranceText);
                        }
                    @endphp
                    <span class="{{ $fontClass }}">
                        {{ $socialInsuranceText }}
                    </span>
                </div>
                <div class="tax-amount">
                    <span class="number">
                        -{{ $insurance_amount ?? '0' }}
                        <span class="{{ $fontClass }}">
                            {{ $currencyText }}
                        </span>
                    </span>
                </div>
            </div>

            <div class="highlight-box">
                <div class="tax-item" style="border: none; font-weight: 700; font-size: 16px;">
                    <div>
                        @php
                            $totalTaxInsurance = floatval(str_replace(',', '', $tax_amount ?? '0')) + floatval(str_replace(',', '', $insurance_amount ?? '0'));
                        @endphp
                        <span class="{{ $fontClass }}">
                            {{ $totalText }}
                        </span>
                    </div>
                    <div class="tax-amount">
                        <span class="number">
                            {{ number_format($totalTaxInsurance, 2) }}
                            <span class="{{ $fontClass }}">
                                {{ $currencyText }}
                            </span>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        {{-- Net Pay Section --}}
        <div class="net-pay-section">
            <div class="net-pay-label">
                @php
                    $netPayText = __('salary_components.net_pay');
                    if (containsArabic($netPayText)) {
                        $netPayText = shapeArabic($netPayText);
                    }
                @endphp
                <span class="{{ $fontClass }}">
                    {{ $netPayText }}
                </span>
            </div>
            <div class="net-pay-amount">
                <span class="number">
                    {{ $employee['net_salary'] ?? '0' }}
                    <span class="{{ $fontClass }}">
                        {{ $currencyText }}
                    </span>
                </span>
            </div>
        </div>

        {{-- Employee Signature Section --}}
        <div class="signature-section">
            <div class="signature-container">
                <div class="signature-label">
                    @php
                        $signatureText = __('salary_components.employee_signature');
                        if (containsArabic($signatureText)) {
                            $signatureText = shapeArabic($signatureText);
                        }
                    @endphp
                    <span class="{{ $fontClass }}">
                        {{ $signatureText }}:
                    </span>
                </div>
                <div class="signature-box"></div>
            </div>
        </div>
    </div>
</body>

</html>