<!DOCTYPE html>
<html dir="{{ $direction ?? 'ltr' }}" lang="{{ $language ?? 'en' }}" class="{{ $direction == 'rtl' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ shapeArabic(__('salary_components.payslip')) }} -
        {{ isset($employee['name']) ? shapeArabic($employee['name']) : '' }}
    </title>
    <!-- Arabic Font CDN Injection -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap"
        rel="stylesheet">

    <style>
        /* Arabic Font Injection via CDN */
        @import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');

        /* Modern CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            direction: rtl;
            unicode-bidi: bidi-override;
            box-sizing: border-box;
        }

        body {
            font-family: 'Amiri', 'DejaVu Sans', sans-serif;
            direction:
                {{ $direction ?? 'ltr' }}
            ;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background: #ffffff;
        }

        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background: #ffffff;
        }

        /* Header Section - Row One: Company Name and Blueworks Logo */
        .header-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px 0;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .blueworks-logo {
            font-size: 24px;
            font-weight: bold;
            color: #4285f4;
        }

        /* Line Break */
        .line-break {
            height: 1px;
            background-color: #ddd;
            margin: 20px 0;
            border: none;
        }

        /* Section One: Employee Info */
        .section-one {
            margin-bottom: 20px;
        }

        .payslip-month {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .employee-name {
            font-size: 16px;
            margin-bottom: 15px;
        }

        .employee-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .employee-code {
            font-size: 14px;
        }

        .employee-title {
            font-size: 14px;
        }

        /* Section Two: Categories (Iterative) */
        .section-two {
            margin-bottom: 30px;
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .category-name {
            font-weight: bold;
            font-size: 16px;
        }

        .amount-keyword {
            font-weight: 600;
            font-size: 16px;
        }

        .dashed-line {
            border-top: 1px dashed #ccc;
            margin: 10px 0;
            height: 1px;
        }

        .component-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .component-name {
            font-weight: bold;
        }

        .component-amount {
            font-weight: normal;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(128, 128, 128, 0.6);
            padding: 10px 15px;
            margin-top: 10px;
            font-weight: bold;
        }

        /* Section Three: Taxes & Social Insurance */
        .section-three {
            margin-bottom: 30px;
        }

        .section-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .section-title-text {
            font-weight: bold;
            font-size: 16px;
        }

        .tax-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .tax-label {
            font-weight: normal;
        }

        .tax-amount {
            font-weight: normal;
        }

        /* Section Four: Net Pay */
        .section-four {
            margin-bottom: 30px;
        }

        .net-pay-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(128, 128, 128, 0.3);
            padding: 15px;
            font-weight: bold;
            font-size: 18px;
        }

        /* Section Five: Employee Signature */
        .section-five {
            margin-top: 40px;
        }

        .signature-row {
            display: flex;
            justify-content: flex-end;
            align-items: flex-start;
        }

        .signature-container {
            text-align: right;
        }

        .signature-label {
            margin-bottom: 10px;
            font-weight: normal;
        }

        .signature-box {
            border: 1px solid #333;
            width: 200px;
            height: 60px;
            background: transparent;
        }

        /* Arabic Text Support */
        .arabic-text {
            font-family: 'Amiri', 'DejaVu Sans', sans-serif;
            direction: rtl;
            unicode-bidi: bidi-override;
            text-align: right;
            font-weight: 500;
        }

        .normal-text {
            font-family: 'DejaVu Sans', sans-serif;
            direction: ltr;
        }

        .number {
            font-family: 'DejaVu Sans', sans-serif !important;
            direction: ltr !important;
            font-weight: 600;
        }

        /* RTL Layout Adjustments */
        .rtl .header-row {
            flex-direction: row-reverse;
        }

        .rtl .employee-details {
            flex-direction: row-reverse;
        }

        .rtl .category-header {
            flex-direction: row-reverse;
        }

        .rtl .component-row {
            flex-direction: row-reverse;
        }

        .rtl .total-row {
            flex-direction: row-reverse;
        }

        .rtl .section-title {
            flex-direction: row-reverse;
        }

        .rtl .tax-row {
            flex-direction: row-reverse;
        }

        .rtl .net-pay-row {
            flex-direction: row-reverse;
        }

        .rtl .signature-row {
            justify-content: flex-start;
        }

        .rtl .signature-container {
            text-align: left;
        }

        /* Page Break */
        .new-page {
            page-break-after: always;
        }
    </style>
</head>

<body>
    @php
        // Global translation variables with Arabic reshaping
        $amountText = __('salary_components.amount');
        if (containsArabic($amountText)) {
            $amountText = shapeArabic($amountText);
        }

        $currencyText = __('salary_components.currency');
        if (containsArabic($currencyText)) {
            $currencyText = shapeArabic($currencyText);
        }

        $totalText = __('salary_components.total');
        if (containsArabic($totalText)) {
            $totalText = shapeArabic($totalText);
        }

        // Determine font class based on language
        $fontClass = $language == 'ar' ? 'arabic-text' : 'normal-text';
        $isRTL = $direction == 'rtl';
    @endphp

    <div class="container">
        {{-- 1. Header: Row One (company_name, blueworks logo), justify-between --}}
        <div class="header-row">
            <div class="company-name">
                @php
                    $companyName = $company_name ?? 'Company Name';
                    $companyNameDisplay = containsArabic($companyName) ? shapeArabic($companyName) : $companyName;
                @endphp
                <span class="{{ $fontClass }}">
                    {{ $companyNameDisplay }}
                </span>
            </div>
            <div class="blueworks-logo">blueworks.</div>
        </div>

        {{-- 2. Line break --}}
        <hr class="line-break">

        {{-- 3. Section One: Employee Info --}}
        <div class="section-one">
            {{-- Row one: payslip month --}}
            <div class="payslip-month">
                @php
                    $payslipForText = __('salary_components.payslip_for');
                    if (containsArabic($payslipForText)) {
                        $payslipForText = shapeArabic($payslipForText);
                    }
                @endphp
                <span class="{{ $fontClass }}">
                    {{ $payslipForText }}
                </span>
                <span class="normal-text">{{ containsArabic($month ?? '') ? shapeArabic($month ?? '') : $month ?? '' }}
                    {{ $year ?? '' }}</span>
            </div>

            {{-- Row two: employee name --}}
            <div class="employee-name">
                @php
                    $employeeNameText = __('salary_components.employee_name');
                    if (containsArabic($employeeNameText)) {
                        $employeeNameText = shapeArabic($employeeNameText);
                    }

                    $employeeName = $employee['name'] ?? '';
                    if (containsArabic($employeeName)) {
                        $employeeName = shapeArabic($employeeName);
                    }
                @endphp
                <span class="{{ $fontClass }}">
                    {{ $employeeNameText }}:
                </span>
                <span class="{{ $fontClass }}">
                    {{ $employeeName }}
                </span>
            </div>

            {{-- Row three: code, title --}}
            <div class="employee-details">
                <div class="employee-code">
                    @php
                        $codeText = __('salary_components.employee_code');
                        if (containsArabic($codeText)) {
                            $codeText = shapeArabic($codeText);
                        }
                    @endphp
                    <span class="{{ $fontClass }}">
                        {{ $codeText }}:
                    </span>
                    <span class="number">#{{ $employee['code'] ?? '' }}</span>
                </div>
                <div class="employee-title">
                    @php
                        $titleText = __('salary_components.job_title');
                        if (containsArabic($titleText)) {
                            $titleText = shapeArabic($titleText);
                        }

                        $employeeTitle = $employee['title'] ?? '';
                        if (containsArabic($employeeTitle)) {
                            $employeeTitle = shapeArabic($employeeTitle);
                        }
                    @endphp
                    <span class="{{ $fontClass }}">
                        {{ $titleText }}:
                    </span>
                    <span class="{{ $fontClass }}">
                        {{ $employeeTitle }}
                    </span>
                </div>
            </div>
        </div>

        {{-- 4. Line break --}}
        <hr class="line-break">

        {{-- 5. Section Two: Categories (Iterative) --}}
        @if(isset($categories) && is_array($categories))
            @foreach($categories as $category_name => $components)
                <div class="section-two">
                    {{-- Row one: category name (bold), amount_keyword (semi bold), justify-between --}}
                    <div class="category-header">
                        <div class="category-name">
                            @php
                                $categoryKey = 'salary_components.' . trim($category_name) ?? '';
                                $translatedCategoryName = __($categoryKey) ?: $category_name;
                                if (containsArabic($translatedCategoryName)) {
                                    $translatedCategoryName = shapeArabic($translatedCategoryName);
                                }
                            @endphp
                            <span class="{{ $fontClass }}">
                                {{ $translatedCategoryName }}
                            </span>
                        </div>
                        <div class="amount-keyword">
                            <span class="{{ $fontClass }}">
                                {{ $amountText }}
                            </span>
                        </div>
                    </div>

                    {{-- Dashed line break --}}
                    <div class="dashed-line"></div>

                    {{-- Row two (iterative): component name (bold), real amount, justify-between --}}
                    @php $categoryTotal = 0; @endphp
                    @if(is_array($components))
                        @foreach($components as $component)
                            @php
                                $componentKey = 'salary_components.' . $component['name'] ?? '';
                                $translatedComponentName = __($componentKey) ?: ($component['name'] ?? '');
                                if (containsArabic($translatedComponentName)) {
                                    $translatedComponentName = shapeArabic($translatedComponentName);
                                }
                                $amount = floatval(str_replace(',', '', $component['amount'] ?? '0'));
                                $categoryTotal += $amount;
                            @endphp
                            <div class="component-row">
                                <div class="component-name">
                                    <span class="{{ $fontClass }}">
                                        {{ $translatedComponentName }}
                                    </span>
                                </div>
                                <div class="component-amount">
                                    <span class="number">
                                        {{ $component['amount'] ?? '0' }}
                                        <span class="{{ $fontClass }}">
                                            {{ $currencyText }}
                                        </span>
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    @endif

                    {{-- Final row: total keyword, total amount (bold), justify-between, with background gray, opacity 60% --}}
                    <div class="total-row">
                        <div>
                            <span class="{{ $fontClass }}">
                                {{ $totalText }} {{ $translatedCategoryName }}
                            </span>
                        </div>
                        <div>
                            <span class="number">
                                {{ number_format($categoryTotal, 2) }}
                                <span class="{{ $fontClass }}">
                                    {{ $currencyText }}
                                </span>
                            </span>
                        </div>
                    </div>
                </div>
            @endforeach
        @endif

        {{-- Page Break --}}
        <div class="new-page"></div>

        {{-- 6. Section Three: Taxes & Social Insurance --}}
        <div class="section-three">
            {{-- Row one: title (taxes & social insurance) (bold), amount (semi bold) --}}
            <div class="section-title">
                <div class="section-title-text">
                    @php
                        $taxInsuranceText = __('salary_components.taxes_social_insurance');
                        if (containsArabic($taxInsuranceText)) {
                            $taxInsuranceText = shapeArabic($taxInsuranceText);
                        }
                    @endphp
                    <span class="{{ $fontClass }}">
                        {{ $taxInsuranceText }}
                    </span>
                </div>
                <div class="amount-keyword">
                    <span class="{{ $fontClass }}">
                        {{ $amountText }}
                    </span>
                </div>
            </div>

            {{-- Row two: taxes keyword, -amount --}}
            <div class="tax-row">
                <div class="tax-label">
                    @php
                        $taxesText = __('salary_components.taxes');
                        if (containsArabic($taxesText)) {
                            $taxesText = shapeArabic($taxesText);
                        }
                    @endphp
                    <span class="{{ $fontClass }}">
                        {{ $taxesText }}
                    </span>
                </div>
                <div class="tax-amount">
                    <span class="number">
                        -{{ $tax_amount ?? '0' }}
                        <span class="{{ $fontClass }}">
                            {{ $currencyText }}
                        </span>
                    </span>
                </div>
            </div>

            {{-- Row three: social insurance keyword, -amount --}}
            <div class="tax-row">
                <div class="tax-label">
                    @php
                        $socialInsuranceText = __('salary_components.social_insurance');
                        if (containsArabic($socialInsuranceText)) {
                            $socialInsuranceText = shapeArabic($socialInsuranceText);
                        }
                    @endphp
                    <span class="{{ $fontClass }}">
                        {{ $socialInsuranceText }}
                    </span>
                </div>
                <div class="tax-amount">
                    <span class="number">
                        -{{ $insurance_amount ?? '0' }}
                        <span class="{{ $fontClass }}">
                            {{ $currencyText }}
                        </span>
                    </span>
                </div>
            </div>

            {{-- Final row: total keyword, total amount (bold), justify-between, with background gray, opacity 60% --}}
            <div class="total-row">
                <div>
                    @php
                        $totalTaxInsurance = floatval(str_replace(',', '', $tax_amount ?? '0')) + floatval(str_replace(',', '', $insurance_amount ?? '0'));
                    @endphp
                    <span class="{{ $fontClass }}">
                        {{ $totalText }}
                    </span>
                </div>
                <div>
                    <span class="number">
                        {{ number_format($totalTaxInsurance, 2) }}
                        <span class="{{ $fontClass }}">
                            {{ $currencyText }}
                        </span>
                    </span>
                </div>
            </div>
        </div>

        {{-- 6. Section Four: Net Pay --}}
        <div class="section-four">
            {{-- Row one: net pay keyword, salary, justify-between, with background gray, opacity 30% --}}
            <div class="net-pay-row">
                <div>
                    @php
                        $netPayText = __('salary_components.net_pay');
                        if (containsArabic($netPayText)) {
                            $netPayText = shapeArabic($netPayText);
                        }
                    @endphp
                    <span class="{{ $fontClass }}">
                        {{ $netPayText }}
                    </span>
                </div>
                <div>
                    <span class="number">
                        {{ $employee['net_salary'] ?? '0' }}
                        <span class="{{ $fontClass }}">
                            {{ $currencyText }}
                        </span>
                    </span>
                </div>
            </div>
        </div>

        {{-- 7. Section Five: Employee Signature --}}
        <div class="section-five">
            {{-- Row one: employee signature: box for signature --}}
            <div class="signature-row">
                <div class="signature-container">
                    <div class="signature-label">
                        @php
                            $signatureText = __('salary_components.employee_signature');
                            if (containsArabic($signatureText)) {
                                $signatureText = shapeArabic($signatureText);
                            }
                        @endphp
                        <span class="{{ $fontClass }}">
                            {{ $signatureText }}:
                        </span>
                    </div>
                    <div class="signature-box"></div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
