<?php

namespace App\Models;

use App\Models\Casts\TimezoneDateTime;
use App\Models\StateMachines\RequestState;
use App\Traits\CompanyRule;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class OvertimeRequest extends BaseModel
{
    use companyRule, HasFactory, LogsActivity, softDeletes;

    protected $casts = [
        'created_at' => TimezoneDateTime::class,
        'updated_at' => TimezoneDateTime::class,
        'deleted_at' => TimezoneDateTime::class.':nullable',
        'status' => RequestState::class,
    ];

    protected $guarded = ['id', 'created_at', 'updated_at'];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($overtime) {
            EmployeeRequest::create([
                'date' => $overtime->date,
                'employee_id' => $overtime->employee_id,
                'status' => $overtime->status,
                'comment' => $overtime->reason,
                'request_name' => 'employee_overtime_request',
                'requestable_id' => $overtime->id,
                'requestable_type' => 'overtime_request',
                'requested_by' => auth()->user()->employee_id,
                'company_id' => $overtime->company_id,
            ]);
        });

    }

    public function timecard()
    {
        return $this->belongsTo(Timecard::class);
    }

    public function employeeRequest()
    {
        return $this->morphOne(EmployeeRequest::class, 'requestable')->latest('id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll();
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function employeeApproves()
    {
        return $this->morphToMany(Role::class, 'requestable', 'workflow_approval_cycle',
            'requestable_id', 'role_id')
            ->withPivot(['id', 'company_id', 'status', 'date', 'order', 'operator', 'requestable_type', 'request_workflow_id', 'branch_id', 'department_id'])
            ->withTimestamps();
    }
}
