<?php

namespace App\Http\Controllers\TimeTracking;

use App\DomainData\CicoDto;
use App\FeatureToggles\Unleash;
use App\Http\Controllers\Controller;
use App\Services\Attendance\ApproveUnverifiedCicoService;
use App\Services\Attendance\EditUnverifiedCicoService;
use App\Services\Attendance\RejectUnverifiedCicoService;
use App\Services\TimeTracking\BusinessServices\GetCicosFilterService;
use stdClass;

class CicoController extends Controller
{
    use CicoDto;

    private $unleash;

    public function __construct(
        private ApproveUnverifiedCicoService $approveUnverifiedCicoService,
        private EditUnverifiedCicoService $editUnverifiedCicoService,
        private RejectUnverifiedCicoService $rejectUnverifiedCicoService,
        private GetCicosFilterService $getCicosFilterService
    ) {
        $this->unleash = app(Unleash::class);
    }

    public function approve(array $request, stdClass &$output): void
    {
        $rules = $this->getActionsRules();

        if (is_null($this->validateRequest($request, $rules, $output))) {
            return;
        }

        if (! isset($request['check_warning'])) {
            $request['check_warning'] = false;
        }

        if ($this->unleash->getUnscheduledShiftsFeatureFlag()) {
            $request['is_unscheduled'] = true;
        }

        $this->approveUnverifiedCicoService->perform($request, $output);
    }

    public function edit(array $request, stdClass &$output): void
    {
        $rules = $this->getActionsRules();
        $this->getEditActionRules($rules);

        if (is_null($this->validateRequest($request, $rules, $output))) {
            return;
        }
        $this->editUnverifiedCicoService->perform($request, $output);

    }

    public function editAutomatedOut(array $request, stdClass &$output): void
    {
        $rules = $this->getEditAutomatedOutRules();
        if (is_null($this->validateRequest($request, $rules, $output))) {
            return;
        }

        $this->editUnverifiedCicoService->editAutomatedOut($request, $output);
    }

    public function reject(array $request, stdClass &$output): void
    {
        $rules['clock_in_id'] = ['required', 'integer'];
        $rules['clock_out_id'] = ['required', 'integer'];

        if (is_null($this->validateRequest($request, $rules, $output))) {
            return;
        }

        $this->rejectUnverifiedCicoService->perform($request, $output);
        unset($output->{'cico'});
    }

    public function validateRequest($request, $rules, &$output)
    {
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        return $validator->validate();
    }

    private function isValidFilters(array &$request, stdClass &$output): bool
    {
        $rules = $this->getFilterRules();

        if (! isset($request['page_size'])) {
            $request['page_size'] = config('globals.MAX_PAGE_SIZE');
        }
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return false;
        }

        $request = $validator->validate();

        return true;
    }

    public function getUnverifiedByFilters(array $request, stdClass &$output): void
    {
        if (! $this->isValidFilters($request, $output)) {
            return;
        }
        $this->getCicosFilterService->perform($request, $output);
    }

    public function workerGetUnverifiedByFilters(array $request, stdClass &$output): void
    {
        if (! $this->isValidFilters($request, $output)) {
            return;
        }

        $request['employee_id'] = auth()->user()->employee_id;

        $this->getCicosFilterService->perform($request, $output);
    }
}
