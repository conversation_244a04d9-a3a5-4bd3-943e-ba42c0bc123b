<?php

namespace App\Handlers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

interface ExceptionHandlerInterface
{
    public static function handle(Request $request, Throwable $exception): JsonResponse;

    public static function render(Request $request, Throwable $exception): JsonResponse;

    public static function shouldLog(Request $request, Throwable $exception): bool;

    public static function getPayload(Request $request, Throwable $exception): array;

    public static function getResponseStatusCode(Throwable $exception): int;
}
