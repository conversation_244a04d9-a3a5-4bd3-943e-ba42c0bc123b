<?php

namespace App\Http\Controllers\V1\EmployeeProfile;

use App\Exports\EmployeesExport;
use App\Http\Controllers\NewController;
use App\Http\Requests\EmployeeProfileListRequest;
use App\Http\Requests\GetEmployeeScopesRequest;
use App\Http\Requests\V1\EmplyoeeProfile\EmployeePayslipRequest;
use App\Http\Requests\V1\EmplyoeeProfile\UploadProfilePictureByManagerRequest;
use App\Http\Requests\V1\EmplyoeeProfile\UploadProfilePictureRequest;
use App\Http\Requests\V1\GetCentralRequest;
use App\Http\Requests\V1\KPIs\GetIncentivesSettingsRequest;
use App\Http\Requests\V1\WorkerApp\GetEmployeeInfoByIdRequest;
use App\Http\Requests\VerifyEmployeePinRequest;
use App\Http\Resources\RequestGroupsEmployeeResource;
use App\Http\Resources\V1\EmployeeProfile\EmployeeAllowancesResource;
use App\Http\Resources\V1\EmployeeProfile\EmployeeInfoResource;
use App\Http\Resources\V1\EmployeeProfile\EmployeeProfileCollection;
use App\Http\Resources\V1\EmployeeProfile\EmployeeRoleCollection;
use App\Http\Resources\V1\EmployeeProfile\EmployeeSalaryInfoResource;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\V1\PayRollPolicyRepository;
use App\Services\V1\EmployeeProfile\EmployeeProfileService;
use App\Services\V1\KPIs\IncentiveService;
use App\Traits\GetLastDraftedPayRollOrCreate;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class EmployeeProfileController extends NewController
{
    use GetLastDraftedPayRollOrCreate, PayrollHelper;

    public function __construct(EmployeeProfileService $service,
        private SystemSettingRepository $systemSettingRepository,
        private PayRollPolicyRepository $payRollPolicyRepository,
        private PayrollsRepository $payrollsRepository,
        private IncentiveService $incentiveService

    ) {
        $this->service = $service;
    }

    public function list(EmployeeProfileListRequest $request)
    {
        $data = $request->validated();

        $employeeProfiles = $this->service->list($data);
        $response = (new EmployeeProfileCollection($employeeProfiles))->response()->getData();

        return getResponseStructure(['data' => $response->data, 'pagination' => $response?->meta ?? []], HttpStatusCodeUtil::OK);
    }

    public function getEmployeeNames(GetCentralRequest $request)
    {
        $importantData = $this->service->getEmployeeNames($request->validated());

        return getResponseStructure(['data' => $importantData], HttpStatusCodeUtil::OK);
    }

    public function exportEmployees(EmployeeProfileListRequest $request)
    {

        $data = $request->all();
        unset($data['page_size']);
        unset($data['page']);

        $employees = $this->service->list($data);

        return Excel::download(new EmployeesExport($employees), 'Employees.xlsx', \Maatwebsite\Excel\Excel::XLSX);

    }

    public function getEmployeeInfo(GetEmployeeInfoByIdRequest $request, $id = null)
    {
        if (! isset($id)) {
            if ($request->has('employee_id')) {
                $id = $request->employee_id;
            } else {
                $id = auth()->user()->employee_id;
            }
        }
        $employeeInfo = $this->service->getEmployeeInfo($id);

        return getResponseStructure(['data' => new EmployeeInfoResource($employeeInfo)], HttpStatusCodeUtil::OK);
    }

    public function getEmployeeSalaryInfo($id)
    {
        [$employeeSalaryInfo, $allowances] = $this->service->getEmployeeSalaryInfo($id);
        $employeeSalaryInfo->payrollId = $this->getCurrentPayroll()->id;
        $response = collect(new EmployeeSalaryInfoResource($employeeSalaryInfo))->merge(new EmployeeAllowancesResource($allowances));

        return getResponseStructure(['data' => $response], HttpStatusCodeUtil::OK);
    }

    public function getEmployeeRoles($id)
    {
        $employeeRoles = $this->service->getEmployeeRoles($id);
        $this->getPermissionsGroupedByCategories($employeeRoles);

        return getResponseStructure(['data' => new EmployeeRoleCollection($employeeRoles)], HttpStatusCodeUtil::OK);
    }

    public function getPermissionsGroupedByCategories(&$employeeRoles)
    {
        $lang = (config('globals.lang') ?? 'ar');

        foreach ($employeeRoles as $role) {
            $permissions = $role->permissions->groupBy('permissionGroup.name_ar');
            $role->permission_groups = $role->permissions->map(function ($permission) use ($permissions, $lang) {
                return [
                    'id' => $permission->permission_group_id,
                    'name' => $lang == 'en' ? $permission->permissionGroup->name_en : $permission->permissionGroup->name_ar,
                    'permissions' => $permissions[$permission->permissionGroup->name_ar]->map(function ($p) use ($lang) {
                        return [
                            'id' => $p->id,
                            'name' => $lang == 'en' ? $p->name_en : $p->name_ar,
                        ];
                    }),
                ];
            })->unique()->values()->all();
        }
    }

    public function assignRoleToEmployee($employee_id, $role_id)
    {
        DB::beginTransaction();
        try {
            $this->service->assignRoleToEmployee($employee_id, $role_id);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function getEmployeePayslip($id, EmployeePayslipRequest $request)
    {
        $data = $request->validated();
        $employeePayslip = $this->service->getEmployeePayslip($id, $data);

        return getResponseStructure(['data' => $employeePayslip], HttpStatusCodeUtil::OK);
    }

    public function getEmployeeProfilePicture(int $id)
    {

        $profilePicture = $this->service->getEmployeeProfilePicture($id);

        return getResponseStructure(['data' => $profilePicture ?? []], HttpStatusCodeUtil::OK);

    }

    public function uploadProfilePicture(UploadProfilePictureRequest $request)
    {

        DB::beginTransaction();
        try {
            $data = $request->validated();
            if (! isset($data['employee_id'])) {
                $data['employee_id'] = auth()->user()->employee_id;
            }
            $profilePicture = $this->service->uploadProfilePicture($data);
            DB::commit();
        } catch (\Exception $e) {
            Log::info($e);
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => $profilePicture ?? []], HttpStatusCodeUtil::OK);

    }

    public function uploadProfilePictureByManager(UploadProfilePictureByManagerRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->validated();

            $profilePicture = $this->service->uploadProfilePicture($data);
            DB::commit();
        } catch (\Exception $e) {
            Log::info($e);
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => $profilePicture ?? []], HttpStatusCodeUtil::OK);
    }

    public function resetDeviceId(int $id)
    {
        DB::beginTransaction();
        try {
            $this->service->resetDeviceId($id);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);

            return getErrorResponseStructure($e->getCode(), $e->getMessage());

        }

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK, 'Device ID reset successfully');
    }

    public function getScopeEmployees(GetEmployeeScopesRequest $request)
    {
        $result = $this->service->getScopeEmployees($request);

        return getResponseStructure([
            'data' => [
                'scope' => $result['scope'],
                'employees' => new EmployeeProfileCollection($result['employees']),
                'count' => $result['employees']->count(),
                'branches' => $result['branches'],
                'departments' => $result['departments'],
                'sub_departments' => $result['sub_departments'],
            ],
        ], HttpStatusCodeUtil::OK);
    }

    public function getEmployeeRequests($id)
    {
        $requestGroup = $this->service->getEmployeeRequests($id);

        return getResponseStructure([
            'data' => new RequestGroupsEmployeeResource($requestGroup),
        ], HttpStatusCodeUtil::OK);
    }

    public function getEmployeeIncentives(GetIncentivesSettingsRequest $request)
    {
        $request->validated();
        if ($request->has('employee_id')) {
            $id = $request->employee_id;
        } else {
            $id = auth()->user()->employee_id;
        }
        $incentives = $this->service->getEmployeeIncentives($id);
        $finalizedIncentives = $this->incentiveService->getFinalizedIncentives($id, $request);

        return getResponseStructure([
            'data' => [
                'incentives' => $incentives,
                'finalizedIncentives' => $finalizedIncentives,
            ],
        ], HttpStatusCodeUtil::OK);
    }

    public function verifyEmployeePin(VerifyEmployeePinRequest $request)
    {
        $data = $request->validated();
        $this->service->verifyEmployeePin($data);

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }
}
