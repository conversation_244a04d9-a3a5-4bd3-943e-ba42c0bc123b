<?php

namespace App\Http\Controllers\V1\EmployeeProfile;

use App\Enums\Employee\EmployeeBulkOperationTypeEnum;
use App\Exports\EmployeesExport;
use App\Http\Controllers\NewController;
use App\Http\Requests\EmployeeProfileListRequest;
use App\Http\Requests\ExportEmployeeProfileListRequest;
use App\Http\Requests\GetEmployeeScopesRequest;
use App\Http\Requests\V1\Employee\AddEmployeeProfileRequest;
use App\Http\Requests\V1\Employee\UpdateEmployeeProfileRequest;
use App\Http\Requests\V1\Employee\CreateEmployeeExportTemplateRequest;
use App\Http\Requests\V1\Employee\EmployeeBulkOperationRequest;
use App\Http\Requests\V1\Employee\EmployeeBulkUploadRequest;
use App\Http\Requests\V1\Employee\GetScopeEmployeesForLoansRequest;
use App\Http\Requests\V1\EmplyoeeProfile\EmployeePayslipRequest;
use App\Http\Requests\V1\EmplyoeeProfile\UploadProfilePictureByManagerRequest;
use App\Http\Requests\V1\EmplyoeeProfile\UploadProfilePictureRequest;
use App\Http\Requests\V1\GetCentralRequest;
use App\Http\Requests\V1\KPIs\GetIncentivesSettingsRequest;
use App\Http\Requests\V1\WorkerApp\GetEmployeeInfoByIdRequest;
use App\Http\Requests\VerifyEmployeePinRequest;
use App\Http\Resources\RequestGroupsEmployeeResource;
use App\Http\Resources\V1\EmployeeProfile\EmployeeAllowancesResource;
use App\Http\Resources\V1\EmployeeProfile\EmployeeInfoResource;
use App\Http\Resources\V1\EmployeeProfile\EmployeeProfileCollection;
use App\Http\Resources\V1\EmployeeProfile\EmployeeRoleCollection;
use App\Http\Resources\V1\EmployeeProfile\EmployeeSalaryInfoResource;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\V1\PayRollPolicyRepository;

use App\Services\V1\EmployeeProfile\EmployeeProfileService;
use App\Services\V1\ExportTemplate\ExportTemplateService;
use App\Services\V1\KPIs\IncentiveService;
use App\Traits\GetLastDraftedPayRollOrCreate;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Services\CompanySetup\BusinessServices\EmployeeCsvService;
use PhpParser\Node\Expr\Cast\String_;

class EmployeeProfileController extends NewController
{
    use GetLastDraftedPayRollOrCreate, PayrollHelper;

    public function __construct(
        EmployeeProfileService $service,
        private SystemSettingRepository $systemSettingRepository,
        private PayRollPolicyRepository $payRollPolicyRepository,
        private PayrollsRepository $payrollsRepository,
        private IncentiveService $incentiveService,
        private ExportTemplateService $exportTemplateService,
        private EmployeeCsvService $employeeCsvService
    ) {
        $this->service = $service;
    }

    public function list(EmployeeProfileListRequest $request)
    {
        $data = $request->validated();

        $employeeProfiles = $this->service->list($data);
        $response = (new EmployeeProfileCollection($employeeProfiles))->response()->getData();

        return getResponseStructure(['data' => $response->data, 'pagination' => $response?->meta ?? []], HttpStatusCodeUtil::OK);
    }

    public function getEmployeeNames(GetCentralRequest $request)
    {
        $importantData = $this->service->getEmployeeNames($request->validated());

        return getResponseStructure(['data' => $importantData], HttpStatusCodeUtil::OK);
    }

    public function exportEmployees(EmployeeProfileListRequest $request)
    {
        $data = $request->all();
        unset($data['page_size']);
        unset($data['page']);

        $employees = $this->service->list($data);

        // Pass both employees data and request to the EmployeesExport class
        return Excel::download(
            new EmployeesExport($employees, $request),
            'Employees.xlsx',
            \Maatwebsite\Excel\Excel::XLSX
        );
    }
    public function enhancedExportEmployees(ExportEmployeeProfileListRequest $request)
    {
        $data = $request->all();
        unset($data['page_size']);
        unset($data['page']);

        $employees = $this->service->list($data);

        // Pass both employees data and request to the EmployeesExport class
        return Excel::download(
            new EmployeesExport($employees, $request),
            'Employees.xlsx',
            \Maatwebsite\Excel\Excel::XLSX
        );
    }
    public function getEmployeeInfo(GetEmployeeInfoByIdRequest $request, $id = null)
    {
        if (! isset($id)) {
            if ($request->has('employee_id')) {
                $id = $request->employee_id;
            } else {
                $id = auth()->user()->employee_id;
            }
        }

        $employeeInfo = $this->service->getEmployeeInfo($id);
        return getResponseStructure(['data' => new EmployeeInfoResource($employeeInfo)], HttpStatusCodeUtil::OK);
    }
    public function getEmployeeSalaryInfo($id)
    {
        [$employeeSalaryInfo, $allowances] = $this->service->getEmployeeSalaryInfo($id);
        $employeeSalaryInfo->payrollId = $this->getCurrentPayroll()->id;
        $response = collect(new EmployeeSalaryInfoResource($employeeSalaryInfo))->merge(new EmployeeAllowancesResource($allowances));

        return getResponseStructure(['data' => $response], HttpStatusCodeUtil::OK);
    }

    public function getEmployeeRoles($id)
    {
        $employeeRoles = $this->service->getEmployeeRoles($id);
        $this->getPermissionsGroupedByCategories($employeeRoles);

        return getResponseStructure(['data' => new EmployeeRoleCollection($employeeRoles)], HttpStatusCodeUtil::OK);
    }

    public function getPermissionsGroupedByCategories(&$employeeRoles)
    {
        $lang = (config('globals.lang') ?? 'ar');

        foreach ($employeeRoles as $role) {
            $permissions = $role->permissions->groupBy('permissionGroup.name_ar');
            $role->permission_groups = $role->permissions->map(function ($permission) use ($permissions, $lang) {
                return [
                    'id' => $permission->permission_group_id,
                    'name' => $lang == 'en' ? $permission->permissionGroup->name_en : $permission->permissionGroup->name_ar,
                    'permissions' => $permissions[$permission->permissionGroup->name_ar]->map(function ($p) use ($lang) {
                        return [
                            'id' => $p->id,
                            'name' => $lang == 'en' ? $p->name_en : $p->name_ar,
                        ];
                    }),
                ];
            })->unique()->values()->all();
        }
    }

    public function assignRoleToEmployee($employee_id, $role_id)
    {
        DB::beginTransaction();
        try {
            $this->service->assignRoleToEmployee($employee_id, $role_id);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function getEmployeePayslip($id, EmployeePayslipRequest $request)
    {
        $data = $request->validated();
        $employeePayslip = $this->service->getEmployeePayslip($id, $data);

        return getResponseStructure(['data' => $employeePayslip], HttpStatusCodeUtil::OK);
    }

    public function getEmployeeProfilePicture(int $id)
    {

        $profilePicture = $this->service->getEmployeeProfilePicture($id);

        return getResponseStructure(['data' => $profilePicture ?? []], HttpStatusCodeUtil::OK);

    }

    public function uploadProfilePicture(UploadProfilePictureRequest $request)
    {

        DB::beginTransaction();
        try {
            $data = $request->validated();
            if (! isset($data['employee_id'])) {
                $data['employee_id'] = auth()->user()->employee_id;
            }
            $profilePicture = $this->service->uploadProfilePicture($data);
            DB::commit();
        } catch (\Exception $e) {
            Log::info($e);
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => $profilePicture ?? []], HttpStatusCodeUtil::OK);

    }

    public function uploadProfilePictureByManager(UploadProfilePictureByManagerRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->validated();

            $profilePicture = $this->service->uploadProfilePicture($data);
            DB::commit();
        } catch (\Exception $e) {
            Log::info($e);
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => $profilePicture ?? []], HttpStatusCodeUtil::OK);
    }

    public function resetDeviceId(int $id)
    {
        DB::beginTransaction();
        try {
            $this->service->resetDeviceId($id);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);

            return getErrorResponseStructure($e->getCode(), $e->getMessage());

        }

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK, 'Device ID reset successfully');
    }

    public function getScopeEmployees(GetEmployeeScopesRequest $request)
    {
        $result = $this->service->getScopeEmployees($request);

        return getResponseStructure([
            'data' => [
                'scope' => $result['scope'],
                'employees' => new EmployeeProfileCollection($result['employees']),
                'count' => $result['employees']->count(),
                'branches' => $result['branches'],
                'departments' => $result['departments'],
                'sub_departments' => $result['sub_departments'],
            ],
        ], HttpStatusCodeUtil::OK);
    }

    public function getEmployeeRequests($id)
    {
        $requestGroup = $this->service->getEmployeeRequests($id);

        return getResponseStructure([
            'data' => new RequestGroupsEmployeeResource($requestGroup),
        ], HttpStatusCodeUtil::OK);
    }

    public function getScopeEmployeesForLoans(GetScopeEmployeesForLoansRequest $request)
    {
        $result = $this->service->getScopeEmployeesForLoans($request);

        return getResponseStructure([
            'data' => $result,
        ], HttpStatusCodeUtil::OK);
    }
    public function updateProfile(int $id, UpdateEmployeeProfileRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->validated();
            $result = $this->service->updateProfile($id, $data);
            DB::commit();

            return getResponseStructure(['data' => $result], HttpStatusCodeUtil::OK, trans('messages.profile_updated_success'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }
    }

    public function addProfile(AddEmployeeProfileRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->validated();
            $employee = $this->service->addProfile($data);
            DB::commit();

            return getResponseStructure(['data' => ['employee_id' => $employee->id]], HttpStatusCodeUtil::OK, trans('messages.employee_profile_created_success'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);

            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }
    }
    public function getEmployeeIncentives(GetIncentivesSettingsRequest $request)
    {
        $request->validated();
        if ($request->has('employee_id')) {
            $id = $request->employee_id;
        } else {
            $id = auth()->user()->employee_id;
        }
        $incentives = $this->service->getEmployeeIncentives($id);
        $finalizedIncentives = $this->incentiveService->getFinalizedIncentives($id, $request);

        return getResponseStructure([
            'data' => [
                'incentives' => $incentives,
                'finalizedIncentives' => $finalizedIncentives,
            ],
        ], HttpStatusCodeUtil::OK);
    }

    public function verifyEmployeePin(VerifyEmployeePinRequest $request)
    {
        $data = $request->validated();
        $this->service->verifyEmployeePin($data);

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function getEmployeeExportTemplates()
    {
        try {
            $templatesCollection = $this->exportTemplateService->getEmployeeExportTemplates();

            return getResponseStructure(
                ['data' => $templatesCollection],
                HttpStatusCodeUtil::OK,
                trans('messages.templates_retrieved_success')
            );
        } catch (\Exception $e) {
            Log::error($e);
            return getErrorResponseStructure(
                HttpStatusCodeUtil::INTERNAL_SERVER_ERROR,
                trans('messages.failed_retrieve_templates')
            );
        }
    }

    public function createEmployeeExportTemplate(CreateEmployeeExportTemplateRequest $request)
    {
        try {
            $data = $request->validated();
            $templateResource = $this->exportTemplateService->createEmployeeExportTemplate($data);

            return getResponseStructure(
                ['data' => $templateResource],
                HttpStatusCodeUtil::CREATED,
                trans('messages.template_created_success')
            );
        } catch (\Exception $e) {
            Log::error($e);
            return getErrorResponseStructure(
                HttpStatusCodeUtil::INTERNAL_SERVER_ERROR,
                trans('messages.failed_create_template')
            );
        }
    }

    public function deleteEmployeeExportTemplate(int $id)
    {
        try {
            $result = $this->exportTemplateService->deleteEmployeeExportTemplate($id);

            if (!$result) {
                return getErrorResponseStructure(
                    HttpStatusCodeUtil::NOT_FOUND,
                    trans('messages.template_not_found')
                );
            }

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                trans('messages.template_deleted_success')
            );
        } catch (\Exception $e) {
            Log::error($e);
            return getErrorResponseStructure(
                HttpStatusCodeUtil::INTERNAL_SERVER_ERROR,
                trans('messages.failed_delete_template')
            );
        }
    }

    public function bulkTemplateDownload(EmployeeBulkOperationRequest $request)
    {
        try {
            $data = $request->validated();
            $output = new \stdClass();


            $employees = null;
            if ($data['type'] === EmployeeBulkOperationTypeEnum::EDIT->value) {
                $requestData = $request->all();
                unset($requestData['page_size']);
                unset($requestData['page']);

                $employees = $this->service->listForBulkEdit($requestData);
            }

            $this->employeeCsvService->downloadDynamicTemplate($data, $output, $employees);

            if (isset($output->Error)) {
                Log::error($output->Error);
                return response()->json([
                    'Error' => $output->Error
                ], HttpStatusCodeUtil::BAD_REQUEST);
            }
            return $output->return_link;

        } catch (\Exception $e) {
            Log::error('Failed to generate bulk operation download: ' . $e->getMessage(), [
                'request_data' => $request->validated(),
                'exception' => $e
            ]);

            return getErrorResponseStructure(
                HttpStatusCodeUtil::INTERNAL_SERVER_ERROR,
                trans('messages.failed_generate_download')
            );
        }
    }

    public function bulkUpload(EmployeeBulkUploadRequest $request)
    {
        try {
            $data = $request->validated();

            $output = new \stdClass();

            $this->employeeCsvService->performBulkUpload($data, $output);
            
            if (isset($output->Error)) {
                return response()->json([
                    'Error' => $output->Error
                ], HttpStatusCodeUtil::BAD_REQUEST);
            }

            $result = $output->result ?? [];
            $hasErrors = !empty($result['errors'] ?? []);

            $successMessage = $data['type'] === 'add'
                ? trans('messages.employees_bulk_added_success')
                : trans('messages.employees_bulk_updated_success');

            // If there are errors but also successful records, modify the message
            if ($hasErrors && ($result['processed_count'] ?? 0) > 0) {
                $successMessage .= ' ' . trans('messages.bulk_upload_partial_success', [
                    'successful' => $result['processed_count'],
                    'errors' => count($result['errors'])
                ]);
            }

            return getResponseStructure(
                ['data' => $result],
                HttpStatusCodeUtil::OK,
                $successMessage
            );

        } catch (\Exception $e) {
            Log::error($e);
            Log::error('Failed to process bulk upload: ' . $e->getMessage(), [
                'request_data' => $request->validated(),
                'exception' => $e
            ]);

            return getErrorResponseStructure(
                HttpStatusCodeUtil::INTERNAL_SERVER_ERROR,
                trans('messages.failed_process_bulk_upload')
            );
        }
    }
}
