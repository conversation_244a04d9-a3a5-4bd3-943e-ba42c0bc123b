<?php

namespace App\Services\V1\LeaveManagement;

use App\Models\EmployeeLeaveRequest;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\Repository;
use App\Repositories\V1\Holidays\PublicHolidaysRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveBalancesRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use App\Services\LeaveManagement\BusinessServices\BalanceAggregatorService;
use App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService;
use App\Traits\DataPreparation;
use App\Traits\LeaveManagementNet;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class LeavesSplitterService
{
    use DataPreparation, LeaveManagementNet;

    private $oldEmployeeLeaveRequestRepository;

    public function __construct(
        private PublicHolidaysRepository $publicHolidayRepository,
        private EmployeeLeaveRequestRepository $employeeLeaveRequestRepository,
        private EmployeeLeaveBalancesRepository $employeeLeaveBalanceRepository,
        private NewEmployeeRepository $employeeRepository
    ) {
        $this->oldEmployeeLeaveRequestRepository = Repository::getRepository('EmployeeLeaveRequest');
    }

    public function splitLeaveBasedOnHolidaysAndRestdays(EmployeeLeaveRequest $employeeLeaveRequest): array
    {
        Log::info('employeeLeaveRequest status: '.json_encode($employeeLeaveRequest->status ?? []));
        if ($employeeLeaveRequest->status != config('globals.REQUEST_STATUSES.APPROVED')) {
            return [$employeeLeaveRequest];
        }
        $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $leaveStart = Carbon::parse($employeeLeaveRequest->from)->toDateString();
        $leaveEnd = Carbon::parse($employeeLeaveRequest->to)->toDateString();
        $intersectingPublicHolidays = $this->publicHolidayRepository->getHolidaysIntersectingWithLeave($leaveStart, $leaveEnd);
        $restDayLeaveId = $employeeLeaveRequest->employee->company->rest_day_leave_id;
        $intersectingRestDays = $this->oldEmployeeLeaveRequestRepository->getRestDays($employeeLeaveRequest->employee_id, $leaveStart, $leaveEnd, $restDayLeaveId);

        $intersectingDays = $this->getIntersectingDays($intersectingPublicHolidays, $intersectingRestDays, $employeeLeaveRequest);
        if (! count($intersectingDays)) {
            return [$employeeLeaveRequest];
        }
        $workflowApprovalCycles = $employeeLeaveRequest->workflowApprovalCycles;
        $originalLeave = clone $employeeLeaveRequest;
        $start = Carbon::parse($employeeLeaveRequest->from)->startOfDay();
        $end = Carbon::parse($employeeLeaveRequest->to)->endOfDay();
        $days = $start->diffInDays($end);
        $nonIntersectingDays = [];
        $currentStart = '';
        $currentEnd = '';
        $prev = '';
        $newLeaveRanges = [];
        $day = clone $start;
        for ($i = 0; $i <= $days; $i++) {
            $date = $day->toDateString();
            if (! in_array($date, $intersectingDays)) {
                if ($currentStart == '') {
                    $currentStart = $date;
                }
                $prev = $date;

            } else {
                if ($currentStart != '') {
                    $currentEnd = $prev;
                    $newLeaveRanges[] = ['start' => $currentStart, 'end' => $currentEnd];
                    $currentStart = '';
                    $currentEnd = '';
                }
            }
            $day->addDay();
        }
        if ($currentStart != '') {
            $currentEnd = $prev;
            $newLeaveRanges[] = ['start' => $currentStart, 'end' => $currentEnd];
        }
        $createdLeaves = [];
        if (count($newLeaveRanges) > 0) {
            foreach ($newLeaveRanges as $range) {
                $newLeaveData = $originalLeave->toArray();
                $newLeaveData['from'] = Carbon::parse($range['start'])->startOfDay()->toDateString();
                $newLeaveData['to'] = Carbon::parse($range['end'])->endOfDay()->toDateString();
                $newLeaveData['net_quantity'] = (Carbon::parse($range['start'])->diffInDays(Carbon::parse($range['end'])) + 1) * config('globals.WORKED_HOURS_PER_DAY');
                $newLeave = $this->oldEmployeeLeaveRequestRepository->create($newLeaveData);
                $newLeave->workflow_id = $originalLeave['workflow_id'];
                $newLeave->save();
                $createdLeaves[] = $newLeave;
                foreach ($workflowApprovalCycles as $workflowApprovalCycle) {
                    $newWorkflow = $workflowApprovalCycle->toArray();
                    $newWorkflow['requestable_id'] = $newLeave->id;
                    $workflowApprovalCycleRepository->add($newWorkflow);
                }
            }
        }
        $originalLeave->status = config('globals.REQUEST_STATUSES.CANCELLED');
        $originalLeave->save();

        return $createdLeaves;

    }

    private function getIntersectingDays($intersectingHolidays, $intersectingRestdays, $leaveRequest): array
    {
        $intersectingDays = [];
        foreach ($intersectingHolidays as $holiday) {
            $start = Carbon::parse($holiday->start);
            $end = Carbon::parse($holiday->end);
            $days = $start->diffInDays($end);
            for ($i = 0; $i <= $days; $i++) {
                $intersectingDays[] = $start->addDays($i)->toDateString();
            }
        }

        foreach ($intersectingRestdays as $restday) {
            $intersectingDays[] = Carbon::parse($restday->from)->toDateString();
        }

        $employee = $this->employeeRepository->find($leaveRequest->employee_id);
        $workType = $employee->title->workTypePolicy->work_days_type;
        if ($workType != 'dynamic_onsite') {
            $restDays = explode(',', $employee->title->workTypePolicy->rest_days);
            $leaveStart = Carbon::parse($leaveRequest->from);
            $leaveEnd = Carbon::parse($leaveRequest->to);
            $days = $leaveStart->diffInDays($leaveEnd);
            for ($i = 0; $i <= $days; $i++) {
                $date = $leaveStart->copy()->addDays($i);
                if (in_array(strtolower($date->format('D')), $restDays)) {
                    $intersectingDays[] = $date->toDateString();
                }
            }
        }
        Log::info('intersecting days '.json_encode($intersectingDays));

        return $intersectingDays;
    }

    public function addMissionTimecardOnLeave(EmployeeLeaveRequest $employeeLeaveRequest, $missionTimecardDate): void // TODO to revist this
    {
        Log::info('in addMissionTimecardOnLeave');
        $dummyOutput = new \stdClass;
        $employeeLeaveRequestCrudService = app(EmployeeLeaveRequestCrudService::class);
        $balanceAggregatorService = app(BalanceAggregatorService::class);

        if ($this->isRestDayLeave($employeeLeaveRequest)) {
            $this->handleRestDayLeave($employeeLeaveRequest, $employeeLeaveRequestCrudService, $dummyOutput);

            return;
        }

        $from = Carbon::parse($employeeLeaveRequest->from);
        $to = Carbon::parse($employeeLeaveRequest->to);
        $date = Carbon::parse($missionTimecardDate);

        $employeeLeaveBalance = $this->employeeLeaveBalanceRepository->leaveBalanceForEmployeeOfLeaveTypePolicy(
            $employeeLeaveRequest->employee_id,
            $missionTimecardDate,
            $employeeLeaveRequest->company_leave_type_policy_id
        );

        if ($this->isSingleDayLeave($from, $to)) {
            Log::info('in addMissionTimecardOnLeave isSingleDayLeave');
            $this->cancelLeaveRequest($employeeLeaveRequest);
        } elseif ($this->isFirstDayOfLeave($date, $from)) {
            Log::info('in addMissionTimecardOnLeave isFirstDayOfLeave');
            $this->updateLeaveRequestForFirstDay($employeeLeaveRequest, $date);
        } elseif ($this->isLastDayOfLeave($date, $to)) {
            Log::info('in addMissionTimecardOnLeave isLastDayOfLeave');
            $this->updateLeaveRequestForLastDay($employeeLeaveRequest, $date);
        } else {
            Log::info('in addMissionTimecardOnLeave splitLeaveRequest');
            $this->splitLeaveRequest($employeeLeaveRequest, $date, $missionTimecardDate);
        }

        $this->aggregateLeaveBalance($employeeLeaveBalance, $employeeLeaveRequest, $balanceAggregatorService, $dummyOutput);
    }

    private function isRestDayLeave(EmployeeLeaveRequest $employeeLeaveRequest): bool
    {
        return $employeeLeaveRequest->company_leave_type_id == config('globals.rest_day_leave_id');
    }

    private function handleRestDayLeave(EmployeeLeaveRequest $employeeLeaveRequest, $crudService, $dummyOutput): void
    {
        Log::info('in addMissionTimecardOnLeave handleRestDayLeave');
        $request['ids'] = [$employeeLeaveRequest->id];
        $crudService->deleteRestDays($request, $dummyOutput);
    }

    private function isSingleDayLeave(Carbon $from, Carbon $to): bool
    {
        return $from->toDateString() === $to->toDateString();
    }

    private function isFirstDayOfLeave(Carbon $date, Carbon $from): bool
    {
        return $date->toDateString() === $from->toDateString();
    }

    private function isLastDayOfLeave(Carbon $date, Carbon $to): bool
    {
        return $date->toDateString() === $to->toDateString();
    }

    private function cancelLeaveRequest(EmployeeLeaveRequest $employeeLeaveRequest): void
    {
        $employeeLeaveRequest->status = config('globals.REQUEST_STATUSES.CANCELLED');
        $employeeLeaveRequest->save();
    }

    private function updateLeaveRequestForFirstDay(EmployeeLeaveRequest $employeeLeaveRequest, Carbon $date): void
    {
        $request = [
            'from' => $date->addDay()->startOfDay()->toDateTimeString(),
            'to' => $employeeLeaveRequest->to,
            'net_quantity' => $this->recalculateNetQuantity(
                config('globals.LEAVE_UNITS.DAYS'),
                $employeeLeaveRequest->employee_id,
                $date->addDay()->startOfDay()->toDateTimeString(),
                $employeeLeaveRequest->to,
                $employeeLeaveRequest->id
            ),
        ];
        $this->employeeLeaveRequestRepository->update($employeeLeaveRequest, $request);
    }

    private function updateLeaveRequestForLastDay(EmployeeLeaveRequest $employeeLeaveRequest, Carbon $date): void
    {
        $request = [
            'from' => $employeeLeaveRequest->from,
            'to' => $date->subDay()->endOfDay()->toDateTimeString(),
            'net_quantity' => $this->recalculateNetQuantity(
                config('globals.LEAVE_UNITS.DAYS'),
                $employeeLeaveRequest->employee_id,
                $employeeLeaveRequest->from,
                $date->subDay()->endOfDay()->toDateTimeString(),
                $employeeLeaveRequest->id
            ),
        ];
        $this->employeeLeaveRequestRepository->update($employeeLeaveRequest, $request);
    }

    private function splitLeaveRequest(EmployeeLeaveRequest $employeeLeaveRequest, Carbon $date, $missionTimecardDate): void
    {
        $firstLeaveRequest = $employeeLeaveRequest->toArray();
        $secondLeaveRequest = $employeeLeaveRequest->toArray();
        Log::info('leaveId to be splitted '.$employeeLeaveRequest->id.' '.$employeeLeaveRequest->employee_id);
        $firstLeaveRequest['from'] = $employeeLeaveRequest->from;
        $firstLeaveRequest['to'] = $date->subDay()->endOfDay()->toDateTimeString();
        $firstLeaveRequest['net_quantity'] = $this->recalculateNetQuantity(
            config('globals.LEAVE_UNITS.DAYS'),
            $employeeLeaveRequest->employee_id,
            $firstLeaveRequest['from'],
            $firstLeaveRequest['to'],
            $employeeLeaveRequest->id
        );
        $this->employeeLeaveRequestRepository->add($firstLeaveRequest);

        $date = Carbon::parse($missionTimecardDate);
        $secondLeaveRequest['from'] = $date->addDay()->startOfDay()->toDateTimeString();
        $secondLeaveRequest['net_quantity'] = $this->recalculateNetQuantity(
            config('globals.LEAVE_UNITS.DAYS'),
            $employeeLeaveRequest->employee_id,
            $secondLeaveRequest['from'],
            $secondLeaveRequest['to'],
            $employeeLeaveRequest->id
        );
        $this->employeeLeaveRequestRepository->add($secondLeaveRequest);

        $this->cancelLeaveRequest($employeeLeaveRequest);
    }

    private function aggregateLeaveBalance($employeeLeaveBalance, EmployeeLeaveRequest $employeeLeaveRequest, $balanceAggregatorService, $dummyOutput): void
    {
        $aggregateBalanceRequest = $this->prepareBalanceAggregatorRequest(
            $employeeLeaveBalance->id,
            $employeeLeaveRequest->employee_id
        );
        $balanceAggregatorService->perform($aggregateBalanceRequest, $dummyOutput);
    }
}
