<?php

namespace App\Repositories\V1\Employee;

use App\Models\EmployeeEmergencyContact;
use App\Repositories\BaseRepository;

class EmployeeEmergencyContactRepository extends BaseRepository
{
    public function model(): string
    {
        return EmployeeEmergencyContact::class;
    }
    public function getByEmployeeId(int $employeeId)
    {
        return $this->model->where('employee_id', $employeeId)->get();
    }
}