<?php

namespace App\Console\Commands\OneTimeScript;

use App\Services\V1\Attendance\FixOvertimePolicyPercentageService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixOvertimePolicyPercentage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:overtime:percentage';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = ' ';

    public function __construct(private FixOvertimePolicyPercentageService $fixOvertimePolicyPercentageService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        DB::beginTransaction();
        try {
            $this->fixOvertimePolicyPercentageService->run();
            DB::commit();
        } catch (Exception $e) {
            // \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
