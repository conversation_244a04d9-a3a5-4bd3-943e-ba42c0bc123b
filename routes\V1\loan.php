<?php

use App\Http\Controllers\V1\Loans\LoanController;
use App\Http\Controllers\V1\Loans\SalaryAdvanceController;
use Illuminate\Support\Facades\Route;

Route::prefix('loans')->group(function () {

    Route::get('get-employee-loan', [LoanController::class, 'getEmployeeLoan']);
    Route::get('get-max-salary-advance-percentage', [SalaryAdvanceController::class, 'getMaxSalaryAdvancePercentage']);
    Route::get('get-max-loan-amount', [LoanController::class, 'getMaxLoanAmount']);
    Route::get('get-max-loan-tenor', [LoanController::class, 'getMaxLoanTenors']);
    Route::get('can-request-loan', [LoanController::class, 'canRequestLoan']);
    Route::get('loan-termination-warning', [LoanController::class, 'loanTerminationWarning'])
        ->middleware(['permission:terminate_employee|lite_terminate']);
    Route::post('repay-loan-on-termination/{loanId}', [LoanController::class, 'repayLoanOnTermination'])
        ->middleware(['permission:edit_loans']);
    Route::post('not-repay-loan-on-termination/{loanId}', [LoanController::class, 'notRepayLoanOnTermination'])
        ->middleware(['permission:edit_loans']);
    Route::post('request-loan', [LoanController::class, 'requestLoan']);
    Route::post('disburse-loan/{id}', [LoanController::class, 'disburseLoan'])
        ->middleware(['permission:edit_loans']);
    Route::post('disburse-salary-advance/{id}', [SalaryAdvanceController::class, 'disburseSalaryAdvance'])
        ->middleware(['permission:edit_loans']);
    Route::post('approve-loan/{id}', [LoanController::class, 'approveLoan']);
    Route::post('approve-salary-advance/{id}', [SalaryAdvanceController::class, 'approveSalaryAdvance']);
    Route::post('reject-loan/{id}', [LoanController::class, 'rejectLoan']);
    Route::post('reject-salary-advance/{id}', [SalaryAdvanceController::class, 'rejectSalaryAdvance']);
    Route::post('request-salary-advance', [SalaryAdvanceController::class, 'requestSalaryAdvance']);
    Route::post('cancel-loan/{id}', [LoanController::class, 'cancelLoan'])
        ->middleware(['permission:cancel_loans|manage_all_requests']);
    Route::post('cancel-salary-advance/{id}', [SalaryAdvanceController::class, 'cancelSalaryAdvance'])
        ->middleware(['permission:cancel_loans|manage_all_requests']);
    Route::put('edit-loan/{id}', [LoanController::class, 'editLoan'])
        ->middleware(['permission:extend_loans']);
    Route::get('get-loans-salary-advance-by-filter', [LoanController::class, 'getLoansAndSalaryAdvancesByFilter'])
        ->middleware('permission:view_loans');
    //Route::get('get-loans-salary-advance-by-filter', [LoanController::class, 'getLoansAndSalaryAdvancesByFilter']);
    Route::get('payroll-loan-warning', [LoanController::class, 'getReadyToDisburseLoans']);

    Route::post('bulk-disburse', [LoanController::class, 'bulkDisburse'])
        ->middleware(['permission:edit_loans']);
    Route::get('export', [LoanController::class, 'export'])
        ->middleware('permission:view_loans');
    Route::get('getById', [LoanController::class, 'getLoansById'])
        ->middleware('permission:view_loans');
    Route::prefix('manager')->group(function () {
        Route::post('add-loan', [LoanController::class, 'addLoanByManager'])
            ->middleware(['permission:add_loan']);
        Route::post('add-salary-advance', [LoanController::class, 'addSalaryAdvanceByManager'])
            ->middleware(['permission:add_loan']);
    });

});
