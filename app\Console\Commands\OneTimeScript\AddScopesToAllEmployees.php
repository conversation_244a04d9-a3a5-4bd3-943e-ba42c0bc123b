<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Role;
use App\Models\Scope;
use App\Util\ScopeUtil;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddScopesToAllEmployees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill:employees:scopes';

    /**
     * this command requires fill:default:scopes to run first.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $this->addScopesToAllEmployees();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }

    public function addScopesToAllEmployees()
    {
        $roles = Role::withTrashed()->get();
        foreach ($roles as $role) {
            if ($role->hasPermissionTo('manage_department', 'user-api')) {
                $this->addManagedDepartmentsToEmployeeRole($role);
                $departmentScope = Scope::where('company_id', $role->company_id)->where('key', ScopeUtil::DEPARTMENT_SCOPE)->first();
                if (count($role?->scopes ?? []) == 0) {
                    $role?->scopes()->attach($departmentScope);
                }
            } elseif ($role->hasPermissionTo('manage_company', 'user-api')) {
                $this->addManagedBranchesToEmployeeRole($role);
                $companyScope = Scope::where('company_id', $role->company_id)->where('key', ScopeUtil::COMPANY_SCOPE)->first();
                if (count($role?->scopes ?? []) == 0) {
                    $role?->scopes()->attach($companyScope);
                }
            } elseif ($role->hasPermissionTo('lite_add', 'user-api')) {
                $branchScope = Scope::where('company_id', $role->company_id)->where('key', ScopeUtil::BRANCH_SCOPE)->first();
                if (count($role?->scopes ?? []) == 0) {
                    $role?->scopes()->attach($branchScope);
                }
            }

        }

    }

    public function addManagedDepartmentsToEmployeeRole(Role $role)
    {
        foreach ($role->users as $user) {
            $employee = $user->employee;
            $mainDepartmentId = $employee->title->department_id;
            $managedDepartmentsIds = $employee->managedDepartments->pluck('id')->toArray();

            // Check if the main department ID is already in the managed departments
            if (in_array($mainDepartmentId, $managedDepartmentsIds)) {
                continue;
            }

            $employee->managedDepartments()->sync($mainDepartmentId);

        }

    }

    public function addManagedBranchesToEmployeeRole(Role $role)
    {
        foreach ($role->users as $user) {
            $employee = $user->employee;
            $companyBranchesIds = $employee->company?->branches->pluck('id')->toArray();
            $managedBranchesIds = $employee->branches->pluck('id')->toArray();

            if (count($companyBranchesIds) === count($managedBranchesIds)) {
                continue;
            }

            $employee->branches()->sync($companyBranchesIds);
        }

    }
}
