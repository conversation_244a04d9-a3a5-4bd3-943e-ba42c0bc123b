<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class EmployeeEmergencyContactRule implements Rule
{
    protected $employeeId;

    public function __construct(?int $employeeId = null)
    {
        $this->employeeId = $employeeId;
    }

    public function passes($attribute, $value)
    {
        if (!is_numeric($value)) {
            return false;
        }

        $query = DB::table('employee_emergency_contacts')
            ->where('id', $value)
            ->where('company_id', Auth::user()->company_id)
            ->whereNull('deleted_at');

        // If employee ID is provided, ensure the emergency contact belongs to this employee
        if ($this->employeeId) {
            $query->where('employee_id', $this->employeeId);
        }

        return $query->exists();
    }

    public function message()
    {
        return __('validation.emergency_contact_id');
    }
}
