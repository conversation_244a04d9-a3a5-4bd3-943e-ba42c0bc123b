<?php

namespace App\Console\Commands;

use App\Models\AttendanceOvertime;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CloneOvertimeStatusToEmployeeRequest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:clone-overtime-status-to-employee-request';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            echo 'Cloning overtime status to employee requests'."\n";

            $attendanceOvertimes = AttendanceOvertime::select('id', 'status', 'company_id')
                ->where('status', '!=', 'pending')
                ->WhereHas('employeeRequests', function ($query) {
                    $query->where('status', 'pending');
                })
                ->get();

            echo 'Overtimes count: '.$attendanceOvertimes->count()."\n";

            foreach ($attendanceOvertimes as $overtime) {
                $employeeRequest = $overtime->employeeRequests()->get();

                foreach ($employeeRequest as $request) {
                    $request->status = $overtime->status;
                    $request->save();
                    echo 'Employee request id '.$request->id.' updated for overtime: '.$overtime->id."\n";
                }

            }

            echo 'Overtime status cloned to employee requests'."\n";
            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
        }
    }
}
