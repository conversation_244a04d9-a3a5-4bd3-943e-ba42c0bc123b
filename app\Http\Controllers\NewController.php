<?php

namespace App\Http\Controllers;

use App\Services\BaseService;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller as BaseController;

class NewController extends BaseController
{
    protected BaseService $service;

    /**
     * BaseController constructor.
     */
    public function __construct(BaseService $service)
    {
        $this->service = $service;
    }

    /**
     * @return JsonResponse
     */
    protected function response(?array $payload, int $statusCode, ?string $message = null)
    {
        return getResponseStructure($payload, $statusCode, $message);
    }
}
