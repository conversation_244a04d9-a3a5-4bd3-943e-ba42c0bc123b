<?php

namespace App\Http\Resources\V1\EmployeeProfile;

use App\Helpers\PhoneParserHelper;
use Illuminate\Http\Resources\Json\JsonResource;

class EmployeeEmergencyContactResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'phone' => $this->phone,
            'parsed_phone' => PhoneParserHelper::parsePhone($this->phone),
            'relation' => $this->relation,
        ];
    }
}