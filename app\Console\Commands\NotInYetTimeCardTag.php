<?php

namespace App\Console\Commands;

use App\Services\TimeTracking\CrudServices\EntityTagCrudService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NotInYetTimeCardTag extends Command
{
    public function __construct(private EntityTagCrudService $entityTagCrudService)
    {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trigger:noInYet:timeCards';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        // Log::info('Not In Yet cron job is started at: ' . date('Y-m-d H:i:s'));
        DB::beginTransaction();
        try {
            $this->entityTagCrudService->addNotInYetTagForTimeCards();
            DB::commit();
        } catch (Exception $e) {
            Log::error('Not In Yet cron job failed at: '.date('Y-m-d H:i:s').' with error: '.$e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
