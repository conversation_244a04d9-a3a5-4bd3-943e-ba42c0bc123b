<?php

namespace App\DomainData;

trait RestEmployeeDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'date' => 'required|date',
            'employee_id' => 'required|numeric',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeRestEmployeeDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
