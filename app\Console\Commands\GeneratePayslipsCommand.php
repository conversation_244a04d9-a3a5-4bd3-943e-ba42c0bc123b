<?php

namespace App\Console\Commands;

use App\Services\V1\Payroll\PayslipServiceV2;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Exception;

class GeneratePayslipsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payslips:generate
                            {end_date : The end date for payroll period (Y-m-d format)}
                            {--employee_ids=* : Comma-separated employee IDs (optional)}
                            {--company_id= : Company ID (optional)}
                            {--print_lang=en : Language for payslips (en|ar)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate payslips ZIP file and store it in storage folder using V2 services';

    private PayslipServiceV2 $payslipService;

    /**
     * Create a new command instance.
     */
    public function __construct(PayslipServiceV2 $payslipService)
    {
        parent::__construct();
        $this->payslipService = $payslipService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $endDate = $this->argument('end_date');
            $employeeIds = $this->option('employee_ids');
            $companyId = $this->option('company_id');
            $language = $this->option('print_lang') ?? 'en';

            // Validate end_date format
            if (!$this->validateDate($endDate)) {
                $this->error('Invalid date format. Please use Y-m-d format (e.g., 2024-01-31)');
                return Command::FAILURE;
            }

            // Parse company_id (now it's a single value, not array)
            if (!empty($companyId)) {
                $companyId = (int) $companyId;
            }

            $this->info('Starting payslip generation using V2 services...');
            $this->info("End Date: {$endDate}");
            $this->info("Language: {$language}");

            if (!empty($employeeIdsArray)) {
                $this->info('Employee IDs: ' . implode(', ', $employeeIdsArray));
            } else {
                $this->info('Generating for all employees in the payroll period');
            }

            if (!empty($companyId)) {
                $this->info('Company ID: ' . $companyId);
            }

            // Prepare data for PayslipServiceV2
            $data = [
                'end_date' => $endDate,
                'print_lang' => $language
            ];

            if (!empty($employeeIdsArray)) {
                $data['employee_ids'] = $employeeIdsArray;
            }

            if (!empty($companyId)) {
                $data['company_id'] = $companyId;
            }

            // Generate and store payslips using PayslipServiceV2
            $this->info('Generating and storing payslips ZIP using V2 service...');
            $result = $this->payslipService->generateAndStorePayslips($data);

            $this->info('✅ Payslips generated and stored successfully!');
            $this->info("📁 ZIP file stored at: {$result['zip_path']}");
            $this->info("👥 Total employees: {$result['total_employees']}");
            $this->info("📦 File size: " . $this->formatBytes($result['zip_size']));

            Log::info('Payslips generated via V2 command', [
                'end_date' => $endDate,
                'language' => $language,
                'company_id' => $companyId,
                'total_employees' => $result['total_employees'],
                'zip_path' => $result['zip_path'],
                'zip_size' => $result['zip_size']
            ]);

            return Command::SUCCESS;

        } catch (Exception $e) {
            $this->error('❌ Failed to generate payslips: ' . $e->getMessage());

            Log::error('Payslip generation V2 command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'end_date' => $this->argument('end_date'),
                'employee_ids' => $this->option('employee_ids'),
                'language' => $this->option('print_lang')
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * Validate date format
     */
    private function validateDate(string $date): bool
    {
        $format = 'Y-m-d';
        $dateTime = \DateTime::createFromFormat($format, $date);
        return $dateTime && $dateTime->format($format) === $date;
    }



    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
