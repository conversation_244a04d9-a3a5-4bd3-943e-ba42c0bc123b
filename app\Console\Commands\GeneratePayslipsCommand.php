<?php

namespace App\Console\Commands;

use App\Services\V1\Payroll\PayslipService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Exception;

class GeneratePayslipsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payslips:generate 
                            {end_date : The end date for payroll period (Y-m-d format)}
                            {--employee_ids=* : Comma-separated employee IDs (optional)}
                            {--company_id= : Company ID (optional)}
                            {--lang=en : Language for payslips (en|ar)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate payslips ZIP file and store it in storage folder';

    private PayslipService $payslipService;

    /**
     * Create a new command instance.
     */
    public function __construct(PayslipService $payslipService)
    {
        parent::__construct();
        $this->payslipService = $payslipService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $endDate = $this->argument('end_date');
            $employeeIds = $this->option('employee_ids');
            $companyId = $this->option('company_id');
            $language = $this->option('lang') ?? 'en';

            // Validate end_date format
            if (!$this->validateDate($endDate)) {
                $this->error('Invalid date format. Please use Y-m-d format (e.g., 2024-01-31)');
                return Command::FAILURE;
            }

            // Parse employee IDs properly
            $employeeIdsArray = [];
            if (!empty($employeeIds)) {
                // Handle both array format and comma-separated string
                if (is_array($employeeIds)) {
                    $employeeIdsArray = array_filter(array_map('intval', $employeeIds));
                } else {
                    $employeeIdsArray = array_filter(array_map('intval', array_map('trim', explode(',', $employeeIds))));
                }
            }

            // Set default values if nothing provided
            if (empty($employeeIdsArray)) {
                $employeeIdsArray = [28264]; // Your default employee ID
            }

            // Parse company_id (now it's a single value, not array)
            if (empty($companyId)) {
                $companyId = 20; // Your default company ID
            } else {
                $companyId = (int) $companyId;
            }

            $this->info('Starting payslip generation...');
            $this->info("End Date: {$endDate}");
            $this->info("Language: {$language}");

            if (!empty($employeeIdsArray)) {
                $this->info('Employee IDs: ' . implode(', ', $employeeIdsArray));
            } else {
                $this->info('Generating for all employees in the payroll period');
            }

            if (!empty($companyId)) {
                $this->info('Company ID: ' . $companyId);
            }

            // Prepare data for PayslipService
            $data = [
                'end_date' => $endDate,
                'lang' => $language
            ];

            if (!empty($employeeIdsArray)) {
                $data['employee_ids'] = $employeeIdsArray;
            }

            if (!empty($companyId)) {
                $data['company_id'] = $companyId;
            }

            // Generate ZIP using PayslipService
            $this->info('Generating payslips ZIP...');
            $result = $this->payslipService->generatePayslipsZipForDownload($data);

            // Store the ZIP file in storage
            $storagePath = $this->storeZipFile($result['content'], $result['filename']);

            $this->info('✅ Payslips generated successfully!');
            $this->info("📁 File stored at: {$storagePath}");
            $this->info("👥 Total employees: {$result['total_employees']}");
            $this->info("✅ Successfully generated: {$result['successfully_downloaded']}");
            $this->info("📦 File size: " . $this->formatBytes(strlen($result['content'])));

            Log::info('Payslips generated via command', [
                'end_date' => $endDate,
                'language' => $language,
                'employee_ids' => $employeeIdsArray,
                'company_id' => $companyId,
                'total_employees' => $result['total_employees'],
                'success_count' => $result['successfully_downloaded'],
                'file_path' => $storagePath,
                'file_size' => strlen($result['content'])
            ]);

            return Command::SUCCESS;

        } catch (Exception $e) {
            $this->error('❌ Failed to generate payslips: ' . $e->getMessage());

            Log::error('Payslip generation command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'end_date' => $this->argument('end_date'),
                'employee_ids' => $this->option('employee_ids'),
                'language' => $this->option('lang')
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * Validate date format
     */
    private function validateDate(string $date): bool
    {
        $format = 'Y-m-d';
        $dateTime = \DateTime::createFromFormat($format, $date);
        return $dateTime && $dateTime->format($format) === $date;
    }

    /**
     * Store ZIP file in storage and return the path
     */
    private function storeZipFile(string $content, string $filename): string
    {
        $directory = 'payslips/' . date('Y/m');
        $fullPath = $directory . '/' . $filename;

        // Ensure directory exists
        Storage::makeDirectory($directory);

        // Store the file
        Storage::put($fullPath, $content);

        return $fullPath;
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
