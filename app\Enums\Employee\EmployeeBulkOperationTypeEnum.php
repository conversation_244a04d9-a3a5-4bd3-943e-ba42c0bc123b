<?php

namespace App\Enums\Employee;

use App\Traits\EnumToArray;

enum EmployeeBulkOperationTypeEnum: string
{
    use EnumToArray;

    case ADD = 'add';
    case EDIT = 'edit';

    public static function getLabel(self $value): string
    {
        return match ($value) {
            self::ADD => 'Add New Employees',
            self::EDIT => 'Edit Existing Employees',
        };
    }

    public static function all(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }
}
