<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Models\RegisterationValidation;
use Illuminate\Console\Command;

class RemoveCompany extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-company {company_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $companyId = $this->argument('company_id');
            $phone = 10000000000;
            $this->info('Removing company');
            $employees = Employee::where('company_id', $companyId)->withTrashed()->get();
            foreach ($employees as $employee) {
                RegisterationValidation::where('phone', $employee->phone)->delete();
                $phone = $phone + 1;
                $employee->phone = $phone;
                $employee->save();
            }
            $this->info('Company removed successfully');
        } catch (\Exception $e) {
            $this->error('An error occurred while removing company');
            $this->error($e->getMessage());
        }
    }
}
