<?php

namespace App\Console\Commands;

use App\Models\Country;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Rinvex\Country\CountryLoader;

class UpdateCurrencies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-currencies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the currency column in the countries table with the corresponding lowercase currency code for each country';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Starting to update currencies for countries...');

        try {
            DB::transaction(function () {
                $countries = $this->getCountriesData();
                $this->updateCountriesCurrencies($countries);
            });

            $this->info('Countries currencies updated successfully!');
        } catch (\Exception $e) {
            $this->error("An error occurred: {$e->getMessage()}");
        }
    }

    /**
     * Get countries data from Rinvex CountryLoader.
     */
    private function getCountriesData(): array
    {
        $countryLoader = new CountryLoader;

        return $countryLoader->countries(true);
    }

    /**
     * Update currencies for each country.
     */
    private function updateCountriesCurrencies(array $countries): void
    {
        foreach ($countries as $iso => $country) {
            if (! $this->isValidCountryData($country)) {
                $this->warn("Missing data for country ISO: {$iso}");

                continue;
            }

            $this->updateCountryCurrency($country);
        }
    }

    /**
     * Check if country data is valid.
     */
    private function isValidCountryData(array $country): bool
    {
        return isset($country['name']['common']) && ! empty($country['currency'] ?? null);
    }

    /**
     * Update currency for a single country.
     */
    private function updateCountryCurrency(array $country): void
    {
        $countryName = $country['name']['common'];
        $currencyCode = $this->extractCurrencyCode($country);

        if (! $currencyCode) {
            $this->warn("No currency code found for {$countryName}");

            return;
        }

        $updated = Country::where('name_en', $countryName)
            ->update(['currency' => $currencyCode]);

        if ($updated) {
            $this->info("Updated {$countryName} with currency: {$currencyCode}");
        } else {
            $this->error("Country '{$countryName}' not found or not updated.");
        }
    }

    /**
     * Extract currency code from country data.
     */
    private function extractCurrencyCode(array $country): ?string
    {
        $currencyValues = array_values($country['currency']);
        $firstCurrency = $currencyValues[0] ?? null;

        if (! $firstCurrency) {
            return null;
        }

        $firstCurrency = array_values($firstCurrency);

        return strtolower($firstCurrency[0] ?? '');
    }
}
