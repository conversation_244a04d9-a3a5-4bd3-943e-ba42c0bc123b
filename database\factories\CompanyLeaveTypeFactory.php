<?php

namespace Database\Factories;

use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CompanyLeave>
 */
class CompanyLeaveTypeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
            'name_en' => $this->faker->words(2, true),
            'name_ar' => $this->faker->words(2, true),
            'name' => $this->faker->words(2, true),
            'balance_period' => $this->faker->randomElement(['calendar_year', 'calendar_month', 'payroll_month']),
            'gender' => $this->faker->randomElement(['all', 'male', 'female']),
            'is_primary' => false,
        ];
    }

    public function annual(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'name_en' => 'Annual Leave',
                'name_ar' => 'إجازة سنوية',
                'balance_period' => 'calendar_year',
                'is_primary' => true,
            ];
        });
    }

    public function sick(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'name_en' => 'Sick Leave',
                'name_ar' => 'إجازة مرضية',
                'balance_period' => 'calendar_year',
                'is_primary' => true,
            ];
        });
    }
}
