<?php

namespace App\Enums\EmployeeInfo;

enum ContractDurationEnum: string
{
    case OPEN = 'open';
    case TWELVE_MONTHS = '12_months';
    case SIX_MONTHS = '6_months';
    case THREE_MONTHS = '3_months';
    case CUSTOM = 'custom';

    public static function all(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    public static function getTranslatedValues(): array
    {
        return array_map(fn ($case) => trans('employeeexport.' . $case->value), self::cases());
    }

    public static function getLabel(self $value): string
    {
        return trans('employeeexport.' . $value->value);
    }

    public static function getDurationInMonths(string $duration): ?int
    {
        return match ($duration) {
            self::TWELVE_MONTHS->value => 12,
            self::SIX_MONTHS->value => 6,
            self::THREE_MONTHS->value => 3,
            default => null
        };
    }
}