<?php

namespace App\Http\Requests\V1\Employee;

use App\Enums\EmployeeInfo\ContractDurationEnum;
use App\Enums\EmployeeInfo\DegreeTypeEnum;
use App\Enums\EmployeeInfo\EmploymentTypeEnum;
use App\Enums\EmployeeInfo\GenderEnum;
use App\Enums\EmployeeInfo\MaritalStatusEnum;
use App\Enums\EmployeeInfo\MilitaryStatus;
use App\Enums\EmployeeInfo\ReligionEnum;
use App\Rules\EntityIdRule;
use App\Rules\IsArabic;
use App\Rules\IsEnglish;
use App\Rules\NationalIdRule;
use App\Rules\PhoneNumberByCountryRule;
use App\Rules\UniqueEmployeeNumberRule;
use App\Rules\UniqueNationalIdRule;
use App\Rules\UniquePassportNumberRule;
use App\Rules\UniquePersonalEmailRule;
use App\Rules\UniquePhoneWithCountryCodeRule;
use App\Rules\UniqueWorkEmailRule;
use App\Traits\QueriesHelper;
use App\Traits\StringLanguageValidation;
use App\Traits\V1\PhoneValidationTrait;
use App\Util\ScopeUtil;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateEmployeeProfileRequest extends FormRequest
{
    use PhoneValidationTrait, QueriesHelper, StringLanguageValidation;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    protected function prepareForValidation()
    {
        $data = $this->all();

        if (isset($data['contact_details'])) {
            if (isset($data['contact_details']['phone'])) {
                $data['contact_details']['phone'] = $this->trimStringSpaces($data['contact_details']['phone']);
            }

            if (isset($data['contact_details']['secondary_phone'])) {
                $data['contact_details']['secondary_phone'] = $this->trimStringSpaces($data['contact_details']['secondary_phone']);
            }

            if (isset($data['contact_details']['emergency_contacts']['items']) && is_array($data['contact_details']['emergency_contacts']['items'])) {
                foreach ($data['contact_details']['emergency_contacts']['items'] as $index => $contact) {
                    if (isset($contact['phone'])) {
                        $data['contact_details']['emergency_contacts']['items'][$index]['phone'] = $this->trimStringSpaces($contact['phone']);
                    }
                }
            }
        }

        $this->replace($data);
    }

    private function hasAnyEnglishNameField(): bool
    {
        return $this->filled('basic_info.first_name_en') ||
               $this->filled('basic_info.second_name_en') ||
               $this->filled('basic_info.third_name_en') ||
               $this->filled('basic_info.fourth_name_en') ||
               $this->filled('basic_info.fifth_name_en');
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [];

        if ($this->has('basic_info')) {
            $rules['basic_info.first_name_ar'] = ['required', 'string', 'max:50', new IsArabic];
            $rules['basic_info.second_name_ar'] = ['required', 'string', 'max:50', new IsArabic];
            $rules['basic_info.third_name_ar'] = ['required', 'string', 'max:50', new IsArabic];
            $rules['basic_info.fourth_name_ar'] = ['nullable', 'string', 'max:50', new IsArabic];
            $rules['basic_info.fifth_name_ar'] = ['nullable', 'string', 'max:50', new IsArabic];
            $rules['basic_info.first_name_en'] = ['nullable', 'string', 'max:50', new IsEnglish];
            $rules['basic_info.second_name_en'] = ['nullable', 'string', 'max:50', new IsEnglish];
            $rules['basic_info.third_name_en'] = ['nullable', 'string', 'max:50', new IsEnglish];
            $rules['basic_info.fourth_name_en'] = ['nullable', 'string', 'max:50', new IsEnglish];
            $rules['basic_info.fifth_name_en'] = ['nullable', 'string', 'max:50', new IsEnglish];
            $rules['basic_info.employee_number'] = ['required', 'string', 'max:50', new UniqueEmployeeNumberRule($this->route('id'))];
            $rules['basic_info.rehire_date'] = 'nullable|date_format:Y-m-d';
            $rules['basic_info.is_trackable'] = 'required|boolean';

            if ($this->hasAnyEnglishNameField()) {
                $rules['basic_info.first_name_en'] = ['required', 'string', 'max:50', new IsEnglish];
                $rules['basic_info.second_name_en'] = ['required', 'string', 'max:50', new IsEnglish];
                $rules['basic_info.third_name_en'] = ['required', 'string', 'max:50', new IsEnglish];
            }
        }

        if ($this->has('personal_details')) {
            $rules['personal_details.nationality'] = 'nullable|string|max:50';
            $rules['personal_details.national_id'] = [
                'nullable',
                'string',
                'max:50',
                new NationalIdRule($this->input('personal_details.nationality')),
                new UniqueNationalIdRule($this->route('id'))
            ];
            $rules['personal_details.birth_date'] = 'nullable|date_format:Y-m-d|before:' . now()->subYears(16)->format('Y-m-d');
            $rules['personal_details.passport_number'] = ['nullable', 'string', 'max:50', new UniquePassportNumberRule($this->route('id'))];
            $rules['personal_details.gender'] = ['required', new Enum(GenderEnum::class)];
            $rules['personal_details.address'] = 'nullable|string|max:255';
            $rules['personal_details.place_of_birth'] = 'nullable|string|max:100';
            $rules['personal_details.religion'] = ['nullable', new Enum(ReligionEnum::class)];
            $rules['personal_details.other_religion'] = ['required_if:personal_details.religion,' . ReligionEnum::OTHER->value, 'nullable', 'string', 'max:100'];
            $rules['personal_details.marital_status'] = ['nullable', new Enum(MaritalStatusEnum::class)];
            $rules['personal_details.military_status'] = ['nullable', new Enum(MilitaryStatus::class)];
            $rules['personal_details.number_kids'] = 'nullable|integer|min:0';
            $rules['personal_details.notes'] = 'nullable|string|max:1000';
        }

        if ($this->has('contact_details')) {
            $rules['contact_details.phone_country_code'] = 'required|string|starts_with:+';
            $rules['contact_details.phone'] = [
                'required',
                new PhoneNumberByCountryRule($this->input('contact_details.phone_country_code')),
                new UniquePhoneWithCountryCodeRule($this->input('contact_details.phone_country_code'), $this->route('id'))
            ];

            $rules['contact_details.secondary_phone_country_code'] = 'nullable|required_with:contact_details.secondary_phone|string|starts_with:+';
            $rules['contact_details.secondary_phone'] = [
                'nullable'
            ];

            if ($this->input('contact_details.secondary_phone_country_code')) {
                $rules['contact_details.secondary_phone'][] = new PhoneNumberByCountryRule($this->input('contact_details.secondary_phone_country_code'));
            }
            $rules['contact_details.work_email'] = ['nullable', 'email', new UniqueWorkEmailRule($this->route('id'))];
            $rules['contact_details.personal_email'] = ['nullable', 'email', new UniquePersonalEmailRule($this->route('id'))];

            if ($this->has('contact_details.emergency_contacts')) {
                $rules['contact_details.emergency_contacts.items'] = 'nullable|array';
                $rules['contact_details.emergency_contacts.items.*.id'] = [
                    'nullable',
                    'integer',
                    new EntityIdRule('employee_emergency_contacts', $this->route('id'))
                ];
                $rules['contact_details.emergency_contacts.items.*.name'] = 'nullable|string|max:100';
                $rules['contact_details.emergency_contacts.items.*.phone_country_code'] = 'nullable|required_with:contact_details.emergency_contacts.items.*.phone|string|starts_with:+';
                $rules['contact_details.emergency_contacts.items.*.phone'] = [
                    'nullable',
                    'required_with:contact_details.emergency_contacts.items.*.name,contact_details.emergency_contacts.items.*.relation',
                    $this->validateEmergencyContactPhone('contact_details.emergency_contacts.items', '3')
                ];
                $rules['contact_details.emergency_contacts.items.*.relation'] = 'nullable|string|max:100';
                $rules['contact_details.emergency_contacts.delete_ids'] = 'nullable|array';
                $rules['contact_details.emergency_contacts.delete_ids.*'] = [
                    'integer',
                    new EntityIdRule('employee_emergency_contacts', $this->route('id'))
                ];

                $rules['contact_details.emergency_contacts.items'] = [
                    'nullable',
                    'array',
                    $this->validateUniqueEmergencyContacts('contact_details.emergency_contacts.items')
                ];
            }
        }

        if ($this->has('contract_details')) {
            $rules['contract_details.join_date'] = 'required|date_format:Y-m-d|before_or_equal:' . now()->addDays(30)->format('Y-m-d');
            $rules['contract_details.employment_type'] = ['nullable', new Enum(EmploymentTypeEnum::class)];

            if ($this->has('contract_details.contracts')) {
                $rules['contract_details.contracts.items'] = 'nullable|array';
                $rules['contract_details.contracts.items.*.id'] = [
                    'nullable',
                    'integer',
                    new EntityIdRule('employee_contracts', $this->route('id'))
                ];
                $rules['contract_details.contracts.items.*.contract_start_date'] = 'required|date_format:Y-m-d|after_or_equal:contract_details.join_date';
                $rules['contract_details.contracts.items.*.contract_duration'] = ['required', new Enum(ContractDurationEnum::class)];
                $rules['contract_details.contracts.items.*.contract_end_date'] = 'required_if:contract_details.contracts.items.*.contract_duration,'.ContractDurationEnum::CUSTOM->value.'|nullable|date_format:Y-m-d|after:contract_details.contracts.items.*.contract_start_date';
                $rules['contract_details.contracts.delete_ids'] = 'nullable|array';
                $rules['contract_details.contracts.delete_ids.*'] = [
                    'integer',
                    new EntityIdRule('employee_contracts', $this->route('id'))
                ];

                $contracts = $this->input('contract_details.contracts.items', []);
                if (is_array($contracts) && count($contracts) > 1) {
                    for ($i = 1; $i < count($contracts); $i++) {
                        $previousIndex = $i - 1;
                        $previousContract = $contracts[$previousIndex];
                        if (isset($previousContract['contract_duration']) && 
                        $previousContract['contract_duration'] === ContractDurationEnum::OPEN->value) {
                            $rules["contract_details.contracts.items.{$i}"][] = 'prohibited';
                            break;
                        }
                        $previousEndDate = null;
                        if (isset($previousContract['contract_end_date']) && !empty($previousContract['contract_end_date'])) {
                            $previousEndDate = $previousContract['contract_end_date'];
                        } elseif (isset($previousContract['contract_duration']) && isset($previousContract['contract_start_date'])) {
                            $previousEndDate = $this->calculateContractEndDate(
                                $previousContract['contract_duration'],
                                $previousContract['contract_start_date']
                            );
                        }

                        if ($previousEndDate) {
                            $rules["contract_details.contracts.items.{$i}.contract_start_date"][] =
                                "after:{$previousEndDate}";
                        }
                    }
                }
            }
        }

        if ($this->has('education')) {
            $rules['education.items'] = 'nullable|array';
            $rules['education.items.*.id'] = [
                'nullable',
                'integer',
                new EntityIdRule('employee_education', $this->route('id'))
            ];
            $rules['education.items.*.degree_type'] = ['nullable', 'required_with:education.items.*.degree_name,education.items.*.institution_name,education.items.*.graduation_year', new Enum(DegreeTypeEnum::class)];
            $rules['education.items.*.degree_name'] = 'nullable|string|max:100';
            $rules['education.items.*.institution_name'] = 'nullable|string|max:100';
            $rules['education.items.*.graduation_year'] = 'nullable|integer|min:1950';
            $rules['education.delete_ids'] = 'nullable|array';
            $rules['education.delete_ids.*'] = [
                'integer',
                new EntityIdRule('employee_education', $this->route('id'))
            ];

        }
        if ($this->has('management_scope')) {
            $rules['management_scope.managed_department_ids'] = ['nullable','array'];
            $rules['management_scope.managed_department_ids.*'] = ['integer',new EntityIdRule('departments')];
            $rules['management_scope.managed_sub_department_ids'] = ['nullable','array'];
            $rules['management_scope.managed_sub_department_ids.*'] = ['integer',new EntityIdRule('sub_departments')];
            $rules['management_scope.managed_branch_ids'] = ['nullable','array'];
            $rules['management_scope.managed_branch_ids.*'] = ['integer',new EntityIdRule('branches')];
            $employeeScope = $this->getEmployeeHighestScopeKey($this->route('id'));
            if (isset($employeeScope) && $employeeScope != ScopeUtil::ME_SCOPE && $employeeScope != ScopeUtil::COMPANY_SCOPE) {

                if ($employeeScope === ScopeUtil::DEPARTMENT_SCOPE) {
                    $rules['management_scope.managed_department_ids'] = ['required', 'array', 'min:1'];
                } elseif ($employeeScope === ScopeUtil::SUB_DEPARTMENT_SCOPE) {
                    $rules['management_scope.managed_sub_department_ids'] = ['required', 'array', 'min:1'];
                }
                $rules['management_scope.managed_branch_ids'] = ['required', 'array', 'min:1'];
            }
        }

        return $rules;
    }

    private function calculateContractEndDate(string $contractDuration, string $contractStartDate): ?string
    {
        if ($contractDuration === ContractDurationEnum::TWELVE_MONTHS->value) {
            return date('Y-m-d', strtotime($contractStartDate . ' + 12 months'));
        } elseif ($contractDuration === ContractDurationEnum::SIX_MONTHS->value) {
            return date('Y-m-d', strtotime($contractStartDate . ' + 6 months'));
        } elseif ($contractDuration === ContractDurationEnum::THREE_MONTHS->value) {
            return date('Y-m-d', strtotime($contractStartDate . ' + 3 months'));
        } else if ($contractDuration === ContractDurationEnum::OPEN->value) {
            return null;
        }
    }
}