<?php

namespace App\Http\Requests\V1\ServiceCharge;

use App\Traits\GetLastDraftedPayRollOrCreate;
use Illuminate\Foundation\Http\FormRequest;

class GetServiceChargeRequest extends FormRequest
{
    use GetLastDraftedPayRollOrCreate;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'payroll_from_date' => [
                'required',

            ],
            'payroll_to_date' => [
                'required',

            ],
            'employee_ids' => [
                'nullable',

            ],
            'payroll' => [
                'required',
            ],
        ];

    }

    public function prepareForValidation()
    {
        $currentPayroll = $this->getCurrentPayroll();

        $this->merge([
            'payroll_from_date' => $currentPayroll->start,
            'payroll_to_date' => $currentPayroll->end,
            'payroll' => $currentPayroll,
            'employee_ids' => null,
        ]);
    }
}
