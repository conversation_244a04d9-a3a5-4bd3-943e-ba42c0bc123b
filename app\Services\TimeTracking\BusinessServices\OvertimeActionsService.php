<?php

namespace App\Services\TimeTracking\BusinessServices;

use App\Models\StateMachines\RequestPending;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\Repository;
use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use App\Services\IBusinessService;
use App\Traits\DataPreparation;
use App\Traits\V1\EmployeeRequestsTrait;
use App\Traits\WorkflowTrait;
use App\Util\AttendanceUtil;
use Illuminate\Support\Facades\Log;
use stdClass;
use Workflow\WorkflowStub;
use App\Traits\V1\NotificationRedirection;
class OvertimeActionsService implements IBusinessService
{
    use DataPreparation, EmployeeRequestsTrait, WorkflowTrait, NotificationRedirection;

    private $attendanceOvertimeRepository;

    private Repository $attendanceSettingRepository;

    private Repository $overtimeGroupRepository;

    private $attendanceOvertime;

    private $employeeId;

    private $workflow;

    public function __construct(
        private ApplyRequestCycleRulesService $applyRequestCycleRulesService,
        private AddRequestsToEmployeeRequest $addRequestsToEmployeeRequest,
        private PayrollsRepository $payrollsRepository
    ) {
        $this->attendanceOvertimeRepository = Repository::getRepository('AttendanceOvertime');
        $this->attendanceSettingRepository = Repository::getRepository('AttendanceSetting');
        $this->overtimeGroupRepository = Repository::getRepository('OvertimeGroup');
    }

    public function isValid(array $request, stdClass &$output): bool
    {

        $this->attendanceOvertime = $this->attendanceOvertimeRepository->find($request['id']);
        if (! isset($this->attendanceOvertime)) {
            $output->Error = ['Invalid attendance overtime Id', 'المعرف الخاص بالحضور الاضافى غير صحيح'];

            return false;
        }

        if ($request['action'] != 'cancel') {
            $workflowCompleted = $this->attendanceOvertime->status != 'pending';
            if ($workflowCompleted) {
                $output->Error = ['This request is already completed', 'هذا الطلب مكتمل بالفعل'];
                return false;
            }
        }

        $this->employeeId = config('globals.user')->employee_id;
        if (is_null($this->employeeId)) {
            $output->Error = ['There is no employee id for this user', 'هذا المستخدم ليس لديه معرف خاص بالموظف'];

            return false;
        }

        $payrollOfOvertime = $this->payrollsRepository->payrollCoversDate($this->attendanceOvertime->date);
        if (isset($payrollOfOvertime) && $payrollOfOvertime->status == 'finalized') {
            $output->Error = ['You can not take an action on this request because the payroll is finalized', 'لا يمكنك اتخاذ اجراء على هذا الطلب ﻷنه تم الانتهاء من حساب المرتبات'];

            return false;
        }

        return true;
    }

    public function perform(array $request, stdClass &$output): void
    {
        Log::info('performing overtime action ' . json_encode($request));

        if (! $this->isValid($request, $output)) {
            return;
        }

        if ($request['action'] == 'edit') {
            $this->editOvertime($request, $output);
        } else {
            $this->actionOnOvertime( $output, $request['action']);
        }

        // $this->attendanceOvertime = $this->attendanceOvertimeRepository->getOvertimeApprovalCycle($request['id']);
        // if($this->attendanceOvertime->status === 'approved'){
        //    dispatch(new ApprovedOverTimePushNotificationJob($this->attendanceOvertime->attendance))->afterCommit();  // done
        // }

    }

    public function actionOnOvertime(stdClass &$output, $actionType): void
    {
        try {
            $roleIds = config('globals.user')->roles->pluck('id')->toArray();
            $userCanTakeAnAction = $this->canTakeAnAction($roleIds, $this->attendanceOvertime->id, config('globals.REQUEST_WORKFLOW_TYPES.ATTENDANCE_OVERTIME'), $actionType);

            if (! $userCanTakeAnAction) {
                $output->Error = ['You are not allowed to take this action', 'غير مسموح لك باتخاذ هذا الاجراء'];

                return;
            }

            $updatedValue = isset($output->overtime_minutes) ? $output->overtime_minutes : $this->attendanceOvertime?->updated_value ?? $this->attendanceOvertime->overtime_minutes;
            $requestName = $actionType == 'cancel' ? 'cancel_overtime' : 'edit_overtime';
            $reason = isset($output->reason) ? $output->reason : $this->attendanceOvertime?->employeeRequests?->first()->comment ?? null;

            $this->doAnAction($actionType);
            $this->attendanceOvertime->update([
                'updated_value' => $updatedValue,
                'updated_by' => config('globals.user')?->employee_id,
            ]);
            if($this->checkRequestIsCompleted($this->attendanceOvertime->employeeRequest)){
                $finalStatus = $this->getFinalStatus($this->attendanceOvertime->employeeRequest, $actionType);
                $this->updateEntity($this->attendanceOvertime, $finalStatus);
                $this->updateRequest($this->attendanceOvertime->employeeRequest, $finalStatus, [
                    'comment' => $reason, 
                    'requested_by' => config('globals.user')?->employee_id,
                    'request_name' => $requestName,
                ]);

                $this->redirectNotificationsAfterRequestFinalized($this->attendanceOvertime, $finalStatus);
            }
            if ($actionType == 'cancel') {
                $this->addLogsForCancelledOvertime($this->attendanceOvertime);
            }
            

        } catch (\Exception $e) {
            Log::error($e);
            $output->Error = ['You are not allowed to take this action', 'غير مسموح لك باتخاذ هذا الاجراء'];

            return;
        }

    }

    public function editOvertime(array $request, stdClass &$output): void
    {
        $this->setOvertimeValue($request['overtime_minutes']);
        $this->attendanceOvertime->overtime_minutes = $request['overtime_minutes'];
        $this->attendanceOvertime->status = 'pending';
        $this->attendanceOvertime->note = $request['comment'] ?? '';
        $this->attendanceOvertime->save();

        $attendanceOvertimeEntity = $this->attendanceOvertime->withoutRelations();
        $requestedBy = config('globals.user')->employee_id;
        $editOvertimeRequestData = $this->prepareEmployeeRequestData($requestedBy, $attendanceOvertimeEntity, config('globals.EMPLOYEE_REQUEST_NAMES.EDIT_OVERTIME'));
        $editOvertimeRequestData['request_data']['comment'] = $request['comment'] ?? '';

        $this->addRequestsToEmployeeRequest->perform($editOvertimeRequestData, $output);

        // activity()->withoutLogs(function () use($editOvertimeRequestData,$output){
        //     $this->addRequestsToEmployeeRequest->perform($editOvertimeRequestData,$output);
        // });

        $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $data = $this->getRequestData();
        $approvals = $workflowApprovalCycleRepository->getWorkflowApprovalCycle($data);
        foreach ($approvals as $approval) {
            if ($approval->status != config('globals.REQUEST_STATUSES.PENDING')) {
                $approval->status->transitionTo(RequestPending::class);
            }
        }

    }

    private function setOvertimeValue($overtimeMinutes)
    {
        $applyClockOutPolicy = $this->attendanceSettingRepository->getByKey('key', config('globals.ATTENDANCE_SETTINGS.APPLY_OVERTIME'))->first();
        if (isset($applyClockOutPolicy) and $applyClockOutPolicy->is_used) {
            $clockOutAllowedTimePolicy = $this->attendanceSettingRepository->getByKey('key', config('globals.ATTENDANCE_SETTINGS.ALLOWED_MINUTES_TO_APPLY_OVERTIME'))->first();

            if (isset($clockOutAllowedTimePolicy) and $clockOutAllowedTimePolicy->value < $overtimeMinutes) {
                $policy = $this->getOverTimePolicy($overtimeMinutes);
                if (! is_null($policy)) {
                    $this->attendanceOvertime->overtime_value = $policy->percentage;
                    $this->attendanceOvertime->daily_overtime_policy_id = $policy->id;
                }
            }

        }
    }

    public function getOverTimePolicy($overtimeMinutes)
    {
        $titleId = $this->attendanceOvertime->employee->title_id;
        $policyObj = null;

        $overtimeGroup = $this->overtimeGroupRepository->getOvertimeGroupRelatedToTitleWithOvertimePolicy($titleId, $overtimeMinutes);
        if (is_null($overtimeGroup)) {
            // if the worker work more than all overtime ranges, so get max policy
            $overtimeGroup = $this->overtimeGroupRepository->getOvertimeGroupRelatedToTitleWithMaxOvertimePolicy($titleId);
        }
        if (! is_null($overtimeGroup) and count($overtimeGroup->dailyOvertimePolicies) > 0) {
            $policyObj = $overtimeGroup->dailyOvertimePolicies[0];
        }

        return $policyObj;
    }

    public function getRequestData()
    {
        return [
            'requestable_id' => $this->attendanceOvertime->id,
            'requestable_type' => config('globals.REQUEST_WORKFLOW_TYPES.ATTENDANCE_OVERTIME'),
            'role_ids' => auth()->user()->roles->pluck('id')->toArray(),
        ];
    }

    public function updateEmployeeRequest($attendanceOvertime, $reason, $actionType)
    {
        $requestName = $actionType == 'cancel' ? 'cancel_overtime' : 'edit_overtime';
        $employeeRequest = $attendanceOvertime->employeeRequests->first();

        $employeeRequest->update([
            'comment' => $reason,
            
        ]);

        if ($actionType == 'cancel') {
            $employeeRequest->update(['status' => config('globals.REQUEST_STATUSES.CANCELLED')]);
        }

    }

    public function addLogsForCancelledOvertime($overtime): void
    {
        $employee = auth()->user()->employee;

        activity(AttendanceUtil::CANCELLED_OVERTIME)
            ->on($overtime)
            ->causedBy($employee)
            ->withProperties(['data' => $overtime])
            ->event('cancelled')
            ->log('employee id '.$employee->id.' cancelled overtime request');
    }
}