<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Timecard;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateOffShiftTag extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:off_shift_tag';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $timecards = Timecard::whereNotNull('shift_id')->withWhereHas('attendance.entityTags', function ($q) {
                $q->where('tag', 'off_shift');
            })->get();
            foreach ($timecards as $timecard) {
                $tag = $timecard->attendance->entityTags()->where('tag', 'off_shift')->first();
                $tag->update(['tag' => 'assigned_to_shift']);
                echo 'Updated tag for attendance id: '.$timecard->attendance->id.' and timecard id '.$timecard->id.PHP_EOL;
            }
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
