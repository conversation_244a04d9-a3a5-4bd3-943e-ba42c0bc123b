<?php

namespace App\Http\Middleware;

use App\Models\Company;
use Closure;
use Illuminate\Http\Request;

class SetInternalCompanyTimezone
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next)
    {
        $companyId = $request->get('company_id');
        if (isset($companyId)) {
            $company = Company::where('id', $companyId)->firstOrFail();
            // Set app timezone to user's timezone
            $timezone = isset($company) ? $company->country->timezone : 'UTC';
            config(['app.timezone' => $timezone]);
            date_default_timezone_set($timezone);
        } else {
            // Default to UTC for unauthenticated requests
            date_default_timezone_set('UTC');
        }

        return $next($request);
    }
}
