<?php

namespace App\Http\Requests\V1\Loans;

use Illuminate\Foundation\Http\FormRequest;

class BulkDisburseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'entities' => 'required|array',
            'entities.*.id' => 'required|integer',
            'entities.*.type' => 'required|string|in:loan,salary_advance',
            'entities.*.date' => 'required|date',
        ];

    }
}
