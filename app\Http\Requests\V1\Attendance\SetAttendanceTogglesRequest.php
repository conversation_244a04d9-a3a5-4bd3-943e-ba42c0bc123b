<?php

namespace App\Http\Requests\V1\Attendance;

use Illuminate\Foundation\Http\FormRequest;

class SetAttendanceTogglesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'apply_late_deduction' => 'boolean',
            'apply_absence_deduction' => 'boolean',
            'apply_overtime' => 'boolean',
            'apply_automated_clock_out' => 'boolean',
            'apply_extra_workday' => 'boolean',
            'apply_excuses' => 'boolean',
        ];
    }
}
