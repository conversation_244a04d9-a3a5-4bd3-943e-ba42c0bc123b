<?php

namespace App\Repositories\V1;

use App\Models\EmployeeRequest;
use App\Repositories\BaseRepository;
use App\Traits\QueriesHelper;
use App\Util\EmployeeUtil;
use Illuminate\Support\Arr;

class EmployeeRequestRepository extends BaseRepository
{
    use QueriesHelper;

    public function model(): string
    {
        return EmployeeRequest::class;
    }

    public function requestsRelatedToMe($data)
    {
        $lang = '_' . (config('globals.lang') ?? 'ar');

        $data['for_employee'] = isset($data['for_employee']) ? $data['for_employee'] : false;

        return $this->model
            ->when(isset($data['status']) && $data['status'] == 'pending', function ($q) {
                $q->whereNot('is_hidden', true);
            })
            ->withWhereHas('employee', function ($query) use ($lang, $data) {
                $query
                    ->select('id', 'branch_id', 'title_id', 'first_name' . $lang, 'second_name' . $lang, 'name' . $lang, 'employee_number')
                    ->with('title:id,name' . $lang . ',color,department_id,sub_department_id')
                    ->with('branch:id,name' . $lang)
                    ->with('profilePicture')
                    ->when(isset($data['employee_id']), function ($q) use ($data) {
                        $q->where('id', $data['employee_id']);
                    })
                    ->when(isset($data['search_value']), function ($q) use ($data) {
                        $this->appendEmployeeTextSearchQuery($q, $data['search_value']);
                    });
            })
            ->with('deciderAdmin', function ($q) use ($lang) {
                $q->select(
                    'id',
                    'employee_number',
                    'first_name' . $lang,
                    'second_name' . $lang
                );
            })
            ->where(function ($q) use ($data, $lang) {
                $q
                    ->when((Arr::get($data, 'filter', false) && $data['filter'] == 'overtimes') || !isset($data['filter']), function ($q) use ($data, $lang) {
                        $q->orWhere(function ($q) use ($data, $lang) {
                            $q->when(
                                (isset($data['request_id']) && $data['request_id']),
                                function ($q) use ($data) {
                                    $q->where('requestable_id', $data['request_id']);

                                })
                                ->where('requestable_type', 'attendance_overtime')
                                ->when(
                                    isset($data['status']),
                                    function ($q) use ($data) {
                                        $q->where('status', $data['status']);
                                    }
                                )
                                ->when(
                                    (isset($data['from_date']) && $data['to_date']),
                                    function ($q) use ($data) {
                                        if ((isset($data['in_payroll_hub']) && ! $data['in_payroll_hub']) || ! isset($data['in_payroll_hub'])) {
                                            $q->whereBetween('date', [$data['from_date'], $data['to_date']]);
                                        }
                                    }
                                )
                                ->orderBy('created_at');

                            if ($data['for_employee'] == false) {
                                $this->appendScopeQuery($q, $data);
                            }
                            $q->withWhereHas('requestable', function ($q) use ($data, $lang) {
                                $q->when(
                                    class_basename($q->getModel()) == 'AttendanceOvertime',
                                    function ($q) use ($data, $lang) {
                                        if (isset($data['in_payroll_hub']) && $data['in_payroll_hub']) {
                                            $q->when(
                                                (isset($data['from_date']) && $data['to_date']),
                                                function ($q) use ($data) {
                                                    $q->whereDate('date', '>=', $data['from_date'])
                                                        ->whereDate('date', '<=', $data['to_date']);
                                                }
                                            );
                                        }
                                        $q->select('overtime_value', 'id', 'overtime_minutes', 'updated_value',
                                            'employee_id', 'attendance_id')
                                            ->selectRaw('CAST(date AS datetime) AS date')
                                            ->with('attendance.id', 'ci_id', 'co_id', 'slotable_id', 'slotable_type')
                                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                                            ->with('employeeRequests', function ($q) use ($lang) {
                                                $q->with('requestedBy:id,name_ar,name_en,name', 'requestedBy:id,first_name' . $lang . ',second_name' . $lang)
                                                    ->select('id', 'requested_by', 'requestable_type', 'requestable_id', 'comment', 'status');
                                            })
                                            ->with('attendance:id,ci_id,co_id,slotable_id,slotable_type',
                                                'attendance.clockIn:id,date',
                                                'slotable:id,name,from',
                                                'attendance.clockOut:id,date', 'attendance.slotable')
                                            ->with('employeeApproves', function ($q) {
                                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                                    ->with('users.employee', function ($employeeQuery) {
                                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                                            ->with('branches', function ($q) {
                                                                $q->select('branch_employee.id', 'branch_id');
                                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                                    })
                                                    ->with('scopes')
                                                    ->orderBy('workflow_approval_cycle.order');
                                            })
                                            ->when(
                                                Arr::get($data, 'pending_on_me', false),
                                                function ($q) use ($data) {
                                                    $this->appendPendingOnMeToApprovers($q, $data['pending_on_me']);
                                                }
                                            );
                                    }
                                );
                            });
                        });
                    })
                    ->when((Arr::get($data, 'filter', false) && $data['filter'] == 'edit_deductions') || !isset($data['filter']), function ($q) use ($data, $lang) {
                        $q->orWhere(function ($q) use ($data, $lang) {
                            $q->when(
                                (isset($data['request_id']) && $data['request_id']),
                                function ($q) use ($data) {
                                    $q->where('requestable_id', $data['request_id']);

                                })->where('requestable_type', 'attendance_deduction')
                                ->when(
                                    isset($data['status']),
                                    function ($q) use ($data) {
                                        $q->where('status', $data['status']);
                                    }
                                )
                                ->when(
                                    (isset($data['from_date']) && $data['to_date']),
                                    function ($q) use ($data) {
                                        if ((isset($data['in_payroll_hub']) && ! $data['in_payroll_hub']) || ! isset($data['in_payroll_hub'])) {
                                            $q->whereBetween('date', [$data['from_date'], $data['to_date']]);
                                        }
                                    }
                                )
                                ->orderBy('created_at');
                            if ($data['for_employee'] == false) {
                                $this->appendScopeQuery($q, $data);
                            }
                            $q->withWhereHas('requestable', function ($q) use ($data, $lang) {
                                $q->when(
                                    class_basename($q->getModel()) == 'AttendanceDeduction',
                                    function ($q) use ($data, $lang) {
                                        if (isset($data['in_payroll_hub']) && $data['in_payroll_hub']) {
                                            $q->when(
                                                (isset($data['from_date']) && $data['to_date']),
                                                function ($q) use ($data) {
                                                    $q->whereDate('date', '>=', $data['from_date'])
                                                        ->whereDate('date', '<=', $data['to_date']);
                                                }
                                            );
                                        }
                                        $q->select('id', 'deduction_value', 'employee_id', 'attendance_id', 'updated_value')
                                            ->selectRaw('CAST(date AS datetime) AS date')
                                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                                            ->with('employeeRequests', function ($q) use ($lang) {
                                                $q->with('requestedBy:id,name_ar,name_en,name', 'requestedBy:id,first_name' . $lang . ',second_name' . $lang)
                                                    ->select('id', 'requested_by', 'requestable_type', 'requestable_id', 'status', 'comment');
                                            })
                                            ->with('attendance:id,ci_id,co_id,slotable_id,slotable_type',
                                                'attendance.clockIn:id,date',
                                                'slotable:id,name,from',
                                                'attendance.clockOut:id,date', 'attendance.slotable')
                                            ->with('employeeApproves', function ($q) {
                                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                                    ->with('users.employee', function ($employeeQuery) {
                                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                                            ->with('branches', function ($q) {
                                                                $q->select('branch_employee.id', 'branch_id');
                                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                                    })
                                                    ->with('scopes')
                                                    ->orderBy('workflow_approval_cycle.order');
                                            })
                                            ->where('deduction_value', '>', 0)
                                            ->when(
                                                Arr::get($data, 'pending_on_me', false),
                                                function ($q) use ($data) {
                                                    $this->appendPendingOnMeToApprovers($q, $data['pending_on_me']);
                                                }
                                            );
                                    }
                                );
                            });
                        });
                    })
                    ->when((Arr::get($data, 'filter', false) && $data['filter'] == 'employee_leave_requests') || !isset($data['filter']), function ($q) use ($data, $lang) {
                        $q->orWhere(function ($q) use ($data, $lang) {
                            $q->when(
                                (isset($data['request_id']) && $data['request_id']),
                                function ($q) use ($data) {
                                    $q->where('requestable_id', $data['request_id']);

                                })->where('requestable_type', 'employee_leave_request')
                                ->when(
                                    isset($data['status']),
                                    function ($q) use ($data) {
                                        $q->where('status', $data['status']);
                                    }
                                )
                                ->when(
                                    (isset($data['from_date']) && $data['to_date']),
                                    function ($q) use ($data) {
                                        if (isset($data['in_payroll_hub']) && ! $data['in_payroll_hub'] || ! isset($data['in_payroll_hub'])) {
                                            $q->whereBetween('date', [$data['from_date'], $data['to_date']]);
                                        }
                                    }
                                )
                                ->orderBy('created_at')
                                ->with('requestable.attachments');
                            if ($data['for_employee'] == false) {
                                $this->appendScopeQuery($q, $data);
                            }
                            $q->withWhereHas('requestable', function ($q) use ($data, $lang) {
                                $q->when(
                                    class_basename($q->getModel()) == 'EmployeeLeaveRequest',
                                    function ($q) use ($lang, $data) {
                                        if (isset($data['in_payroll_hub']) && $data['in_payroll_hub']) {
                                            $q->when(
                                                (isset($data['from_date']) && $data['to_date']),
                                                function ($q) use ($data) {
                                                    $q->where(function ($query) use ($data) {
                                                        $query->whereDate('from', '>=', $data['from_date'])
                                                            ->whereDate('from', '<=', $data['to_date'])
                                                            ->orWhere(function ($subQuery) use ($data) {
                                                                $subQuery->whereDate('to', '>=', $data['from_date'])
                                                                    ->whereDate('to', '<=', $data['to_date']);
                                                            });
                                                    });
                                                }
                                            );
                                        }
                                        $q->select('id', 'from', 'to', 'company_leave_type_id',
                                            'employee_id', 'note', 'company_leave_type_policy_id', 'status')
                                            ->where('company_leave_type_id', '!=', config('globals.rest_day_leave_id'))
                                            ->with('employee:id,branch_id,first_name'.$lang.',second_name'.$lang.',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name'.$lang.',color,role_id')
                                            ->with('employeeRequests', function ($q) use ($lang) {
                                                $q->with('requestedBy:id,first_name' . $lang . ',second_name' . $lang)
                                                    ->select('id', 'requested_by', 'requestable_type', 'requestable_id')->first();
                                            })
                                            ->with('employeeApproves', function ($q) {
                                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                                    ->with('users.employee', function ($employeeQuery) {
                                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                                            ->with('branches', function ($q) {
                                                                $q->select('branch_employee.id', 'branch_id');
                                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                                    })
                                                    ->with('scopes')
                                                    ->orderBy('workflow_approval_cycle.order');
                                            })
                                            ->with('companyLeaveType:id,name' . $lang)
                                            ->with('companyLeaveTypePolicy:id,unit')
                                            ->with('attachments')
                                            ->when(isset($data['leave_type_ids']), function ($q) use ($data) {
                                                return $q->whereIn('company_leave_type_id', $data['leave_type_ids']);
                                            });
                                    }
                                )->when(
                                    Arr::get($data, 'pending_on_me', false),
                                    function ($q) use ($data) {
                                        $this->appendPendingOnMeToApprovers($q, $data['pending_on_me']);
                                    }
                                );
                            });
                        });
                    })
                    ->when((Arr::get($data, 'filter', false) && $data['filter'] == 'penalties') || !isset($data['filter']), function ($q) use ($data, $lang) {
                        $q->orWhere(function ($q) use ($data, $lang) {
                            $q->when(
                                (isset($data['request_id']) && $data['request_id']),
                                function ($q) use ($data) {
                                    $q->where('requestable_id', $data['request_id']);

                                })->where('requestable_type', 'penalty')
                                ->when(
                                    isset($data['status']),
                                    function ($q) use ($data) {
                                        $q->where('status', $data['status']);
                                    }
                                )
                                ->when(
                                    (isset($data['from_date']) && $data['to_date']),
                                    function ($q) use ($data) {
                                        if ((isset($data['in_payroll_hub']) && ! $data['in_payroll_hub']) || ! isset($data['in_payroll_hub'])) {
                                            $q->whereDate('date', '>=', $data['from_date'])
                                                ->whereDate('date', '<=', $data['to_date']);
                                        }
                                    }
                                )
                                ->orderBy('created_at');

                            if ($data['for_employee'] == false) {
                                $this->appendScopeQuery($q, $data);
                            }
                            $q->withWhereHas('requestable', function ($q) use ($data, $lang) {
                                $q->when(
                                    class_basename($q->getModel()) == 'Penalty',
                                    function ($q) use ($data, $lang) {
                                        if (isset($data['in_payroll_hub']) && $data['in_payroll_hub']) {
                                            $q->when(
                                                (isset($data['from_date']) && $data['to_date']),
                                                function ($q) use ($data) {
                                                    $q->whereDate('date', '>=', $data['from_date'])
                                                        ->whereDate('date', '<=', $data['to_date']);
                                                }
                                            );
                                        }
                                        $q->select('id', 'amount', 'recurrence', 'unit', 'date', 'reason', 'submitted_by', 'employee_id')
                                            ->selectRaw('CAST(date AS datetime) AS date')
                                            ->with('penaltyGroup:id,name' . $lang)
                                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                                            ->with('employeeApproves', function ($q) {
                                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                                    ->with('users.employee', function ($employeeQuery) {
                                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                                            ->with('branches', function ($q) {
                                                                $q->select('branch_employee.id', 'branch_id');
                                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                                    })
                                                    ->with('scopes')
                                                    ->orderBy('workflow_approval_cycle.order');
                                            })
                                            ->when(
                                                Arr::get($data, 'pending_on_me', false),
                                                function ($q) use ($data) {
                                                    $this->appendPendingOnMeToApprovers($q, $data['pending_on_me']);
                                                }
                                            );
                                    }
                                );
                            });
                        });
                    })
                    ->when((Arr::get($data, 'filter', false) && $data['filter'] == 'termination_request') || !isset($data['filter']), function ($q) use ($data, $lang) {
                        $q->orWhere(function ($q) use ($data, $lang) {
                            $q->when(
                                isset($data['request_id']) && $data['request_id'],
                                function ($q) use ($data) {
                                    $q->where('requestable_id', $data['request_id']);
                                })
                                ->where('requestable_type', 'termination_request')
                                ->when(
                                    isset($data['status']),
                                    function ($q) use ($data) {
                                        $q->where('status', $data['status']);
                                    }
                                )
                                ->when(
                                    isset($data['status']),
                                    function ($q) use ($data) {
                                        $q->where('status', $data['status']);
                                    }
                                )
                                ->when(
                                    (isset($data['from_date']) && $data['to_date']),
                                    function ($q) use ($data) {
                                        if (isset($data['in_payroll_hub']) && ! $data['in_payroll_hub'] || ! isset($data['in_payroll_hub'])) {
                                            $q->whereBetween('date', [$data['from_date'], $data['to_date']]);
                                        }
                                    }
                                )
                                ->orderBy('created_at');

                            if ($data['for_employee'] == false) {
                                $this->appendScopeQuery($q, $data);
                            }
                            $q->withWhereHas('requestable', function ($q) use ($data, $lang) {
                                $q->when(
                                    class_basename($q->getModel()) == 'TerminationRequest',
                                    function ($q) use ($data, $lang) {
                                        if (isset($data['in_payroll_hub']) && $data['in_payroll_hub']) {
                                            $q->when(
                                                (isset($data['from_date']) && $data['to_date']),
                                                function ($q) use ($data) {
                                                    $q->whereDate('terminate_date', '>=', $data['from_date'])
                                                        ->whereDate('terminate_date', '<=', $data['to_date']);
                                                }
                                            );
                                        }
                                        $q->select('id', 'employee_id', 'terminate_date', 'terminate_reason', 'terminate_type', 'status')
                                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                                            ->with('employeeRequests', function ($q) use ($lang) {
                                                $q->with('requestedBy:id,name_ar,name_en,name', 'requestedBy:id,first_name' . $lang . ',second_name' . $lang)
                                                    ->select('id', 'requested_by', 'requestable_type', 'requestable_id', 'status', 'comment');
                                            })
                                            ->with('employeeApproves', function ($q) {
                                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                                    ->with('users.employee', function ($employeeQuery) {
                                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                                            ->with('branches:id,branch_id')
                                                            ->with(['managedDepartments', 'managedSubDepartments']);
                                                    })
                                                    ->with('scopes')
                                                    ->orderBy('workflow_approval_cycle.order');
                                            })
                                            ->when(
                                                Arr::get($data, 'pending_on_me', false),
                                                function ($q) use ($data) {
                                                    $this->appendPendingOnMeToApprovers($q, $data['pending_on_me']);
                                                }
                                            );
                                    }
                                );
                            });
                        });
                    }
                    )
                    ->when((Arr::get($data, 'filter', false) && $data['filter'] == 'loans') || !isset($data['filter']), function ($q) use ($data, $lang) {
                        $q->orWhere(function ($q) use ($data, $lang) {

                            $q->when(
                                (isset($data['request_id']) && $data['request_id']),
                                function ($q) use ($data) {
                                    $q->where('requestable_id', $data['request_id']);

                                })->where('requestable_type', 'loan')
                                ->when(
                                    isset($data['status']),
                                    function ($q) use ($data) {
                                        $q->where('status', $data['status']);
                                    }
                                )
                                ->when(
                                    (isset($data['from_date']) && $data['to_date']),
                                    function ($q) use ($data) {
                                        if (isset($data['in_payroll_hub']) && ! $data['in_payroll_hub'] || ! isset($data['in_payroll_hub'])) {
                                            $q->whereDate('created_at', '>=', $data['from_date'])
                                                ->whereDate('created_at', '<=', $data['to_date']);
                                        }
                                    }
                                )
                                ->orderBy('created_at');

                            if ($data['for_employee'] == false) {
                                $this->appendScopeQuery($q, $data);
                            }
                            $q->withWhereHas('requestable', function ($q) use ($data, $lang) {
                                $q->when(
                                    class_basename($q->getModel()) == 'Loan',
                                    function ($q) use ($data, $lang) {
                                        if (isset($data['in_payroll_hub']) && $data['in_payroll_hub']) {
                                            $q->when(
                                                (isset($data['from_date']) && $data['to_date']),
                                                function ($q) use ($data) {
                                                    $q->whereDate('disbursement_date', '>=', $data['from_date'])
                                                        ->whereDate('disbursement_date', '<=', $data['to_date']);
                                                }
                                            );
                                        }
                                        $q->select('id', 'amount', 'number_of_installments', 'comment', 'disbursement_date', 'employee_id')
                                            ->selectRaw('CAST(created_at AS datetime) AS date')
                                            ->with('installments:id,status,date,amount')
                                            ->with('attachments:attachable_id,attachable_type,attachment_url,file_name')
                                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                                            ->with('employeeApproves', function ($q) {
                                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                                    ->with('users.employee', function ($employeeQuery) {
                                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                                            ->with('branches', function ($q) {
                                                                $q->select('branch_employee.id', 'branch_id');
                                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                                    })
                                                    ->with('scopes')
                                                    ->orderBy('workflow_approval_cycle.order');
                                            })
                                            ->when(
                                                Arr::get($data, 'pending_on_me', false),
                                                function ($q) use ($data) {
                                                    $this->appendPendingOnMeToApprovers($q, $data['pending_on_me']);
                                                }
                                            );
                                    }
                                );
                            });
                        });
                    })
                    ->when((Arr::get($data, 'filter', false) && $data['filter'] == 'salary_advances') || !isset($data['filter']), function ($q) use ($data, $lang) {
                        $q->orWhere(function ($q) use ($data, $lang) {
                            $q->when(
                                (isset($data['request_id']) && $data['request_id']),
                                function ($q) use ($data) {
                                    $q->where('requestable_id', $data['request_id']);

                                })->where('requestable_type', 'salary_advance')
                                ->when(
                                    isset($data['status']),
                                    function ($q) use ($data) {
                                        $q->where('status', $data['status']);
                                    }
                                )
                                ->when(
                                    (isset($data['from_date']) && $data['to_date']),
                                    function ($q) use ($data) {
                                        if (isset($data['in_payroll_hub']) && ! $data['in_payroll_hub'] || ! isset($data['in_payroll_hub'])) {
                                            $q->whereDate('created_at', '>=', $data['from_date'])
                                                ->whereDate('created_at', '<=', $data['to_date']);
                                        }
                                    }
                                )
                                ->orderBy('created_at');

                            if ($data['for_employee'] == false) {
                                $this->appendScopeQuery($q, $data);
                            }
                            $q->withWhereHas('requestable', function ($q) use ($data, $lang) {
                                $q->when(
                                    class_basename($q->getModel()) == 'SalaryAdvance',
                                    function ($q) use ($data, $lang) {
                                        if (isset($data['in_payroll_hub']) && $data['in_payroll_hub']) {
                                            $q->when(
                                                (isset($data['from_date']) && $data['to_date']),
                                                function ($q) use ($data) {
                                                    $q->whereDate('disbursement_date', '>=', $data['from_date'])
                                                        ->whereDate('disbursement_date', '<=', $data['to_date']);
                                                }
                                            );
                                        }
                                        $q->select('id', 'amount', 'comment', 'disbursement_date', 'employee_id')
                                            ->selectRaw('CAST(created_at AS datetime) AS date')
                                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                                            ->with('employeeApproves', function ($q) {
                                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                                    ->with('users.employee', function ($employeeQuery) {
                                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                                            ->with('branches', function ($q) {
                                                                $q->select('branch_employee.id', 'branch_id');
                                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                                    })
                                                    ->with('scopes')
                                                    ->orderBy('workflow_approval_cycle.order');
                                            })
                                            ->when(
                                                Arr::get($data, 'pending_on_me', false),
                                                function ($q) use ($data) {
                                                    $this->appendPendingOnMeToApprovers($q, $data['pending_on_me']);
                                                }
                                            );
                                    }
                                );
                            });
                        });
                    })
                    ->when((Arr::get($data, 'filter', false) && $data['filter'] == 'extra_work_day_request') || !isset($data['filter']), function ($q) use ($data, $lang) {
                        $q->orWhere(function ($q) use ($data, $lang) {
                            $q->when(
                                isset($data['request_id']) && $data['request_id'],
                                function ($q) use ($data) {
                                    $q->where('requestable_id', $data['request_id']);
                                })
                                ->where('requestable_type', 'extra_work_day_request')
                                ->when(
                                    isset($data['status']),
                                    function ($q) use ($data) {
                                        $q->where('status', $data['status']);
                                    }
                                )
                                ->when(
                                    isset($data['from_date']) && isset($data['to_date']),
                                    function ($q) use ($data) {
                                        if (isset($data['in_payroll_hub']) && ! $data['in_payroll_hub'] || ! isset($data['in_payroll_hub'])) {
                                            $q->whereBetween('date', [$data['from_date'], $data['to_date']]);
                                        }
                                    }
                                )
                                ->orderBy('created_at');

                            if ($data['for_employee'] == false) {
                                $this->appendScopeQuery($q, $data);
                            }
                            $q->withWhereHas('requestable', function ($q) use ($data, $lang) {
                                $q->when(
                                    class_basename($q->getModel()) == 'ExtraWorkDayRequest',
                                    function ($q) use ($data, $lang) {
                                        if (isset($data['in_payroll_hub']) && $data['in_payroll_hub']) {
                                            $q->when(
                                                (isset($data['from_date']) && $data['to_date']),
                                                function ($q) use ($data) {
                                                    $q->whereDate('extra_work_day_date', '>=', $data['from_date'])
                                                        ->whereDate('extra_work_day_date', '<=', $data['to_date']);
                                                }
                                            );
                                        }
                                        $q->select('id', 'employee_id', 'extra_work_day_date', 'compensation_rate', 'status')
                                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                                            ->with('employeeRequests', function ($q) use ($lang) {
                                                $q->with('requestedBy:id,name_ar,name_en,name', 'requestedBy:id,first_name' . $lang . ',second_name' . $lang)
                                                    ->select('id', 'requested_by', 'requestable_type', 'requestable_id', 'status', 'comment');
                                            })
                                            ->with('attendance:id,ci_id,co_id,slotable_id,slotable_type',
                                                'attendance.clockIn:id,date,lat,long',
                                                'attendance.clockOut:id,date,lat,long',
                                                'attendance.slotable:id,required_ci_branch_id,required_co_branch_id',
                                                'attendance.clockIn.branch:id,name',
                                                'attendance.clockOut.branch:id,name')
                                            ->with('employeeApproves', function ($q) {
                                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                                    ->with('users.employee', function ($employeeQuery) {
                                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                                            ->with('branches:id,branch_id')
                                                            ->with(['managedDepartments', 'managedSubDepartments']);
                                                    })
                                                    ->with('scopes')
                                                    ->orderBy('workflow_approval_cycle.order');
                                            })
                                            ->when(
                                                Arr::get($data, 'pending_on_me', false),
                                                function ($q) use ($data) {
                                                    $this->appendPendingOnMeToApprovers($q, $data['pending_on_me']);
                                                }
                                            );
                                    }
                                );
                            });
                        });
                    })
                    ->when((Arr::get($data, 'filter', false) && $data['filter'] == 'missions') || !isset($data['filter']), function ($q) use ($data, $lang) {
                        $q->orWhere(function ($q) use ($data, $lang) {
                            $q->when(
                                (isset($data['request_id']) && $data['request_id']),
                                function ($q) use ($data) {
                                    $q->where('requestable_id', $data['request_id']);

                                })->where('requestable_type', 'mission_request')
                                ->when(
                                    isset($data['status']),
                                    function ($q) use ($data) {
                                        $q->where('status', $data['status']);
                                    }
                                )
                                ->when(
                                    (isset($data['from_date']) && $data['to_date']),
                                    function ($q) use ($data) {
                                        if (isset($data['in_payroll_hub']) && ! $data['in_payroll_hub'] || ! isset($data['in_payroll_hub'])) {
                                            $q->whereDate('date', '>=', $data['from_date'])
                                                ->whereDate('date', '<=', $data['to_date']);
                                        }
                                    }
                                )
                                ->orderBy('created_at');

                            if ($data['for_employee'] == false) {
                                $this->appendScopeQuery($q, $data);
                            }
                            $q->withWhereHas('requestable', function ($q) use ($data, $lang) {
                                $q->when(
                                    class_basename($q->getModel()) == 'MissionRequest',
                                    function ($q) use ($data, $lang) {
                                        if (isset($data['in_payroll_hub']) && $data['in_payroll_hub']) {
                                            $q->when(
                                                (isset($data['from_date']) && $data['to_date']),
                                                function ($q) use ($data) {
                                                    $q->where(function ($query) use ($data) {
                                                        $query->whereDate('from', '>=', $data['from_date'])
                                                            ->whereDate('from', '<=', $data['to_date'])
                                                            ->orWhere(function ($subQuery) use ($data) {
                                                                $subQuery->whereDate('to', '>=', $data['from_date'])
                                                                    ->whereDate('to', '<=', $data['to_date']);
                                                            });
                                                    });
                                                }
                                            );
                                        }
                                        $q->select('id', 'employee_id', 'company_id', 'mission_type', 'from', 'to', 'status', 'reason', 'location')
                                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                                            ->with('employeeApproves', function ($q) {
                                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                                    ->with('users.employee', function ($employeeQuery) {
                                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                                            ->with('branches', function ($q) {
                                                                $q->select('branch_employee.id', 'branch_id');
                                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                                    })
                                                    ->with('scopes')
                                                    ->orderBy('workflow_approval_cycle.order');
                                            })
                                            ->when(
                                                Arr::get($data, 'pending_on_me', false),
                                                function ($q) use ($data) {
                                                    $this->appendPendingOnMeToApprovers($q, $data['pending_on_me']);
                                                }
                                            )
                                            ->when(isset($data['mission_type']), function ($q) use ($data) {
                                                return $q->whereIn('mission_type', $data['mission_type']);
                                            });
                                    }
                                );
                            });
                        });
                    })
                    ->when((Arr::get($data, 'filter', false) && $data['filter'] == 'probation_request') || !isset($data['filter']), function ($q) use ($data, $lang) {
                        $q->orWhere(function ($q) use ($data, $lang) {
                            $q->when(
                                (isset($data['request_id']) && $data['request_id']),
                                function ($q) use ($data) {
                                    $q->where('requestable_id', $data['request_id']);

                                })->where('requestable_type', 'probation_request')
                                ->when(
                                    isset($data['status']),
                                    function ($q) use ($data) {
                                        $q->where('status', $data['status']);
                                    }
                                )
                                ->when(
                                    (isset($data['from_date']) && $data['to_date']),
                                    function ($q) use ($data) {
                                        if (isset($data['in_payroll_hub']) && ! $data['in_payroll_hub'] || ! isset($data['in_payroll_hub'])) {
                                            $q->whereDate('date', '>=', $data['from_date'])
                                                ->whereDate('date', '<=', $data['to_date']);
                                        }
                                    }
                                )
                                ->orderBy('created_at');

                            if ($data['for_employee'] == false) {
                                $this->appendScopeQuery($q, $data);
                            }
                            $q->withWhereHas('requestable', function ($q) use ($data, $lang) {
                                $q->when(
                                    class_basename($q->getModel()) == 'ProbationRequest',
                                    function ($q) use ($data, $lang) {
                                        if (isset($data['in_payroll_hub']) && $data['in_payroll_hub']) {
                                            $q->when(
                                                (isset($data['from_date']) && $data['to_date']),
                                                function ($q) use ($data) {
                                                    $q->whereDate('probation_end_date', '>=', $data['from_date'])
                                                        ->whereDate('probation_end_date', '<=', $data['to_date']);
                                                }
                                            );
                                        }
                                        $q->select('id', 'employee_id', 'company_id', 'probation_end_date', 'status')
                                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                                            ->with('employeeApproves', function ($q) {
                                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                                    ->with('users.employee', function ($employeeQuery) {
                                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                                            ->with('branches', function ($q) {
                                                                $q->select('branch_employee.id', 'branch_id');
                                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                                    })
                                                    ->with('scopes')
                                                    ->orderBy('workflow_approval_cycle.order');
                                            })
                                            ->when(
                                                Arr::get($data, 'pending_on_me', false),
                                                function ($q) use ($data) {
                                                    $this->appendPendingOnMeToApprovers($q, $data['pending_on_me']);
                                                }
                                            );
                                    }
                                );
                            });
                        });
                    })
                    ->when((Arr::get($data, 'filter', false) && $data['filter'] == 'overtimes') || !isset($data['filter']), function ($q) use ($data, $lang) {
                        $q->orWhere(function ($q) use ($data, $lang) {
                            $q->when(
                                (isset($data['request_id']) && $data['request_id']),
                                function ($q) use ($data) {
                                    $q->where('requestable_id', $data['request_id']);

                                })->where('requestable_type', 'overtime_request')
                                ->when(
                                    isset($data['status']),
                                    function ($q) use ($data) {
                                        $q->where('status', $data['status']);
                                    }
                                )
                                ->when(
                                    (isset($data['from_date']) && $data['to_date']),
                                    function ($q) use ($data) {
                                        if (isset($data['in_payroll_hub']) && ! $data['in_payroll_hub'] || ! isset($data['in_payroll_hub'])) {
                                            $q->whereDate('date', '>=', $data['from_date'])
                                                ->whereDate('date', '<=', $data['to_date']);
                                        }
                                    }
                                )
                                ->orderBy('created_at');

                            if ($data['for_employee'] == false) {
                                $this->appendScopeQuery($q, $data);
                            }
                            $q->withWhereHas('requestable', function ($q) use ($data, $lang) {
                                $q->when(
                                    class_basename($q->getModel()) == 'OvertimeRequest',
                                    function ($q) use ($data, $lang) {
                                        if (isset($data['in_payroll_hub']) && $data['in_payroll_hub']) {
                                            $q->when(
                                                (isset($data['from_date']) && $data['to_date']),
                                                function ($q) use ($data) {
                                                    $q->whereDate('date', '>=', $data['from_date'])
                                                        ->whereDate('date', '<=', $data['to_date']);
                                                }
                                            );
                                        }
                                        $q->select('id', 'employee_id', 'company_id', 'date', 'status', 'reason')
                                            ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                                            ->with('employeeApproves', function ($q) {
                                                $q->select('spatie_roles.id', 'spatie_roles.name')
                                                    ->with('users.employee', function ($employeeQuery) {
                                                        $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                                            ->with('branches', function ($q) {
                                                                $q->select('branch_employee.id', 'branch_id');
                                                            })->with(['managedDepartments', 'managedSubDepartments']);
                                                    })
                                                    ->with('scopes')
                                                    ->orderBy('workflow_approval_cycle.order');
                                            })
                                            ->when(
                                                Arr::get($data, 'pending_on_me', false),
                                                function ($q) use ($data) {
                                                    $this->appendPendingOnMeToApprovers($q, $data['pending_on_me']);
                                                }
                                            );
                                    }
                                );
                            });
                        });
                    });

            })
            ->orderBy('employee_requests.created_at');

    }

    public function getLoanAndSalaryAdvanceRequestsByFilters($data)
    {
        $lang = '_' . (config('globals.lang') ?? 'ar');

        $requests = $this->model
            ->select(
                'employee_requests.*',
                'employees.first_name' . $lang,
                'employees.second_name' . $lang,
                'employees.name' . $lang,
                'employees.employee_number',
                'employees.branch_id',
                'employees.title_id',
                'branches.name' . $lang . ' as branch_name',
                'titles.name' . $lang . ' as title_name',
                'titles.color',
                'employees_info.termination_date')
            ->join('employees', 'employee_requests.employee_id', '=', 'employees.id')
            ->join('branches', 'employees.branch_id', '=', 'branches.id')
            ->join('titles', 'employees.title_id', '=', 'titles.id')
            ->leftJoin('employees_info', 'employees.id', '=', 'employees_info.employee_id') // If employee_info exists
            ->leftJoin('loans', function ($join) {
                $join->on('employee_requests.requestable_id', '=', 'loans.id')
                    ->where('employee_requests.requestable_type', 'loan');
            })
            ->leftJoin('salary_advances', function ($join) {
                $join->on('employee_requests.requestable_id', '=', 'salary_advances.id')
                    ->where('employee_requests.requestable_type', 'salary_advance');
            })
            ->when(isset($data['requestable_type']), function ($q) use ($data) {
                $q->whereIn('employee_requests.requestable_type', $data['requestable_type']);
            }, function ($q) {
                $q->whereIn('employee_requests.requestable_type', ['loan', 'salary_advance']); // default behavior
            }) // Filter by requestable_type
            ->when(isset($data['search_value']), function ($q) use ($data, $lang) {
                $searchValue = $data['search_value'];
                $q->where(function ($searchQuery) use ($searchValue, $lang) {
                    $searchQuery->where('employees.name' . $lang, 'LIKE', '%' . $searchValue . '%')
                        ->orWhere('employees.phone', $searchValue)
                        ->orWhere('employees.employee_number', $searchValue);
                });
            })
            ->when(isset($data['statuses']), function ($q) use ($data) {
                $q->where(function ($query) use ($data) {
                    // For Loans
                    $query->orWhere(function ($subQuery) use ($data) {
                        $subQuery->where('employee_requests.requestable_type', 'loan')
                            ->where(function ($loanStatusQuery) use ($data) {
                                if (in_array('outstanding', $data['statuses'])) {
                                    $loanStatusQuery->whereIn('loans.status', ['disbursed', 'ready_to_disburse', 'not_repaid']);
                                } else {
                                    $loanStatusQuery->whereIn('loans.status', $data['statuses']);
                                }
                            });
                    });
                    // For Salary Advances
                    $query->orWhere(function ($subQuery) use ($data) {
                        $subQuery->where('employee_requests.requestable_type', 'salary_advance')
                            ->where(function ($advanceStatusQuery) use ($data) {
                                if (in_array('outstanding', $data['statuses'])) {
                                    $advanceStatusQuery->whereIn('salary_advances.status', ['disbursed', 'ready_to_disburse', 'not_repaid']);
                                } else {
                                    $advanceStatusQuery->whereIn('salary_advances.status', $data['statuses']);
                                }
                            });
                    });
                });
            })
            ->when(isset($data['hiring_status']), function ($q) use ($data) {
                $q->where('employees.status', $data['hiring_status']);
            })
            ->when(isset($data['employeeId']), function ($q) use ($data) {
                $q->where('employee_requests.employee_id', $data['employeeId']);
            })
            ->when(isset($data['start_date'], $data['end_date']), function ($q) use ($data) {
                $q->where(function ($query) use ($data) {
                    // For Loans (checking disbursement date)
                    $query->orWhere(function ($subQuery) use ($data) {
                        $subQuery->where('employee_requests.requestable_type', 'loan')
                            ->whereDate('loans.created_at', '>=', $data['start_date'])
                            ->whereDate('loans.created_at', '<=', $data['end_date']);
                    });
                    // For Salary Advances (checking disbursement date)
                    $query->orWhere(function ($subQuery) use ($data) {
                        $subQuery->where('employee_requests.requestable_type', 'salary_advance')
                            ->whereDate('salary_advances.created_at', '>=', $data['start_date'])
                            ->whereDate('salary_advances.created_at', '<=', $data['end_date']);
                    });
                });
            })
            ->groupBy('employee_requests.id',
                'employees.first_name' . $lang,
                'employees.second_name' . $lang,
                'employees.name' . $lang,
                'employees.employee_number',
                'employees.branch_id',
                'employees.title_id',
                'branches.name' . $lang,
                'titles.name' . $lang,
                'titles.color',
                'employees_info.termination_date');

        // Load related data without installments
        $requests->withWhereHas('requestable', function ($q) use ($data, $lang) {
            $q->when(
                class_basename($q->getModel()) == 'SalaryAdvance' || class_basename($q->getModel()) == 'Loan',
                function ($q) use ($data, $lang) {
                    $q->select('id', 'amount', 'comment', 'disbursement_date', 'employee_id')
                        ->selectRaw('CAST(created_at AS datetime) AS date')
                        ->when(class_basename($q->getModel()) == 'Loan', function ($q) {
                            $q->with('installments:id,status,date,amount')
                              ->with('attachments:id,attachable_id,attachable_type,attachment_url,file_name');
                        })
                        ->with('employee:id,branch_id,first_name' . $lang . ',second_name' . $lang . ',employee_number,title_id', 'employee.title:id,department_id,sub_department_id,name' . $lang . ',color,role_id')
                        ->with('employeeApproves', function ($q) {
                            $q->select('spatie_roles.id', 'spatie_roles.name')
                                ->with('users.employee', function ($employeeQuery) {
                                    $employeeQuery->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
                                        ->with('branches', function ($q) {
                                            $q->select('branch_employee.id', 'branch_id');
                                        })->with(['managedDepartments', 'managedSubDepartments']);
                                })
                                ->with('scopes')
                                ->orderBy('workflow_approval_cycle.order');
                        })
                        ->when(
                            Arr::get($data, 'pending_on_me', false),
                            function ($q) use ($data) {
                                $this->appendPendingOnMeToApprovers($q, $data['pending_on_me']);
                            }
                        );
                }
            );
        });

        // Apply sorting by status and type
        $requests->when(isset($data['order_by_status']), function ($q) use ($data) {
            $orderDirection = in_array($data['order_by_status'], ['asc', 'desc']) ? $data['order_by_status'] : 'asc';
            $q->orderByRaw("(CASE WHEN employee_requests.requestable_type = 'loan' THEN loans.status ELSE salary_advances.status END) {$orderDirection}");
        });

        $requests->when(isset($data['order_by_type']), function ($q) use ($data) {
            $orderDirection = in_array($data['order_by_type'], ['asc', 'desc']) ? $data['order_by_type'] : 'asc';
            $q->orderBy('employee_requests.requestable_type', $orderDirection);
        });

        // Apply any additional scope queries
        $this->appendScopeQuery($requests, $data);

        // Handle pagination
        if (isset($data['page_size'])) {
            return $this->paginate($requests, $data['page_size']);
        }

        // Fetch the requests without installments
        $results = $requests->get();

        // Step 2: After fetching the results, load installments for loans (attachments are already loaded in the initial query)
        foreach ($results as $request) {
            if ($request->requestable_type === 'loan') {
                $request->load('requestable.installments');
            }
        }

        // Return the results with installments loaded for loans
        return $results;
    }

    public function requestsRelatedToMeCount($data)
    {
        return $this->requestsRelatedToMe($data)->count();
    }

    public function getRequestsRelatedToMe($data)
    {
        if (isset($data['page_size'])) {
            return $this->requestsRelatedToMe($data)->paginate($data['page_size']);
        }

        return $this->requestsRelatedToMe($data)->get();

    }

    public function getRequestByRequestableId($data)
    {
        $requestableId = $data['requestable_id'];
        $requestableType = $data['requestable_type'];

        // Find the request using the polymorphic relationship
        $request = EmployeeRequest::where('requestable_id', $requestableId)
            ->where('requestable_type', $requestableType)
            ->first();

        return $request;
    }

    public function paginate($obj, $pageSize)
    {
        return $obj->paginate($pageSize);
    }

    public function getExtraWorkDayRequests($requestIds)
    {
        return $this->model
            ->whereIn('requestable_id', $requestIds)
            ->where('requestable_type', 'extra_work_day_request')
            ->get();
    }
}
