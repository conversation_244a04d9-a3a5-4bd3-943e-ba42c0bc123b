<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\DB;

class UniquePersonalEmailRule implements Rule
{
    protected ?int $exceptId;

    public function __construct(?int $exceptId = null)
    {
        $this->exceptId = $exceptId;
    }

    public function passes($attribute, $value): bool
    {
        if (empty($value)) {
            return true;
        }

        $query = DB::table('employees_info')
            ->join('employees', 'employees.id', '=', 'employees_info.employee_id')
            ->where('employees_info.personal_email', $value)
            ->whereNull('employees.deleted_at');

        if ($this->exceptId) {
            $query->where('employees_info.employee_id', '!=', $this->exceptId);
        }

        return !$query->exists();
    }

    public function message(): string
    {
        return __('validation.unique_personal_email', ['attribute' => 'personal email']);
    }
}
