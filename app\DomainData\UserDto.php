<?php

namespace App\DomainData;

trait UserDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'email' => 'required|email',
            'password' => 'required|string',
            'is_admin' => 'boolean',
            'employee_id' => 'numeric',
            'company_id' => 'numeric',
            'name' => 'string',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeUserDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
