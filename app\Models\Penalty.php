<?php

namespace App\Models;

use App\Models\Casts\TimezoneDateTime;
use App\Models\StateMachines\RequestState;
use App\Traits\CompanyRule;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\ModelStates\HasStates;

class Penalty extends BaseModel
{
    use CompanyRule, HasFactory, HasStates, LogsActivity, SoftDeletes;

    protected $guarded = ['created_at', 'updated_at', 'id'];

    protected static $recordEvents = ['updated', 'deleted'];

    protected $casts = [
        'created_at' => TimezoneDateTime::class,
        'updated_at' => TimezoneDateTime::class,
        'deleted_at' => TimezoneDateTime::class.':nullable',
        'status' => RequestState::class,
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($penalty) {
            EmployeeRequest::create([
                'date' => $penalty->created_at,
                'employee_id' => $penalty->employee_id,
                'status' => $penalty->status, // or any default status you want to set
                'comment' => $penalty->reason, // if `comment` exists in Employeepenalty,
                'request_name' => 'penalty',
                'requestable_id' => $penalty->id,
                'requestable_type' => 'penalty',
                'requested_by' => auth()->user()->employee_id,
                'company_id' => $penalty->company_id,
            ]);
        });


    }

    public function PenaltyGroup()
    {
        return $this->belongsTo(PenaltyGroup::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function submittedBy()
    {
        return $this->belongsTo(Employee::class, 'submitted_by');
    }

    public function employeeRequest()
    {
        return $this->morphOne(EmployeeRequest::class, 'requestable')->latest('id');
    }

    // public function employeeApproves()
    // {
    //     return $this->morphToMany(Employee::class, 'approval', 'approval_cycles',
    //                                                'approval_id', 'employee_id')->withPivot(['id','company_id', 'status', 'status_date', 'order', 'operator'])
    //                                                ->withTimestamps();
    // }

    public function employeeApproves()
    {
        return $this->morphToMany(Role::class, 'requestable', 'workflow_approval_cycle',
            'requestable_id', 'role_id')
            ->withPivot(['id', 'company_id', 'status', 'date', 'order', 'operator', 'requestable_type', 'request_workflow_id', 'branch_id', 'department_id'])
            ->withTimestamps();
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll();
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        set_impersonate_activity_causer($activity);
        if ($eventName == 'deleted') {
            return;
        }

        $oldStatus = $activity->properties['old']['status'];
        $newStatus = $activity->properties['attributes']['status'];

        if ($newStatus == $oldStatus || $newStatus != config('globals.REQUEST_STATUSES.CANCELLED')) {
            return;
        }

        $actionName = 'penalty_cancelled';

        $penaltyId = $this->id;
        $activity->properties = $activity->properties->merge(['leave_id' => $penaltyId,
            'action_name' => $actionName]);
    }

    public function workflow()
    {
        return $this->belongsTo(Workflow::class);
    }

    public function workflowApprovalCycles()
    {
        return $this->morphMany(WorkflowApprovalCycle::class, 'requestable');
    }
}
