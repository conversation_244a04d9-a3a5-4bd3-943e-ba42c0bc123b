<?php

namespace App\Http\Requests\V1\ServiceCharge;

use App\Enums\ServiceCharge\ServiceChargeApplicationEnum;
use App\Exceptions\UnprocessableException;
use App\Traits\GetLastDraftedPayRollOrCreate;
use App\Util\PayrollUtil;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class SaveServiceChargeRequest extends FormRequest
{
    use GetLastDraftedPayRollOrCreate;

    protected $currentPayroll;

    public function __construct()
    {
        $this->currentPayroll = $this->getCurrentPayroll();
    }

    /**
     * Authorize the request.
     */
    public function authorize()
    {

        if ($this->currentPayroll->status !== PayrollUtil::PAYROLL_STATUS['DRAFT']) {
            throw new UnprocessableException('You cannot update payroll service charge unless it is drafted');
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'application_policy' => [
                'required',
                new Enum(ServiceChargeApplicationEnum::class),
            ],

            // Entire company rules
            'total_service_charge' => [
                Rule::requiredIf(fn () => $this->input('application_policy') === ServiceChargeApplicationEnum::ENTIRE_COMPANY),
                'numeric',
                'min:1',
            ],
            'total_basic_salaries' => [
                Rule::requiredIf(fn () => $this->input('application_policy') === ServiceChargeApplicationEnum::ENTIRE_COMPANY),
                'numeric',
                'min:1',
            ],

            // Branch group rules
            'groups' => [
                Rule::requiredIf(fn () => $this->input('application_policy') === ServiceChargeApplicationEnum::BRANCH_GROUP),
                'array',
                'min:1',
            ],
            'groups.*.id' => [
                'required_with:groups',
                'integer',
            ],
            'groups.*.branches' => [
                'required_with:groups',
                'array',
                'min:1',
            ],
            'groups.*.branches.*.id' => [
                'required_with:groups.*.branches',
                'integer',
            ],
            'groups.*.total_service_charge' => [
                'required_with:groups',
                'numeric',
                'min:1',
            ],
            'groups.*.total_basic_salaries' => [
                'required_with:groups',
                'numeric',
                'min:1',
            ],

            // Branches rules
            'branches' => [
                Rule::requiredIf(fn () => $this->input('application_policy') === ServiceChargeApplicationEnum::BRANCHES),
                'array',
                'min:1',
            ],
            'branches.*.id' => [
                'required_with:branches',
                'integer',
            ],
            'branches.*.total_service_charge' => [
                'required_with:branches',
                'numeric',
                'min:1',
            ],
            'branches.*.total_basic_salaries' => [
                'required_with:branches',
                'numeric',
                'min:1',
            ],
            'payroll_from_date' => [
                'required',
            ],
            'payroll_to_date' => [
                'required',
            ],
            'payroll' => [
                'required',
            ],
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'payroll_from_date' => $this->currentPayroll->start,
            'payroll_to_date' => $this->currentPayroll->end,
            'payroll' => $this->currentPayroll,

        ]);
    }
}
