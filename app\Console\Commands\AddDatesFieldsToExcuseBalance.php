<?php

namespace App\Console\Commands;

use App\Models\ExcusesBalance;
use Illuminate\Console\Command;

class AddDatesFieldsToExcuseBalance extends Command
{
    protected $signature = 'app:add-dates-fields-to-excuse-balance';
    protected $description = 'Update dates and used_excuse_hours for excuses_balance records, grouped by payroll_id';

    public function handle()
    {
        $payrollGroups = ExcusesBalance::orderBy('payroll_id')
            ->orderBy('employee_id')
            ->orderBy('created_at')
            ->get()
            ->groupBy('payroll_id');

        foreach ($payrollGroups as $payrollId => $excuseBalances) {
            $this->info("Processing records for payroll_id: $payrollId");

            $previousRemaining = [];

            foreach ($excuseBalances as $excuse) {
                $payroll = $excuse->payroll;
                if (!isset($payroll)) {
                    $this->error("Payroll not found for excuse balance id: $excuse->id");
                    continue;
                }
                $excuse->from_date = $payroll->start;
                $excuse->to_date = $payroll->end;

                $employeeId = $excuse->employee_id;

                if (!isset($previousRemaining[$employeeId])) {
                    $usedHours = $excuse->base_hours - $excuse->remaining_hours;
                } else {
                    $usedHours = $previousRemaining[$employeeId] - $excuse->remaining_hours;
                }

                $usedHours = max(0, $usedHours);

                if ($usedHours > 0) {
                    $excuse->used_excuse_hours = $usedHours;
                } else {
                    continue;
                }

                $previousRemaining[$employeeId] = $excuse->remaining_hours;
                $excuse->save();
            }
        }

        $this->info('Dates and used_excuse_hours updated successfully, grouped by payroll_id.');
    }
}
