<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Employee;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\Repository;
use App\Util\EmployeeUtil;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FillPreviousYearBalances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill:previous-year-balances';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    private Repository $employeeLeaveBalanceRepository;

    public function __construct(
        private SystemSettingRepository $systemSettingRepository,
    ) {
        parent::__construct();
        $this->employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        DB::beginTransaction();
        try {
            $this->fillPreviousYearsBalances();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }

    public function fillPreviousYearsBalances()
    {
        $employees = Employee::where('company_id', 37)->get();

        foreach ($employees as $employee) {

            if ($employee->status == EmployeeUtil::STATUSES['TERMINATED']) {
                continue;
            }

            $companyLeaveTypePolicies = $employee->title->CompanyLeaveTypePolicies;

            foreach ($companyLeaveTypePolicies as $companyLeaveTypePolicy) {
                if ($companyLeaveTypePolicy->companyLeaveType->balance_period ==
                     config('globals.BALANCE_PERIODS.CALENDAR_YEAR')) {
                    $previousYearStart = Carbon::now()->subYear()->startOfYear()->toDateTimeString();
                    $PreviousYearEnd = Carbon::now()->subYear()->endOfYear()->toDateTimeString();

                    $balanceExists = $this->employeeLeaveBalanceRepository->leaveBalanceExistForEmployee($employee->id, $previousYearStart, $companyLeaveTypePolicy->id);
                    ////dd($balanceExists);
                    if (! $balanceExists) {
                        $request = [
                            'company_leave_type_id' => $companyLeaveTypePolicy->companyLeaveType->id,
                            'company_leave_type_policy_id' => $companyLeaveTypePolicy->id,
                            'employee_id' => $employee->id,
                            'balance' => $companyLeaveTypePolicy->base_balance,
                            'start' => $previousYearStart,
                            'end' => $PreviousYearEnd,
                        ];
                        $this->employeeLeaveBalanceRepository->create($request);
                    }
                }
            }

        }
    }
}
