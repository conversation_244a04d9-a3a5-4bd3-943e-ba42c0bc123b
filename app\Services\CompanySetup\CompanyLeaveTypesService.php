<?php

namespace App\Services\CompanySetup;

use App\Models\Title;
use App\Repositories\NewCompanyLeaveTypePolicyRepository;
use App\Repositories\NewCompanyLeaveTypeRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\Repository;
use App\Repositories\V1\EmployeeRepository;
use App\Repositories\V1\Leaves\CompanyDefaultLeaveTypesRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use App\Services\BaseService;
use App\Services\LeaveManagement\FillEmployeeBalancesService;
use App\Services\PayrollSetup\SalaryComponentsCategoriesService;
use App\Traits\GenerateUuid;
use App\Util\DefaultLeaveTypesUtil;

class CompanyLeaveTypesService extends BaseService
{
    use GenerateUuid;

    private $companyLeaveType;

    private $employeeLeaveBalanceRepository;

    public function __construct(NewCompanyLeaveTypeRepository               $newCompanyLeaveTypeRepository,
                                private NewCompanyLeaveTypePolicyRepository $newCompanyLeaveTypePolicyRepository,
                                private EmployeeLeaveRequestRepository      $newEmployeeLeaveRequestRepository,
                                private CompanyDefaultLeaveTypesRepository  $companyDefaultLeaveTypesRepository,
                                private SalaryComponentsCategoriesService   $salaryComponentsCategoriesService,
                                private FillEmployeeBalancesService         $fillEmployeeLeaveBalancesService,
                                private EmployeeRepository                  $employeeRepository,
                                protected SystemSettingRepository           $systemSettingRepository,
    )
    {
        parent::__construct($newCompanyLeaveTypeRepository);
        $this->employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
    }

//    public function addWithPolicy($data)
//    {
//
//        $titles = $data['titles'];
//        unset($data['titles']);
//        $data['uuid'] = $this->Uuid7();
//        $keysLeaveTypeOnly = ['gender', 'balance_period', 'name_en', 'name_ar', 'name', 'leave_deduction_percentage', 'uuid'];
//
//        $companyLeaveTypeData = array_filter($data, function ($key) use ($keysLeaveTypeOnly) {
//            return in_array($key, $keysLeaveTypeOnly);
//        }, ARRAY_FILTER_USE_KEY);
//
//        $companyLeaveType = $this->repository->add($companyLeaveTypeData);
//
//        $companyLeaveTypePolicyData = array_filter($data, function ($key) use ($keysLeaveTypeOnly) {
//            return !in_array($key, $keysLeaveTypeOnly);
//        }, ARRAY_FILTER_USE_KEY);
//
//        $companyLeaveTypePolicyData['company_leave_type_id'] = $companyLeaveType->id;
//
//        $companyLeaveTypePolicy = $this->newCompanyLeaveTypePolicyRepository->add($companyLeaveTypePolicyData);
//
//        $companyLeaveTypePolicy->titles()->attach($titles);
//
//        $employees = $this->employeeRepository->getNonTerminatedEmployees();
//        foreach ($employees as $employee) {
//            dispatch(new FillEmployeeBaseBalancesJob([$employee], [$employee->company_id], $companyLeaveType->id));
//        }
//    }

//    public function updateWithPolicy($id, $data)
//    {
//        if (isset($data['partial_leave_allowed'])) {
//            $partialLeaveToggle = $data['partial_leave_allowed'];
//            unset($data['partial_leave_allowed']);
//
//            $setting = $this->systemSettingRepository->findByKey('key', 'partial_leave_enabled')->first();
//            if (!isset($setting)) {
//                $this->systemSettingRepository->add([
//                    'key' => 'partial_leave_enabled',
//                    'value' => $partialLeaveToggle,
//                    'company_id' => auth()->user()->company_id,
//                    'as_of_date' => now()->format('Y-m-d'),
//                ]);
//            } else {
//                $setting->value = $partialLeaveToggle;
//                $setting->save();
//            }
//        }
//
//        $companyLeaveType = $this->repository->findOrFail($id);
//
//        $keysLeaveTypeOnly = ['gender', 'balance_period', 'name_en', 'name_ar', 'name', 'leave_deduction_percentage'];
//        $companyLeaveTypeData = array_filter($data, function ($key) use ($keysLeaveTypeOnly) {
//            return in_array($key, $keysLeaveTypeOnly);
//        }, ARRAY_FILTER_USE_KEY);
//
//        $companyLeaveType->update($companyLeaveTypeData);
//
//        if ($companyLeaveType->leave_deduction_percentage > 0) {
//            $this->salaryComponentsCategoriesService->setSickLeaveDeductionComponent();
//        }
//
//        $companyLeaveTypePolicyData = array_filter($data, function ($key) use ($keysLeaveTypeOnly) {
//            return !in_array($key, $keysLeaveTypeOnly);
//        }, ARRAY_FILTER_USE_KEY);
//
//        $oldBalance = $companyLeaveType->companyLeaveTypePolicy->base_balance;
//        $companyLeaveType->companyLeaveTypePolicy->update($companyLeaveTypePolicyData);
//
//        if (isset($data['titles'])) {
//            $oldTitles = $companyLeaveType->companyLeaveTypePolicy->titles;
//            $deletedTitles = array_diff($oldTitles->pluck('id')->toArray(), $data['titles']);
//
//            if (count($deletedTitles)) {
//                $this->employeeLeaveBalanceRepository->deleteEmployeeLeaveBalancesByTitles($deletedTitles);
//            }
//
//            $companyLeaveType->companyLeaveTypePolicy->titles()->sync($data['titles']);
//        }
//
//        if (isset($companyLeaveTypePolicyData['base_balance'])) {
//            $newBalance = $companyLeaveTypePolicyData['base_balance'];
//            $balances = $companyLeaveType->companyLeaveTypePolicy->employeeLeaveBalances;
//
//            foreach ($balances as $balanceEntity) {
//                $balanceEntity->balance = max(0, $balanceEntity->balance + ($newBalance - $oldBalance));
//                $balanceEntity->save();
//            }
//        }
//
//        $nonTerminatedEmployees = $this->employeeRepository->getNonTerminatedEmployees();
//        dispatch(new FillEmployeeBaseBalancesJob($nonTerminatedEmployees));
//    }

//    public function delete($id)
//    {
//
//        $companyLeaveType = $this->repository->findOrFail($id);
//
//        $presetLeaveIds = [config('globals.company')->annual_leave_id, #TODO replace all preset leave ids with the leave types relations in company model
//            config('globals.company')->sick_leave_id, config('globals.company')->rest_day_leave_id];
//
//        if (in_array($companyLeaveType->id, $presetLeaveIds)) {
//            throw new UnprocessableException('This leave type is preset and cannot be deleted');
//        }
//
//        $leaveRequestsWithType = $this->newEmployeeLeaveRequestRepository->findByKey('company_leave_type_id', $companyLeaveType->id);
//
//        if (isset($leaveRequestsWithType) && count($leaveRequestsWithType)) {
//            throw new UnprocessableException('This leave type is used in leave requests');
//        }
//
//        $companyLeaveType->companyLeaveTypePolicy->titles()->detach();
//
//        $companyLeaveType->companyLeaveTypePolicy()->delete();
//
//        $companyLeaveType->employeeLeaveBalances()->delete();
//
//        $companyLeaveType->delete();
//    }

    public function setDefaultPublicHolidaysLeaveTypes()
    {
        $companyHasPublicHolidays = $this->companyDefaultLeaveTypesRepository->findFirstByKey('key', DefaultLeaveTypesUtil::PUBLIC_HOLIDAY);

        if (!$companyHasPublicHolidays) {
            $publicHolidayLeaveType = [
                'name' => 'Public Holiday Compensation',
                'name_en' => 'Public Holiday Compensation',
                'name_ar' => 'بدل العطلة الرسمية',
                'is_primary' => 1,
                'uuid' => $this->Uuid7(),
                'balance_period' => config('globals.BALANCE_PERIODS.CALENDAR_YEAR'),
                'gender' => 'all',
            ];
            $leaveType = $this->repository->add($publicHolidayLeaveType);

            $publicHolidayPolicy = [
                'company_leave_type_id' => $leaveType->id,
                'base_balance' => 0,
                'unit' => 'days',
                'is_probation_allowed' => 1,
            ];
            $leaveTypePolicy = $this->newCompanyLeaveTypePolicyRepository->add($publicHolidayPolicy);

            $companyTitleIds = Title::all()->pluck('id')->toArray();
            $leaveTypePolicy->titles()->attach($companyTitleIds);

            $companyDefaultLeaveType = [
                'key' => DefaultLeaveTypesUtil::PUBLIC_HOLIDAY,
                'company_leave_type_id' => $leaveType->id,
            ];
            $this->companyDefaultLeaveTypesRepository->add($companyDefaultLeaveType);
        }
    }
}
