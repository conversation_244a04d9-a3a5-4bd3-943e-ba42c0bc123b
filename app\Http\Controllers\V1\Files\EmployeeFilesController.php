<?php

namespace App\Http\Controllers\V1\Files;

use App\Exceptions\UnprocessableException;
use App\Exports\V1\ImportFilesMetaDataMulitSheetSample;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\Files\AddEmployeeFileRequest;
use App\Http\Requests\V1\Files\BulkUploadEmployeeFilesRequest;
use App\Http\Requests\V1\Files\EditEmployeeFileRequest;
use App\Http\Requests\V1\Files\GetEmployeeFilesRequest;
use App\Http\Requests\V1\Files\GetWorkerFilesRequest;
use App\Http\Resources\V1\EmployeeFiles\EmployeeFilesCollection;
use App\Http\Resources\V1\EmployeeFiles\EmployeeFilesResource;
use App\Http\Resources\V1\EmployeeFiles\PendingFileCategoriesCollection;
use App\Http\Resources\V1\EmployeeFiles\WorkerViewEmployeeFilesCollection;
use App\Imports\V1\FilesMetaDataMultiSheetImport;
use App\Models\Employee;
use App\Models\EmployeeFileCategory;
use App\Services\V1\Files\EmployeeFileCategoryService;
use App\Services\V1\Files\EmployeeFilesService;
use App\Util\HttpStatusCodeUtil;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class EmployeeFilesController extends NewController
{
    public function __construct(EmployeeFilesService $employeeFilesService,
        private EmployeeFileCategoryService $employeeFileCategoryService)
    {
        $this->service = $employeeFilesService;

    }

    public function getEmployeeFiles(GetEmployeeFilesRequest $request)
    {
        $data = $request->validated();
        if (! isset($data['is_archived'])) {
            $data['is_archived'] = 0;
        }

        $data['page_size'] = $data['page_size'] ?? 10;

        $files = $this->service->getFilesByFilter($data);

        $paginatedData = (new EmployeeFilesCollection($files))->response()->getData();

        return getResponseStructure(['data' => $paginatedData->data, 'pagination' => $paginatedData->meta],
            HttpStatusCodeUtil::OK, 'Available employee files');

    }

    public function addEmployeeFile(AddEmployeeFileRequest $request)
    {

        $data = $request->validated();

        DB::beginTransaction();
        try {
            $this->service->add($data);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();
            throw $e;
        }

        return $this->response(['data' => []],
            HttpStatusCodeUtil::OK, 'Employee File Added successfully');
    }

    public function editEmployeeFile($id, EditEmployeeFileRequest $request)
    {
        $data = $request->validated();

        $data['existing_attachment_ids'] = $data['existing_attachment_ids'] ?? [];

        DB::beginTransaction();
        try {
            $this->service->update($id, $data);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();
            throw $e;
        }

        return $this->response(['data' => []],
            HttpStatusCodeUtil::OK, 'Employee File Has Successfully Updated');
    }

    public function archive($id)
    {
        DB::beginTransaction();
        try {
            $this->service->archive($id);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();
            throw $e;
        }

        return $this->response(['data' => []],
            HttpStatusCodeUtil::OK, 'Employee File Has Successfully Archived');
    }

    public function unarchive($id)
    {
        DB::beginTransaction();
        try {
            $this->service->unarchive($id);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();
            throw $e;
        }

        return $this->response(['data' => []],
            HttpStatusCodeUtil::OK, 'Employee File Has Successfully Unarchived');
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            $this->service->delete($id);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();
            throw $e;
        }

        return $this->response(['data' => []],
            HttpStatusCodeUtil::OK, 'Employee File Has Successfully Deleted');
    }

    public function getFilesCounter()
    {
        $counters = $this->service->getFilesCounter();

        return $this->response(['data' => $counters],
            HttpStatusCodeUtil::OK, 'Employee Files Counter');
    }

    public function getFileWithCategoryAndAttachments($id)
    {
        $file = $this->service->getFileWithCategoryAndAttachments($id);

        return $this->response(['data' => new EmployeeFilesResource($file)],
            HttpStatusCodeUtil::OK, 'Employee File With Category And Attachments');
    }

    public function getWorkerFiles(GetWorkerFilesRequest $request)
    {
        $data = $request->validated();
        $data['employee_id'] = auth()->user()->employee_id;
        $data['is_archived'] = 0;

        $data['page_size'] = $data['page_size'] ?? 10;

        $files = $this->service->getFilesByFilter($data);
        // $employees = $this->employeeRepository->getEmployeesByFilter($data);

        $paginatedData = (new WorkerViewEmployeeFilesCollection($files))->response()->getData();

        return getResponseStructure(['data' => $paginatedData->data, 'pagination' => $paginatedData->meta],
            HttpStatusCodeUtil::OK, 'Available employee files');
    }

    public function getPendingFilesCategories(GetEmployeeFilesRequest $request)
    {
        $data = $request->validated();

        $pendingFileCategories = (new PendingFileCategoriesCollection($this->employeeFileCategoryService->getEmployeesPendingCategories($data)))
            ->response()->getData();

        return getResponseStructure(['data' => $pendingFileCategories->data, 'pagination' => $pendingFileCategories->meta],
            HttpStatusCodeUtil::OK, 'Available employee files');
    }

    public function uploadFilesMetaData(FormRequest $request)
    {
        $filesMetaData = $request['attachment'];
        if (! isset($filesMetaData)) {
            throw new UnprocessableException('attachment is required');
        }

    }

    public function downloadFilesMetaDataSample()
    {
        $employees = Employee::orderBy('name_ar', 'asc')->get();
        $employeesNames = array_column($employees->toArray(), 'name_ar');

        $employeeFileCategories = EmployeeFileCategory::orderBy('name_ar', 'asc')->get();
        $employeeFileCategoriesNames = array_column($employeeFileCategories->toArray(), 'name_ar');

        $maxCount = max(count($employeesNames), count($employeeFileCategoriesNames));

        $data = [];
        for ($i = 0; $i < $maxCount; $i += 1) { // data in excel should be saved by row not by column
            $data[] = [
                'Company Employees' => isset($employeesNames[$i]) ? $employeesNames[$i] : null,
                'File Categories' => isset($employeeFileCategoriesNames[$i]) ? $employeeFileCategoriesNames[$i] : null,
            ];
        }

        return Excel::download(new ImportFilesMetaDataMulitSheetSample($data, count($employeesNames), count($employeeFileCategories)), 'MetaData Import.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function bulkUploadFilesMetaData(FormRequest $request)
    {
        $filesMetaData = $request->file('files-metadata');
        if (! isset($filesMetaData)) {
            throw new UnprocessableException('Excel File is required');
        }

        $import = new FilesMetaDataMultiSheetImport($this->service);

        try {
            $import->onlySheets('Worksheet');
            Excel::import($import, $filesMetaData);
        } catch (Exception $e) {
            Log::info($e);
            throw new UnprocessableException($e->getMessage());
        }

        return $this->response([], 200, 'Files Metadata uploaded successfully');
    }

    public function bulkUploadEmployeeFiles(BulkUploadEmployeeFilesRequest $request)
    {

        $data = $request->validated();

        DB::beginTransaction();
        try {
            $this->service->bulkUploadEmployeeFiles($data);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();
            throw $e;
        }

        return $this->response(['data' => []],
            HttpStatusCodeUtil::OK, 'Bulk Employee Files Added successfully');

    }
}
