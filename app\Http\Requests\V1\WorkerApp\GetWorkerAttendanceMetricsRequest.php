<?php

namespace App\Http\Requests\V1\WorkerApp;

use App\Repositories\PayrollRepositories\PayrollsRepository;
use Illuminate\Foundation\Http\FormRequest;

class GetWorkerAttendanceMetricsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'from_date' => [
                'required',
                'date',
            ],
            'to_date' => [
                'required',
                'date',
            ],
        ];
    }

    protected function prepareForValidation()
    {
        if (! isset($this->from_date)) {

            $payrollRepository = new PayrollsRepository;
            $payroll = $payrollRepository->getLastDraftedPayroll();
            $this->merge([
                'from_date' => $this->input('from_date', $payroll->start),
                'to_date' => $this->input('to_date', $payroll->end),
            ]);
        }

    }
}
