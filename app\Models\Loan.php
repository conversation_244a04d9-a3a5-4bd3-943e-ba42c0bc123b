<?php

namespace App\Models;

use App\Models\Casts\TimezoneDateTime;
use App\Models\StateMachines\LoanRequest\LoansRequestState;
use App\Models\StateMachines\RequestApproved;
use App\Models\StateMachines\RequestCancelled;
use App\Models\StateMachines\RequestPending;
use App\Models\StateMachines\RequestRejected;
use App\Traits\CompanyRule;
use App\Util\LoansUtil;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Loan extends BaseModel
{
    use companyRule, HasFactory, LogsActivity, softDeletes;

    protected $casts = [
        'created_at' => TimezoneDateTime::class,
        'updated_at' => TimezoneDateTime::class,
        'deleted_at' => TimezoneDateTime::class.':nullable',
        'status' => LoansRequestState::class,
    ];

    protected $guarded = ['id', 'created_at', 'updated_at'];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($loan) {
            $requesStatus = $loan->getEmployeeRequestStatus($loan->status);

            EmployeeRequest::create([
                'date' => $loan->created_at,
                'employee_id' => $loan->employee_id,
                'status' => $requesStatus, // or any default status you want to set
                'comment' => $loan->reason, // if `comment` exists in Employeepenalty,
                'request_name' => 'loan',
                'requestable_id' => $loan->id,
                'requestable_type' => 'loan',
                'requested_by' => auth()->user()->employee_id ?? null,
                'company_id' => $loan->company_id,
            ]);

        });

    }

    private function getEmployeeRequestStatus($status)
    {
        switch ($status) {
            case LoansUtil::READY_TO_DISBURSE:
            case LoansUtil::DISBURSED:
            case LoansUtil::PAID_IN_FULL:
                $requesStatus = RequestApproved::class;
                break;
            case LoansUtil::REJECTED:
                $requesStatus = RequestRejected::class;
                break;
            case LoansUtil::CANCELLED:
                $requesStatus = RequestCancelled::class;
                break;
            default:
                $requesStatus = RequestPending::class;
                break;
        }

        return $requesStatus;
    }

    // Define relationships
    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function policy()
    {
        return $this->belongsTo(LoanPolicy::class);
    }

    public function installments()
    {
        return $this->hasMany(Installment::class);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable();
    }

    public function employeeRequests()
    {
        return $this->morphMany(EmployeeRequest::class, 'requestable');
    }

    public function employeeRequest()
    {
        return $this->morphOne(EmployeeRequest::class, 'requestable')->latest('id');
    }

    public function employeeApproves()
    {
        return $this->morphToMany(Role::class, 'requestable', 'workflow_approval_cycle',
            'requestable_id', 'role_id')
            ->withPivot(['id', 'company_id', 'status', 'date', 'order', 'operator', 'requestable_type', 'request_workflow_id', 'branch_id', 'department_id'])
            ->withTimestamps();
    }

    public function workflowApprovalCycles()
    {
        return $this->morphMany(WorkflowApprovalCycle::class, 'requestable');
    }

    public function attachments()
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    public function workflow()
    {
        return $this->belongsTo(Workflow::class);
    }
}
