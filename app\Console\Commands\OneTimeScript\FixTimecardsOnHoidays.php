<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Timecard;
use App\Traits\V2\WorkTypesTrait;
use Illuminate\Console\Command;

class FixTimecardsOnHoidays extends Command
{
    use WorkTypesTrait;

    protected $signature = 'fix:timecards-on-holidays {date}';

    protected $description = 'Fix timecards for employees on public holidays';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $date = $this->argument('date');
        // get timecards that have 'from' in the given date and has tag 'absent' and has employee who has title who has work type policy that's not dynamic_on_site

        $timecards = Timecard::whereDate('from', $date)
            ->whereHas('entityTags', function ($query) {
                $query->whereIn('tag', ['absent', 'absent_without_permission']);
            })
            ->whereHas('employee', function ($query) {
                $query->whereHas('title', function ($query) {
                    $query->whereHas('workTypePolicy', function ($query) {
                        $query->whereIn('work_days_type', $this->getFixedTypes());
                    });
                });
            })
            ->whereDoesntHave('attendance')
            ->delete();
        echo "Timecards deleted: $timecards \n";
    }
}
