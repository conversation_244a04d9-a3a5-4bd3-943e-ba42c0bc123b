<?php

namespace App\Console\Commands;

use App\Models\CostCenter;
use App\Models\CostCenterBasicComponent;
use App\Models\CostCenterDepartment;
use App\Models\CostCenterLocation;
use App\Models\CostCenterResult;
use App\Models\Payroll;
use App\Services\V1\CostCenter\CostCenterFacade;
use App\Services\V1\CostCenter\CustomCostCenterService;
use App\Services\V1\CostCenter\DepartmentsViewService;
use App\Services\V1\CostCenter\LocationsViewService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateCostCenter extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-cost-center {company_id} {month} {year}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $companyId = $this->argument('company_id');
            $month = $this->argument('month');
            $year = $this->argument('year');

            $payroll = Payroll::where('company_id', $companyId)->where('month', $month)->where('year', $year)->first();

            $payrollUser = $payroll->company->employees()->first();
            config(['globals.user' => $payrollUser]);

            Log::info('start'.Carbon::now()->format('H:i:s'));
            $customCostCenterService = app(abstract: CustomCostCenterService::class);
            $costCenterFacade = app(CostCenterFacade::class);
            $departmentsViewService = app(DepartmentsViewService::class);
            $locationViewService = app(LocationsViewService::class);

            CostCenterBasicComponent::where('month', $payroll->month)->where('year', $payroll->year)->delete();
            CostCenterDepartment::where('month', $payroll->month)->where('year', $payroll->year)->delete();
            CostCenterLocation::where('month', $payroll->month)->where('year', $payroll->year)->delete();
            CostCenterResult::where('month', $payroll->month)->where('year', $payroll->year)->delete();

            $costCenterFacade->calculateCost($payroll);
            Log::info('after-basic-component '.Carbon::now()->format('H:i:s'));

            $departmentsViewService->CalculateDepartmentsCostsForMonth($payroll->month, $payroll->year);
            Log::info('after-dept '.Carbon::now()->format('H:i:s'));

            $locationViewService->CalculateLocationsCostsForMonth($payroll->month, $payroll->year);
            Log::info('after-location '.Carbon::now()->format('H:i:s'));

            $costCenters = CostCenter::where('company_id', $companyId)->get();

            foreach ($costCenters as $costCenter) {
                $customCostCenterService->calculateCustomCostCenters($payroll->month, $payroll->year, $costCenter);
            }
            Log::info('after-custom '.Carbon::now()->format('H:i:s'));

        } catch (\Exception $e) {
            Log::error('when dispatching cost center job : '.$e);
        }
    }
}
