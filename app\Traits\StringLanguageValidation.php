<?php

namespace App\Traits;

trait StringLanguageValidation
{
    /**
     * Check if a string is all in Arabic letters.
     */
    public function isArabic(string $string): bool
    {
        return preg_match('/^[\p{Arabic} ]+$/u', $string);
    }

    /**
     * Check if a string is all in English letters.
     */
    public function isEnglish(string $string): bool
    {
        return preg_match('/^[A-Za-z ]+$/u', $string);
    }

    public function trimStringSpaces(?string $value): ?string
    {
        if ($value === null) {
            return null;
        }

        return str_replace(' ', '', $value);
    }
}
