<?php

namespace App\Services\V1\Attendance;

use App\Exceptions\UnprocessableException;
use App\Repositories\NewAttendanceSettingRepository;
use App\Repositories\NewCompanyRepository;
use App\Repositories\NewTitleRepository;
use App\Repositories\PayrollRepositories\EmployeeSalaryRepository;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\V1\Attendance\AttendanceRepository;
use App\Repositories\V1\EmployeeRequestRepository;
use App\Repositories\V1\ExtraWorkdayRequestRepository;
use App\Services\BaseService;
use App\Traits\GetLastDraftedPayRollOrCreate;
use App\Traits\V1\EmployeeRequestsTrait;
use App\Traits\V1\NotificationRedirection;
use App\Traits\V1\PayrollHub\PayrollHubTrait;
use App\Traits\WorkflowTrait;
use App\Util\PayrollUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Workflow\WorkflowStub;


class ExtraWorkdayRequestsService extends BaseService
{
    use EmployeeRequestsTrait, GetLastDraftedPayRollOrCreate, PayrollHubTrait, WorkflowTrait, NotificationRedirection;

    public function __construct(
        ExtraWorkdayRequestRepository $repository,
        private NewTitleRepository $titleRepository,
        private NewAttendanceSettingRepository $newAttendanceSettingRepository,
        private PayrollsRepository $payrollsRepository,
        private AttendanceRepository $attendanceRepository,
        private EmployeeRequestRepository $employeeRequestRepository,
        private NewCompanyRepository $companyRepository,
        private EmployeeSalaryRepository $employeeSalaryRepository,
    ) {
        parent::__construct($repository);
    }

    public function approveExtraWorkdayRequest($id)
    {
        $extraWorkdayRequest = $this->repository->findOrFail($id);
        $this->isValidForPayroll($extraWorkdayRequest);
        $this->actionOnExtraWorkdayRequest($extraWorkdayRequest, 'approve');
    }

    public function rejectExtraWorkdayRequest($id)
    {
        $extraWorkdayRequest = $this->repository->findOrFail($id);
        $this->isValidForPayroll($extraWorkdayRequest);
        $this->actionOnExtraWorkdayRequest($extraWorkdayRequest, 'reject');
    }

    public function cancelExtraWorkdayRequest($id)
    {
        $extraWorkdayRequest = $this->repository->findOrFail($id);
        $this->isValidForPayroll($extraWorkdayRequest);
        $this->actionOnExtraWorkdayRequest($extraWorkdayRequest, 'cancel');
    }

    public function actionOnExtraWorkdayRequest($extraWorkdayRequest, $actionType): void
    {
        try {
            $roleIds = config('globals.user')->roles->pluck('id')->toArray();
            $userCanTakeAnAction = $this->canTakeAnAction($roleIds, $extraWorkdayRequest->id, config('globals.REQUEST_WORKFLOW_TYPES.EXTRA_WORKDAY'), $actionType);
            if (! $userCanTakeAnAction) {
                throw new UnprocessableException(trans('messages.can_not_take_this_action'));
            }
            if ($actionType != 'cancel') {
                if ($extraWorkdayRequest->status != 'pending') {
                    throw new UnprocessableException(trans('messages.workflow_is_completed'));
                }
            }

            $this->doAnAction($actionType);
            if($this->checkRequestIsCompleted($extraWorkdayRequest->employeeRequest)) {
                $finalStatus = $this->getFinalStatus($extraWorkdayRequest->employeeRequest, $actionType);
                $this->updateEntity($extraWorkdayRequest, $finalStatus);
                $this->updateRequest($extraWorkdayRequest->employeeRequest, $finalStatus);
                $extraWorkdayRequest->attendance->extra_work_day_status = $finalStatus;
                $extraWorkdayRequest->attendance->save();
                $this->redirectNotificationsAfterRequestFinalized($extraWorkdayRequest, $finalStatus);
            }
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException(trans('messages.can_not_take_this_action'));
        }
    }

    private function getRequestData($request)
    {
        return [
            'requestable_id' => $request->id,
            'requestable_type' => config('globals.REQUEST_WORKFLOW_TYPES.EXTRA_WORKDAY'),
            'role_ids' => auth()->user()->roles->pluck('id')->toArray(),
        ];
    }

    private function isValidForPayroll($extraWorkdayRequest)
    {
        $payroll = $this->payrollsRepository->payrollCoversDate($extraWorkdayRequest->extra_work_day_date);
        if (isset($payroll) && $payroll->status == 'finalized') {
            throw new UnprocessableException(trans('messages.can_not_take_action_on_request_with_finalized_payroll'));
        }
    }

    public function getExtraWorkDaysStatistics($filters)
    {
        $previousPayroll = $this->payrollsRepository->getLastFinalizedPayroll();

        $currentApprovedRequests = $this->repository->getAllApprovedRequestsInDate($filters['from_date'], $filters['to_date']);
        $previousApprovedRequests = [];
        $previousExtraWorkdaysAmount = 0;
        $currentExtraWorkdaysAmount = $this->calculateExtraWorkdaysAmount($currentApprovedRequests);
        if ($previousPayroll) {
            $previousApprovedRequests = $this->repository->getAllApprovedRequestsInDate($previousPayroll->start, $previousPayroll->end);
            $previousExtraWorkdaysAmount = $this->calculateExtraWorkdaysAmount($previousApprovedRequests);
        }
        // Calculate the extra workdays amounts using a helper function to avoid code duplication.
        // Calculate the percentage difference for extra workdays amount.
        if ($previousExtraWorkdaysAmount != 0) {
            $amountChangePercentage = $this->calculatePercentageChange($currentExtraWorkdaysAmount, $previousExtraWorkdaysAmount);
        } else {
            $amountChangePercentage = ($currentExtraWorkdaysAmount > 0) ? 100 : 0;
        }

        // Calculate counts for approved requests.
        $currentRequestsCount = $this->countExtraWorkDays($currentApprovedRequests);
        $previousRequestsCount = $this->countExtraWorkDays($previousApprovedRequests);

        // Calculate the percentage difference for request counts.
        if ($previousRequestsCount != 0) {
            $requestsChangePercentage = $this->calculatePercentageChange($currentRequestsCount, $previousRequestsCount);
        } else {
            $requestsChangePercentage = ($currentRequestsCount > 0) ? 100 : 0;
        }

        return [
            'current_extra_workdays_amount' => $currentExtraWorkdaysAmount,
            'extra_workdays_amount_change_percentage' => $amountChangePercentage,
            'current_requests_count' => $currentRequestsCount,
            'requests_count_change_percentage' => $requestsChangePercentage,
        ];
    }

    private function calculateExtraWorkdaysAmount($approvedRequests)
    {
        $totalAmount = 0.0;
        foreach ($approvedRequests as $request) {
            $grossSalary = $request->employee->employeeSalary?->gross_salary ?? 0;
            $dailySalary = $grossSalary / PayrollUtil::PAYROLL_MONTH_DAYS;
            $totalAmount += $dailySalary * $request->total_compensation_rate;
        }

        return round($totalAmount, 2);
    }

    public function getAllExtraWorkDaysForCompany($filters)
    {
        // Get the grouped extra workday requests.
        $groupedRequests = $this->repository->getApprovedExtraWorkDaysGroupedByEmployee($filters);
        $sumOfAmount = 0;

        // Execute the query and get the results
        $records = $groupedRequests->get();

        Log::info('extra workday grouped requests: '. $groupedRequests->count());

        // Process each record to calculate amounts
        foreach ($records as &$record) {
            $grossSalary = 0;
            if ($record->employee->employeeSalary) {
                $grossSalary = $record->employee->employeeSalary->gross_salary ?? 0;
            }

            $dailySalary = $grossSalary / PayrollUtil::PAYROLL_MONTH_DAYS;
            $rates = explode(',', $record->compensation_rates);
            $amount = 0;
            foreach ($rates as $rate) {
                $amount += $dailySalary * floatval($rate);
            }
            $record->total_extra_workdays_amount = round($amount, 2);
            $sumOfAmount += $record->total_extra_workdays_amount;
        }

        // Sort the collection by multiple criteria
        if (isset($filters['sort_by']) && ! empty($filters['sort_by'])) {
            // Build sort array for collection multisort
            $sortParams = [];
            foreach ($filters['sort_by'] as $sortBy) {
                $sortParams[] = [
                    'field' => $sortBy['field'],
                    'order' => $sortBy['order'],
                ];
            }

            // Apply multiple sorting
            $records = $this->multipleSort($records, $sortParams);
        } else {
            $records = $records->sortBy('total_extra_workdays_amount', SORT_REGULAR, true);
        }

        // Create paginated result
        $perPage = $filters['page_size'];
        $currentPage = request()->get('page', 1);
        $recordsArray = $records->values()->all();

        $paginatedResults = new \Illuminate\Pagination\LengthAwarePaginator(
            array_slice($recordsArray, ($currentPage - 1) * $perPage, $perPage),
            count($recordsArray),
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        // Add total to paginator
        $paginatedResults->total_extra_workdays_amount = $sumOfAmount;

        return $paginatedResults;
    }

    public function getAllExtraWorkDaysForEmployee($filters)
    {
        // Get the extra workday requests for a specific employee.
        $extraWorkdayRequests = $this->repository->getEmployeeApprovedExtraWorkDays($filters);
        $sumOfAmount = 0;
        foreach ($extraWorkdayRequests as $request) {
            $grossSalary = 0;
            if ($request->employee->employeeSalary) {
                $grossSalary = $request->employee->employeeSalary->gross_salary ?? 0;
            }
            $dailySalary = $grossSalary / PayrollUtil::PAYROLL_MONTH_DAYS;
            $amount = $dailySalary * $request->compensation_rate;
            $request->total_extra_workdays_amount = round($amount, 2);
            $sumOfAmount += $amount;
        }

        return [
            'all_total_extra_workdays' => $extraWorkdayRequests,
            'extra_workday_amount' => round($sumOfAmount, 2),
        ];
    }

    public function bulkApproveExtraWorkdayRequests($requests)
    {
        foreach ($requests['requests'] as $request) {
            $this->approveExtraWorkdayRequest($request['id']);
        }
    }

    public function bulkRejectExtraWorkdayRequests($requests)
    {
        foreach ($requests['requests'] as $request) {
            $this->rejectExtraWorkdayRequest($request['id']);
        }
    }

    public function bulkCancelExtraWorkdayRequests($requests)
    {
        foreach ($requests['requests'] as $request) {
            $this->cancelExtraWorkdayRequest($request['id']);
        }
    }

    private function countExtraWorkDays($approvedRequests)
    {
        $totalCount = 0;
        foreach ($approvedRequests as $request) {
            $totalCount += $request->days_count;
        }

        return $totalCount;
    }

    public function getMonthlyAnalytics($filters)
    {
        $currentFilters = $filters;
        $currentFilters['is_analytics'] = true;
        $currentExtraWorkdays = $this->getAllExtraWorkDaysForCompany($currentFilters);

        // Get previous month data
        $previousFilters = $filters;
        $previousFilters['from_date'] = Carbon::parse($filters['from_date'])->subMonth();
        $previousFilters['to_date'] = Carbon::parse($filters['to_date'])->subMonth();
        $previousFilters['is_analytics'] = true;
        $previousExtraWorkdays = $this->getAllExtraWorkDaysForCompany($previousFilters);

        // Calculate the percentage difference for extra workdays amount
        $amountChangePercentage = $this->calculatePercentageChange(
            $currentExtraWorkdays->total_extra_workdays_amount,
            $previousExtraWorkdays->total_extra_workdays_amount
        );

        // Calculate counts for approved requests
        $currentRequestsCount = $this->countExtraWorkDays($currentExtraWorkdays->data);
        $previousRequestsCount = $this->countExtraWorkDays($previousExtraWorkdays->data);

        // Calculate the percentage difference for request counts
        $requestsChangePercentage = $this->calculatePercentageChange(
            $currentRequestsCount,
            $previousRequestsCount
        );

        return [
            (object) [
                'title' => 'current_requests_count',
                'value' => $currentRequestsCount,
                'value_diff' => $requestsChangePercentage,
            ],
            (object) [
                'title' => 'current_extra_workdays_amount',
                'value' => $currentExtraWorkdays->total_extra_workdays_amount,
                'value_diff' => $amountChangePercentage,
                'unit' => $this->companyRepository->getCountryOfCompany($filters['company_id'])?->country->currency,
            ],
        ];
    }
}
