<?php

namespace App\Http\Middleware;

use App\Traits\QueriesHelper;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Symfony\Component\HttpFoundation\Response;

class RequestResponseLogger
{
    use QueriesHelper;

    private Logger $logger;

    public function __construct()
    {
        $this->logger = new Logger('RequestResponseLogger');
        $this->logger->pushHandler(new StreamHandler(storage_path('logs/datadog.log')));
    }

    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Only log POST and PUT requests
        if (! in_array($request->method(), ['POST', 'PUT'])) {
            return $next($request);
        }

        // Generate unique request ID
        $requestId = (string) Str::uuid();
        config(['globals.request_id' => $requestId]);

        // Get authenticated user info
        $user = auth()->user();
        $employee = $user?->employee;

        $scopeKey = $this->getUserHighestScopeKey($user);

        $user->load('customRoles');
        $roleIds = $user->customRoles->pluck('id')->toArray() ?? [];

        $userInfo = $user ? [
            'user_id' => $user->id,
            'employee_id' => $employee?->id,
            'phone' => $employee?->phone,
            'company_id' => $employee?->company_id,
            'role_ids' => $roleIds,
            'scope_key' => $scopeKey,
        ] : null;

        // Log request payload
        $this->logger->info('Request Payload', [
            'request_id' => $requestId,
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'payload' => $request->all(),
            'headers' => $request->headers->all(),
            'user' => $userInfo,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Get the response
        $response = $next($request);

        // Get response content and decode if it's JSON
        $content = $response->getContent();
        $decodedContent = json_decode($content, true);
        $responseContent = json_last_error() === JSON_ERROR_NONE ? $decodedContent : $content;

        // Log response
        $this->logger->info('Response', [
            'request_id' => $requestId,
            'status' => $response->getStatusCode(),
            'content' => $responseContent,
            'user' => $userInfo,
        ]);

        return $response;
    }
}
