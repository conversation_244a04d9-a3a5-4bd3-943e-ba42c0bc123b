<?php

namespace App\Console\Commands;

use App\Jobs\OvertimeCalculationService;
use App\Models\Attendance;
use App\Models\AttendanceOvertime;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Log;

class FixAttendanceOvertimesCalculation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:attendance-overtime-calculation {company_id} {from_date : The start date (YYYY-MM-DD)} {to_date : The end date (YYYY-MM-DD)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate overtime records from a specific date';

    public function handle()
    {
        $companyId = $this->argument('company_id');
        $fromDate = $this->argument('from_date');
        $toDate = $this->argument('to_date');

        if (! Date::hasFormat($fromDate, 'Y-m-d') || ! Date::hasFormat($toDate, 'Y-m-d')) {
            $this->error('Invalid date format. Please use YYYY-MM-DD.');

            return 1;
        }

        $query = Attendance::query()
            ->where('company_id', $companyId)
           //  ->where('status', 'pending')
            ->where('date', '>=', $fromDate)
            ->where('date', '<=', $toDate)
            ->select('id', 'date', 'employee_id');

        $count = $query->count();

        if ($count === 0) {
            $this->info('No attendance records found.');

            return 0;
        }

        if (! $this->confirm("This will dispatch {$count} jobs. Continue?", true)) {
            $this->info('Operation canceled.');

            return 0;
        }

        $progressBar = $this->output->createProgressBar($count);
        $progressBar->setFormat(" %current%/%max% [%bar%] %percent:3s%% %estimated:-6s% remaining\n");

        $this->info("Dispatching {$count} overtime calculation jobs...");
        $progressBar->start();

        $query->chunkById(200, function ($attendances) use ($progressBar) {
            foreach ($attendances as $attendance) {
                try {
                    dispatch(new OvertimeCalculationService($attendance->employee_id, $attendance->date))
                        ->onConnection(config('globals.OVERTIME_JOB.CONNECTION'))
                        ->onQueue(config('globals.OVERTIME_JOB.QUEUE'))->afterCommit();

                    Log::info('Processing attendance record', ['id' => $attendance->id]);
                } catch (\Exception $e) {
                    Log::error('Failed to dispatch job for attendance record', [
                        'id' => $attendance->id,
                        'error' => $e->getMessage(),
                    ]);
                }

                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine();
        $this->info('All jobs have been dispatched successfully.');

        return 0;
    }
}
