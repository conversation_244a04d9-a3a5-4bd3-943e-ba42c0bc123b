<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Employee;
use App\Models\EmployeeLeaveRequest;
use App\Models\Timecard;
use App\Traits\V2\WorkTypesTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RemoveAbsentOnPublicHoliday extends Command
{
    use WorkTypesTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-absent-on-public-holiday';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove absent on public holiday';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Removing absent on public holiday');
        try {
            DB::beginTransaction();
            // $timecards = Timecard::whereDate('from', '2025-01-01')
            //     ->whereHas('employee', function ($query) {
            //         $query->where('company_id', 167);
            //     })
            //     ->whereHas('entityTags', function ($query) {
            //         $query->where('tag', 'absent');
            //     })
            //     ->get();

            // $this->info('Found '.count($timecards).' TCs');

            // foreach ($timecards as $timecard) {
            //     EmployeeLeaveRequest::create([
            //         'employee_id' => $timecard->employee_id,
            //         'company_leave_type_id' => 616,
            //         'company_leave_type_policy_id' => 581,
            //         'from' => '2025-01-01 00:00:00',
            //         'to' => '2025-01-01 23:59:59',
            //         'net_quantity' => 8,
            //         'status' => 'approved',
            //         'type' => 'regular',
            //         'branch_id' => $timecard->branch_id,
            //     ]);
            //     $timecard->delete();

            // }

            // $employees = Employee::where('company_id', 167)
            //     ->whereDoesntHave('employeeLeaveRequests', function ($query) {
            //         $query->where('company_leave_type_id', 616);
            //     })
            //     ->whereDoesntHave('timecards', function ($query) {
            //         $query->whereDate('from', '2025-01-01');
            //     })
            //     ->get();

            // $this->info('Found '.count($employees).' employees');

            // foreach ($employees as $employee) {
            //     $employee->employeeLeaveRequests()->create([
            //         'company_leave_type_id' => 616,
            //         'company_leave_type_policy_id' => 581,
            //         'from' => '2025-01-01 00:00:00',
            //         'to' => '2025-01-01 23:59:59',
            //         'net_quantity' => 8,
            //         'status' => 'approved',
            //         'type' => 'regular',
            //         'branch_id' => $employee->branch_id,
            //     ]);
            // }

            $timecards = Timecard::whereHas('employee', function ($query) {
                $query->where('company_id', 106)
                    ->whereHas('title.workTypePolicy', function ($query) {
                        $query->whereIn('work_days_type', $this->getFixedTypes());
                    });
            })
                ->where(function ($query) {
                    $query->whereDate('from', '2025-01-07')
                        ->orWhereDate('from', '2025-01-25');
                })
                ->doesntHave('attendance')
                ->get();

            $this->info('Found '.count($timecards).' TCs');

            foreach ($timecards as $timecard) {
                $timecard->entityTags()->delete();
                $timecard->delete();
            }

            DB::commit();
            $this->info('Done');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error: '.$e->getMessage());
        }
    }
}
