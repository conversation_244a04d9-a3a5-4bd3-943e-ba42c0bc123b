<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\CompanyLeaveTypePolicy;
use App\Models\Employee;
use App\Models\EmployeeLeaveRequest;
use App\Models\EntityTag;
use App\Models\Timecard;
use App\Repositories\AttendanceSettingRepository;
use App\Traits\CICOHelper;
use App\Traits\V1\AttendancePoliciesTrait;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixStaticEmployeesTcsIssue extends Command
{
    use AttendancePoliciesTrait, CICOHelper;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-static-employees-tcs-issue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        try {
            $this->info('Starting to fix static employees TCS issue');
            DB::beginTransaction();
            try {
                // handle late deduction policy for unscheduled timecards for static employees
                // $unscheduledTimecards = Timecard::withWhereHas('employee.title.workTypePolicy', function ($q) {
                //     $q->where('work_days_type', '!=', 'dynamic_onsite');
                // })->where('name', '=', 'unscheduled timecard')
                //     ->whereDate('from', '>=', '2025-03-28')
                //     ->with('attendance.clockIn.pairedClock')
                //     ->with('employee.company.country')
                //     ->get();

                // $this->info('Found '.count($unscheduledTimecards).' unscheduled timecards');
                // foreach ($unscheduledTimecards as $unscheduledTimecard) {
                //     config(['app.timezone' => $unscheduledTimecard->employee->company->country->timezone]);

                //     // update timecard with new times from work type
                //     $unscheduledDate = Carbon::parse($unscheduledTimecard->from)->format('Y-m-d');
                //     $unscheduledTimecard->from = Carbon::parse($unscheduledDate.' '.$unscheduledTimecard->employee->title->workTypePolicy->start_time, config('app.timezone'))
                //         ->setTimezone('UTC')
                //         ->toDateTimeString();
                //     $unscheduledTimecard->to = Carbon::parse($unscheduledDate.' '.$unscheduledTimecard->employee->title->workTypePolicy->end_time, config('app.timezone'))
                //         ->setTimezone('UTC')
                //         ->toDateTimeString();
                //     $unscheduledTimecard->name = 'Timecard';
                //     $unscheduledTimecard->save();

                //     $attendance = $unscheduledTimecard->attendance;
                //     if (! isset($attendance)) {
                //         $unscheduledTimecard->entityTags()->create(['tag' => 'absent', 'company' => $unscheduledTimecard->employee->company_id]);

                //         continue;
                //     }
                //     $clockIn = $attendance->clockIn->date;
                //     $startShiftDate = Carbon::parse($unscheduledDate.' '.$unscheduledTimecard->employee->title->workTypePolicy->start_time);
                //     $endShiftDate = Carbon::parse($unscheduledDate.' '.$unscheduledTimecard->employee->title->workTypePolicy->end_time);
                //     $clockInDifferenceTime = Carbon::parse($clockIn)->diffInMinutes($startShiftDate);
                //     if ($clockIn >= $startShiftDate && $clockIn <= $endShiftDate) {

                //         $lateDeductionPolicy = $this->getLateDeductionPolicy();

                //         $isLate = isset($lateDeductionPolicy) && $lateDeductionPolicy->value < $clockInDifferenceTime;

                //         if ($isLate) {
                //             $this->info('Creating late tag for attendance '.$attendance->id);
                //             EntityTag::firstOrCreate(
                //                 [
                //                     'entity_id' => $attendance->id,
                //                     'entity_type' => 'attendance',
                //                     'tag' => 'late',
                //                     'company' => $unscheduledTimecard->employee->company_id,
                //                 ]
                //             );

                //             if ($lateDeductionPolicy->is_used) {
                //                 $lateDeduction = $this->createAttendanceDeductionIfExist($unscheduledTimecard->employee, $clockInDifferenceTime, $attendance);
                //                 if (isset($lateDeduction)) {
                //                     $this->info('Creating deduction for attendance '.$attendance->id);
                //                 }
                //             }
                //         }
                //     }

                //     EntityTag::where('entity_id', $attendance->id)->where('entity_type', 'attendance')->where('tag', 'off_shift')->delete();

                // }

                // $this->info('Adding rest day for static employees based on their work type policy');
                // // add rest day for static employees based on their work type policy
                // $staticEmployees = Employee::withWhereHas('title.workTypePolicy', function ($q) {
                //     $q->where('work_days_type', '!=', 'dynamic_onsite');
                // })->with('company')->get();

                // foreach ($staticEmployees as $staticEmployee) {
                //     $restDayLeaveId = $staticEmployee->company->rest_day_leave_id;
                //     $restDayLeavePolicyId = CompanyLeaveTypePolicy::where('company_leave_type_id', $restDayLeaveId)->first()->id;
                //     $restDays = $staticEmployee->title->workTypePolicy->rest_days; // sat,fri
                //     $restDayList = explode(',', $restDays);
                //     $date = Carbon::parse('2025-03-30');
                //     while ($date->lessThan(Carbon::parse('2025-04-30'))) {
                //         if (in_array(strtolower($date->format('D')), $restDayList)) {
                //             $leaveRequest = EmployeeLeaveRequest::where('employee_id', $staticEmployee->id)->whereDate('from', $date)
                //                 ->where('company_leave_type_id', $restDayLeaveId)
                //                 ->first();
                //             if (! $leaveRequest) {
                //                 EmployeeLeaveRequest::create([
                //                     'employee_id' => $staticEmployee->id,
                //                     'company_leave_type_id' => $restDayLeaveId,
                //                     'company_leave_type_policy_id' => $restDayLeavePolicyId,
                //                     'from' => $date->startOfDay()->format('Y-m-d H:i:s'),
                //                     'to' => $date->endOfDay()->format('Y-m-d H:i:s'),
                //                     'status' => 'approved',
                //                     'net_quantity' => 8,
                //                     'type' => 'regular',
                //                     'branch_id' => $staticEmployee->branch_id,
                //                     'created_at' => now(),
                //                     'updated_at' => now(),
                //                 ]);
                //             }
                //         }
                //         $date->addDay();
                //     }
                // }

                // $this->info('update timecards that does not have entity tag');
                // $timecardsWithoutEntityTag = Timecard::withWhereHas('employee.title.workTypePolicy', function ($q) {
                //     $q->where('work_days_type', '!=', 'dynamic_onsite');
                // })->where('name', '=', 'Timecard')
                //     ->whereDate('from', '>=', '2025-03-28')
                //     ->doesntHave('attendance')
                //     ->doesntHave('entityTags')
                //     ->get();

                $this->info('update timecards that does not have entity tag');
                $timecardsWithoutEntityTag = Timecard::with('employee')
                    ->doesntHave('attendance')
                    ->doesntHave('entityTags')
                    ->get();
                $this->info('Found '.count($timecardsWithoutEntityTag).' timecards that does not have entity tag');
                foreach ($timecardsWithoutEntityTag as $timecard) {
                    $companyId = $timecard?->employee?->company_id ?? null;
                    if (! isset($companyId)) {
                        $this->info('Company id is not set for timecard '.$timecard->id);

                        continue;
                    }
                    $timecard->entityTags()->create(['tag' => 'absent', 'company' => $companyId]);
                }
                DB::commit();

            } catch (Exception $e) {
                DB::rollBack();
                Log::error($e);
                \Sentry\captureException($e);
                throw $e;
            }
            $this->info('Fixing static employees TCS issue');
        } catch (\Exception $e) {
            $this->error('Error fixing static employees TCS issue: '.$e->getMessage());
        }
    }

    private function getLateDeductionPolicy()
    {
        $attendanceSettingRepository = app(AttendanceSettingRepository::class, ['model' => 'AttendanceSetting']);
        $featureFlag = $this->getAttendancePoliciesFlag();

        return $featureFlag == 0 ?
        $attendanceSettingRepository->getByKey('key', config('globals.ATTENDANCE_SETTINGS.APPLY_DEDUCTION'))->first()
            : $attendanceSettingRepository->getByKey('key', config('globals.ATTENDANCE_SETTINGS.APPLY_LATE_DEDUCTION'))->first();
    }
}
