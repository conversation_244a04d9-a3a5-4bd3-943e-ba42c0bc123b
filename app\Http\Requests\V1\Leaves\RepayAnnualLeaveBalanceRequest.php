<?php

namespace App\Http\Requests\V1\Leaves;

use App\Rules\EmployeeIdRule;
use Illuminate\Foundation\Http\FormRequest;

class RepayAnnualLeaveBalanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        // make it bulk repay annual leave balance
        return [
            'employees' => 'required|array',
            'employees.*.employee_id' => [
                'required',
                new EmployeeIdRule,
            ],
            'employees.*.repaid_balance' => 'required|numeric',
        ];
    }
}
