<?php

namespace App\Console\Commands;

use App\Models\Cico;
use App\Models\EmployeeLeaveRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RejectPendingRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reject:pending-requests {company_id} {date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reject all pending requests.';

    /**
     * Execute the console command.
     */
    public function handle(

    ) {
        DB::beginTransaction();
        try {
            $companyId = $this->argument('company_id');
            $date = $this->argument('date');
            $unverivedCicos = Cico::whereHas('employee', function ($q) use ($companyId) {
                $q->where('company_id', $companyId);
            })
                ->where('status', 'unverified')
                ->whereDate('date', '<=', $date)
                ->update(['status' => 'rejected']);

            $pendingLeaveRequests = EmployeeLeaveRequest::whereHas('employee', function ($q) use ($companyId) {
                $q->where('company_id', $companyId);
            })
                ->where('status', 'pending')
                ->whereDate('from', '<=', $date)
                ->update(['status' => 'rejected']);

            $pendingOvertimeRequests = DB::table('attendance_overtimes')
                ->where('company_id', $companyId)
                ->where('status', 'pending')
                ->whereDate('date', '<=', $date)
                ->update(['status' => 'rejected']);

            $approvalCycles = DB::table('workflow_approval_cycle')
                ->where('date', '<=', $date)
                ->where('company_id', $companyId)
                ->where('status', 'applied')
                ->where(
                    'requestable_type', 'attendance_deduction'
                )->orderBy('order')->get();

            foreach ($approvalCycles as $approvalCycle) {
                if (! $approvalCycle?->status) {
                    continue;
                }
                $entity = $approvalCycle->approval ?? null;
                if (! $entity) {
                    continue;
                }

                if ($approvalCycle->order == 1) { // I need to check the pending only if the order is then / else I should have checked if pending before
                    $isPending = $this->checkIfRequestIsPending($entity, $approvalCycle->status);
                    if ($isPending) {
                        $approvalCycle->status = 'rejected';
                        $approvalCycle->save();
                    }
                }
            }

            $pendingPenalties = DB::table('penalties')
                ->where('company_id', $companyId)
                ->where('status', 'pending')
                ->whereDate('date', '<=', $date)
                ->update(['status' => 'rejected']);

            $employeeRequests = DB::table('employee_requests')
                ->where('company_id', $companyId)
                ->where('status', 'pending')
                ->whereDate('date', '<=', $date)
                ->update(['status' => 'rejected']);

            echo 'num of rejected cicos: '.$unverivedCicos."\n";
            echo 'num of rejected leave requests: '.$pendingLeaveRequests."\n";
            echo 'num of rejected overtime requests: '.$pendingOvertimeRequests."\n";
            echo 'num of rejected penalties: '.$pendingPenalties."\n";
            echo 'num of rejected deductions : '.count($approvalCycles)."\n";
            echo 'total num of rejected requests: '.$employeeRequests."\n";
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
        }

    }

    public function checkIfRequestIsPending($entity, $firstApproverStatus): bool
    {
        if ($entity->status == 'applied') {
            return $firstApproverStatus != 'rejected';
        } else {
            return false;
        }

    }
}
