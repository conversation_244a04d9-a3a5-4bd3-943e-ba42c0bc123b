<?php

namespace App\Console\Commands;

use App\Models\PublicHoliday;
use App\Models\Timecard;
use App\Traits\V2\WorkTypesTrait;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AdjustTimecardsForPublicHolidayChange extends Command
{
    use WorkTypesTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:adjust-timecards-for-holiday {public_holiday_id} {start_date} {end_date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Adjust timecards for employees with static workTypes when a public holiday date changes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Starting timecard adjustment for changed public holiday date');
            
            $publicHolidayId = $this->argument('public_holiday_id');
            $startDate = $this->argument('start_date');
            $endDate = $this->argument('end_date');
            
            // Validate the dates
            if (!$this->validateDates($startDate, $endDate)) {
                $this->error('Invalid date format. Please use Y-m-d format.');
                return 1;
            }
            
            // Get the public holiday
            $publicHoliday = PublicHoliday::find($publicHolidayId);
            if (!$publicHoliday) {
                $this->error("Public holiday with ID {$publicHolidayId} not found");
                return 1;
            }
            
            $this->info("Adjusting timecards for public holiday: {$publicHoliday->name}");
            $this->info("Original period: {$publicHoliday->start} to {$publicHoliday->end}");
            $this->info("New period: {$startDate} to {$endDate}");
            
            // Start a transaction
            DB::beginTransaction();
            
            // Get static work types
            $staticWorkTypes = $this->getFixedTypes();
            
            // Find timecards that are in the new public holiday date range and 
            // belong to employees with static work types
            $timecards = Timecard::whereDate('from', '>=', $startDate)
                ->whereDate('to', '<=', $endDate)
                ->whereHas('employee', function ($query) use ($staticWorkTypes) {
                    $query->whereHas('title', function ($query) use ($staticWorkTypes) {
                        $query->whereHas('workTypePolicy', function ($query) use ($staticWorkTypes) {
                            $query->whereIn('work_days_type', $staticWorkTypes);
                        });
                    });
                })
                ->get();
            
            $this->info("Found {$timecards->count()} timecards to adjust");
            
            if ($timecards->count() > 0) {
                // Check if the holiday was moved forward or backward
                $originalStart = Carbon::parse($publicHoliday->start);
                $newStart = Carbon::parse($startDate);
                
                if ($newStart->gt($originalStart)) {
                    // Holiday moved forward - move timecards before the new date
                    $this->info("Holiday moved forward - adjusting timecards");
                    $this->moveTimecardsBeforeHoliday($timecards, $startDate, $publicHoliday);
                } else if ($newStart->lt($originalStart)) {
                    // Holiday moved backward - move timecards after the new date
                    $this->info("Holiday moved backward - adjusting timecards");
                    $this->moveTimecardsAfterHoliday($timecards, $endDate, $publicHoliday);
                } else {
                    $this->info("No date change detected, no timecards need adjustment");
                }
            }
            
            // Update the public holiday dates
            $publicHoliday->start = $startDate;
            $publicHoliday->end = $endDate;
            $publicHoliday->save();
            
            DB::commit();
            $this->info('Timecard adjustment completed successfully');
            
            return 0;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error adjusting timecards for public holiday change: ' . $e->getMessage());
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }
    }
    
    /**
     * Validate date format
     */
    private function validateDates($startDate, $endDate)
    {
        try {
            Carbon::parse($startDate);
            Carbon::parse($endDate);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Move timecards when holiday is moved forward
     */
    private function moveTimecardsBeforeHoliday($timecards, $newHolidayStart, $publicHoliday)
    {
        // Calculate how many days the holiday moved forward
        $originalStart = Carbon::parse($publicHoliday->start);
        $newStart = Carbon::parse($newHolidayStart);
        $daysMoved = $originalStart->diffInDays($newStart);
        
        $this->info("Holiday moved {$daysMoved} days forward");
        
        foreach ($timecards as $timecard) {
            $fromDate = Carbon::parse($timecard->from);
            $toDate = Carbon::parse($timecard->to);
            
            // Get the date parts to shift the timecard by exact days (not just to day before holiday)
            $newFromDate = $fromDate->copy()->subDays($daysMoved);
            $newToDate = $toDate->copy()->subDays($daysMoved);
            
            // Update the timecard
            $timecard->from = $newFromDate->toDateTimeString();
            $timecard->to = $newToDate->toDateTimeString();
            $timecard->save();
            
            $this->info("Moved timecard ID {$timecard->id} from {$fromDate} to {$newFromDate} ({$daysMoved} days backward)");
        }
    }
    
    /**
     * Move timecards when holiday is moved backward
     */
    private function moveTimecardsAfterHoliday($timecards, $newHolidayEnd, $publicHoliday)
    {
        // Calculate how many days the holiday moved backward
        $originalEnd = Carbon::parse($publicHoliday->end);
        $newEnd = Carbon::parse($newHolidayEnd);
        $daysMoved = $originalEnd->diffInDays($newEnd);
        
        $this->info("Holiday moved {$daysMoved} days backward");
        
        foreach ($timecards as $timecard) {
            $fromDate = Carbon::parse($timecard->from);
            $toDate = Carbon::parse($timecard->to);
            
            // Get the date parts to shift the timecard by exact days (not just to day after holiday)
            $newFromDate = $fromDate->copy()->addDays($daysMoved);
            $newToDate = $toDate->copy()->addDays($daysMoved);
            
            // Update the timecard
            $timecard->from = $newFromDate->toDateTimeString();
            $timecard->to = $newToDate->toDateTimeString();
            $timecard->save();
            
            $this->info("Moved timecard ID {$timecard->id} from {$fromDate} to {$newFromDate} ({$daysMoved} days forward)");
        }
    }
} 