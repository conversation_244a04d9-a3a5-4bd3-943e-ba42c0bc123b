name: Deployment

on: 
  push:
    branches-ignore:
      - master
      - staging
      - development
      
jobs:
  dev-deployment:

    runs-on: ubuntu-latest
    if: "contains(github.event.head_commit.message, '#QA_push')"    
    steps:        
    - name: Set output
      id: vars
      run: echo ::set-output name=short_ref::${GITHUB_REF#refs/*/}

    - name: Check output
      run: echo ${{ steps.vars.outputs.short_ref }}
      
    - name: Install SSH Key
      run: |
        install -m 600 -D /dev/null ~/.ssh/id_rsa
        echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.SSH_HOST }} > ~/.ssh/known_hosts
          
    - name: delete current branch if exists
      continue-on-error: true
      run: ssh bluworks@${{ secrets.SSH_HOST }} "cd ${{secrets.QA_DIR}} && git checkout development && git branch -d ${{ steps.vars.outputs.short_ref }}"
     
    - name: checkout new branch
      run: ssh bluworks@${{ secrets.SSH_HOST }} "cd ${{secrets.QA_DIR}} && echo updateing && git remote update && echo fetching 1 && git fetch origin && echo fetching &&git fetch  --all && echo checkingout && git checkout --track origin/${{ steps.vars.outputs.short_ref }} && git pull && composer install --no-dev --no-interaction --no-progress --optimize-autoloader --classmap-authoritative &&  php artisan route:cache && php artisan view:cache && php artisan event:cache && php artisan migrate --force && php artisan queue:restart && exit"
