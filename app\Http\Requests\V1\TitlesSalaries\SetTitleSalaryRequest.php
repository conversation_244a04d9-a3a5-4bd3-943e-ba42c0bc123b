<?php

namespace App\Http\Requests\V1\TitlesSalaries;

use App\Services\V1\Payroll\ConvertNetToGrossService;
use App\Traits\V1\Payroll\PayrollHelper;
use Illuminate\Foundation\Http\FormRequest;

class SetTitleSalaryRequest extends FormRequest
{
    use PayrollHelper;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {

        return [
            'net_salary' => [
                'integer',
                'required',
            ],
            'gross_salary' => [
                'integer',
                'nullable',
            ],

        ];
    }

    public function prepareForValidation()
    {
        $convertNetToGrossService = new ConvertNetToGrossService;
        $companiesTaxInsurancePolicy = $this->getTaxInsurancePolicy(null, config('globals.company')->id);
        $salary = $convertNetToGrossService->calculate($this->net_salary, null, $companiesTaxInsurancePolicy);

        $this->merge([
            'gross_salary' => $salary['grossSalary'],
        ]);
    }
}
