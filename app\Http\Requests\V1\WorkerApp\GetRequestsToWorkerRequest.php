<?php

namespace App\Http\Requests\V1\WorkerApp;

use App\Enums\RequestTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class GetRequestsToWorkerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'from_date' => [
                'required',
                'date:Y-m-d',
            ],
            'to_date' => [
                'required',
                'date:Y-m-d',
                'after_or_equal:from_date',
            ],
            'filter' => [
                'sometimes',
                'string',
                new Enum(RequestTypeEnum::class),

            ],
        ];
    }
}
