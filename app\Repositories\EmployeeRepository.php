<?php

namespace App\Repositories;

use App\Models\EmployeeSalary;
use App\Traits\QueriesHelper;
use App\Traits\V2\WorkTypesTrait;
use App\Util\EmployeeUtil;
use App\Util\HolidaysUtil;
use App\Util\UserWorkTypesUtil;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class EmployeeRepository extends Repository
{
    use QueriesHelper, WorkTypesTrait;

    public function getAllMangerEmployees(string $companyId, string $managerId): array
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');
        $bindings = [$managerId, $companyId];
        $query = "WITH RECURSIVE subordinate AS(
            SELECT  id,name'. $lang.',direct_manager_id,branch_id,0 AS level
            FROM employees
            WHERE id = ? AND company_id = ?
            UNION ALL
            SELECT  e.id,e.name,e.direct_manager_id,e.branch_id,level + 1
            FROM employees e
            JOIN subordinate s
            ON e.direct_manager_id = s.id
            )
            SELECT s.id AS id,s.name AS employee_name, s.branch_id,m.id AS manger_id,m.name AS manager_name,s.level
            from subordinate s
            LEFT JOIN employees m
            ON s.direct_manager_id = m.id
            ORDER BY level;";

        return DB::connection()->select($query, $bindings);
    }

    public function getEmployeeTitlesOnBranch($branchId): object
    {
        return $this->getModel
            ->distinct()
            ->where('branch_id', $branchId)
            ->with('title')
            ->get(['title_id']);
    }

    public function getEmployeeCountOnBranch(string $branchId): int
    {
        return $this->getModel
            ->where('branch_id', $branchId)
            ->count();
    }

    public function getEmployeeTitlesCountOnBranch(string $branchId, array $titleIds): int
    {
        return $this->getModel
            ->where('branch_id', $branchId)
            ->whereIn('title_id', $titleIds)
            ->count();
    }

    public function getEmployeesCountInTitles(array $titleIds): int
    {
        return $this->getModel
            ->whereIn('title_id', $titleIds)
            ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->count();
    }

    public function getEmployeeTitle($employeeId)
    {
        return $this->getModel
            ->with('title')
            ->where('id', $employeeId)
            ->get(['title_id']);
    }

    public function getEmployeesWithShifts(string $startDate, string $endDate, string $branchId): object
    {
        return $this->getModel
            ->where('branch_id', $branchId)
            ->whereHas(
                'titleShiftEmployees',
                fn ($query) => $query->whereHas(
                    'titleShift',
                    fn ($q) => $q->whereHas(
                        'shift',
                        fn ($builder) => $builder->where('branch_id', $branchId)
                            ->whereBetween('date', [$startDate, $endDate])
                    )
                )
            )
            ->get();
    }

    public function numberMatchesName(string $employee_number, string $first_name, string $second_name): bool
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');

        return $this->getModel->where('employee_number', $employee_number)
            ->where('first_name'.$lang, $first_name)->where('second_name'.$lang, $second_name)
            ->exists();
    }

    public function getEmployeesWithWhereShifts(string $startDate, string $endDate, string $branchId): object
    {
        return $this->getModel
            ->where('branch_id', $branchId)
            ->withWhereHas(
                'titleShiftEmployees',
                fn ($query) => $query->withWhereHas(
                    'titleShift',
                    fn ($q) => $q->withWhereHas(
                        'shift',
                        fn ($builder) => $builder->where('branch_id', $branchId)
                            ->whereBetween('date', [$startDate, $endDate])
                    )
                )
            )
            ->get();
    }

    public function getEmployeesWithCommonBranch($branchId)
    {
        return $this->getModel
            ->whereHas('branches', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->with(['title.role', 'user'])->get();
    }

    public function getEmployeesWithCommonBranchAndTitles($branchId, $titleIds)
    {
        return $this->getModel->whereIn('title_id', $titleIds)
            ->whereHas('employeeInfo', function ($query) {
                $query->whereNull('termination_date')->orWhere('termination_date', '>', date('Y-m-d'));
            })
            ->whereHas('branches', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })->get();
    }

    public function getAllEmployeeBranchesIds(int $id): array
    {
        $branches = $this->getModel->find($id)->branches()->get(['branches.id'])->toArray();
        $branchesIds = array_column($branches, 'id');

        return $branchesIds;
    }

    public function getDefaultRestDaysInBranch(int $branchId)
    {
        return $this->getModel->where('branch_id', $branchId)->with(['DefaultRestDays', 'title'])->get();
    }

    public function timecardsAndLeaves(array $data): Collection
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');
        $timezone = config('app.timezone');

        $obj = $this->getModel->where('branch_id', $data['branch_id'])
            ->join('titles', 'titles.id', '=', 'employees.title_id')
            ->join('branches', 'branches.id', '=', 'employees.branch_id')
            ->join('departments', 'departments.id', '=', 'titles.department_id')
            ->join('work_type_policies', 'work_type_policies.id', '=', 'titles.work_type_policy_id')
            // ->when(config('globals.user')->canManageDepartments(), function ($q) use ($data) {
            //
            //     $q->whereHas('title', function ($q) {
            //         $q->where('department_id', optional(config('globals.user')->employee->title)->department_id);
            //     });
            // })
            // ->when(isset($request['department_ids']) && !config('globals.user')->canManageDepartments(), function ($q) use ($data) {
            //
            //     $q->whereHas('title', function ($q) use ($data) {
            //         $q->whereIn('department_id', $data['department_ids']);
            //     });
            // })
            ->with('branches:id')
            ->with('title', function ($query) use ($lang, $data) {
                $query
                    ->withoutTrashed()
                    ->with('newShifts', function ($query) use ($data) {
                        $query->where('from_date', '<=', $data['to'])
                            ->where('to_date', '>=', $data['from'])
                            ->where('branch_id', $data['branch_id'])
                            ->whereNotNull('sun_from_time')
                            ->whereNotNull('sun_duration')
                            ->select([
                                'new_shifts.*',
                                DB::raw('DATE(from_date) as date'),
                            ]);
                    })
                    ->select([
                        'id',
                        'name'.$lang,
                        'color',
                        'department_id',
                    ]);
            })
            ->with('timeCards', function ($query) use ($data, $lang, $timezone) {
                $query->with('timecardType', function ($query) {
                    $query->select([
                        'id',
                        'name',
                        'is_default',
                    ]);
                })
                    ->with('shift', function ($query) {
                        $query->select([
                            'id',
                            'name',
                            'colorhex',
                            'is_custom',
                        ]);
                    })
                    ->with('branch:id,company_id,name'.$lang, 'branch.company:id,radius')
                    ->where(function ($query) use ($data) {
                        $query->whereDate('from', '<=', $data['to'])
                            ->whereDate('to', '>=', $data['from'])
                            ->whereDate('from', '>=', $data['from']);
                    })
                    ->select([
                        'timecards.*',
                        DB::raw("DATE(CONVERT_TZ(`from`, 'UTC', '$timezone')) as date"),
                        // DB::raw('DATE(timecards.from) as date'),

                    ])
                    ->orderBy('from');
            })
            ->with('employeeLeaveRequests', function ($query) use ($timezone, $data, $lang) {
                $query
                    ->whereDate('from', '<=', $data['to'])
                    ->whereDate('to', '>=', $data['from'])
                    ->where('status', config('globals.REQUEST_STATUSES.APPROVED'))
                    ->select([
                        'employee_leave_requests.*',
                        DB::raw("DATE(CONVERT_TZ(`from`, 'UTC', '$timezone')) as date"),
                    ])
                    ->with(['companyLeaveType:id,name'.$lang, 'companyLeaveTypePolicy:id,unit,company_leave_type_id']);
            })
            ->when(isset($data['employee_id']), function ($q) use ($data) {
                $q->where('employees.id', $data['employee_id']);
            })
            ->when(Arr::get($data, 'is_new_work_types', false) == false, function ($q) {
                $q->where('work_type_policies.work_days_type', UserWorkTypesUtil::DYNAMIC_ON_SITE);
            })
            ->whereHas('employeeInfo', function ($q) use ($data) {
                $q->whereDate('join_date', '<', $data['to'])
                    ->where(function ($q) use ($data) {
                        // The following logic filters employees to ensure that only those with a
                        // termination_date within the specified range or with a null termination_date are included
                        $q->whereNull('termination_date')
                            // Include employees whose termination_date is within the date range
                            ->orWhere(function ($q) use ($data) {
                                $q->whereDate('termination_date', '>=', $data['from']);
                            });
                    });
            })
            ->select([
                'departments.name as department_name',
                'employees.id',
                'employee_number',
                'employees.first_name'.$lang,
                'employees.second_name'.$lang,
                'employees.third_name'.$lang,
                'employees.fourth_name'.$lang,
                'employees.name'.$lang,
                'employees.address',
                'employees.status',
                'phone',
                'gender',
                'title_id',
                'gender',
                'branches.name as branch_name',
                'work_type_policies.work_days_type as work_days_type',
                'work_type_policies.rest_days_type as rest_days_type',
            ])
            ->orderBy('departments.name', 'asc');

        if (! isset($data['employee_id'])) {
            $this->appendScopeQuery($obj, $data);
        }

        return $obj->get();
    }

    public function getEmployeesWithBalances($request)
    {
        $obj =
            $this->getModel
                ->when(isset($request['employee_id']), function ($q) use ($request) {
                    $q->where('id', $request['employee_id']);
                })
                ->when(! isset($request['employee_id']), function ($q) use ($request) {
                    $this->appendScopeQuery($q, $request);
                })
                ->withWhereHas('employeeLeaveBalances', function ($q) use ($request) {

                    $q->when($request['with_restdays'] == false, function ($q) {
                        $q->where('company_leave_type_id', '!=', config('globals.rest_day_leave_id'));
                    })
                        ->where(function ($currentPeriodQuery) use ($request) {
                            $currentPeriodQuery->where('start', '<=', $request['now'])
                                ->where('end', '>=', $request['now']);
                        })
                        ->where('balance', '>', 0);

                })
                ->with('profilePicture');

        if ($request['page_size'] == 0) {
            return $obj->get();
        } else {
            return $obj->paginate($request['page_size']);
        }

    }

    // public function missingSalaryDataCounter($data){
    //    return $this->getModel->whereHas('employeeInfo', function($query){
    //         $query->whereNull('termination_date')->orWhere('termination_date', '>', date('Y-m-d'));})
    //         ->whereDoesntHave('employeeSalary')
    //         ->orWhereHas('employeeSalary', function($q){
    //                 $q->whereNull("basic_salary");
    //                 $q->whereNull("social_insurance");
    //             })->count();
    // }

    public function getEmployeesByFilter($request)
    {
        $lang = '_' . (config('globals.lang') ?? 'ar');
        $obj = $this->getModel
            ->select([
                'employees.id as id',
                'employees.name_en',
                'employees.name_ar',
                'employees.title_id',
                'employees.phone',
                'employees.employee_number',
                'employees.revoke_date',
                'employees_info.termination_date as termination_date',
                'employees.branch_id',
                'employees.first_name_ar',
                'employees.second_name_ar',
                'employees.third_name_ar',
                'employees.fourth_name_ar',
                'employees.fifth_name_en',
                'employees.first_name_en',
                'employees.second_name_en',
                'employees.third_name_en',
                'employees.fourth_name_en',
                'employees.fifth_name_en',
                'employees.status',
            ])
            ->join('employees_info', 'employees_info.employee_id', '=', 'employees.id')
            ->when(isset($request['search_value']), function ($q) use ($request) {
                $q->where(function ($query) use ($request) {
                    $query->where('name_en', 'LIKE', '%'.$request['search_value'].'%')
                        ->orWhere('name_ar', 'LIKE', '%'.$request['search_value'].'%')
                        ->orWhere('employee_number', 'LIKE', '%'.$request['search_value'].'%');
                });
            })
            ->when(isset($request['missing_salary_data']) && $request['missing_salary_data'], function ($q) {
                $q->leftJoin('employee_salaries as s', function($join) {
                    $join->on('employees.id', '=', 's.employee_id')
                        ->whereNull('s.deleted_at');
                })
                ->whereNull('s.id');
            })
            ->when(isset($request['title_ids']), function ($q) use ($request) {
                $q->whereIn('title_id', $request['title_ids']);
            })
            ->when(isset($request['department_ids']), function ($q) use ($request) {
                $q->whereHas('title', function ($q) use ($request) {
                    $q->whereIn('department_id', $request['department_ids']);
                });
            })
            ->when(isset($request['branch_ids']), function ($q) use ($request) {
                $q->whereIn('branch_id', $request['branch_ids']);
            })
            ->when(isset($request['status']) && $request['status'] == 'active', function ($q) {
                $q->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED']);
            })
            ->when(isset($request['status']) && $request['status'] == 'terminated', function ($q) {
                $q->where('status', EmployeeUtil::STATUSES['TERMINATED']);

            })
            ->when(isset($request['missing_employee_info']) && $request['missing_employee_info'], function ($q) {
                $q->orWhereHas('employeeInfo', function ($q) {
                    $q->whereNull('join_date');
                });
            })
            ->when(isset($request['payroll_start']) && isset($request['payroll_end']), function ($q) use ($request) {
                $q->where(function ($query) use ($request) {
                    $query->whereNull('employees_info.termination_date')
                        ->orWhereDate('employees_info.termination_date', '>=', $request['payroll_start']);
                });
            })
            ->with('title:id,name_en,name_ar,color,department_id', 'branch:id,name_en,name_ar', 'employeeInsurance', 'employeeSalary', 'branches:id,name'.$lang, 'title.department', 'payrollPolicy')
            ->orderBy('employee_number', 'asc');
        $this->appendScopeQuery($obj, $request);

        if (! isset($request['page_size'])) {
            return $obj->get();
        }

        return $this->getFilterPaginated($obj, $request['page_size']);
    }

    public function getFilterPaginated($obj, $pageSize)
    {
        return $obj->paginate($pageSize);
    }

    public function setStatus($employee, $status)
    {
        $employee->status = $status;
        $employee->save();
    }

    public function employeeHasRelationToBranch($employeeId, $branchId): bool
    {

        return $this->getModel
            ->where('id', $employeeId)
            ->whereHas('branches', function ($q) use ($branchId) {
                $q->Where('branch_id', $branchId);
            })
            ->exists();
    }

    public function getAggregatedLateDeductionsGroupedByEmployee($payrollPeriodStart, $payrollPeriodEnd)
    {
        return $this->getModel
            ->withCount(['attendanceDeductions as total_deduction' => function ($query) use ($payrollPeriodStart, $payrollPeriodEnd) {
                $query->where('status', 'applied')
                    ->whereBetween('date', [$payrollPeriodStart, $payrollPeriodEnd])
                    ->select(DB::raw('SUM(COALESCE(updated_value, deduction_value))'));
            }])
            ->withCount(['attendanceDeductions as deduction_count' => function ($query) use ($payrollPeriodStart, $payrollPeriodEnd) {
                $query->where('status', 'applied')
                    ->whereBetween('date', [$payrollPeriodStart, $payrollPeriodEnd])
                    ->select(DB::raw('count(*)'));
            }])
            ->groupBy('id')
            ->where('company_id', config('globals.company')->id)
            ->get();
    }

    public function getAggregatedOvertimeGroupedByEmployee($payrollPeriodStart, $payrollPeriodEnd)
    {
        return $this->getModel
            ->withCount(['attendanceOvertimes as total_rate_overtime_amount' => function ($query) use ($payrollPeriodStart, $payrollPeriodEnd) {
                $query->where('status', 'approved')
                    ->where('calculation_method', 'rate')
                    ->whereBetween('date', [$payrollPeriodStart, $payrollPeriodEnd])
                    ->select(DB::raw('SUM(COALESCE(updated_value, overtime_minutes)/60 * overtime_value)'));
            }])
            ->withCount(['attendanceOvertimes as total_fixed_overtime_amount' => function ($query) use ($payrollPeriodStart, $payrollPeriodEnd) {
                $query->where('status', 'approved')
                    ->where('calculation_method', 'fixed')
                    ->whereBetween('date', [$payrollPeriodStart, $payrollPeriodEnd])
                    ->select(DB::raw('SUM(COALESCE(updated_value, overtime_minutes)/60 * overtime_value)'));
            }])
            ->with('employeeSalary', 'title:id,working_hours')
            ->with(['attendanceOvertimes' => function ($query) use ($payrollPeriodStart, $payrollPeriodEnd) {
                $query
                    ->where('status', 'approved')
                    ->whereBetween('date', [$payrollPeriodStart, $payrollPeriodEnd])
                    ->select('employee_id', DB::raw('overtime_value as rate'),
                        DB::raw('SUM(COALESCE(updated_value, overtime_minutes)) as total_overtime_minutes'), 'calculation_method')
                    ->groupBy('employee_id', 'overtime_value', 'calculation_method')
                    ->orderBy('overtime_value', 'asc');
            }])
            ->groupBy('id')
            ->where('company_id', config('globals.company')->id)
            ->get();
    }

    public function getAggregatedVariableComponentsGroupedByEmployee($payrollId)
    {
        return $this->getModel
            ->withCount(['employeeMonthlySalaryComponents as total_amount' => function ($query) use ($payrollId) {
                $query->where('payroll_id', $payrollId)
                    ->select(DB::raw('employee_id, SUM(amount)'));
            }])
            ->groupBy('employee_id')
            ->where('company_id', config('globals.company')->id)
            ->get();
    }

    public function getUnaccountedEmployeesCountOnBranchesAndDate($date, $branchIds): int
    {
        return $this->getModel
            ->whereIn('branch_id', $branchIds)
            ->whereDoesntHave('timecards', function ($timecardQuery) use ($date) {
                $timecardQuery->whereDate('from', $date);
            })
            ->whereDoesntHave('attendance', function ($attendanceQuery) use ($date) {
                $attendanceQuery->where('date', $date);
            })
            ->whereDoesntHave('cicos', function ($cicoRepository) use ($date) {
                $cicoRepository->whereDate('date', $date);
            })
            ->whereDoesntHave('employeeLeaveRequests', function ($leavesQuery) use ($date) {
                $leavesQuery->whereDate('from', $date);
            })
            ->count();
    }

    public function getPayrollEmployees($payrollStartDate, $payrollEndDate, $employeeIds = null, bool $onlyInsuredEmployees = false, bool $byCronJob = false)
    {
        $q = $this->getModel
            ->withWhereHas('employeeInfo', function ($q) use ($payrollEndDate, $payrollStartDate) {
                $q->whereDate('join_date', '<=', $payrollEndDate)
                    ->where(function ($q) use ($payrollStartDate) {
                        $q->whereNull('termination_date')
                            ->orWhereDate('termination_date', '>=', $payrollStartDate);
                    });
            })
            ->when(isset($employeeIds), function ($q) use ($employeeIds) {
                $q->whereIn('id', $employeeIds);
            })
            ->when($onlyInsuredEmployees == true, function ($q) {
                $q->whereHas('employeeSalary', function ($q) {
                    $q->where('is_employee_socially_insured', true);
                });
            })
            // ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->with(['title', 'branch', 'employeeSalary.bank', 'title.attendanceGroup', 'title.absenceDeductionPolicy', 'employeeInsurance']);

        if (! $byCronJob) {
            $this->appendScopeQuery($q);
        }

        return $q->get();
    }

    public function getEmployeesSummaryInRange($employeeIds, $fromDate, $toDate)
    {
        $restDayLeaveId = config('globals.rest_day_leave_id');

        return $this->getModel
            ->select('id')
            ->withCount([
                'attendance as all_attendance_count_P' => function ($query) use ($fromDate, $toDate) {
                    $query->whereBetween('date', [$fromDate, $toDate])
                        ->where(function ($q) {
                            $q->whereNull('employees.revoke_date')
                                ->orWhere->where('attendances.date', '<', 'employees.revoke_date');
                        })
                        ->select(DB::raw('count(distinct date)'));
                },
            ])
            ->withCount([
                'attendance as attendance_on_timecard_count' => function ($query) use ($fromDate, $toDate) {
                    $query->whereBetween('date', [$fromDate, $toDate])
                        ->where(function ($q) {
                            $q->whereNull('employees.revoke_date')
                                ->orWhere->where('attendances.date', '<', 'employees.revoke_date');
                        })
                        ->where('slotable_type', 'time_card')
                        ->select(DB::raw('count(distinct date)'));
                },
            ])
            ->withCount([
                'employeeLeaveRequests as rest_days_count_R' => function ($query) use ($fromDate, $toDate, $restDayLeaveId) {
                    $query
                        ->where('company_leave_type_id', $restDayLeaveId)
                        ->where(function ($q) {
                            $q->whereNull('employees.revoke_date')
                                ->orWhere->where('employee_leave_requests.from', '<', 'employees.revoke_date');
                        })
                        ->whereDate('from', '>=', $fromDate)
                        ->whereDate('to', '<=', $toDate)
                        ->where('type', 'regular');
                },
            ])
            ->withCount(['timecards as timecards_count' => function ($query) use ($fromDate, $toDate) {
                $query
                    ->where(function ($q) {
                        $q->whereNull('employees.revoke_date')
                            ->orWhere->where('timecards.from', '<', 'employees.revoke_date');
                    })
                    ->whereDate('from', '>=', $fromDate)
                    ->whereDate('from', '<=', $toDate);
            }])
            ->whereIn('id', $employeeIds)
            ->get();
    }

    public function employeesAttendanceAndLeavesInDateRange($startDate, $endDate, $employeeIds = null, bool $byCronJob = false)
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');

        $query = $this->getModel
            ->select('id', 'name_ar', 'first_name_ar', 'second_name_ar', 'third_name_ar',
                'name_en', 'first_name_en', 'second_name_en', 'third_name_en', 'employee_number',
                'branch_id', 'title_id', 'revoke_date', 'status')
            ->when(isset($ids), function ($ids) {
                $ids->whereIn('id', $ids);
            })
            ->where('is_trackable', true)
            ->when(isset($employeeIds), function ($q) use ($employeeIds) {
                $q->whereIn('id', $employeeIds);
            })
            ->whereHas('employeeInfo', function ($q) use ($endDate) {
                $q->where('join_date', '<=', $endDate);
            })
            ->when($byCronJob == true, function ($q) {
                $q->where('status', '!=', config('globals.EMPLOYEE_STATUSES.TERMINATED'))
                    ->with('user');
            })
            ->with('title:id,name_ar,name_en,color')
            ->with('branch:id,name_ar,name_en')
            ->with('timecards', function ($q) use ($startDate, $endDate) {
                $q->select('id', 'employee_id', 'from', 'to')
                    ->whereDate('from', '>=', $startDate)
                    ->whereDate('from', '<=', $endDate);
            })
            ->with('employeeLeaveRequests', function ($q) use ($startDate, $endDate) {
                $q->select('id', 'employee_id', 'from', 'to', 'company_leave_type_id')
                    ->where('status', 'approved')
                    ->where(function ($q) use ($startDate, $endDate) {
                        $q->where(function ($fromQuery) use ($startDate, $endDate) {
                            $fromQuery->whereDate('from', '>=', $startDate)
                                ->whereDate('from', '<=', $endDate);
                        })
                            ->orWhere(function ($toQuery) use ($startDate, $endDate) {
                                $toQuery->whereDate('to', '>=', $startDate)
                                    ->whereDate('to', '<=', $endDate);
                            })
                            ->orWhere(function ($fromQuery) use ($startDate, $endDate) {
                                $fromQuery->whereDate('from', '<=', $startDate)
                                    ->whereDate('to', '>=', $endDate);
                            });
                    })
                    ->orderBy('from', 'asc');
            });

        // $this->appendScopeQuery($query);

        return $query->get();
    }

    public function getTrackableEmployeesWithEmployeeChanges($data = [])
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');
        $query = $this->getModel
            ->select('id', 'employee_number', 'first_name'.$lang, 'second_name'.$lang, 'title_id', 'branch_id', 'revoke_date', 'name', 'name_ar', 'name_en')
            ->with('title:id,work_type_policy_id,department_id,name'.$lang.',color', 'branch:id,name'.$lang)
            ->with('employeeChanges', function ($q) {
                $q->select('id', 'employee_id', 'from_value', 'to_value', 'as_of_date')
                    ->where('change_type', 'branch')
                    ->where('status', 'completed')
                    ->orderBy('as_of_date', 'asc');
            })
            ->with('employeeInfo', function ($q) {
                $q->select('id', 'employee_id', 'join_date', 'termination_date');
            })
            ->with('profilePicture')
            // ->when(config('globals.user')->canManageDepartments(), function ($q) {
            //     $q->whereHas('title', function ($q) {
            //         $q->where('department_id', optional(config('globals.user')->employee->title)->department_id);
            //     });
            // })
            ->when(isset($data['employee_id']), function ($q) use ($data) {
                $q->where('id', $data['employee_id']);
            })
            ->where('is_trackable', true);

        $this->appendScopeQuery($query, [], false, true);

        return $query->get();
    }

    public function getEmployeesInBranches($branchIds)
    {
        $lang = '_'.(config('globals.lang') ?? 'ar');

        return $this->getModel
            ->select('id', 'employee_number', 'first_name'.$lang, 'second_name'.$lang, 'title_id', 'branch_id', 'revoke_date', 'status')
            ->with('title:id,name'.$lang.',color', 'branch:id,name'.$lang)
            ->with('employeeInfo', function ($q) {
                $q->select('id', 'employee_id', 'join_date', 'termination_date');
            })
            ->whereIn('branch_id', $branchIds)
            ->where('is_trackable', true)->get();
    }

    public function getWithPhoneWithoutGlobalScope($phone)
    {
        return $this->getModel
            ->withoutGlobalScopes()
            ->where('phone', $phone)
            ->first();
    }

    public function getPenaltyGroupsAppliesToEmployee($employeeId)
    {
        return $this->getModel
            ->where('id', $employeeId)
            ->withWhereHas('title', function ($q) {
                $q->select('titles.id')
                    ->withWhereHas('penaltyGroups', function ($q) {
                        $q->select('penalty_groups  .id');
                    });
            })
            ->first();
    }

    public function getEmployeeTerminationDateAndJoiningDate($employeeId)
    {
        $employee = $this->getModel
            ->where('id', $employeeId)
            ->whereHas('employeeInfo')
            ->with(['employeeInfo' => function ($q) {
                $q->select('employee_id', 'join_date', 'termination_date');
            }])
            ->first();

        return $employee?->employeeInfo;
    }

    public function getEmployeesWithPermissions(array $permissionKeys, string $guardName = 'user-api')
    {
        return $this->getModel
            ->select(
                'id',
                'first_name_ar',
                'first_name_en',
                'second_name_ar',
                'second_name_en',
                'branch_id'
            )
            ->where('status', '!=', config('globals.EMPLOYEE_STATUSES.TERMINATED'))
            ->where('is_trackable', true)
            ->withWhereHas('user', function ($q) use ($permissionKeys, $guardName) {
                $q->where(function ($q) use ($permissionKeys, $guardName) {
                    $q->whereHas('permissions', function ($q) use ($permissionKeys, $guardName) {
                        $q->whereIn('name', $permissionKeys)
                            ->where('guard_name', $guardName);
                    })
                        ->orWhereHas('roles', function ($q) use ($permissionKeys, $guardName) {
                            $q->whereHas('permissions', function ($q) use ($permissionKeys, $guardName) {
                                $q->whereIn('name', $permissionKeys)
                                    ->where('guard_name', $guardName);
                            });
                        });
                });
            })->get();
    }

    public function getEmployeeWithApprovedPenaltiesInPeriod($startDate, $endDate)
    {
        return $this->getModel
            ->with(['penalties' => function ($q) use ($startDate, $endDate) {
                $q->where('status', 'approved')
                    ->whereBetween('date', [$startDate, $endDate]);
            },
                'employeeSalary'])
            ->whereHas('penalties', function ($q) use ($startDate, $endDate) {
                $q->where('status', 'approved')
                    ->whereBetween('date', [$startDate, $endDate]);
            })
            ->get();
    }

    public function getEmployeeBasicInfo($employeeId)
    {
        return $this->getModel
            ->where('id', $employeeId)
            ->with('profilePicture', 'title', 'employeeInfo')
            ->first();
    }

    public function getEmployeeWithExtraWorkDayOnHolidaysWithTimecards($startDate, $endDate)
    {
        return $this->getModel
            ->with([
                'attendance' => function ($q) use ($startDate, $endDate) {
                    $q->whereBetween('date', [$startDate, $endDate])
                        ->whereHas('entityTags', function ($q) {
                            $q->where('tag', config('globals.ATTENDANCE_TAGS.PUBLIC_HOLIDAY'));
                        });
                },
                'title.publicHolidaysPolicy' => function ($q) {
                    $q->where('compensation_method', HolidaysUtil::EXTRA_WORK_DAY);
                },
                'employeeSalary',
            ])
            ->WhereHas('title.publicHolidaysPolicy', function ($q) {
                $q->where('compensation_method', HolidaysUtil::EXTRA_WORK_DAY);
            })
            ->whereHas('attendance', function ($q) use ($startDate, $endDate) {
                $q->whereBetween('date', [$startDate, $endDate])
                    ->whereHas('entityTags', function ($q) {
                        $q->where('tag', config('globals.ATTENDANCE_TAGS.PUBLIC_HOLIDAY'));
                    });
            })
            ->get();
    }

    public function getEmployeeWithApprovedCustmoCompenstationPublicHolidaysForPayroll($payrollId)
    {
        return $this->getModel
            ->with(['publicHolidaysAttendancePayout' => function ($q) use ($payrollId) {
                $q->where('payroll_id', $payrollId);
            },
                'employeeSalary', 'publicHolidaysAttendance', 'title.publicHolidaysPolicy'])
            ->whereHas('publicHolidaysAttendancePayout', function ($q) use ($payrollId) {
                $q->where('payroll_id', $payrollId);
            })
            ->whereHas('employeeSalary')
            ->get();
    }
}
