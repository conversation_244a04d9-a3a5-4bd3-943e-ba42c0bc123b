<?php

namespace App\DomainData;

trait TitleDeductionReasonCategoryDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'title_id' => 'numeric',
            'deduction_reason_category_id' => 'numeric',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeTitleDeductionReasonCategoryDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
