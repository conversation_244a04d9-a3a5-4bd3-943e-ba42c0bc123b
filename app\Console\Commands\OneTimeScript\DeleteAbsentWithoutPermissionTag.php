<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Timecard;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeleteAbsentWithoutPermissionTag extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:absent_without_permission_tag';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $timecards = Timecard::whereHas('attendance')->whereHas('entityTags', function ($query) {
                $query->where('tag', 'absent_without_permission');
            })->get();
            foreach ($timecards as $timecard) {
                $timecard->entityTags()->delete();
            }
            DB::commit();
        } catch (Exception $e) {
            //// dd($e);
            \Sentry\captureException($e);
            Log::error($e);
            DB::rollBack();
        }
    }
}
