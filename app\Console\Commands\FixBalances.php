<?php

namespace App\Console\Commands;

use App\Services\LeaveManagement\FixBalancesService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixBalances extends Command
{
    public function __construct(private FixBalancesService $service)
    {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:balances';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'remove duplicate leave balances for each employee but dont remove the first leave balance for each leave type';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $this->service->run();
            DB::commit();
        } catch (Exception $e) {
            // \Sentry\captureException($e);
            //dd($e->getMessage());
            Log::info($e);
            DB::rollBack();
        }
    }
}
