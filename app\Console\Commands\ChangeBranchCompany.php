<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use App\Models\AttendanceDeduction;
use App\Models\AttendanceOvertime;
use App\Models\BranchEmployee;
use App\Models\Cico;
use App\Models\Employee;
use App\Models\EmployeeLeaveBalance;
use App\Models\EmployeeLeaveRequest;
use App\Models\EmployeeRequest;
use App\Models\EmployeeSalary;
use App\Models\PublicHolidayAbsence;
use App\Models\PublicHolidaysAttendance;
use App\Models\Timecard;
use App\Models\User;
use App\Models\WorkflowApprovalCycle;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ChangeBranchCompany extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:change-branch-company';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'move specific branch to another company with all related data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $runTheFullScript = true;
            $this->info('Changing branch company');
            $oldCompanyId = 125;
            $newCompanyId = 147;
            $oldBranchId = 520;
            $newBranchId = 616;
            // create titlesMapper
            $titlesMapper = [
                '1578' => '1947',
                '1739' => '1952',
                '1581' => '1953',
                '1576' => '1958',
                '1575' => '1959',
                '1584' => '1954',
                '1524' => '1955',
                '1577' => '1960',
                '1583' => '1956',
                '1582' => '1961',
                '1579' => '1957',
            ];

            // create departmentsMapper
            $departmentsMapper =
            [
                '386' => '514',
                '389' => '515',
                '436' => '516',
            ];

            // create rolesMapper
            $rolesMapper = [
                '384' => '465',
                '385' => '466',
                '386' => '467',
                '401' => '468',
                '367' => '465',
                '365' => '460',
            ];

            // create leave types and policies mapper
            $leavesMapper = [
                '403' => [
                    'leave_type_id' => '477',
                    'policy_id' => '442',
                ],
                '395' => [
                    'leave_type_id' => '475',
                    'policy_id' => '440',
                ],
                '396' => [
                    'leave_type_id' => '476',
                    'policy_id' => '441',
                ],
                '394' => [
                    'leave_type_id' => '474',
                    'policy_id' => '439',
                ],
            ];
            // select all employeeIds where branch_id = 520
            $employees = Employee::where('branch_id', $oldBranchId)->get();
            $employeeIds = $employees->pluck('id')->toArray();
            $this->info('Total employees: '.count($employeeIds));

            if ($runTheFullScript) {

                // update all employees branch id to new branch id , company id and title id
                foreach ($employees as $employee) {
                    $employee->company_id = $newCompanyId;
                    $employee->branch_id = $newBranchId;
                    $employee->title_id = $titlesMapper[$employee->title_id];
                    $employee->save();
                }
                $this->info('Employees updated successfully');
                // update user company id to the new one
                User::whereIn('employee_id', $employeeIds)->update(['company_id' => $newCompanyId]);

                $this->info('Users updated successfully');
                // change old roles to the new ones for all employees
                $users = User::whereIn('employee_id', $employeeIds)->get();
                foreach ($users as $user) {
                    $user->roles()->sync($rolesMapper[$user->roles[0]->id]);
                }
                $this->info('Roles updated successfully');
                // update all timecards required branch id for clock in and out to new branch id and company id
                Timecard::whereIn('employee_id', $employeeIds)->update([
                    'branch_id' => $newBranchId,
                    'required_ci_branch_id' => $newBranchId,
                    'required_co_branch_id' => $newBranchId,
                ]); // TODO: should update the shift id

                $this->info('Timecards updated successfully');
                // update all attendances branch id to new branch id and company id
                Attendance::whereIn('employee_id', $employeeIds)->update([
                    'branch_id' => $newBranchId,
                    'company_id' => $newCompanyId,
                ]);
                $this->info('Attendances updated successfully');

                // update all cicos to the new branch id and company id
                Cico::whereIn('employee_id', $employeeIds)->update([
                    'branch_id' => $newBranchId,
                ]);
                $this->info('Cicos updated successfully');

                // update all attendance overtime and deduction to the new branch id and company id
                AttendanceOvertime::whereIn('employee_id', $employeeIds)->update([
                    'branch_id' => $newBranchId,
                    'company_id' => $newCompanyId,
                ]);
                $this->info('Attendance overtime updated successfully');

                AttendanceDeduction::whereIn('employee_id', $employeeIds)->update([
                    'branch_id' => $newBranchId,
                    'company_id' => $newCompanyId,
                ]);
                $this->info('Attendance deduction updated successfully');

                // update all leaves to the new branch id and company id and new policy and new type
                $employeesLeaveRequests = EmployeeLeaveRequest::whereIn('employee_id', $employeeIds)->get();
                foreach ($employeesLeaveRequests as $leaveRequest) {
                    $companyLeaveTypeId = $leavesMapper[$leaveRequest->company_leave_type_id]['leave_type_id'];
                    $companyLeavePolicyId = $leavesMapper[$leaveRequest->company_leave_type_id]['policy_id'];
                    $leaveRequest->company_leave_type_id = $companyLeaveTypeId;
                    $leaveRequest->company_leave_type_policy_id = $companyLeavePolicyId;
                    $leaveRequest->branch_id = $newBranchId;
                    activity()->withoutLogs(function () use ($leaveRequest) {
                        $leaveRequest->save();
                    });
                }
                $this->info('Leaves updated successfully');

                // update employee leave balances
                $employeeLeaveBalances = EmployeeLeaveBalance::whereIn('employee_id', $employeeIds)->get();
                foreach ($employeeLeaveBalances as $leaveBalance) {
                    $companyLeaveTypeId = $leavesMapper[$leaveBalance->company_leave_type_id]['leave_type_id'];
                    $companyLeavePolicyId = $leavesMapper[$leaveBalance->company_leave_type_id]['policy_id'];
                    $leaveBalance->company_leave_type_id = $companyLeaveTypeId;
                    $leaveBalance->company_leave_type_policy_id = $companyLeavePolicyId;
                    activity()->withoutLogs(function () use ($leaveBalance) {
                        $leaveBalance->save();
                    });
                }
                $this->info('Leave balances updated successfully');

                // update employee requests table to change the company id
                activity()->withoutLogs(function () use ($employeeIds, $newCompanyId) {
                    EmployeeRequest::whereIn('employee_id', $employeeIds)->update(['company_id' => $newCompanyId]);
                });
                $this->info('Employee requests updated successfully');

                // update all workflow approval cycle
                $employeesApprovalCycles = WorkflowApprovalCycle::whereIn('employee_id', $employeeIds)->get();
                foreach ($employeesApprovalCycles as $approvalCycle) {
                    $approvalCycle->company_id = $newCompanyId;
                    $approvalCycle->branch_id = $newBranchId;
                    $approvalCycle->department_id = $departmentsMapper[$approvalCycle->department_id];
                    $approvalCycle->role_id = $rolesMapper[$approvalCycle->role_id];
                    activity()->withoutLogs(function () use ($approvalCycle) {
                        $approvalCycle->save();
                    });
                }// TODO;; should update the request workflow id
                $this->info('Approval cycles updated successfully');

                // update all employees salaries
                EmployeeSalary::whereIn('employee_id', $employeeIds)->update(['company_id' => $newCompanyId]);
                $this->info('Salaries updated successfully');

                // update public holidays absences
                PublicHolidayAbsence::whereIn('employee_id', $employeeIds)->update([
                    'company_id' => $newCompanyId,
                    'current_branch_id' => $newBranchId,
                ]);
                $this->info('Public holidays absences updated successfully');

                // update public holidays attendances
                activity()->withoutLogs(function () use ($employeeIds, $newCompanyId) {
                    PublicHolidaysAttendance::whereIn('employee_id', $employeeIds)->update([
                        'company_id' => $newCompanyId,
                    ]);
                });
                $this->info('Public holidays attendances updated successfully');

            }

            // add new branch as a managed to all employees thy were managed the old branch
            $employeesManagedOldBranch = BranchEmployee::whereIn('employee_id', $employeeIds)
                ->where('branch_id', $oldBranchId)->get();
            foreach ($employeesManagedOldBranch as $employeeManagedOldBranch) {
                $employeeManagedOldBranch->branch_id = $newBranchId;
                activity()->withoutLogs(function () use ($employeeManagedOldBranch) {
                    $employeeManagedOldBranch->save();
                });
            }
            $this->info('Branch employees updated successfully');

            $this->info('Company removed successfully');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('An error occurred while changing branch company');
            $this->error($e);
        }
    }
}
