<?php

namespace App\Http\Requests\V1\Employee;

use App\Enums\EmployeeInfo\ContractDurationEnum;
use App\Enums\EmployeeInfo\DegreeTypeEnum;
use App\Enums\EmployeeInfo\EmploymentTypeEnum;
use App\Enums\EmployeeInfo\GenderEnum;
use App\Enums\EmployeeInfo\MaritalStatusEnum;
use App\Enums\EmployeeInfo\MilitaryStatus;
use App\Enums\EmployeeInfo\ReligionEnum;
use App\Rules\EntityIdRule;
use App\Rules\IsArabic;
use App\Rules\IsEnglish;
use App\Rules\NationalIdRule;
use App\Rules\PhoneNumberByCountryRule;
use App\Rules\UniqueEmployeeNumberRule;
use App\Rules\UniqueNationalIdRule;
use App\Rules\UniquePassportNumberRule;
use App\Rules\UniquePersonalEmailRule;
use App\Rules\UniquePhoneWithCountryCodeRule;
use App\Rules\UniqueWorkEmailRule;
use App\Traits\QueriesHelper;
use App\Traits\StringLanguageValidation;
use App\Traits\V1\PhoneValidationTrait;
use App\Util\ScopeUtil;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class AddEmployeeProfileRequest extends FormRequest
{
    use PhoneValidationTrait, QueriesHelper, StringLanguageValidation;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    protected function prepareForValidation()
    {
        $data = $this->all();
        if (isset($data['phone'])) {
            $data['phone'] = $this->trimStringSpaces($data['phone']);
        }

        if (isset($data['secondary_phone'])) {
            $data['secondary_phone'] = $this->trimStringSpaces($data['secondary_phone']);
        }

        if (isset($data['emergency_contacts']) && is_array($data['emergency_contacts'])) {
            foreach ($data['emergency_contacts'] as $index => $contact) {
                if (isset($contact['phone'])) {
                    $data['emergency_contacts'][$index]['phone'] = $this->trimStringSpaces($contact['phone']);
                }
            }
        }

        $this->replace($data);
    }
    private function hasAnyEnglishNameField(): bool
    {
        return $this->filled('first_name_en') ||
               $this->filled('second_name_en') ||
               $this->filled('third_name_en') ||
               $this->filled('fourth_name_en') ||
               $this->filled('fifth_name_en');
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */

    public function rules()
    {
        $rules = [
            'first_name_ar' => ['required', 'string', 'max:50', new IsArabic],
            'second_name_ar' => ['required', 'string', 'max:50', new IsArabic],
            'third_name_ar' => ['required', 'string', 'max:50', new IsArabic],
            'fourth_name_ar' => ['nullable', 'string', 'max:50', new IsArabic],
            'fifth_name_ar' => ['nullable', 'string', 'max:50', new IsArabic],
            'first_name_en' => ['nullable', 'string', 'max:50', new IsEnglish],
            'second_name_en' => ['nullable', 'string', 'max:50', new IsEnglish],
            'third_name_en' => ['nullable', 'string', 'max:50', new IsEnglish],
            'fourth_name_en' => ['nullable', 'string', 'max:50', new IsEnglish],
            'fifth_name_en' => ['nullable', 'string', 'max:50', new IsEnglish],
            'employee_number' => ['required', 'string', 'max:50', new UniqueEmployeeNumberRule()],
            'gender' => ['required', new Enum(GenderEnum::class)],
            'phone_country_code' => 'required|string|starts_with:+',
            'phone' => [
                'required',
                new PhoneNumberByCountryRule($this->input('phone_country_code')),
                new UniquePhoneWithCountryCodeRule($this->input('phone_country_code'))
            ],
            'title_id' => [
                'required',
                'integer',
                new EntityIdRule('titles'),
            ],
            'role_id' => [
                'required',
                'integer',
                new EntityIdRule('spatie_roles'),
            ],

            'branch_id' => [
                'required',
                'integer',
                new EntityIdRule('branches'),
            ],
            'managed_department_ids' => 'nullable|array',
            'managed_department_ids.*' => [
                'integer',
                new EntityIdRule('departments'),
            ],
            'managed_sub_department_ids' => 'nullable|array',
            'managed_sub_department_ids.*' => [
                'integer',
                new EntityIdRule('sub_departments'),
            ],
            'managed_branch_ids' => 'nullable|array',
            'managed_branch_ids.*' => [
                'integer',
                new EntityIdRule('branches'),
            ],
            'is_trackable' => 'required|boolean',
            'nationality' => 'nullable|string|max:50',
            'national_id' => ['nullable', 'string', 'max:50', new NationalIdRule($this->input('nationality')), new UniqueNationalIdRule()],
            'passport_number' => ['nullable', 'string', 'max:50', new UniquePassportNumberRule()],
            'birth_date' => 'nullable|date_format:Y-m-d|before:' . now()->subYears(16)->format('Y-m-d'),
            'address' => 'nullable|string|max:255',
            'place_of_birth' => 'nullable|string|max:100',
            'religion' => ['nullable', new Enum(ReligionEnum::class)],
            'other_religion' => ['required_if:religion,' . ReligionEnum::OTHER->value, 'nullable', 'string', 'max:100'],
            'marital_status' => ['nullable', new Enum(MaritalStatusEnum::class)],
            'military_status' => ['nullable', new Enum(MilitaryStatus::class)],
            'number_kids' => 'nullable|integer|min:0',
            'notes' => 'nullable|string|max:1000',

            'secondary_phone_country_code' => 'nullable|required_with:secondary_phone|string|starts_with:+',
            'secondary_phone' => ['nullable'],
            'work_email' => ['nullable', 'email', new UniqueWorkEmailRule()],
            'personal_email' => ['nullable', 'email', new UniquePersonalEmailRule()],

            'join_date' => 'required|date_format:Y-m-d|before_or_equal:' . now()->addDays(30)->format('Y-m-d'),
            'employment_type' => ['nullable', new Enum(EmploymentTypeEnum::class)],
            'years_of_experience' => 'nullable|integer|min:0',
            'include_contract_details' => 'required|boolean',
            'contract_start_date_same_as_join_date' => 'required_if:include_contract_details,true|boolean',
            'contract_start_date' => ['nullable','date_format:Y-m-d', 'after_or_equal:join_date'],
            'contract_duration' => ['nullable', new Enum(ContractDurationEnum::class)],
            'contract_end_date' => ['nullable','date_format:Y-m-d','after:contract_start_date'],

            'emergency_contacts' => 'nullable|array',
            'emergency_contacts.*.name' => 'nullable|string|max:100',
            'emergency_contacts.*.phone_country_code' => 'nullable|required_with:emergency_contacts.*.phone|string|starts_with:+',
            'emergency_contacts.*.phone' => [
                'nullable',
                'required_with:emergency_contacts.*.name,emergency_contacts.*.relation',
                $this->validateEmergencyContactPhone('emergency_contacts')
            ],
            'emergency_contacts.*.relation' => 'nullable|string|max:100',

            'education' => 'nullable|array',
            'education.*.degree_type' => ['nullable', 'required_with:education.*.degree_name,education.*.institution_name,education.*.graduation_year', new Enum(DegreeTypeEnum::class)],
            'education.*.degree_name' => 'nullable|string|max:100',
            'education.*.institution_name' => 'nullable|string|max:100',
            'education.*.graduation_year' => 'nullable|integer|min:1950',
        ];

        $rules['emergency_contacts'] = [
            'nullable',
            'array',
            $this->validateUniqueEmergencyContacts('emergency_contacts')
        ];
        if ($this->input('secondary_phone_country_code')) {
            $rules['secondary_phone'][] = new PhoneNumberByCountryRule($this->input('secondary_phone_country_code'));
        }

        if ($this->hasAnyEnglishNameField()) {
            $rules['first_name_en'] = ['required', 'string', 'max:50', new IsEnglish];
            $rules['second_name_en'] = ['required', 'string', 'max:50', new IsEnglish];
            $rules['third_name_en'] = ['required', 'string', 'max:50', new IsEnglish];
        }

        
        $roleScope = null;
        if ($this->input('role_id')) {
            $roleScope = $this->getRoleHighestScopeKey($this->input('role_id'));
        }

        if (isset($roleScope) && $roleScope != ScopeUtil::ME_SCOPE && $roleScope != ScopeUtil::COMPANY_SCOPE) {

            if ($roleScope === ScopeUtil::DEPARTMENT_SCOPE) {
                $rules['managed_department_ids'] = ['required', 'array', 'min:1'];
            } elseif ($roleScope === ScopeUtil::SUB_DEPARTMENT_SCOPE) {
                $rules['managed_sub_department_ids'] = ['required', 'array', 'min:1'];
            }
            $rules['managed_branch_ids'] = ['required', 'array', 'min:1'];
        }

        if ($this->input('include_contract_details')) {
            $rules['contract_duration'][] = 'required';

            if ($this->input('contract_start_date_same_as_join_date') === false) {
                $rules['contract_start_date'][] = 'required';
            }

            if ($this->input('contract_duration') === ContractDurationEnum::CUSTOM->value) {
                $rules['contract_end_date'][] = 'required';
            }
        }
        return $rules;
    }
}
