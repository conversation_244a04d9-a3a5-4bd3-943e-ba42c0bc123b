<?php

namespace App\DomainData;

trait ScheduleDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'start_date' => 'required|date',
            'end_date' => 'required|date',
            'is_published' => 'required|boolean',
            'publish_date' => 'required|date',
            'branch_id' => 'required|numeric',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeScheduleDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
