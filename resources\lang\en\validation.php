<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => 'The :attribute must be accepted.',
    'accepted_if' => 'The :attribute must be accepted when :other is :value.',
    'active_url' => 'The :attribute is not a valid URL.',
    'after' => 'The :attribute must be a date after :date.',
    'after_or_equal' => 'The :attribute must be a date after or equal to :date.',
    'alpha' => 'The :attribute must only contain letters.',
    'alpha_dash' => 'The :attribute must only contain letters, numbers, dashes and underscores.',
    'alpha_num' => 'The :attribute must only contain letters and numbers.',
    'array' => 'The :attribute must be an array.',
    'before' => 'The :attribute must be a date before :date.',
    'before_or_equal' => 'The :attribute must be a date before or equal to :date.',
    'between' => [
        'array' => 'The :attribute must have between :min and :max items.',
        'file' => 'The :attribute must be between :min and :max kilobytes.',
        'numeric' => 'The :attribute must be between :min and :max.',
        'string' => 'The :attribute must be between :min and :max characters.',
    ],
    'boolean' => 'The :attribute field must be true or false.',
    'confirmed' => 'The :attribute confirmation does not match.',
    'current_password' => 'The password is incorrect.',
    'date' => 'The :attribute is not a valid date.',
    'date_equals' => 'The :attribute must be a date equal to :date.',
    'date_format' => 'The :attribute does not match the format :format.',
    'declined' => 'The :attribute must be declined.',
    'declined_if' => 'The :attribute must be declined when :other is :value.',
    'different' => 'The :attribute and :other must be different.',
    'digits' => 'The :attribute must be :digits digits.',
    'digits_between' => 'The :attribute must be between :min and :max digits.',
    'dimensions' => 'The :attribute has invalid image dimensions.',
    'distinct' => 'The :attribute field has a duplicate value.',
    'doesnt_end_with' => 'The :attribute may not end with one of the following: :values.',
    'doesnt_start_with' => 'The :attribute may not start with one of the following: :values.',
    'email' => 'The :attribute must be a valid email address.',
    'ends_with' => 'The :attribute must end with one of the following: :values.',
    'enum' => 'The selected :attribute is invalid.',
    'exists' => 'The selected :attribute is invalid.',
    'file' => 'The :attribute must be a file.',
    'filled' => 'The :attribute field must have a value.',
    'gt' => [
        'array' => 'The :attribute must have more than :value items.',
        'file' => 'The :attribute must be greater than :value kilobytes.',
        'numeric' => 'The :attribute must be greater than :value.',
        'string' => 'The :attribute must be greater than :value characters.',
    ],
    'gte' => [
        'array' => 'The :attribute must have :value items or more.',
        'file' => 'The :attribute must be greater than or equal to :value kilobytes.',
        'numeric' => 'The :attribute must be greater than or equal to :value.',
        'string' => 'The :attribute must be greater than or equal to :value characters.',
    ],
    'image' => 'The :attribute must be an image.',
    'in' => 'The selected :attribute is invalid.',
    'in_array' => 'The :attribute field does not exist in :other.',
    'integer' => 'The :attribute must be an integer.',
    'ip' => 'The :attribute must be a valid IP address.',
    'ipv4' => 'The :attribute must be a valid IPv4 address.',
    'ipv6' => 'The :attribute must be a valid IPv6 address.',
    'json' => 'The :attribute must be a valid JSON string.',
    'lt' => [
        'array' => 'The :attribute must have less than :value items.',
        'file' => 'The :attribute must be less than :value kilobytes.',
        'numeric' => 'The :attribute must be less than :value.',
        'string' => 'The :attribute must be less than :value characters.',
    ],
    'lte' => [
        'array' => 'The :attribute must not have more than :value items.',
        'file' => 'The :attribute must be less than or equal to :value kilobytes.',
        'numeric' => 'The :attribute must be less than or equal to :value.',
        'string' => 'The :attribute must be less than or equal to :value characters.',
    ],
    'mac_address' => 'The :attribute must be a valid MAC address.',
    'max' => [
        'array' => 'The :attribute must not have more than :max items.',
        'file' => 'The :attribute must not be greater than :max kilobytes.',
        'numeric' => 'The :attribute must not be greater than :max.',
        'string' => 'The :attribute must not be greater than :max characters.',
    ],
    'max_digits' => 'The :attribute must not have more than :max digits.',
    'mimes' => 'The :attribute must be a file of type: :values.',
    'mimetypes' => 'The :attribute must be a file of type: :values.',
    'min' => [
        'array' => 'The :attribute must have at least :min items.',
        'file' => 'The :attribute must be at least :min kilobytes.',
        'numeric' => 'The :attribute must be at least :min.',
        'string' => 'The :attribute must be at least :min characters.',
    ],
    'min_digits' => 'The :attribute must have at least :min digits.',
    'multiple_of' => 'The :attribute must be a multiple of :value.',
    'not_in' => 'The selected :attribute is invalid.',
    'not_regex' => 'The :attribute format is invalid.',
    'numeric' => 'The :attribute must be a number.',
    'password' => [
        'letters' => 'The :attribute must contain at least one letter.',
        'mixed' => 'The :attribute must contain at least one uppercase and one lowercase letter.',
        'numbers' => 'The :attribute must contain at least one number.',
        'symbols' => 'The :attribute must contain at least one symbol.',
        'uncompromised' => 'The given :attribute has appeared in a data leak. Please choose a different :attribute.',
    ],
    'present' => 'The :attribute field must be present.',
    'prohibited' => 'The :attribute field is prohibited.',
    'prohibited_if' => 'The :attribute field is prohibited when :other is :value.',
    'prohibited_unless' => 'The :attribute field is prohibited unless :other is in :values.',
    'prohibits' => 'The :attribute field prohibits :other from being present.',
    'regex' => 'The :attribute format is invalid.',
    'required' => 'The :attribute field is required.',
    'required_array_keys' => 'The :attribute field must contain entries for: :values.',
    'required_if' => 'The :attribute field is required when :other is :value.',
    'required_unless' => 'The :attribute field is required unless :other is in :values.',
    'required_with' => 'The :attribute field is required when :values is present.',
    'required_with_all' => 'The :attribute field is required when :values are present.',
    'required_without' => 'The :attribute field is required when :values is not present.',
    'required_without_all' => 'The :attribute field is required when none of :values are present.',
    'same' => 'The :attribute and :other must match.',
    'size' => [
        'array' => 'The :attribute must contain :size items.',
        'file' => 'The :attribute must be :size kilobytes.',
        'numeric' => 'The :attribute must be :size.',
        'string' => 'The :attribute must be :size characters.',
    ],
    'starts_with' => 'The :attribute must start with one of the following: :values.',
    'string' => 'The :attribute must be a string.',
    'timezone' => 'The :attribute must be a valid timezone.',
    'unique' => 'The :attribute has already been taken.',
    'uploaded' => 'The :attribute failed to upload.',
    'url' => 'The :attribute must be a valid URL.',
    'uuid' => 'The :attribute must be a valid UUID.',

    'unique_phone' => 'The phone number with this country code already exists in the system.',
    'phone_number_format' => 'The :attribute must be a valid phone number for country code \':country_code\'.',
    'duplicate_emergency_contact' => 'Duplicate emergency contact phone number found: :phone. Each emergency contact must have a unique phone number.',
    'is_arabic' => 'The :attribute field must be in Arabic.',
    'is_english' => 'The :attribute field must be in English.',
    'egyptian_national_id' => 'For Egyptian nationality, the national ID must be exactly 14 digits.',
    'title_id' => 'The :attribute must be "all" or a valid title ID for your company.',
    'branch_id' => 'The :attribute must be "all" or a valid branch ID for your company.',
    'spatie_roles_id' => 'The :attribute must be a valid role ID for your company.',
    'unique_employee_number' => 'The employee number already exists in the system.',
    'unique_national_id' => 'The national ID already exists in the system.',
    'unique_passport_number' => 'The passport number already exists in the system.',
    'unique_work_email' => 'The work email already exists in the system.',
    'unique_personal_email' => 'The personal email already exists in the system.',
    'department_id' => 'The :attribute must be a valid department ID for your company.',
    'sub_department_id' => 'The :attribute must be "all" or a valid sub-department ID for your company.',
    'emergency_contact_id' => 'The :attribute must be a valid emergency contact ID for this employee.',
    'education_id' => 'The :attribute must be a valid education record ID for this employee.',
    'contract_id' => 'The :attribute must be a valid contract ID for this employee.',

    'titles_id' => 'The :attribute must be a valid title ID for your company.',
    'branches_id' => 'The :attribute must be a valid branch ID for your company.',
    'departments_id' => 'The :attribute must be a valid department ID for your company.',
    'sub_departments_id' => 'The :attribute must be a valid sub-department ID for your company.',
    'employee_emergency_contacts_id' => 'The :attribute must be a valid emergency contact ID for this employee.',
    'employee_contracts_id' => 'The :attribute must be a valid contract ID for this employee.',
    'employee_education_id' => 'The :attribute must be a valid education record ID for this employee.',

    'template_name_required' => 'Template name is required',
    'template_name_exists' => 'Template name already exists. Please choose a different name.',
    'category_fields_required' => 'Fields for :category are required',
    'category_fields_array' => 'Fields for :category must be an array',
    'field_required' => 'Field :field in :category is required',
    'field_boolean' => 'Field :field in :category must be a boolean',
    'category_all_fields_required_when_any_selected' => 'When any field in :category is selected, all fields in this category must be selected. Please select :field or deselect all fields in :category.',
    'file_size_too_large' => 'File size is too large, maximum allowed size is :max',
    'file_empty' => 'The uploaded file is empty',
    'no_data_rows' => 'No data rows found in the file',
    'edit_operation_not_implemented' => 'Edit operation is not yet implemented',
    'duplicate_national_id_in_excel' => 'Duplicate national ID found in Excel file: :national_id',
    'duplicate_phone_in_excel' => 'Duplicate phone number found in Excel file: :phone',
    'duplicate_employee_code_in_excel' => 'Duplicate employee code found in Excel file: :employee_code',
    'duplicate_passport_number_in_excel' => 'Duplicate passport number found in Excel file: :passport_number',
    'duplicate_work_email_in_excel' => 'Duplicate work email found in Excel file: :work_email',
    'duplicate_personal_email_in_excel' => 'Duplicate personal email found in Excel file: :personal_email',
    'primary_phone_required_for_bulk_edit' => 'Primary phone number is required for bulk edit operations',
    'employee_not_found_with_phone' => 'No employee found with phone number: :phone',
    'invalid_operation_type' => 'Invalid operation type specified',
    'at_row_number' => 'at row number# :row',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'birth_date' => [
            'before' => 'The :attribute must be a date before :date (employee must be at least 16 years old).',
        ],
        'years_of_experience' => [
            'min' => 'The :attribute must be at least :min.',
        ],
        'contract_start_date' => [
            'required' => 'The :attribute field is required.',
            'after_or_equal' => 'The :attribute must be a date after or equal to the join date.',
        ],
        'contract_duration' => [
            'required' => 'The :attribute field is required.',
        ],

        'contract_details.join_date' => [
            'required' => 'The :attribute field is required.',
        ],
        'contract_details.contracts.items.*.contract_start_date' => [
            'required' => 'The :attribute field is required.',
            'after_or_equal' => 'The :attribute must be a date after or equal to the join date.',
            'after' => 'The :attribute must be a date after the previous contract end date.',
        ],
        'contract_details.contracts.items.*.contract_duration' => [
            'required' => 'The :attribute field is required.',
        ],
        'contract_details.contracts.items.*.contract_end_date' => [
            'required_if' => 'The :attribute field is required when contract duration is custom.',
            'after' => 'The :attribute must be a date after the contract start date.',
        ],
        'contract_details.contracts.items.*' => [
            'prohibited' => 'No contracts are allowed after an open contract.',
        ],


        'emergency_contacts.0.phone' => [
            'required_with' => 'The :attribute field is required when emergency contact name or emergency contact relation is present.',
        ],
        'education.0.degree_type' => [
            'required_with' => 'The :attribute field is required when degree name or institution name or graduation year is present.',
        ],

        'include_contract_details' => [
            'required' => 'The :attribute field is required.',
        ],
        'contract_start_date_same_as_join_date' => [
            'required_if' => 'The :attribute field is required when include contract details is enabled.',
        ],

        'contract_end_date' => [
            'required' => 'The :attribute field is required.',
            'after' => 'The :attribute must be a date after the contract start date.',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [
        'birth_date' => 'birth date',
        'years_of_experience' => 'years of experience',
        'phone' => 'phone number',
        'national_id' => 'national ID',
        'passport_number' => 'passport number',
        'work_email' => 'work email',
        'personal_email' => 'personal email',
        'employee_number' => 'employee number',
        'join_date' => 'join date',
        'rehire_date' => 'rehire date',
        'contract_start_date' => 'contract start date',
        'contract_duration' => 'contract duration',
        'contract_end_date' => 'contract end date',

        'basic_info.first_name_ar' => 'first name (Arabic)',
        'basic_info.second_name_ar' => 'second name (Arabic)',
        'basic_info.third_name_ar' => 'third name (Arabic)',
        'basic_info.fourth_name_ar' => 'fourth name (Arabic)',
        'basic_info.fifth_name_ar' => 'fifth name (Arabic)',
        'basic_info.first_name_en' => 'first name (English)',
        'basic_info.second_name_en' => 'second name (English)',
        'basic_info.third_name_en' => 'third name (English)',
        'basic_info.fourth_name_en' => 'fourth name (English)',
        'basic_info.fifth_name_en' => 'fifth name (English)',
        'basic_info.employee_number' => 'employee number',
        'basic_info.rehire_date' => 'rehire date',
        'basic_info.is_trackable' => 'trackable',

        'personal_details.nationality' => 'nationality',
        'personal_details.national_id' => 'national ID',
        'personal_details.birth_date' => 'birth date',
        'personal_details.passport_number' => 'passport number',
        'personal_details.gender' => 'gender',
        'personal_details.address' => 'address',
        'personal_details.place_of_birth' => 'place of birth',
        'personal_details.religion' => 'religion',
        'personal_details.other_religion' => 'other religion',
        'personal_details.marital_status' => 'marital status',
        'personal_details.military_status' => 'military status',
        'personal_details.number_kids' => 'number of children',
        'personal_details.years_of_experience' => 'years of experience',
        'personal_details.notes' => 'notes',

        'contact_details.phone_country_code' => 'phone country code',
        'contact_details.phone' => 'phone number',
        'contact_details.secondary_phone_country_code' => 'secondary phone country code',
        'contact_details.secondary_phone' => 'secondary phone number',
        'contact_details.work_email' => 'work email',
        'contact_details.personal_email' => 'personal email',

        'contract_details.join_date' => 'join date',
        'contract_details.employment_type' => 'employment type',
        'contract_details.contracts.items.*.contract_start_date' => 'contract start date',
        'contract_details.contracts.items.*.contract_duration' => 'contract duration',
        'contract_details.contracts.items.*.contract_end_date' => 'contract end date',

        'emergency_contacts.0.phone' => 'emergency contact phone',
        'emergency_contacts.0.name' => 'emergency contact name',
        'emergency_contacts.0.relation' => 'emergency contact relation',
        'contact_details.emergency_contacts.items.*.phone' => 'emergency contact phone',
        'contact_details.emergency_contacts.items.*.name' => 'emergency contact name',
        'contact_details.emergency_contacts.items.*.relation' => 'emergency contact relation',

        'education.0.degree_type' => 'degree type',
        'education.0.degree_name' => 'degree name',
        'education.0.institution_name' => 'institution name',
        'education.0.graduation_year' => 'graduation year',
        'education.items.*.degree_type' => 'degree type',
        'education.items.*.degree_name' => 'degree name',
        'education.items.*.institution_name' => 'institution name',
        'education.items.*.graduation_year' => 'graduation year',

        'first_name_ar' => 'first name (Arabic)',
        'second_name_ar' => 'second name (Arabic)',
        'third_name_ar' => 'third name (Arabic)',
        'fourth_name_ar' => 'fourth name (Arabic)',
        'fifth_name_ar' => 'fifth name (Arabic)',
        'first_name_en' => 'first name (English)',
        'second_name_en' => 'second name (English)',
        'third_name_en' => 'third name (English)',
        'fourth_name_en' => 'fourth name (English)',
        'fifth_name_en' => 'fifth name (English)',
        'gender' => 'gender',
        'phone_country_code' => 'phone country code',
        'title_id' => 'job title',
        'role_id' => 'role',
        'branch_id' => 'branch',
        'managed_department_ids' => 'managed departments',
        'managed_sub_department_ids' => 'managed sub departments',
        'managed_branch_ids' => 'managed branches',
        'nationality' => 'nationality',
        'address' => 'address',
        'place_of_birth' => 'place of birth',
        'religion' => 'religion',
        'other_religion' => 'other religion',
        'marital_status' => 'marital status',
        'military_status' => 'military status',
        'number_kids' => 'number of children',
        'notes' => 'notes',
        'secondary_phone_country_code' => 'secondary phone country code',
        'secondary_phone' => 'secondary phone number',

        'include_contract_details' => 'include contract details',
        'contract_start_date_same_as_join_date' => 'contract start date same as join date',

        'emergency_contacts.*.phone' => 'emergency contact phone',
        'emergency_contacts.*.name' => 'emergency contact name',
        'emergency_contacts.*.relation' => 'emergency contact relation',
        'emergency_contacts.*.phone_country_code' => 'emergency contact phone country code',
        'contact_details.emergency_contacts.items.*.phone_country_code' => 'emergency contact phone country code',
        'education.*.degree_type' => 'degree type',
        'education.*.degree_name' => 'degree name',
        'education.*.institution_name' => 'institution name',
        'education.*.graduation_year' => 'graduation year',

        'management_scope.managed_department_ids' => 'managed departments',
        'management_scope.managed_sub_department_ids' => 'managed sub departments',
        'management_scope.managed_branch_ids' => 'managed branches',
        'management_scope.managed_department_ids.*' => 'managed department',
        'management_scope.managed_sub_department_ids.*' => 'managed sub department',
        'management_scope.managed_branch_ids.*' => 'managed branch',

        'contract_details.contracts.items' => 'employee contracts',
        'contract_details.contracts.delete_ids' => 'contracts to delete',
        'education.items' => 'education records',
        'education.delete_ids' => 'education records to delete',
        'contact_details.emergency_contacts.items' => 'emergency contacts',
        'contact_details.emergency_contacts.delete_ids' => 'emergency contacts to delete',

        'is_trackable' => 'trackable',
        'employment_type' => 'employment type',
        'emergency_contact_name' => 'emergency contact name',
        'emergency_contact_phone' => 'emergency contact phone',
        'emergency_contact_relation' => 'emergency contact relation',
    ],

];
