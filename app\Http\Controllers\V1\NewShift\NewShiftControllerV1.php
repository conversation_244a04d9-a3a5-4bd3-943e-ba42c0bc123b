<?php

namespace App\Http\Controllers\V1\NewShift;

use App\Http\Controllers\NewController;
use App\Services\V1\Attendance\NewShiftsService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Foundation\Http\FormRequest;

class NewShiftControllerV1 extends NewController
{
    public function __construct(
        NewShiftsService $service
    ) {
        parent::__construct($service);

    }

    public function getRunningShifts(FormRequest $request)
    {
        $data = $request;

        $shifts = $this->service->getRunningShifts($data);

        return getResponseStructure(['data' => $shifts,

        ],
            HttpStatusCodeUtil::OK, 'Shifts that employee can take action on ');
    }
}
