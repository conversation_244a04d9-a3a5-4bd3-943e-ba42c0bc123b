<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\EmployeeLeaveBalance;
use App\Models\EmployeeLeaveRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ReturnDeletedLeaves extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:return-deleted-leaves {company_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $this->info('Starting script');
            $companyId = $this->argument('company_id');
            // $employeeLeaveRequests = EmployeeLeaveRequest::whereDate('from', '>=', '2025-01-01')
            //     ->where('deleted_at', '!=', null)
            //     ->whereHas('employee', function ($query) use ($companyId) {
            //         $query->where('company_id', $companyId);
            //     })
            //     ->withTrashed()
            //     ->get();

            // foreach ($employeeLeaveRequests as $employeeLeaveRequest) {
            //     $employeeLeaveRequest->restore();
            // }

            EmployeeLeaveBalance::where('company_leave_type_id', 575)
                ->whereHas('employee', function ($query) use ($companyId) {
                    $query->where('company_id', $companyId);
                })
                ->update(['balance' => 15]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error: '.$e->getMessage());
        }
    }
}
