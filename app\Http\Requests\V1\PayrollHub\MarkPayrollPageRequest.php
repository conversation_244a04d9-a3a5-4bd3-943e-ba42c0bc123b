<?php

namespace App\Http\Requests\V1\PayrollHub;

use Illuminate\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class MarkPayrollPageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'section' => [
                'required',
                'string',
                'in:employee_changes,attendance,additions,deductions,loans'
            ],
            'tab' => [
                'required',
                'string',
                'in:deductions,absences,leave_compensation,missing_salary,pending_requests,leaves,missions,unaccounted_days,penalties,overtime,extra_work_day,public_holiday_compensation,loans'
            ],
            'updated_value' => [
                'required',
                'boolean',
            ],
        ];
    }

    public function withValidator(Validator $validator): void
    {
        $validator->after(function ($validator) {
            $section = $this->input('section');
            $tab = $this->input('tab');

            $validTabs = [
                'employee_changes' => ['pending_requests', 'missing_salary'],
                'attendance' => ['absences', 'unaccounted_days', 'missions', 'leaves'],
                'additions' => ['overtime', 'extra_work_day', 'public_holiday_compensation', 'leave_compensation'],
                'deductions' => ['deductions', 'penalties'],
                'loans' => ['loans'],
            ];

            if (isset($validTabs[$section]) && !in_array($tab, $validTabs[$section])) {
                $validator->errors()->add('tab', "Invalid tab '{$tab}' for section '{$section}");
            }
        });
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'section' => $this->route('section'),
            'tab' => $this->route('tab'),
        ]);
    }
}
