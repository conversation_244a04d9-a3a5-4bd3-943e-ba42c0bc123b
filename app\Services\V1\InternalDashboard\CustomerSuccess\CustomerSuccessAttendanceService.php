<?php

namespace App\Services\V1\InternalDashboard\CustomerSuccess;

use App\Enums\V2\WorkTypes\RestDaysTypesEnum;
use App\Exceptions\UnprocessableException;
use App\FeatureToggles\Unleash;
use App\Repositories\NewCompanyLeaveTypeRepository;
use App\Repositories\NewCompanyRepository;
use App\Repositories\NewDepartmentRepository;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\NewTitleRepository;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\V1\Attendance\AttendanceDeductionRepository;
use App\Repositories\V1\Attendance\AttendanceRepository;
use App\Repositories\V1\Attendance\CicoRepository;
use App\Repositories\V1\Attendance\TimecardRepository;
use App\Repositories\V1\AttendanceOvertimeRepository;
use App\Repositories\V1\BranchRepository;
use App\Repositories\V1\EmployeeChangeRepository;
use App\Repositories\V1\EmployeeRequestRepository;
use App\Repositories\V1\EntityTagRepository;
use App\Repositories\V1\ExtraWorkdayRequestRepository;
use App\Repositories\V1\Holidays\PublicHolidaysRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveBalancesRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use App\Repositories\V1\Schedule\TimecardTypeRepository;
use App\Services\BaseService;
use App\Traits\V1\ShiftsValidationTrait;
use App\Traits\WorkflowTrait;
use App\Util\PayrollUtil;
use App\Util\UserWorkTypesUtil;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\Log;
use App\Jobs\AssignApprovalsJob;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;

class CustomerSuccessAttendanceService extends BaseService
{
    use ShiftsValidationTrait, WorkflowTrait, PrepareAssignRequestCycleDataTrait;

    public array $errorsArray = [];

    public function __construct(
        protected NewEmployeeRepository $newEmployeeRepository,
        protected PublicHolidaysRepository $publicHolidaysRepository,
        protected TimecardRepository $timecardRepository,
        protected AttendanceRepository $attendanceRepository,
        protected EntityTagRepository $entityTagRepository,
        protected EmployeeLeaveRequestRepository $employeeLeaveRequestRepository,
        protected EmployeeLeaveBalancesRepository $employeeLeaveBalancesRepository,
        protected CicoRepository $cicoRepository,
        protected AttendanceDeductionRepository $attendanceDeductionRepository,
        protected AttendanceOvertimeRepository $attendanceOvertimeRepository,
        protected BranchRepository $branchRepository,
        protected TimecardTypeRepository $timecardTypeRepository,
        protected EmployeeRequestRepository $employeeRequestRepository,
        protected ExtraWorkdayRequestRepository $extraWorkdayRequestRepository,
        protected EmployeeChangeRepository $employeeChangeRepository,
        protected NewDepartmentRepository $departmentRepository,
        protected NewTitleRepository $titleRepository,
        protected PayrollsRepository $payrollRepository,
        protected NewCompanyLeaveTypeRepository $companyLeaveTypeRepository,
        protected SystemSettingRepository $systemSettingRepository,
        protected NewCompanyRepository $companyRepository,
    ) {}

    public function getAttendanceDetails($from, $to, $company_id, $branch_id)
    {
        $data = $this->newEmployeeRepository->getEmployeesAttendanceDetails($from, $to, $company_id, $branch_id);

        return $this->prepareAttendanceResponse($data, $from, $to);
    }

    private function prepareAttendanceResponse($data, $from, $to)
    {
        $dateObj = [
            'start_date' => $from,
            'end_date' => $to,
        ];
        $publicHolidays = $this->publicHolidaysRepository->getHolidaysMonthlyView($dateObj);

        // Get all period dates
        $periodDates = collect(CarbonPeriod::create($from, $to))->map(fn ($date) => $date->format('Y-m-d'));

        foreach ($data as $employee) {
            $employee->has_scheduled_transfer = $this->hasScheduledTransfer($employee->id, $from);

            // Prepare the different arrays
            $attendance = $this->prepareAttendance($employee);
            $restDays = $this->prepareRestDays($employee);
            $leaves = $this->prepareLeaves($employee);
            $absentWithPermission = $this->prepareAbsentWithPermission($employee);
            $absentWithoutPermission = $this->prepareAbsentWithoutPermission($employee);
            $publicHolidayDays = $this->preparePublicHolidays($employee, $publicHolidays);
            $pendingRequests = $this->preparePendingRequests($employee);

            // Create collections of dates from different arrays
            $presentDays = collect($attendance['data'])->pluck('date');
            $restDayDates = collect($restDays['data'])->pluck('date');

            // Flatten approved leave days into one collection
            $leaveDays = collect();
            foreach ($employee->employeeLeaveRequests as $leave) {
                if ($leave->status != 'approved') {
                    continue;
                }
                $startDate = Carbon::parse($leave->from)->startOfDay();
                $endDate = Carbon::parse($leave->to)->startOfDay();
                $diffInDays = $startDate->diffInDays($endDate);

                for ($i = 0; $i <= $diffInDays; $i++) {
                    $leaveDays->push($startDate->copy()->addDays($i)->format('Y-m-d'));
                }
            }
            $leaveDays = $leaveDays->unique();

            $absentWithPermissionDates = collect($absentWithPermission['data'])->pluck('date');
            $absentWithoutPermissionDates = collect($absentWithoutPermission['data'])->pluck('date');

            $publicHolidayDates = collect($publicHolidayDays['data']);
            $offBranchDates = collect($pendingRequests['data'])
                ->filter(fn ($request) => $request['key'] === 'off-branch')
                ->pluck('date');
            $pendingLeaveDates = collect($pendingRequests['data'])
                ->filter(fn ($request) => $request['key'] === 'leave')
                ->flatMap(fn ($request) => CarbonPeriod::create($request['from'], $request['to']))
                ->map(fn ($date) => $date->format('Y-m-d'));
            $missionDates = collect($pendingRequests['data'])
                ->filter(fn ($request) => $request['key'] === 'mission')
                ->pluck('date');

            // STEP 1: Mark attendance/public holiday collisions
            foreach ($attendance['data'] as &$att) {
                if ($publicHolidayDates->contains($att['date'])) {
                    $att['is_public_holiday'] = true;
                    $publicHolidayDates = $publicHolidayDates->reject(fn ($d) => $d === $att['date']);
                }
            }
            foreach ($absentWithPermission['data'] as &$abs) {
                if ($publicHolidayDates->contains($abs['date'])) {
                    $abs['is_public_holiday'] = true;
                    $publicHolidayDates = $publicHolidayDates->reject(fn ($d) => $d === $abs['date']);
                }
            }
            foreach ($absentWithoutPermission['data'] as &$abs) {
                if ($publicHolidayDates->contains($abs['date'])) {
                    $abs['is_public_holiday'] = true;
                    $publicHolidayDates = $publicHolidayDates->reject(fn ($d) => $d === $abs['date']);
                }
            }

            // STEP 2: Update the public holiday array with remaining unclaimed public holiday dates
            $publicHolidayDays['data'] = $publicHolidayDates->values()->all();
            $publicHolidayDays['count'] = count($publicHolidayDays['data']);

            // STEP 3: Remove pending requests' dates from absent arrays and update counts
            $absentWithPermission['data'] = collect($absentWithPermission['data'])
                ->reject(fn ($item) => $offBranchDates->contains($item['date'])
                    || $pendingLeaveDates->contains($item['date'])
                    || $missionDates->contains($item['date'])
                )
                ->values()
                ->all();
            $absentWithPermission['count'] = count($absentWithPermission['data']);

            $absentWithoutPermission['data'] = collect($absentWithoutPermission['data'])
                ->reject(fn ($item) => $offBranchDates->contains($item['date'])
                    || $pendingLeaveDates->contains($item['date'])
                    || $missionDates->contains($item['date'])
                )
                ->values()
                ->all();
            $absentWithoutPermission['count'] = count($absentWithoutPermission['data']);

            // STEP 4: Merge all accounted dates
            $accountedDates = collect()
                ->merge($presentDays)
                ->merge($restDayDates)
                ->merge($leaveDays)
                ->merge(collect($absentWithPermission['data'])->pluck('date'))
                ->merge(collect($absentWithoutPermission['data'])->pluck('date'))
                ->merge($publicHolidayDays['data'])
                ->merge($offBranchDates)
                ->merge($pendingLeaveDates)
                ->merge($missionDates)
                ->unique();

            $joinDate = Carbon::parse($employee->employeeInfo->join_date);
            $terminationDate = isset($employee->employeeInfo->termination_date)
                ? Carbon::parse($employee->employeeInfo->termination_date)
                : null;

            // STEP 5: Determine unaccounted days
            $unaccountedDays = $periodDates->diff($accountedDates)->filter(function ($date) use ($joinDate, $terminationDate) {
                $date = Carbon::parse($date);

                return $date->greaterThanOrEqualTo($joinDate)
                    && ($terminationDate === null || $date->lessThanOrEqualTo($terminationDate))
                    && $date->lessThanOrEqualTo(now()->toDateString());
            })->values();

            // Final response array
            $employee->attendance_info = [
                'present' => $attendance,
                'rest_day' => $restDays,
                'leave' => $leaves,
                'absent_with_permission' => $absentWithPermission,
                'absent_without_permission' => $absentWithoutPermission,
                'overtime' => $this->prepareOvertime($employee),
                'deductions' => $this->prepareDeductions($employee),
                'public_holidays' => $publicHolidayDays,
                'pending_requests' => $pendingRequests,
                'unaccounted_days' => [
                    'count' => $unaccountedDays->count(),
                    'data' => $unaccountedDays,
                ],
            ];
        }

        return $data;
    }

    private function prepareAttendance($employee)
    {
        $data = [];
        foreach ($employee->attendance as $attendance) {
            $data[] = [
                'date' => $attendance->date,
                'branch' => [
                    'id' => $attendance->branch_id,
                    'name' => optional($attendance->branch)->name,
                ],
                'overtime_hours' => $attendance->attendanceOvertimes->first() !== null ? $attendance->attendanceOvertimes->first()->overtime_minutes / 60 : 0,
                'deducted_days' => $attendance->attendanceDeductions->first() !== null ? $attendance->attendanceDeductions->first()->updated_vale ?? $attendance->attendanceDeductions->first()->deduction_value : 0,
                'by_admin' => isset($attendance->clockIn?->by_admin),
            ];
        }

        return [
            'count' => count($data),
            'data' => $data,
        ];
    }

    private function prepareRestDays($employee)
    {
        $dates = [];
        foreach ($employee->employeeLeaveRequests as $leave) {
            if ($leave->companyLeaveType->id == $employee->company->restDayLeaveType->id) {
                $dates[] = [
                    'date' => Carbon::parse($leave->from)->format('Y-m-d'),
                    'by_admin' => isset($leave->by_admin),
                ];
            }
        }

        return [
            'count' => count($dates),
            'data' => $dates,
        ];
    }

    private function prepareLeaves($employee)
    {
        $data = [];
        foreach ($employee->employeeLeaveRequests as $leave) {
            if ($leave->companyLeaveType->id != $employee->company->restDayLeaveType->id && $leave->status == 'approved') {
                $data[] = [
                    'from' => Carbon::parse($leave->from)->format('Y-m-d'),
                    'to' => Carbon::parse($leave->to)->format('Y-m-d'),
                    'leave_type_id' => $leave->companyLeaveType->id,
                    'by_admin' => isset($leave->by_admin),
                ];
            }
        }

        return [
            'count' => count($data),
            'data' => $data,
        ];
    }

    private function prepareAbsentWithPermission($employee)
    {
        $dates = [];

        foreach ($employee->timecards as $timecard) {
            if ($timecard->entityTags->pluck('tag')->contains('absent')) {
                $dates[] = [
                    'date' => Carbon::parse($timecard->from)->format('Y-m-d'),
                    'by_admin' => true,
                ];
            }
        }

        return [
            'count' => count($dates),
            'data' => $dates,
        ];
    }

    private function prepareAbsentWithoutPermission($employee)
    {
        $dates = [];
        foreach ($employee->timecards as $timecard) {
            if ($timecard->entityTags->pluck('tag')->contains('absent_without_permission')) {
                $dates[] = [
                    'date' => Carbon::parse($timecard->from)->format('Y-m-d'),
                    'by_admin' => isset($timecard->by_admin),
                ];
            }
        }

        return [
            'count' => count($dates),
            'data' => $dates,
        ];
    }

    public function preparePendingRequests($employee)
    {
        $pendingRequests = [];

        // Handle mission requests
        foreach ($employee->missionRequests as $mission) {
            if ($mission->status === 'pending') {
                $pendingRequests[] = [
                    'key' => 'mission',
                    'date' => Carbon::parse($mission->from)->format('Y-m-d'),
                ];
            }
        }

        // Handle leave requests
        foreach ($employee->employeeLeaveRequests as $leave) {
            if ($leave->status == 'pending') {
                $pendingRequests[] = [
                    'key' => 'leave',
                    'from' => Carbon::parse($leave->from)->format('Y-m-d'),
                    'to' => Carbon::parse($leave->to)->format('Y-m-d'),
                ];
            }
        }

        // Handle unverified cicos (off-branch)
        $unverifiedDates = collect($employee->cicos)
            ->pluck('date')
            ->map(function ($date) {
                return Carbon::parse($date)->format('Y-m-d');
            })
            ->unique()
            ->values();

        foreach ($unverifiedDates as $date) {
            $pendingRequests[] = [
                'key' => 'off-branch',
                'date' => $date,
            ];
        }

        return [
            'count' => count($pendingRequests),
            'data' => $pendingRequests,
        ];
    }

    private function prepareOvertime($employee)
    {
        $count = 0;
        foreach ($employee->attendance as $attendance) {
            if ($attendance->attendanceOvertimes->first() !== null) {
                $count += $attendance->attendanceOvertimes->first()->overtime_minutes / 60 ?? 0;
            }
        }

        return [
            'count' => round($count, 2),
            'data' => [],
        ];
    }

    private function prepareDeductions($employee)
    {
        $count = 0;
        foreach ($employee->attendance as $attendance) {
            if ($attendance->attendanceDeductions->first() !== null) {
                $count += $attendance->attendanceDeductions->first()->updated_vale ??
                    $attendance->attendanceDeductions->first()->deduction_value ?? 0;
            }
        }

        return [
            'count' => $count,
            'data' => [],
        ];
    }

    private function preparePublicHolidays($employee, $publicHolidays)
    {
        $count = 0;
        $dates = [];
        $today = Carbon::today();

        foreach ($publicHolidays as $holiday) {
            $holidayStart = Carbon::parse($holiday->start);
            $holidayEnd = Carbon::parse($holiday->end);
            $effectiveEnd = $holidayEnd->greaterThan($today) ? $today : $holidayEnd;
            if ($holidayStart->greaterThan($effectiveEnd)) {
                continue;
            }
            $holidayDates = $this->getDatesBetween($holidayStart->format('Y-m-d'), $effectiveEnd->format('Y-m-d'));

            foreach ($holidayDates as $holidayDate) {
                if (! $employee->timecards->contains('date', $holidayDate)) {
                    $count++;
                    $dates[] = $holidayDate;
                }
            }
        }

        return [
            'count' => $count,
            'data' => $dates,
        ];
    }

    private function getDatesBetween($startDate, $endDate)
    {
        $dates = [];
        $currentDate = strtotime($startDate);
        $end = strtotime($endDate);

        while ($currentDate <= $end) {
            $dates[] = date('Y-m-d', $currentDate);
            $currentDate = strtotime('+1 day', $currentDate);
        }

        return $dates;
    }

    public function handleAttendanceInfoData($data)
    {
        $preparedData = $this->prepareAttendanceData($data);
        $from = $data['from'];
        $to = $data['to'];

        $this->deleteAttendanceData($preparedData['to_delete'], $from, $to);

        $this->addAttendanceData($preparedData['to_add'], $from, $to);

        return [
            'message' => 'Attendance info processed successfully',
            'errors' => $this->errorsArray,
        ];
    }

    private function prepareAttendanceData($data)
    {
        $uniqueResult = $this->checkDateUniqueness($data);
        if (! $uniqueResult['status']) {
            throw new UnprocessableException($uniqueResult['message']);
        }

        $from = $data['from'];
        $to = $data['to'];

        $comparison = $this->compareAttendanceData($data, $from, $to);

        return $comparison;
    }

    private function checkDateUniqueness($data)
    {
        $employeeDateMap = [];

        foreach ($data['attendance_records'] as $record) {
            $employeeId = $record['employee_id'];
            $employee = $this->newEmployeeRepository->find($employeeId);
            if (! isset($employeeDateMap[$employeeId])) {
                $employeeDateMap[$employeeId] = [];
            }

            foreach ($record['attendance_info'] as $category => $attendanceCategory) {
                if ($category == 'present' || $category == 'absent_with_permission' || $category == 'absent_without_permission') {
                    continue;
                }
                if (isset($attendanceCategory['data']) && is_array($attendanceCategory['data'])) {
                    foreach ($attendanceCategory['data'] as $entry) {
                        $date = $entry['date'] ?? null;

                        if ($date) {
                            if (isset($employeeDateMap[$employeeId][$date])) {
                                return [
                                    'status' => false,
                                    'message' => "Duplicate date found for employee ID {$employee->employee_number} on {$date} in category {$category}.",
                                ];
                            }
                            $employeeDateMap[$employeeId][$date] = true;
                        }
                    }
                }
            }
        }

        return ['status' => true];
    }

    /**
     * Compare new requested attendance data with existing data to decide which records to add or delete.
     * Result: Returns 'to_add' and 'to_delete' arrays.
     */
    public function compareAttendanceData($requestData, $from, $to)
    {
        $existingAttendance = $this->getAttendanceDetails(
            $from,
            $to,
            $requestData['company_id'],
            $requestData['branch_id'],
        );
        $existingMap = $this->buildExistingMap($existingAttendance);

        $toAdd = [];
        $toDeleteMap = [];
        foreach ($requestData['attendance_records'] as $employeeRecord) {
            $this->processOneEmployeeRecord($employeeRecord, $existingMap, $toAdd, $toDeleteMap);
        }

        $this->handleRemainingExistingData($existingMap, $toDeleteMap);
        Log::info('to_add : '.json_encode($toAdd).' to_delete : '.json_encode($toDeleteMap));

        return [
            'to_add' => array_values($toAdd),
            'to_delete' => array_values($toDeleteMap),
        ];
    }

    /**
     * Process a single employee’s requested attendance record, updating our map of what to add or delete.
     * Result: Potential additions or deletions recorded in the references passed in.
     */
    private function processOneEmployeeRecord($employeeRecord, &$existingMap, &$toAdd, &$toDeleteMap)
    {
        $employeeId = $employeeRecord['employee_id'];

        if (! isset($existingMap[$employeeId])) {
            $existingMap[$employeeId] = [];
        }

        foreach ($employeeRecord['attendance_info'] as $category => $attendanceCategory) {
            if (empty($attendanceCategory['data']) || ! is_array($attendanceCategory['data'])) {
                continue;
            }

            foreach ($attendanceCategory['data'] as $entry) {
                $this->processOneAttendanceEntry($employeeId, $category, $entry, $existingMap, $toAdd, $toDeleteMap);
            }
        }
    }

    /**
     * Process one date-specific attendance entry, deciding if it's new, changed, or unchanged.
     * Result: Updates $toAdd or $toDeleteMap accordingly.
     */
    private function processOneAttendanceEntry($employeeId, $category, $entry, &$existingMap, &$toAdd, &$toDeleteMap)
    {
        $date = $entry['date'] ?? null;
        if (! $date) {
            return;
        }

        $foundIndex = $this->findCategoryIndex($existingMap[$employeeId][$date] ?? [], $category);

        if ($foundIndex == -1) {
            $toAdd[] = $this->prepareAddEntry($employeeId, $category, $date, $entry);
        } else {
            $this->classifyChangedAttendance(
                $employeeId,
                $category,
                $date,
                $entry,
                $existingMap,
                $foundIndex,
                $toAdd,
                $toDeleteMap
            );
        }
    }

    /**
     * Determine if an existing attendance record has changed enough to remove and re-add.
     * Result: Possibly adds to the deletion list, and also to the addition list.
     */
    private function classifyChangedAttendance(
        $employeeId,
        $category,
        $date,
        $newEntry,
        &$existingMap,
        $foundIndex,
        &$toAdd,
        &$toDeleteMap
    ) {
        $oldItem = $existingMap[$employeeId][$date][$foundIndex];
        $oldData = $oldItem['data'] ?? [];
        $byAdmin = $oldData['by_admin'] ?? null;

        // If no by_admin => skip
        if (! $byAdmin) {
            return;
        }

        // 1) If the category is 'present' and we detect changes (branch/overtime/deduction), do a delete+add.
        if ($category === 'present' && $this->hasAttendanceChanged($oldData, $newEntry)) {
            $uniqueKey = "{$employeeId}|{$category}|{$date}";
            if (! isset($toDeleteMap[$uniqueKey])) {
                $toDeleteMap[$uniqueKey] = $this->prepareDeleteEntry($employeeId, $category, $date, $oldData);
            }
            $toAdd[] = $this->prepareAddEntry($employeeId, $category, $date, $newEntry);
        } // 2) If the category is 'leave' and the leave_type_id changed, do a delete+add similarly.
        elseif ($category === 'leave' && $this->hasLeaveTypeChanged($oldData, $newEntry)) {
            $uniqueKey = "{$employeeId}|{$category}|{$date}";
            if (! isset($toDeleteMap[$uniqueKey])) {
                $toDeleteMap[$uniqueKey] = $this->prepareDeleteEntry($employeeId, $category, $date, $oldData);
            }
            $toAdd[] = $this->prepareAddEntry($employeeId, $category, $date, $newEntry);
        }

        // Remove the old entry from the existing map
        array_splice($existingMap[$employeeId][$date], $foundIndex, 1);
        if (empty($existingMap[$employeeId][$date])) {
            unset($existingMap[$employeeId][$date]);
        }
    }

    /**
     * Check if the old leave record vs. new record differ by leave_type_id.
     * Returns true if changed, false otherwise.
     */
    private function hasLeaveTypeChanged(array $oldData, array $newData): bool
    {
        $oldLeaveType = $oldData['leave_type_id'] ?? null;
        $newLeaveType = $newData['leave_type_id'] ?? null;

        return (int) $oldLeaveType !== (int) $newLeaveType;
    }

    /**
     * Check if the old record and the new record differ in branch, overtime, or deducted days.
     * Result: Returns true if changed, false otherwise.
     */
    private function hasAttendanceChanged($oldData, $newData)
    {
        $oldBranchId = $oldData['branch']['id'] ?? null;
        $newBranchId = $newData['branch_id'] ?? null;

        $oldOvertime = $oldData['overtime_hours'] ?? null;
        $newOvertime = $newData['overtime_hours'] ?? null;

        $oldDeducted = $oldData['deducted_days'] ?? null;
        $newDeducted = $newData['deducted_days'] ?? null;

        return
            $oldBranchId != $newBranchId ||
            $oldOvertime != $newOvertime ||
            $oldDeducted != $newDeducted;
    }

    /**
     * Create a record representing a new attendance we plan to add.
     * Result: Returns an associative array describing the addition.
     */
    private function prepareAddEntry($employeeId, $category, $date, $entry)
    {
        [$branchId, $overtimeHours, $deductedDays, $leaveTypeId] = $this->extractAdditionalFields($category, $entry);

        return [
            'employee_id' => $employeeId,
            'category' => $category,
            'date' => $date,
            'data' => $entry,
            'branch_id' => $branchId,
            'overtime_hours' => $overtimeHours,
            'deducted_days' => $deductedDays,
            'leave_type_id' => $leaveTypeId,
        ];
    }

    /**
     * Create a record representing an existing attendance we plan to remove.
     * Result: Returns an associative array describing the deletion.
     */
    private function prepareDeleteEntry($employeeId, $category, $date, $oldData)
    {
        [$branchId, $overtimeHours, $deductedDays, $leaveTypeId] = $this->extractAdditionalFields($category, $oldData);

        return [
            'employee_id' => $employeeId,
            'category' => $category,
            'date' => $date,
            'data' => $oldData,
            'branch_id' => $branchId,
            'overtime_hours' => $overtimeHours,
            'deducted_days' => $deductedDays,
            'leave_type_id' => $leaveTypeId,
        ];
    }

    /**
     * For any leftover existing data that has 'by_admin' set, we plan to delete it.
     * Result: Adds those records to the 'to_delete' map if not already queued.
     */
    private function handleRemainingExistingData(&$existingMap, &$toDeleteMap)
    {
        foreach ($existingMap as $employeeId => $dates) {
            foreach ($dates as $date => $entries) {
                foreach ($entries as $info) {
                    $byAdmin = $info['data']['by_admin'] ?? false;
                    if (! $byAdmin) {
                        continue;
                    }

                    $uniqueKey = "{$employeeId}|{$info['category']}|{$date}";
                    if (! isset($toDeleteMap[$uniqueKey])) {
                        $toDeleteMap[$uniqueKey] = $this->prepareDeleteEntry(
                            $employeeId,
                            $info['category'],
                            $date,
                            $info['data']
                        );
                    }
                }
            }
        }
    }

    /**
     * Build a map of existing attendance data in the form:
     *  [
     *    employeeId => [
     *      date => [
     *        ['category' => ..., 'data' => ...],
     *        ...
     *      ],
     *    ],
     *  ]
     * Result: Returns this structured map for easy lookups.
     */
    private function buildExistingMap($existingData): array
    {
        $map = [];
        foreach ($existingData as $employeeObj) {
            $employeeId = $employeeObj->id;

            foreach ($employeeObj->attendance_info as $category => $attendanceCategory) {
                if (empty($attendanceCategory['data']) || ! is_array($attendanceCategory['data'])) {
                    continue;
                }

                foreach ($attendanceCategory['data'] as $entry) {
                    $date = $entry['date'] ?? $entry['from'] ?? null;
                    if (! $date) {
                        continue;
                    }

                    if (! isset($map[$employeeId])) {
                        $map[$employeeId] = [];
                    }
                    if (! isset($map[$employeeId][$date])) {
                        $map[$employeeId][$date] = [];
                    }

                    $map[$employeeId][$date][] = [
                        'category' => $category,
                        'data' => $entry,
                    ];
                }
            }
        }

        return $map;
    }

    /**
     * Search the given array of attendance records for an item matching the specified category.
     * Result: Returns the array index if found, or -1 if not found.
     */
    private function findCategoryIndex(array $records, string $category): int
    {
        foreach ($records as $index => $info) {
            if ($info['category'] == $category) {
                return $index;
            }
        }

        return -1;
    }

    /**
     * Extract additional relevant fields (branch, overtime, deducted, leaveType) from the given entry.
     * Result: Returns [branchId, overtimeHours, deductedDays, leaveTypeId].
     */
    private function extractAdditionalFields(string $category, array $entry): array
    {
        $branchId = null;
        $overtimeHours = null;
        $deductedDays = null;
        $leaveTypeId = null;

        if ($category === 'present') {
            $branchId = $entry['branch_id'] ?? null;
            $overtimeHours = $entry['overtime_hours'] ?? null;
            $deductedDays = $entry['deducted_days'] ?? null;
        } elseif ($category === 'leave') {
            $leaveTypeId = $entry['leave_type_id'] ?? null;
        }

        return [$branchId, $overtimeHours, $deductedDays, $leaveTypeId];
    }

    public function deleteAttendance($records)
    {
        // 1) Collect employee_ids and dates
        $employeeIds = collect($records)
            ->pluck('employee_id')
            ->unique()
            ->values()
            ->all();

        $dates = collect($records)
            ->pluck('date')
            ->unique()
            ->values()
            ->all();

        // 2) Retrieve all matching attendances in one go
        $attendances = $this->attendanceRepository->getAttendancesInBulk($employeeIds, $dates);
        if ($attendances->isEmpty()) {
            return;
        }

        $dynamicOnsiteAttendance = collect();
        $staticAttendances = collect();

        foreach ($attendances as $attendance) {
            if (! $this->doesEmployeeHaveFixedRestdays($attendance->employee)) {
                $dynamicOnsiteAttendance->push($attendance);
            } else {
                $staticAttendances->push($attendance);
            }
        }

        $attendanceIds = $attendances->pluck('id')->unique()->values();

        // Clock-In / Clock-Out
        $clockInIds = $attendances->pluck('ci_id')->filter()->unique()->values();
        $clockOutIds = $attendances->pluck('co_id')->filter()->unique()->values();

        $dynamicTimecards = $dynamicOnsiteAttendance->pluck('slotable.id')->filter()->unique()->values();

        $staticTimecards = $staticAttendances->pluck('slotable')->filter();

        // Entity Tags (already done)
        $entityTagIds = $attendances
            ->flatMap(fn ($a) => $a->entityTags)
            ->pluck('id')
            ->unique()
            ->values();

        $attendanceOvertimeIds = $attendances
            ->flatMap(fn ($a) => $a->attendanceOvertimes)
            ->pluck('id')
            ->unique()
            ->values();

        $attendanceDeductionIds = $attendances
            ->flatMap(fn ($a) => $a->attendanceDeductions)
            ->pluck('id')
            ->unique()
            ->values();

        // 6) Delete OverTimes & Deductions in bulk if they exist
        if ($attendanceOvertimeIds->isNotEmpty()) {
            $this->attendanceOvertimeRepository->delete($attendanceOvertimeIds->toArray());
        }

        if ($attendanceDeductionIds->isNotEmpty()) {
            $this->attendanceDeductionRepository->delete($attendanceDeductionIds->toArray());
        }

        // 7) Delete entity tags
        if ($entityTagIds->isNotEmpty()) {
            $this->entityTagRepository->delete($entityTagIds->toArray());
        }

        // 8) Delete clockIn & clockOut
        if ($clockInIds->isNotEmpty()) {
            $this->cicoRepository->delete($clockInIds->toArray());
        }
        if ($clockOutIds->isNotEmpty()) {
            $this->cicoRepository->delete($clockOutIds->toArray());
        }

        // 9) Delete dynamic timecards
        if ($dynamicTimecards->isNotEmpty()) {
            $this->timecardRepository->delete($dynamicTimecards->toArray());
        }

        $this->bulkDeleteExtraWorkdayRequests($attendances);

        $absentInserts = [];
        foreach ($staticTimecards as $timecard) {
            if (! empty($timecard->id)) {
                $absentInserts[] = [
                    'entity_id' => $timecard->id,
                    'entity_type' => 'time_card',
                    'tag' => 'absent',
                    'company' => $timecard->employee->company_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }
        if (! empty($absentInserts)) {
            $this->entityTagRepository->insert($absentInserts);
        }

        $this->attendanceRepository->delete($attendanceIds->toArray());
    }

    public function deleteRestDays($records)
    {
        $data = collect($records)
            ->unique(fn ($r) => $r['employee_id'].'|'.$r['date'])
            ->values();

        if ($data->isEmpty()) {
            return;
        }

        $employeeIds = $data->pluck('employee_id')->unique()->values()->all();
        $dates = $data->pluck('date')->unique()->values()->all();

        $firstEmployeeId = $employeeIds[0] ?? null;
        if (! $firstEmployeeId) {
            return;
        }

        $employee = $this->newEmployeeRepository->find($firstEmployeeId);
        if (! $employee || ! $employee->company || ! $employee->company->restDayLeaveType) {
            return;
        }
        $restDayLeaveId = $employee->company->restDayLeaveType->id;
        $restDays = $this->employeeLeaveRequestRepository
            ->getRestDaysInBulk($employeeIds, $restDayLeaveId, $dates);

        if ($restDays->isEmpty()) {
            return;
        }

        $restDayIds = $restDays->pluck('id')->unique()->values();
        $this->employeeLeaveRequestRepository->delete($restDayIds->toArray());

    }

    public function updateLeaveBalance($balance, $isIncrement = true, $amount = 8)
    {
        if (! $balance) {
            return;
        }

        $balance->balance = $isIncrement
            ? $balance->balance + $amount
            : $balance->balance - $amount;

        $balance->save();
    }

    public function deleteLeaves($records)
    {
        $data = collect($records)
            ->unique(fn ($r) => $r['employee_id'].'|'.$r['date'])
            ->values();

        if ($data->isEmpty()) {
            return;
        }
        $employeeIds = $data->pluck('employee_id')->unique()->values()->all();
        $dates = $data->pluck('date')->unique()->values()->all();
        $leaves = $this->employeeLeaveRequestRepository->getEmployeeLeavesByAdminBulk($employeeIds, $dates);
        if ($leaves->isEmpty()) {
            return;
        }
        $leaveIds = $leaves->pluck('id')->unique()->values();
        $this->employeeLeaveRequestRepository->delete($leaveIds->toArray());
        $grouped = $leaves->groupBy(fn ($leave) => $leave->employee_id.'|'.$leave->company_leave_type_id.'|'.$leave->from);

        foreach ($grouped as $key => $items) {
            [$empId, $leaveTypeId, $fromDate] = explode('|', $key);
            $hours = $items->count() * 8;
            $balance = $this->employeeLeaveBalancesRepository->getLeaveBalanceOfLeaveType($empId, $leaveTypeId, $fromDate);

            $this->updateLeaveBalance($balance, true, $hours);
        }
    }

    public function bulkAddAttendance($toAdd, $from, $to)
    {
        $presentRecords = collect($toAdd)->where('category', 'present')->values();

        if ($presentRecords->isEmpty()) {
            return;
        }
        Log::error('Bulk Add Attendance', ['toAdd' => $toAdd]);

        $employeeIds = $presentRecords->pluck('employee_id')->unique()->toArray();

        $employees = $this->newEmployeeRepository->findByIds($employeeIds);

        $presentRecords = $presentRecords->filter(function ($record) use ($employees, &$errorsArray) {
            $employee = $employees->get($record['employee_id']);

            if (! $employee || ! $employee->title || ! $employee->title->extra_workday_policy_id) {
                return true;
            }

            $approvalCycleExists = optional($employee->title->requestGroup)
                ->requestWorkflows
                ->isNotEmpty();

            if (! $approvalCycleExists) {
                if (! isset($this->errorsArray[$employee->id])) {
                    $this->errorsArray[$employee->id] = []; // Initialize if not exists
                }

                $this->errorsArray[$employee->id][] = [
                    'message' => 'Could not add attendance for employee because there is no extra workday approval cycle for their title',
                ];

                return false;
            }

            return true;
        })->values();

        if ($presentRecords->isEmpty()) {
            return;
        }

        $grouped = $presentRecords->groupBy(fn ($item) => $item['employee_id'].'|'.$item['date']);

        $insertStartTime = now();
        $defaultTimecardType = $this->timecardTypeRepository->getDefaultTimeCardTypeByCompanyId($employees[0]->company_id);

        // Prepare data
        $timecardsData = $this->buildTimecardsForDynamic($grouped, $defaultTimecardType);
        $cicosData = $this->buildCicoRecords($grouped);

        if (! empty($timecardsData)) {
            $this->timecardRepository->insert($timecardsData);
        }

        if (! empty($cicosData)) {
            $this->cicoRepository->insert($cicosData);
        }

        $newTimecards = $this->fetchNewlyInsertedTimecards($timecardsData, $insertStartTime);
        $newCicos = $this->fetchNewlyInsertedCicos($cicosData, $insertStartTime);
        Log::error('Bulk Add Attendance', ['newTimecards' => $newTimecards, 'newCicos' => $newCicos]);

        $this->pairCicos($newCicos, auth()->user()->id);

        // Build Attendance Data (without inserting overtime_hours or deducted_days)
        $attendanceData = $this->buildAttendances($grouped, $newTimecards, $newCicos);
        $this->attendanceRepository->insert($attendanceData);

        $employeeIds = collect($attendanceData)->pluck('employee_id')->unique()->values()->all();
        $dates = collect($attendanceData)->pluck('date')->unique()->values()->all();

        $newAttendances = $this->attendanceRepository->fetchNewlyInsertedAttendances($employeeIds, $dates, now()->toDateTimeString(), auth()->user()->id);
        $entityTagsData = $this->buildEntityTagsForAttendance($toAdd, $newAttendances);

        if (! empty($entityTagsData)) {
            $this->entityTagRepository->insert($entityTagsData);
        }

        $overtimeMap = $this->buildOvertimeMap($toAdd->toArray());
        $this->bulkAddAttendanceOvertime($newAttendances, $overtimeMap);

        // Handle Attendance Deductions if deducted_days > 0
        $this->bulkAddAttendanceDeductions($newAttendances, $toAdd);

        $newOvertimes = $this->attendanceOvertimeRepository->fetchNewlyInsertedAttendanceOvertimes($employeeIds, $dates, $insertStartTime, auth()->user()->id);
        $this->bulkCreateEmployeeRequestsForOvertime($newOvertimes);
        $this->bulkAddExtraWorkdays($newAttendances, $from, $to);
    }

    private function buildTimecardsForDynamic($grouped, $defaultTimecardType)
    {
        $timecards = [];
        $now = now();
        $adminId = auth()->user()->id;

        foreach ($grouped as $key => $items) {
            [$employeeId, $date] = explode('|', $key);

            $employee = $this->newEmployeeRepository->find($employeeId);
            if (! $employee) {
                continue;
            }

            $branchId = $items->first()['data']['branch_id'] ?? $employee->branch_id;

            $startTime = Carbon::parse($date)->setTime(8, 0, 0)->toDateTimeString();
            $endTime = Carbon::parse($date)->setTime(16, 0, 0)->toDateTimeString();
            $timecards[] = [
                'employee_id' => $employee->id,
                'branch_id' => $branchId,
                'by_admin' => $adminId,
                'shift_id' => null,
                'from' => $startTime,
                'to' => $endTime,
                'timecard_type_id' => $defaultTimecardType->id,
                'required_ci_branch_id' => $branchId,
                'required_co_branch_id' => $branchId,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        return $timecards;
    }

    private function buildCicoRecords($grouped): array
    {
        $cico = [];
        $now = now();
        $adminId = auth()->user()->id;

        foreach ($grouped as $key => $items) {
            [$employeeId, $date] = explode('|', $key);

            // 1) Find the employee
            $employee = $this->newEmployeeRepository->find($employeeId);
            if (! $employee) {
                continue;
            }

            $branchId = $items->first()['data']['branch_id'] ?? $employee->branch_id;

            $latLong = $this->getBranchLatLong($branchId);

            $startDate = Carbon::parse($date)->setTime(8, 0, 0)->toDateTimeString();
            $cico[] = [
                'employee_id' => $employee->id,
                'branch_id' => $branchId,
                'cico_by_id' => $employee->id,
                'date' => $startDate,
                'lat' => $latLong['lat'],
                'by_admin' => $adminId,
                'long' => $latLong['long'],
                'in_out' => 'in',
                'source' => 'app',
                'status' => 'verified',
                'created_at' => $now,
                'updated_at' => $now,
            ];

            $endDate = Carbon::parse($date)->setTime(16, 0, 0)->toDateTimeString();
            $cico[] = [
                'employee_id' => $employee->id,
                'branch_id' => $branchId,
                'cico_by_id' => $employee->id,
                'by_admin' => $adminId,
                'date' => $endDate,
                'lat' => $latLong['lat'],
                'long' => $latLong['long'],
                'in_out' => 'out',
                'source' => 'app',
                'status' => 'verified',
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        return $cico;
    }

    private function getBranchLatLong($branchId): array
    {
        $branch = $this->branchRepository->find($branchId);
        if ($branch && ! empty($branch->location)) {
            $coords = array_map('trim', explode(',', $branch->location, 2));
            if (count($coords) === 2) {
                [$lat, $long] = $coords;

                if (is_numeric($lat) && is_numeric($long)) {
                    return [
                        'lat' => (float) $lat,
                        'long' => (float) $long,
                    ];
                }
            }
        }

        return ['lat' => 0, 'long' => 0];
    }

    private function fetchNewlyInsertedTimecards(array $timecardsData, $insertStartTime)
    {
        if (empty($timecardsData)) {
            return collect();
        }
        $employeeIds = collect($timecardsData)->pluck('employee_id')->unique()->values()->all();

        $adminId = collect($timecardsData)->pluck('by_admin')->unique()->first();

        return $this->timecardRepository->fetchTimecardsByAdmin(
            $employeeIds,
            $adminId,
            $insertStartTime
        );
    }

    private function fetchNewlyInsertedCicos($cicosData, $insertStartTime)
    {
        if (empty($cicosData)) {
            return collect();
        }

        $employeeIds = collect($cicosData)->pluck('employee_id')->unique()->values()->all();

        $adminId = auth()->user()->id;

        return $this->cicoRepository->fetchCicosBySlotableAdmin($employeeIds, $adminId, $insertStartTime);
    }

    private function pairCicos($cicos, $adminId = null)
    {
        $filteredCicos = $cicos->filter(function ($cico) use ($adminId) {

            if ($cico->status !== 'verified') {
                return false;
            }

            if ($adminId && isset($cico->by_admin) && $cico->by_admin !== $adminId) {
                return false;
            }

            return true;
        });

        $grouped = $filteredCicos->groupBy(
            fn ($c) => $c->employee_id.'|'.Carbon::parse($c->date)->format('Y-m-d')
        );

        foreach ($grouped as $key => $entries) {
            $in = $entries->firstWhere('in_out', 'in');
            $out = $entries->firstWhere('in_out', 'out');

            if ($in && $out) {
                $in->paired_clock_id = $out->id;
                $out->paired_clock_id = $in->id;

                $this->cicoRepository->update($in->id, ['paired_clock_id' => $out->id]);
                $this->cicoRepository->update($out->id, ['paired_clock_id' => $in->id]);
            }
        }
    }

    private function buildAttendances($grouped, $timecards, $cicos)
    {
        $attendanceArray = [];
        $now = now();
        Log::error('Build Attendances', ['grouped' => $grouped, 'timecards' => $timecards, 'cicos' => $cicos]);

        $timecardsByEmployeeAndDate = $timecards->groupBy(fn ($tc) => $tc->employee_id.'|'.Carbon::parse($tc->from)->format('Y-m-d'));
        $cicosByEmployeeAndDate = $cicos->groupBy(fn ($cc) => $cc->employee_id.'|'.Carbon::parse($cc->date)->format('Y-m-d'));

        foreach ($grouped as $key => $items) {
            [$employeeId, $date] = explode('|', $key);
            $timecard = $timecardsByEmployeeAndDate["$employeeId|$date"]->first() ?? $this->timecardRepository->getEmployeeTimecardOnDate($employeeId, $date);

            $cicoEntries = $cicosByEmployeeAndDate["$employeeId|$date"] ?? collect();
            $clockIn = $cicoEntries->firstWhere('in_out', 'in');
            $clockOut = $cicoEntries->firstWhere('in_out', 'out');

            if (! $clockIn || ! $clockOut) {
                continue;
            }

            $overtimeHours = $items->first()['data']['overtime_hours'] ?? 0;
            if ($overtimeHours > 0) {
                $clockOut->date = Carbon::parse($clockOut->date)->addMinutes($overtimeHours * 60);
            }

            $attendanceArray[] = [
                'employee_id' => $employeeId,
                'date' => $date,
                'actual_start' => $clockIn->date,
                'actual_end' => $clockOut->date,
                'clock_in' => $clockIn->date,
                'clock_out' => $clockOut->date,
                'company_id' => $clockIn->employee->company_id,
                'by_admin' => $clockIn->by_admin,
                'branch_id' => $clockIn->branch_id,
                'slotable_id' => $timecard?->id,
                'slotable_type' => 'time_card',
                'ci_id' => $clockIn->id,
                'co_id' => $clockOut->id,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        return $attendanceArray;
    }

    private function buildOvertimeMap(array $toAdd): array
    {
        $overtimeMap = [];

        foreach ($toAdd as $record) {
            if ($record['category'] === 'present') {
                $employeeId = $record['employee_id'];
                $date = $record['date'];
                $overtimeHours = $record['data']['overtime_hours'] ?? 0; // Extract from `data`

                if ($overtimeHours > 0) {
                    $key = "{$employeeId}|{$date}";

                    // Store overtime hours in the map
                    if (! isset($overtimeMap[$key])) {
                        $overtimeMap[$key] = $overtimeHours;
                    } else {
                        // Accumulate overtime if multiple records exist for the same day
                        $overtimeMap[$key] += $overtimeHours;
                    }
                }
            }
        }

        return $overtimeMap;
    }

    private function bulkAddAttendanceOvertime($attendances, $overtimeMap)
    {
        $overtimeRows = [];
        $now = now();

        $dailyOvertimePolicyId = null;
        $calculationMethod = 'rate';

        foreach ($attendances as $attendance) {
            $key = $attendance->employee_id.'|'.Carbon::parse($attendance->date)->format('Y-m-d');

            if (isset($overtimeMap[$key])) {
                $overtimeHours = (float) $overtimeMap[$key];
                if ($overtimeHours <= 0) {
                    continue;
                }

                $overtimeMinutes = (int) ($overtimeHours * 60);

                $startTime = $attendance->actual_start;
                $endTime = $attendance->actual_end;

                $date = Carbon::parse($attendance->date)->format('Y-m-d');
                $overtimeRows[] = [
                    'date' => $date,
                    'overtime_value' => $overtimeHours,
                    'calculation_method' => $calculationMethod,
                    'status' => 'approved',
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                    'overtime_minutes' => $overtimeMinutes,
                    'daily_overtime_policy_id' => $dailyOvertimePolicyId,
                    'note' => null,
                    'employee_id' => $attendance->employee_id,
                    'attendance_id' => $attendance->id,
                    'company_id' => $attendance->company_id,
                    'branch_id' => $attendance->branch_id,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }

        if (! empty($overtimeRows)) {
            $this->attendanceOvertimeRepository->insert($overtimeRows);
        }
    }

    public function bulkAddAttendanceDeductions($newAttendances, $toAdd)
    {
        $deductions = [];
        $now = now();

        // Map Attendances by (employee_id, date) for fast lookup
        $attendanceMap = $newAttendances->mapWithKeys(fn ($a) => [
            "{$a->employee_id}|{$a->date}" => $a->id,
        ]);

        foreach ($toAdd as $record) {
            if ($record['category'] !== 'present') {
                continue;
            }

            $employeeId = $record['employee_id'];
            $date = $record['date'];
            $attendanceId = $attendanceMap["$employeeId|$date"] ?? null;
            $deductedDays = $record['data']['deducted_days'] ?? 0;

            if (! $attendanceId || $deductedDays <= 0) {
                continue;
            }

            $companyId = $newAttendances->first()->company_id;
            $branchId = $record['data']['branch_id'] ?? null;

            $deductions[] = [
                'date' => $date,
                'deduction_value' => $deductedDays,
                'deduction_minutes' => 0,
                'updated_value' => null,
                'status' => 'applied',
                'excuse_applied' => 0,
                'employee_id' => $employeeId,
                'attendance_id' => $attendanceId,
                'employee_leave_request_id' => null,
                'daily_attendance_policy_id' => null,
                'updated_by' => null,
                'company_id' => $companyId,
                'created_at' => $now,
                'updated_at' => $now,
                'deleted_at' => null,
                'branch_id' => $branchId,
                'workflow_id' => null,
                'late_deduction_group_policy_id' => null,
            ];
        }

        if (! empty($deductions)) {
            $this->attendanceDeductionRepository->insert($deductions);
        }
    }

    private function buildEntityTagsForAttendance($toAdd, $newAttendances)
    {
        $tags = [];
        $now = now();

        // Normalize date format in attendanceMap
        $attendanceMap = $newAttendances->mapWithKeys(fn ($a) => [
            "{$a->employee_id}|".Carbon::parse($a->date)->format('Y-m-d') => $a->id,
        ]);

        // Get all unique employee IDs from $toAdd
        $employeeIds = collect($toAdd)->pluck('employee_id')->unique()->toArray();

        // Retrieve all employees at once and map them by ID
        $employees = $this->newEmployeeRepository->findByIds($employeeIds)->keyBy('id');

        foreach ($toAdd as $record) {
            if ($record['category'] !== 'present') {
                continue;
            }

            $employeeId = $record['employee_id'];
            $date = Carbon::parse($record['date'])->format('Y-m-d');
            $attendanceId = $attendanceMap["$employeeId|$date"] ?? null;

            if (! $attendanceId) {
                Log::warning("Attendance ID not found for employee {$employeeId} on {$date}");

                continue;
            }

            // Retrieve the employee from the preloaded collection
            $employee = $employees->get($employeeId);
            $companyId = $employee?->company_id ?? null;

            $deductedDays = $record['data']['deducted_days'] ?? 0;
            $overtimeHours = $record['data']['overtime_hours'] ?? 0;

            $tags[] = [
                'entity_id' => $attendanceId,
                'entity_type' => 'attendance',
                'tag' => ($deductedDays > 0) ? 'late' : 'on_time',
                'company' => $companyId,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            if ($overtimeHours > 0) {
                $tags[] = [
                    'entity_id' => $attendanceId,
                    'entity_type' => 'attendance',
                    'tag' => 'overtime',
                    'company' => $companyId,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }

        return $tags;
    }

    public function bulkCreateEmployeeRequestsForOvertime($newOvertimes)
    {
        if ($newOvertimes->isEmpty()) {
            return;
        }

        $now = now();
        $requests = [];

        foreach ($newOvertimes as $overtime) {
            $requests[] = [
                'company_id' => $overtime->company_id,
                'requested_by' => null,
                'updated_by' => null,
                'requestable_id' => $overtime->id,
                'requestable_type' => 'attendance_overtime',
                'request_name' => 'system_overtime',
                'comment' => null,
                'created_at' => $now,
                'updated_at' => $now,
                'deleted_at' => null,
                'status' => 'approved',
                'employee_id' => $overtime->employee_id,
                'date' => $overtime->date,
            ];
        }

        if (! empty($requests)) {
            $this->employeeRequestRepository->insert($requests);
        }
    }

    private function processLeaveRequests($leaveRecords)
    {
        if (empty($leaveRecords)) {
            return;
        }

        $now = now();
        $adminId = auth()->user()->id;

        $records = collect($leaveRecords);

        $employeeIds = $records->pluck('employee_id')->unique()->values()->all();

        $employees = $this->newEmployeeRepository->findByIds($employeeIds)->keyBy('id');

        $leaveRequests = [];

        foreach ($records as $record) {
            if (! isset($record['employee_id'], $record['date'], $record['data']['leave_type_id'])) {
                continue;
            }

            $employeeId = $record['employee_id'];
            $date = $record['date'];
            $leaveTypeId = $record['data']['leave_type_id'];

            $employee = $employees[$employeeId] ?? null;
            if (! $employee || ! $employee->company) {
                continue;
            }
            $hasScheduledTransfer = $this->hasScheduledTransfer($employeeId, $date);
            if ($hasScheduledTransfer) {
                $scheduledBranch = $this->employeeChangeRepository->getScheduledTransferBranch($employeeId, $date);
            }

            $branchId = isset($scheduledBranch) ? $scheduledBranch->to_value : $employee->branch_id;
            $policyId = $employee->company->companyLeaveTypes->firstWhere('id', $leaveTypeId)?->companyLeaveTypePolicy->id;

            $start = Carbon::parse($date)->startOfDay();
            $end = Carbon::parse($date)->endOfDay();
            $leaveRequests[] = [
                'company_leave_type_id' => $leaveTypeId,
                'employee_id' => $employeeId,
                'status' => 'approved',
                'from' => $start,
                'to' => $end,
                'company_leave_type_policy_id' => $policyId,
                'net_quantity' => 8,
                'branch_id' => $branchId,
                'by_admin' => $adminId,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        if (! empty($leaveRequests)) {
            $validRequests = [];

            foreach ($leaveRequests as $request) {
                $balance = $this->employeeLeaveBalancesRepository->getLeaveBalanceOfLeaveType(
                    $request['employee_id'],
                    $request['company_leave_type_id'],
                    $request['from']
                );

                if ($balance && $balance->balance >= $request['net_quantity']) {
                    $validRequests[] = $request;
                } else {
                    if (! isset($this->errorsArray[$request['employee_id']])) {
                        $this->errorsArray[$request['employee_id']] = [];
                    }

                    $this->errorsArray[$request['employee_id']][] = [
                        'date' => Carbon::parse($request['from'])->format('Y-m-d'),
                        'message' => 'Could not add leave for employee because there is no balance',
                    ];
                }
            }

            if (! empty($validRequests)) {
                $this->employeeLeaveRequestRepository->insert($validRequests);

                foreach ($validRequests as $request) {
                    $balance = $this->employeeLeaveBalancesRepository->getLeaveBalanceOfLeaveType(
                        $request['employee_id'],
                        $request['company_leave_type_id'],
                        $request['from']
                    );

                    $this->updateLeaveBalance(
                        $balance,
                        false,
                        $request['net_quantity']
                    );
                }
            }
        }
    }

    private function processRestDayRequests($restDayRecords)
    {
        if (empty($restDayRecords)) {
            return;
        }

        $now = now();
        $adminId = auth()->user()->id;

        $records = collect($restDayRecords);

        $employeeIds = $records->pluck('employee_id')->unique()->values()->all();

        $employees = $this->newEmployeeRepository->findByIds($employeeIds)->keyBy('id');

        $restDayRequests = [];
        $leaveBalanceUpdates = [];

        foreach ($records as $record) {
            if (! isset($record['employee_id'], $record['date'])) {
                continue;
            }

            $employeeId = $record['employee_id'];
            $date = $record['date'];

            // Ensure employee exists
            $employee = $employees[$employeeId] ?? null;
            if (! $employee || ! $employee->company || ! $employee->company->restDayLeaveType) {
                continue;
            }

            $restDayLeaveId = $employee->company->restDayLeaveType->id;

            $hasScheduledTransfer = $this->hasScheduledTransfer($employeeId, $date);
            if ($hasScheduledTransfer) {
                $scheduledBranch = $this->employeeChangeRepository->getScheduledTransferBranch($employeeId, $date);
            }

            $branchId = isset($scheduledBranch) ? $scheduledBranch->to_value : $employee->branch_id;

            $start = Carbon::parse($date)->startOfDay();
            $end = Carbon::parse($date)->endOfDay();
            $restDayRequests[] = [
                'company_leave_type_id' => $restDayLeaveId,
                'employee_id' => $employeeId,
                'company_leave_type_policy_id' => $employee->company->restDayLeaveType->companyLeaveTypePolicy->id,
                'status' => 'approved',
                'from' => $start,
                'to' => $end,
                'net_quantity' => 8,
                'branch_id' => $branchId,
                'by_admin' => $adminId,
                'created_at' => $now,
                'updated_at' => $now,
            ];

        }
        if (! empty($restDayRequests)) {
            $this->employeeLeaveRequestRepository->insert($restDayRequests);
        }

    }

    private function bulkAddAbsentTimecards($absentRecords)
    {
        if (empty($absentRecords)) {
            return;
        }

        $now = now();
        $adminId = auth()->user()->id;

        $records = collect($absentRecords);

        $employeeIds = $records->pluck('employee_id')->unique()->values()->all();

        $employees = $this->newEmployeeRepository->findByIds($employeeIds)->keyBy('id');


        $timecards = [];

        $newTimecards = [];

        foreach ($records as $record) {
            if (! isset($record['employee_id'], $record['date'])) {
                continue;
            }

            $employeeId = $record['employee_id'];
            $date = $record['date'];

            $employee = $employees[$employeeId] ?? null;
            if (! $employee) {
                continue;
            }
            $timecardType = $this->timecardTypeRepository->getDefaultTimeCardTypeByCompanyId($employee->company_id);

            $branchId = $employee->branch_id;

            $timecards[] = [
                'employee_id' => $employeeId,
                'branch_id' => $branchId,
                'by_admin' => $adminId,
                'shift_id' => null,
                'from' => Carbon::parse($date)->setTime(8, 0, 0),
                'to' => Carbon::parse($date)->setTime(17, 0, 0),
                'timecard_type_id' => $timecardType->id ?? null,
                'required_ci_branch_id' => $branchId,
                'required_co_branch_id' => $branchId,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        if (! empty($timecards)) {
            // Create new timecards and assign them directly.
            $newTimecards = $this->timecardRepository->createMany($timecards);

            // If createMany returns an array, wrap it in a collection.
            $newTimecards = collect($newTimecards);

            $entityTags = $newTimecards->map(fn ($timecard) => [
                'entity_id' => $timecard->id,
                'entity_type' => 'time_card',
                'tag' => 'absent',
                'company' => $timecard->employee->company_id,
                'created_at' => $now,
                'updated_at' => $now,
            ])->toArray();

            if (! empty($entityTags)) {
                $this->entityTagRepository->insert($entityTags);
            }
        }

    }

    private function bulkDeleteAbsentTimecards($absentRecords)
    {
        if (empty($absentRecords)) {
            return;
        }

        $records = collect($absentRecords);

        $employeeIds = $records->pluck('employee_id')->unique()->values()->all();
        $dates = $records->pluck('date')->unique()->values()->all();

        $absentTimecards = $this->timecardRepository->getAbsentTimecardsOnDates($employeeIds, $dates);

        if ($absentTimecards->isEmpty()) {
            return;
        }

        $timecardIds = $absentTimecards->pluck('id')->unique()->values()->all();

        $entityTagIds = $this->entityTagRepository->getEntityTagsForTimecards($timecardIds)->pluck('id')->toArray();

        if (! empty($entityTagIds)) {
            $this->entityTagRepository->delete($entityTagIds);
        }

        $this->timecardRepository->delete($timecardIds);
    }

    private function fetchNewlyInsertedAbsentTimecards(array $employeeIds, $insertStartTime)
    {
        if (empty($employeeIds)) {
            return collect();
        }

        return $this->timecardRepository->fetchTimecardsByAdmin(
            $employeeIds,
            auth()->user()->id,
            $insertStartTime
        );
    }

    private function bulkAddExtraWorkdays($attendances, $from, $to): void
    {
        if ($attendances->isEmpty()) {
            return;
        }

        // 1) Group attendances by employee
        $attendanceByEmployee = $attendances->groupBy('employee_id');

        foreach ($attendanceByEmployee as $employeeId => $records) {
            $employee = $this->newEmployeeRepository->find($employeeId);
            if (! $employee || ! $employee->title) {
                continue;
            }

            $workTypePolicy = optional($employee->title->workTypePolicy);
            $extraWorkdayPolicy = optional($employee->title->extraWorkdayPolicy);

            $approvalCycleExists = $employee->title
                ->requestGroup
                ?->requestWorkflows()
                ->where('type', 'extra_work_day_request')
                ->whereNull('deleted_at')
                ->exists();

            if (! isset($workTypePolicy) || ! isset($extraWorkdayPolicy) || ! isset($approvalCycleExists)) {
                // put log info to show the variables as string
                Log::info('Extra work day policy '.json_encode($workTypePolicy).
                    ' or extra work day policy '.json_encode($extraWorkdayPolicy).
                    ' or approval cycle exists '.json_encode($approvalCycleExists).
                    ' for employee id '.$employeeId);

                continue;
            }

            // If non-dynamic => we can rely on $records (the newly inserted attendances)
            if ($this->doesEmployeeHaveFixedRestdays($employee)) {
                $this->processExtraWorkdaysForNonDynamic($employee, $records, $extraWorkdayPolicy);
            } else {
                // dynamic => fetch from DB
                $this->processExtraWorkdaysForDynamic($employee, $records, $extraWorkdayPolicy, $workTypePolicy, $from, $to);
            }
        }
    }

    private function processExtraWorkdaysForNonDynamic($employee, $attendances, $extraWorkdayPolicy)
    {
        // 1) Get rest days array from policy
        $restDaysAbbrev = explode(',', $employee->title->workTypePolicy->rest_days); // e.g. ["sat","fri"]
        // Map them to full day names or compare in a flexible manner
        $dayMap = [
            'sat' => 'Saturday',
            'sun' => 'Sunday',
            'mon' => 'Monday',
            'tue' => 'Tuesday',
            'wed' => 'Wednesday',
            'thu' => 'Thursday',
            'fri' => 'Friday',
        ];
        $restDaysFull = array_map(fn ($abbrev) => $dayMap[strtolower($abbrev)] ?? null, $restDaysAbbrev);

        // 2) Filter: only attendances on rest days
        $extraAttendances = $attendances->filter(function ($attendance) use ($restDaysFull) {
            $dayName = Carbon::parse($attendance->date)->format('l');

            return in_array($dayName, $restDaysFull);
        });

        // 3) Create requests
        $this->addExtraWorkDayRequests($employee, $extraAttendances, $extraWorkdayPolicy->compensation_rate ?? 1.0);
    }

    private function processExtraWorkdaysForDynamic($employee, $records, $extraWorkdayPolicy, $workTypePolicy, $from, $to)
    {
        if ($records->isEmpty()) {
            return;
        }

        $startDate = $from;
        $endDate = $to;

        $payrollMonthDays = PayrollUtil::PAYROLL_MONTH_DAYS;
        $restDaysCount = $workTypePolicy->rest_days_count;
        $thresholdCount = $payrollMonthDays - $restDaysCount;

        // Get all attendance records within the range
        $allAttendances = $this->attendanceRepository
            ->getSortedAttendances($employee->id, $startDate, $endDate)
            ->values();

        if ($allAttendances->isEmpty()) {
            return;
        }

        $eligibleAttendances = $allAttendances
            ->whereNull('extra_work_day_status')
            ->sortBy('date')
            ->skip($thresholdCount)
            ->values();

        if ($eligibleAttendances->isNotEmpty()) {
            $this->addExtraWorkDayRequests(
                $employee,
                $eligibleAttendances,
                $extraWorkdayPolicy->compensation_rate ?? 1.0
            );
        }
    }

    private function addExtraWorkDayRequests($employee, $attendances, $compensationRate)
    {
        // 1) Quick exit if no records
        if ($attendances->isEmpty()) {
            return;
        }

        $now = now();
        $requestsToInsert = [];
        $employeeRequests = [];
        $entityTags = [];
        $requesterRoleIds = $employee->user->roles->pluck('id')->toArray();

        foreach ($attendances as $attendance) {
            // Create the extra workday request
            $extraWorkdayRequest = $this->extraWorkdayRequestRepository->add([
                'employee_id' => $employee->id,
                'extra_work_day_date' => $attendance->date,
                'status' => 'pending',
                'compensation_rate' => $compensationRate,
                'attendance_id' => $attendance->id,
            ]);

            // Build the employee_requests record
            $employeeRequests[] = [
                'company_id' => $employee->company_id,
                'requested_by' => $employee->id,
                'requestable_type' => 'extra_work_day_request',
                'request_name' => 'extra_work_day_request',
                'comment' => null,
                'status' => 'pending',
                'employee_id' => $employee->id,
                'date' => $attendance->date,
                'requestable_id' => $extraWorkdayRequest->id,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            // Mark attendance as pending extra_work_day_status
            $attendance->extra_work_day_status = 'pending';
            $attendance->save();

            // Optionally add an entity tag to the attendance
            $entityTags[] = [
                'tag' => config('globals.ATTENDANCE_TAGS.EXTRA_WORKDAY') ?? 'extra_workday',
                'entity_id' => $attendance->id,
                'entity_type' => 'attendance',
                'company' => $employee->company_id,
                'created_at' => $now,
                'updated_at' => $now,
            ];


            AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('extra_workday', $extraWorkdayRequest, $requesterRoleIds));
        }

        // 2) Insert in bulk
        if (! empty($employeeRequests)) {
            $this->employeeRequestRepository->insert($employeeRequests);
        }
        if (! empty($entityTags)) {
            $this->entityTagRepository->insert($entityTags);
        }
    }

    private function bulkDeleteExtraWorkdayRequests($attendances)
    {
        // 1) Build (employee => dates)
        $attMap = [];
        foreach ($attendances as $attendance) {
            $empId = $attendance->employee_id;
            $date = $attendance->date;

            if (! isset($attMap[$empId])) {
                $attMap[$empId] = [];
            }
            $attMap[$empId][] = $date;
        }

        $extraWorkdayRequestIds = [];
        $employeeRequestIds = [];
        $attendanceToReset = collect();
        foreach ($attMap as $employeeId => $datesArr) {
            if (! is_array($datesArr)) {
                $datesArr = [$datesArr];
            }

            $requests = $this->extraWorkdayRequestRepository->getExtraworkDaysRequestsInDateRange($employeeId, $datesArr);

            if ($requests->isEmpty()) {
                continue;
            }

            $reqIds = $requests->pluck('id')->unique()->values()->all();

            $empRequests = $this->employeeRequestRepository->getExtraWorkDayRequests($reqIds);
            if ($empRequests->isNotEmpty()) {
                $employeeRequestIds = array_merge(
                    $employeeRequestIds,
                    $empRequests->pluck('id')->unique()->values()->all()
                );
            }

            foreach ($requests as $req) {
                $attendance = $this->attendanceRepository->getAttendanceOnDate($employeeId, $req->extra_work_day_date);

                if ($attendance) {
                    $attendanceToReset->push($attendance->id);
                }
            }

            $extraWorkdayRequestIds = array_merge($extraWorkdayRequestIds, $reqIds);
        }

        if ($attendanceToReset->isNotEmpty()) {
            $this->attendanceRepository->multipleUpdate($attendanceToReset->unique()->toArray(), ['extra_work_day_status' => null, 'extra_work_day_workflow_id' => null]);
        }

        if (! empty($employeeRequestIds)) {
            $this->employeeRequestRepository->delete($employeeRequestIds);
        }

        // 5) Bulk delete ExtraWorkdayRequests
        if (! empty($extraWorkdayRequestIds)) {
            $this->extraWorkdayRequestRepository->delete($extraWorkdayRequestIds);
        }
    }

    public function deleteAttendanceData($to_delete, $from, $to)
    {
        $groupedDeletes = collect($to_delete)->groupBy('category');

        foreach ($groupedDeletes as $category => $records) {
            switch ($category) {
                case 'present':
                    $this->deleteAttendance($records, $from, $to);
                    break;
                case 'rest_day':
                    $this->deleteRestDays($records);
                    break;
                case 'leave':
                    $this->deleteLeaves($records);
                    break;
                case 'absent_with_permission':
                    $this->bulkDeleteAbsentTimecards($records);
                    break;
                default:
                    // Possibly log or skip unknown categories
                    break;
            }
        }
    }

    public function addAttendanceData($to_add, $from, $to)
    {
        $groupedAdds = collect($to_add)->groupBy('category');

        foreach ($groupedAdds as $category => $records) {
            switch ($category) {
                case 'present':
                    $this->bulkAddAttendance($records, $from, $to);
                    break;
                case 'leave':
                    $this->processLeaveRequests($records);
                    break;
                case 'rest_day':
                    $this->processRestDayRequests($records);
                    break;
                case 'absent_with_permission':
                    $this->bulkAddAbsentTimecards($records);
                    break;
                default:
                    // Possibly log or skip unknown categories
                    break;
            }
        }
    }

    public function getDepartments($companyId)
    {
        return $this->departmentRepository->findByKey('company_id', $companyId);
    }

    public function getBranches($companyId)
    {
        return $this->branchRepository->findByKey('company_id', $companyId);
    }

    public function getTitles($companyId)
    {
        return $this->titleRepository->findByKey('company_id', $companyId);
    }

    public function getDraftedPayroll($companyId)
    {
        return [
            'payroll' => $this->payrollRepository->getLastDraftedPayroll($companyId),
            'is_dynamic' => $this->systemSettingRepository->isCompanyDynamic($companyId),
        ];
    }

    public function getLeaveTypes($companyId)
    {
        $company = $this->companyRepository->find($companyId);
        $allLeaveTypes = $this->companyLeaveTypeRepository->findByKey('company_id', $companyId);
        $restDayLeaveType = $company->restDayLeaveType;

        return $allLeaveTypes->filter(function ($leaveType) use ($restDayLeaveType) {
            return $leaveType->id != $restDayLeaveType->id;
        });
    }

    public function doesEmployeeHaveFixedRestdays($employee)
    {
        $workTypePolicy = $employee?->title?->workTypePolicy;

        $unleash = app(Unleash::class);
        $isNewWorkTypes = $unleash->getNewWorkTypesFeatureFlag($employee->company_id);

        return $isNewWorkTypes ? $workTypePolicy->rest_days_type == RestDaysTypesEnum::FIXED->value :
            $workTypePolicy->work_days_type != UserWorkTypesUtil::DYNAMIC_ON_SITE;

    }
}
