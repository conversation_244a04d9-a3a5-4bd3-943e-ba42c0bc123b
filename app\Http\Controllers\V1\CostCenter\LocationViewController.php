<?php

namespace App\Http\Controllers\V1\CostCenter;

use App\Exceptions\UnprocessableException;
use App\Exports\V1\CostCenter\LocationsViewExport;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\CostCenter\GetLocationViewRequest;
use App\Http\Requests\V1\CostCenter\UpdateLocationCalculationMethodRequest;
use App\Http\Resources\V1\CostCenter\LocationDetailsResource;
use App\Http\Resources\V1\CostCenter\LocationsViewResource;
use App\Models\Employee;
use App\Models\Payroll;
use App\Services\V1\CostCenter\LocationsViewService;
use App\Traits\EmployeeChangesHelper;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class LocationViewController extends NewController
{
    use EmployeeChangesHelper;

    public function __construct(protected LocationsViewService $locationsViewService) {}

    public function updateCalculationMethod(UpdateLocationCalculationMethodRequest $request)
    {
        try {

            $this->locationsViewService->updateCalculationMethod($request->validated('method'));

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'setting changed successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function getCalculationMethod()
    {
        try {
            $method = $this->locationsViewService->getCalculationMethod();

            return getResponseStructure(
                ['data' => ! empty($method) ? ['method' => $method] : null],
                HttpStatusCodeUtil::OK,
                'setting retrieved successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function getLocationsCosts(GetLocationViewRequest $request)
    {
        try {
            $data = $this->locationsViewService->getLocationsCosts($request->validated());

            return getResponseStructure(
                ['data' => LocationsViewResource::collection($data)],
                HttpStatusCodeUtil::OK,
                'locations retrieved successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e);
        }
    }

    public function exportLocationsCosts(GetLocationViewRequest $request)
    {
        $data = $this->locationsViewService->getLocationsCosts($request->validated());

        return Excel::download(new LocationsViewExport($data->toArray()), 'locations-Costs-'.$request->validated('month').'-'.$request->validated('year').'.xlsx');
    }

    public function getLocationCostDetails(int $id)
    {
        $data = $this->locationsViewService->getLocationCostDetails($id);

        return getResponseStructure(
            ['data' => new LocationDetailsResource($data)],
            HttpStatusCodeUtil::OK,
            'location details retrieved successfully');

    }

    public function test(int $id, int $month)
    {

        $emp = Employee::find($id);
        $payroll = Payroll::where('month', $month)->where('year', 2024)->first();
        $branchesPercentage = $this->getBranchesPercentageInRange($emp->id, $emp->branch_id, $payroll->start, $payroll->end);

        return getResponseStructure(
            ['data' => $branchesPercentage],
            HttpStatusCodeUtil::OK,
            'location details retrieved successfully');

    }
}
