<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Company;
use App\Models\Employee;
use App\Models\EmployeeLeaveRequest;
use App\Repositories\Repository;
use App\Services\LeaveManagement\BusinessServices\BalanceAggregatorService;
use App\Traits\LeaveManagementNet;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RecalculateAllLeavesNetQuantities extends Command
{
    use LeaveManagementNet;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recalculate:leaves:net_quantites';

    /**
     * this command requires fill:default:scopes to run first.
     *
     * @var string
     */
    protected $description = '';

    public function __construct(
        private BalanceAggregatorService $balanceAggregatorService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $this->recalculate();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            //// dd($e);
            DB::rollBack();
        }
    }

    public function recalculate()
    {
        $employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
        $companies = Company::all();
        foreach ($companies as $company) {
            $leaves = EmployeeLeaveRequest::where('status', 'approved')
                ->whereHas('companyLeaveType', function ($query) use ($company) {
                    $query->where('company_id', $company->id)
                        ->where('id', '!=', $company->rest_day_leave_id);
                })
                ->get();
            $count = 0;
            foreach ($leaves as $leave) {
                $leaveSpanningAnotherLeave = $this->leaveSpanningAnotherLeave($leave);
                if ($leaveSpanningAnotherLeave == true) {
                    $leave->status = config('globals.REQUEST_STATUSES.REJECTED');
                    $leave->save();
                    $balanceEntity = $employeeLeaveBalanceRepository->getBalanceOfLeave($leave);
                    $dummyOutput = new \stdClass;
                    $employee = Employee::find($leave->employee_id);
                    if (isset($employee)) {
                        $this->balanceAggregatorService->perform(['id' => $balanceEntity->id,
                            'employee_id' => $leave->employee_id], $dummyOutput);
                    }
                    $count++;
                }
            }
            echo 'Company: '.$company->id.' :  '.$count." leaves \n";
        }
    }

    public function leaveSpanningAnotherLeave($leave)
    {
        return EmployeeLeaveRequest::where('status', 'approved')
            ->where('id', '!=', $leave->id)
            ->where('employee_id', $leave->employee_id)
            ->where('from', '<=', $leave->from)
            ->where('to', '>=', $leave->to)
            ->where('created_at', '<', $leave->created_at)
            ->where('net_quantity', '>', 0)
            ->exists();
    }
}
