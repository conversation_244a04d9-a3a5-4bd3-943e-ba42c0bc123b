<?php

namespace App\Console\Commands;

use App\Models\Timecard;
use Carbon\Carbon;
use Illuminate\Console\Command;

class FixTimeCardToTime extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:timeCardToTime';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $timeCards = TimeCard::whereColumn('to', '<', 'from')
            ->get();

        foreach ($timeCards as $timeCard) {
            $newTo = Carbon::parse($timeCard->to)->addDay();
            $timeCard->update(['to' => $newTo]);
        }

    }
}
