<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Models\Timecard;
use Exception;
use Illuminate\Support\Carbon;

class WorkedHoursTagHandler implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        return [
            'name' => $employeeAttendance->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($employeeAttendance->attendance->date)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    /**
     * @throws Exception
     */
    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        $clockInDateTime = Carbon::parse($employeeAttendance->attendance->clockIn->date);
        $clockOutDateTime = Carbon::parse($employeeAttendance->attendance->clockOut->date);
        $currentDurationInMinutes = $clockOutDateTime->diffInMinutes($clockInDateTime);
        if (isset($tags[$employeeId]['tags']['worked_hours']['count'])) {
            $result = ($tags[$employeeId]['tags']['worked_hours']['count'] * 60) + $currentDurationInMinutes;
        } else {
            $result = $currentDurationInMinutes;
        }

        return number_format($result / 60, 2, '.', '');
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return $employeeAttendance instanceof Timecard &&
            $employeeAttendance->attendance &&
            $employeeAttendance->attendance->clockIn &&
            $employeeAttendance->attendance->clockOut;
    }

    public function handleTagUnit(): string
    {
        return 'hours';
    }
}
