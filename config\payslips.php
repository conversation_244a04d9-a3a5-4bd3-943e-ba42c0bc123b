<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Payslip Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for payslip generation including
    | font settings, default language, and direction settings.
    |
    */

    'fonts' => [
        'arabic' => [
            'regular' => [
                'name' => 'Amiri',
                'file' => 'Amiri-Regular.ttf',
            ],
            'bold' => [
                'name' => 'Amiri',
                'file' => 'Amiri-Bold.ttf',
                'weight' => 700,
            ],
        ],
        'latin' => [
            'regular' => [
                'name' => 'Arial',
                'fallback' => ['Helvetica', 'sans-serif'],
            ],
            'bold' => [
                'name' => 'Arial-Bold',
                'fallback' => ['Helvetica-Bold', 'sans-serif'],
            ],
        ],
    ],

    'default_direction' => 'ltr',
    'default_language' => 'en',

    'supported_languages' => [
        'en' => [
            'name' => 'English',
            'direction' => 'ltr',
            'font' => 'latin',
        ],
        'ar' => [
            'name' => 'Arabic',
            'direction' => 'rtl',
            'font' => 'arabic',
        ],
    ],

    'storage' => [
        'fonts_path' => storage_path('fonts/Amiri/static'),
    ],

    'browsershot_options' => [
        'format' => 'A4',
        'margins' => [20, 20, 20, 20], // top, right, bottom, left
        'timeout' => 60,
        'wait_until_network_idle' => true,
        'show_background' => true,
        'temp_dir' => 'C:/temp/laravel_pdf/',
        'chrome_args' => [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--no-first-run',
            '--disable-default-apps',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            '--font-render-hinting=none',
            '--disable-font-subpixel-positioning',
            '--disable-web-security',
            '--allow-running-insecure-content',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-images',
            '--run-all-compositor-stages-before-draw',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
            '--disable-features=VizDisplayCompositor',
        ],
    ],
];
