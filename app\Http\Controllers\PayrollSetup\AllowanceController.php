<?php

namespace App\Http\Controllers\PayrollSetup;

use App\DomainData\AllowanceDto;
use App\DomainData\FilterDto;
use App\Http\Controllers\Controller;
use App\Services\PayrollSetup\CrudServices\DynamicAllowanceCrudService;
use App\Services\PayrollSetup\CrudServices\StaticAllowanceCrudService;
use App\Services\PayrollSetup\FactoryService;
use App\Traits\FilterHelper;

class AllowanceController extends Controller
{
    use AllowanceDto, FactoryService, FilterDto, FilterHelper;

    private $entityName;

    public function __construct(
        private StaticAllowanceCrudService $staticAllowanceCrudService,
        private DynamicAllowanceCrudService $dynamicAllowanceCrudService
    ) {}

    public function create(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules([]);
        $rules = $this->getAdditionalRules($rules);

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();
        $service = $this->getService('allowance', $request['static_value']);

        unset($request['static_value']);
        $service->create($request, $output);
    }

    public function update(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules([]);
        $rules = $this->getAdditionalRules($rules);
        $rules['id'] = 'required|numeric';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $service = $this->getService('allowance', $request['static_value']);

        unset($request['static_value']);
        $service->update($request, $output);
    }

    public function getById(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules(['related_objects.*', 'related_objects_count.*']);
        $rules['id'] = 'required|numeric';
        $rules['static_value'] = 'required|boolean';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['related_objects'])) {
            $request['related_objects'] = [];
        }
        if (! isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $service = $this->getService('allowance', $request['static_value']);

        unset($request['static_value']);
        $service->getById($request, $output);
        $this->entityName = $service->getEntityName();
        $this->getRelatedObjectsDistinct($request, $output);
    }

    public function getByFilter(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules(['page', 'filters', 'related_objects.*', 'related_objects_count.*',  'page_size']);

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['related_objects'])) {
            $request['related_objects'] = [];
        }
        if (! isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $request['page_size'] = $request['page_size'] > 1 ? $request['page_size'] / 2 : $request['page_size'];
        $this->staticAllowanceCrudService->getByFilter($request, $output);
        $this->entityName = $this->staticAllowanceCrudService->getEntityName();
        $this->getByFilterRelatedObjectsDistinct($request, $output);

        $request['page_size'] = $this->getPageSize($request['page_size'], count($output->static_allowance));
        $this->dynamicAllowanceCrudService->getByFilter($request, $output);
        $this->entityName = $this->dynamicAllowanceCrudService->getEntityName();
        $this->getByFilterRelatedObjectsDistinct($request, $output);
    }

    public function delete(array $request, \stdClass &$output): void
    {
        $rules['ids'] = ['required', 'array'];
        $rules['ids.*'] = ['required', 'numeric'];
        $rules['static_value'] = ['required', 'boolean'];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $this->failMessages($validator->messages());

            return;
        }

        $request = $validator->validate();

        $service = $this->getService('allowance', $request['static_value']);

        unset($request['static_value']);
        $service->delete($request, $output);
    }
}
