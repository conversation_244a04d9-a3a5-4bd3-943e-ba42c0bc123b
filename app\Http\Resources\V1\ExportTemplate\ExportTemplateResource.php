<?php

namespace App\Http\Resources\V1\ExportTemplate;

use App\Enums\Employee\EmployeeExportCategoryEnum;
use App\Enums\Employee\EmployeeExportFieldEnum;
use App\Http\Resources\BaseResource;

class ExportTemplateResource extends BaseResource
{

    public function toArray($request)
    {
        $templateData = [
            'id' => $this->id,
            'name' => $this->name,
            'is_default' => $this->is_default,
        ];

        if (is_array($this->fields)) {
            /* 
                Filter fields to only include categories and fields that still exist in the enum
                and Any missing fields from the current enum are added with a default value of false
            */
            
            $filteredFields = $this->filterFieldsToExistingEnums($this->fields);

            $templateData = array_merge($templateData, $filteredFields);

            // Generate binary string for the template fields showing what is false and what is true
            $templateData['binary_representation'] = $this->generateBinaryStringForEmployeeExportTemplate($filteredFields);
        }

        return $templateData;
    }

    private function filterFieldsToExistingEnums(array $fields): array
    {
        $filteredFields = [];

        $categories = array_filter(
            EmployeeExportCategoryEnum::cases(),
            fn($category) => $category !== EmployeeExportCategoryEnum::ESSENTIAL
        );

        foreach ($categories as $category) {
            $categoryValue = $category->value;
            $validFields = EmployeeExportFieldEnum::getFieldsForCategory($category);

            $filteredFields[$categoryValue] = [];

            if (isset($fields[$categoryValue]) && is_array($fields[$categoryValue])) {
                foreach ($fields[$categoryValue] as $field => $isSelected) {
                    // Only include fields that still exist in the enum
                    if (in_array($field, $validFields)) {
                        $filteredFields[$categoryValue][$field] = $isSelected;
                    }
                }
            }
            foreach ($validFields as $field) {
                if (!isset($filteredFields[$categoryValue][$field])) {
                    $filteredFields[$categoryValue][$field] = false;
                }
            }
        }

        return $filteredFields;
    }

    private function generateBinaryStringForEmployeeExportTemplate(array $fields): string
    {
        $binaryString = '';

        $categories = array_filter(
            EmployeeExportCategoryEnum::cases(),
            fn($category) => $category !== EmployeeExportCategoryEnum::ESSENTIAL
        );

        foreach ($categories as $category) {
            $categoryFields = EmployeeExportFieldEnum::getFieldsForCategory($category);
            $categoryValue = $category->value;

            foreach ($categoryFields as $field) {
                $isSelected = isset($fields[$categoryValue][$field]) && $fields[$categoryValue][$field] === true;
                $binaryString .= $isSelected ? '1' : '0';
            }
        }

        return $binaryString;
    }
}
