<?php

namespace App\Models;

use App\DomainData\EmployeeLeaveRequestDto;
use App\Models\Casts\TimezoneDateTime;
use App\Models\StateMachines\RequestState;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\ModelStates\HasStates;

class EmployeeLeaveRequest extends BaseModel
{
    use EmployeeLeaveRequestDto, HasFactory, HasStates, LogsActivity, SoftDeletes;

    protected $guarded = ['id', 'created_at', 'updated_at'];

    protected static $recordEvents = ['updated', 'deleted'];

    protected $casts = [
        'created_at' => TimezoneDateTime::class,
        'updated_at' => TimezoneDateTime::class,
        'deleted_at' => TimezoneDateTime::class.':nullable',
        'status' => RequestState::class,
    ];

    // public function getFromAttribute($value)
    // {
    //     return Carbon::parse($value, 'UTC')->setTimezone(config('app.timezone'))->format('Y-m-d H:i:s');
    // }

    // public function getToAttribute($value)
    // {
    //     return Carbon::parse($value, 'UTC')->setTimezone(config('app.timezone'))->format('Y-m-d H:i:s');
    // }

    // public function setFromAttribute($value)
    // {
    //     if (is_null($value)) {
    //         return null;
    //     }
    //     $this->attributes['from'] = Carbon::parse($value, config('app.timezone'))->setTimezone('UTC')->format('Y-m-d H:i:s');
    // }

    // public function setToAttribute($value)
    // {
    //     if (is_null($value)) {
    //         return null;
    //     }
    //     $this->attributes['to'] = Carbon::parse($value, config('app.timezone'))->setTimezone('UTC')->format('Y-m-d H:i:s');
    // }

    protected static function boot()
    {
        parent::boot();

        static::created(function ($leaveRequest) {
            EmployeeRequest::create([
                'date' => $leaveRequest->created_at,
                'employee_id' => $leaveRequest->employee_id,
                'status' => $leaveRequest->status, // or any default status you want to set
                'comment' => $leaveRequest->note, // if `comment` exists in EmployeeLeaveRequest,
                'request_name' => 'employee_leave_request',
                'requestable_id' => $leaveRequest->id,
                'requestable_type' => 'employee_leave_request',
                'company_id' => $leaveRequest->employee->company_id,
                'requested_by' => config('globals.user')?->employee_id ?? null,
            ]);
        });

    }

    public function companyLeaveType()
    {
        return $this->belongsTo(CompanyLeaveType::class);
    }

    public function companyLeaveTypePolicy()
    {
        return $this->belongsTo(CompanyLeaveTypePolicy::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function employeeRequests()
    {
        return $this->morphMany(EmployeeRequest::class, 'requestable');
    }

    public function employeeRequest()
    {
        return $this->morphOne(EmployeeRequest::class, 'requestable')->latest('id');
    }

    // public function employeeApproves()
    // {
    //     return $this->morphToMany(Employee::class, 'approval', 'approval_cycles',
    //                                                'approval_id', 'employee_id')->withPivot(['id','company_id', 'status', 'status_date', 'order', 'operator'])
    //                                                ->withTimestamps();
    // }

    public function employeeApproves()
    {
        return $this->morphToMany(Role::class, 'requestable', 'workflow_approval_cycle',
            'requestable_id', 'role_id')->withPivot(['id', 'company_id', 'status', 'order', 'operator', 'request_workflow_id', 'employee_id', 'branch_id', 'department_id', 'date'])
            ->withTimestamps();
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable();
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        set_impersonate_activity_causer($activity);
        if ($eventName == 'deleted') {
            return;
        }

        $oldProperties = Arr::get($activity->properties, 'old', null);
        $properties = Arr::get($activity->properties, 'attributes', null);

        $oldStatus = ! is_null($oldProperties) ? Arr::get($oldProperties, 'status', null) : null;
        $newStatus = ! is_null($properties) ? Arr::get($properties, 'status', null) : null;

        if ($newStatus == $oldStatus || $newStatus != config('globals.REQUEST_STATUSES.CANCELLED')) {
            return;
        }

        $actionName = 'leave_cancel';

        $leaveId = $this->id;
        $activity->properties = $activity->properties->merge(['leave_id' => $leaveId,
            'action_name' => $actionName]);
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'rest_day_leave_id');
    }

    public function attachments()
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    public function detach($attachments)
    {

        foreach ($attachments as $attachment) {
            $attachment->update([
                'attachable_type' => null,
                'attachable_id' => null,
            ]);
            $attachment->delete();
        }

    }

    public function attach($attachments)
    {
        $this->attachments()->saveMany($attachments);
    }

    public function workflow()
    {
        return $this->belongsTo(Workflow::class);
    }

    public function workflowApprovalCycles()
    {
        return $this->morphMany(WorkflowApprovalCycle::class, 'requestable');
    }

    public function parentTimecard(): BelongsTo
    {
        return $this->belongsTo(Timecard::class, 'parent_timecard_id');
    }
}
