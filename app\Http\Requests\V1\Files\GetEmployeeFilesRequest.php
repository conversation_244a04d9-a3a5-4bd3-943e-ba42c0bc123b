<?php

namespace App\Http\Requests\V1\Files;

use App\Rules\ScopeBranchIdsRule;
use App\Rules\ScopeDepartmentIdsRule;
use App\Rules\ScopeSubDepartmentIdsRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;

class GetEmployeeFilesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {

        $rules['branch_ids'] = ['array'];
        $rules['branch_ids.*'] = ['integer', new ScopeBranchIdsRule];

        $rules['department_ids'] = ['array'];
        $rules['department_ids.*'] = ['integer', new ScopeDepartmentIdsRule];

        $rules['title_ids'] = ['array'];
        $rules['title_ids.*'] = [
            'integer',
            (new Exists('titles', 'id'))->where(function ($query) {
                $query->where('company_id', auth()->user()->company_id)
                    ->whereNull('deleted_at');
            }),
        ];

        $rules['employee_file_category_ids'] = ['array'];
        $rules['employee_file_category_ids.*'] = ['integer', (new Exists('employee_file_categories', 'id'))->where(function ($query) {
            $query->where('company_id', auth()->user()->company_id) // company rule doesn't apply here
                ->whereNull('deleted_at');
        }), ];
        $rules['page'] = ['integer', 'min:1'];
        $rules['page_size'] = ['integer', 'min:0']; // 0 means no pagination
        $rules['search_value'] = ['min:1', 'max:30'];
        $rules['is_archived'] = ['boolean'];
        $rules['employee_id'] = ['integer', (new Exists('employees', 'id'))->where(function ($query) {
            $query->where('company_id', auth()->user()->company_id) // company rule doesn't apply here
                ->whereNull('deleted_at');
        }), ];

        $rules['statuses'] = ['array'];
        $rules['statuses.*'] = ['string', 'in:completed,expired,expiring_soon,archived'];
        $rules['sub_department_ids'] = ['array'];
        $rules['sub_department_ids.*'] = ['integer', new ScopeSubDepartmentIdsRule];

        return $rules;
    }
}
