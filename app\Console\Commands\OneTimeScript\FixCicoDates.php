<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Cico;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixCicoDates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:cico:dates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $cicos = Cico::where('in_out', 'in')->whereHas('attendance', function ($q) {
                $q->where('slotable_type', 'time_card');
            })->with('attendance.slotable', 'pairedClock')->get();

            $countWrongDates = 0;

            foreach ($cicos as $cico) {
                $pairedClock = $cico->pairedClock;
                $oldCicoDate = $cico->date;
                if (! $pairedClock) {
                    continue;
                }
                $oldPairedCicoDate = $pairedClock->date;
                $cicoDateObject = Carbon::parse($cico->date);
                $pairedCicoDateObject = Carbon::parse($pairedClock->date);
                $slotableDateObject = Carbon::parse($cico->attendance->slotable->from ?? $cico->attendance->date);
                if ($slotableDateObject->diffInHours($cicoDateObject) > 48) {
                    $cicoDate = $slotableDateObject->format('Y-m-d');
                    $cicoTime = $cicoDateObject->format('H:i:s');

                    $pairedCicoDate = $cicoDateObject->diffInDays($pairedCicoDateObject) != 0 ?
                            $slotableDateObject->addDay()->format('Y-m-d') : $slotableDateObject->format('Y-m-d');
                    $pairedCicoTime = $pairedCicoDateObject->format('H:i:s');

                    $cico->update(['date' => $cicoDate.' '.$cicoTime]);
                    $pairedClock->update(['date' => $pairedCicoDate.' '.$pairedCicoTime]);
                    $countWrongDates++;
                    echo "-----------------------------------\n";
                    echo 'Cico ID: '.$cico->id.' has been updated from : '.$oldCicoDate.' to: '.$cico->date.". \n";
                    echo 'Paired Cico ID: '.$pairedClock->id.' has been updated from : '.$oldPairedCicoDate.' to: '.$pairedClock->date.". \n";
                    echo 'timecard Id: '.$cico->attendance->slotable_id.' has date '.$slotableDateObject->format('Y-m-d')." . \n";
                    echo "-----------------------------------\n";
                }
            }

            echo 'Total wrong dates: '.$countWrongDates."\n";
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
            //// dd($e);
        }
    }
}
