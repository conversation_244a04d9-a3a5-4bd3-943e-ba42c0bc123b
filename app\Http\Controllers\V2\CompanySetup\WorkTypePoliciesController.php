<?php

namespace App\Http\Controllers\V2\CompanySetup;

use App\Http\Requests\V2\WorkTypePolicy\AddWorkTypePolicyRequest;
use App\Http\Requests\V2\WorkTypePolicy\EditWorkTypePolicyRequest;
use App\Http\Resources\V2\CompanySetup\WorkTypePolicyResource;
use App\Services\V2\WorkTypePolicies\WorkTypePolicyService;
use App\Traits\DataPreparation;
use App\Traits\GetIdsTrait;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WorkTypePoliciesController
{
    use DataPreparation, GetIdsTrait;

    private $titlesRepository;

    private $departmentRepository;

    private $employeeRepository;

    private $areaRepository;

    private $branchRepository;

    public function __construct(
        private WorkTypePolicyService $workTypePoliciesService,
    ) {}

    public function getWorkTypePolicies()
    {
        $workTypePolicies = $this->workTypePoliciesService->listWorkTypePolicies();

        return getResponseStructure(['data' => WorkTypePolicyResource::collection($workTypePolicies)],
            HttpStatusCodeUtil::OK, 'Work Type Policies');
    }

    public function addWorkTypePolicy(AddWorkTypePolicyRequest $request)
    {
        $data = $request->validated();
        try {
            DB::transaction(function () use ($data) {
                $this->workTypePoliciesService->addWorkTypePolicy($data);
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Added successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw $e;
        }
    }

    public function updateWorkTypePolicy(EditWorkTypePolicyRequest $request, $id)
    {
        $data = $request->validated();
        try {
            DB::transaction(function () use ($id, $data) {
                $this->workTypePoliciesService->updateWorkTypePolicy($id, $data);
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Edited successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw $e;
        }
    }
}
