<?php

namespace App\Http\Requests\V1\Billing;

use Illuminate\Foundation\Http\FormRequest;

class PayBillRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'discount' => [
                'nullable',
                'integer',
            ],
            'payment_date' => [
                'required',
                'date',
            ],
            'status' => [
                'nullable',
                'string',
            ],
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'status' => config('globals.BILL_STATUS.PAID'),
            'discount' => $this->input('discount', 0),
        ]);
    }
}
