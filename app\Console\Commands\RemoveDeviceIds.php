<?php

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RemoveDeviceIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:device-ids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove all device ids from users table';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            DB::table('users')->update(['device_id' => null]);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();
        }
    }
}
