<?php

namespace App\Services\V1\LeaveManagement;

use App\Models\Employee;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\Repository;
use App\Repositories\V1\Leaves\EmployeeLeaveBalancesRepository;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Util\EmployeeUtil;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Monolog\Logger;

class FillEmployeeBalancesService
{
    use PayrollHelper;

    private Logger $logger;

    private $balanceMap;

    private $oldEmployeeLeaveBalanceRepository;

    private $employeeLeaveBalanceRepository;

    public function __construct(private SystemSettingRepository $systemSettingRepository)
    {
        $this->oldEmployeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
        $this->employeeLeaveBalanceRepository = new EmployeeLeaveBalancesRepository;
        $this->balanceMap = [];
    }


    public function fill($employees = null, $companyIds = null, $leaveTypeId = null)
    {
        try {
            $employees = $this->getEmployees($employees, $companyIds);
            foreach ($employees as $employee) {

                if ($this->shouldSkipEmployee($employee)) {
                    continue;
                }

                $this->processEmployeeLeaveBalances($employee, $leaveTypeId);
            }
        } catch (Exception $e) {
            Log::info($e->getMessage());
            dd($e);
        }
    }

    private function getEmployees($employees, $companyIds = null)
    {
        return $employees ?? Employee::with('employeeInfo', 'title.companyLeaveTypePolicies', 'company.maternityLeaveType', 'user', 'employeeLeaveBalances')
            ->when(isset($companyIds), function ($query) use ($companyIds) {
                return $query->whereIn('company_id', $companyIds);
            })->get();
    }

    private function shouldSkipEmployee($employee)
    {
        return $employee->status == EmployeeUtil::STATUSES['TERMINATED'] ||
            !$employee?->title?->companyLeaveTypePolicies->isNotEmpty();
    }

    private function processEmployeeLeaveBalances($employee, $leaveTypeId = null)
    {
        if (!isset($employee->title)) {
            return;
        }
        Log::info('processEmployeeLeaveBalances for employee ' . $employee->id);
        $companyLeaveTypePolicies = $employee->title->companyLeaveTypePolicies;

        if ($leaveTypeId) {
            $companyLeaveTypePolicies = $companyLeaveTypePolicies->filter(function ($policy) use ($leaveTypeId) {
                return $policy->company_leave_type_id == $leaveTypeId;
            });
        }

        $companyPublicHolidayId = $employee->company->publicHolidayLeaveType?->id ?? null;
        $maternityLeaveId = $employee->company->maternityLeaveType?->id;

        foreach ($companyLeaveTypePolicies as $companyLeaveTypePolicy) {

            if ($this->shouldSkipLeaveType($employee, $companyLeaveTypePolicy, $companyPublicHolidayId, $maternityLeaveId)) {
                continue;
            }

            $this->generateLeaveBalancesForEmployee($employee, $companyLeaveTypePolicy);
        }
    }

    private function shouldSkipLeaveType($employee, $policy, $publicHolidayId, $maternityLeaveId)
    {
        return
            $policy->company_leave_type_id == $publicHolidayId
            ||
            $employee?->employeeInfo?->gender == 'male' && $policy->company_leave_type_id == $maternityLeaveId
            ||
            (isset($employee->company->OvertimeLeaveType) && $policy->company_leave_type_id == $employee->company->OvertimeLeaveType->id);
    }

    private function generateLeaveBalancesForEmployee($employee, $companyLeaveTypePolicy)
    {
        config(['globals.user' => $employee->user]);
        // Step 1: The service looks back to the employee's join date (or up to 1 year ago)
        // For an employee who joined a year ago, this will process 12 past months + future months
        $firstBalanceDate = $this->getFirstBalanceDateToBeAdded($employee);
        $endDate = now()->addMonths(3);
        $date = Carbon::parse($firstBalanceDate)->startOfMonth();

        // Step 2: This loop processes EVERY month from join date until 3 months in the future
        // For each month, it will either create a new balance or update an existing one
        while ($date->lte($endDate)) {
            $dateCopy = clone $date;
            $this->handleBalanceForDate($employee, $companyLeaveTypePolicy, $dateCopy);
            $date->addMonth();
        }
        config(['globals.user' => auth()->user()]);
    }

    private function getFirstBalanceDateToBeAdded($employee)
    {
        // Step 3: Gets the starting point - either join date or 1 year ago, whichever is more recent
        // For an employee who joined >1 year ago, this returns 1 year ago
        return Carbon::parse(max($employee?->employeeInfo?->join_date, now()->subYear()->toDateString())) ?? now();
    }

    private function handleBalanceForDate($employee, $companyLeaveTypePolicy, $date)
    {
        $month = Carbon::parse($date)->month;
        $payrollClosingDate = $this->getMonthlyClosingDay($date);
        [$start, $end] = $this->calculatePeriod($companyLeaveTypePolicy, $date, $payrollClosingDate);
        if (!$start || !$end) {
            return;
        }

        $companyLeaveTypeGender = $companyLeaveTypePolicy->companyLeaveType->gender;
        if ($companyLeaveTypeGender != $employee->employeeInfo?->gender && $companyLeaveTypeGender != 'all') {
            return;
        }

        $key = $employee->id . '-' . $end . '-' . $companyLeaveTypePolicy->id;
        $leaveBalance = Arr::get($this->balanceMap, $key) ??
            $this->employeeLeaveBalanceRepository->leaveBalanceForEmployee($employee, $end, $companyLeaveTypePolicy->id);

        if (!$leaveBalance) {
            // Step 4: Creates a new balance if one doesn't exist for this period
            $leaveBalance = $this->createLeaveBalance($employee, $companyLeaveTypePolicy, $start, $end);
            $this->balanceMap[$key] = $leaveBalance;

        } else {
            // Step 5: CRITICAL - For monthly prorated leaves, when today is the 1st of the month
            // this will call updateProratedBalance for EVERY month between join_date and now
            // This is why we see ~110 hours added each month (11 × 10 hour monthly increments)
            if ($companyLeaveTypePolicy->prorated_monthly && now()->toDateString() == now()->startOfMonth()->toDateString()) { // dont add prorated balance for future months
                $this->updateProratedBalance($employee, $companyLeaveTypePolicy, $leaveBalance, $date);
            }
            $deductMonth = $companyLeaveTypePolicy->transferred_balance_usable_until + 1;

            if ($companyLeaveTypePolicy->transferred_balance_usable_until != 0 && Carbon::now()->month >= $deductMonth) { // no need to handle the case of (transferred_balance_usable_until == 12 ) this means the balance can be used till the end of the year

                if ($leaveBalance->expired_transferred_balance == 0.00) {
                    $this->deductTransferredBalance($leaveBalance);
                }
            }
            $leaveBalance->refresh();
        }
    }

    private function calculatePeriod($policy, $date, $payrollClosingDate)
    {
        $dateCopy = clone $date;
        switch ($policy->companyLeaveType?->balance_period) {
            case config('globals.BALANCE_PERIODS.CALENDAR_MONTH'):
                return [
                    $dateCopy->startOfMonth()->toDateTimeString(),
                    $dateCopy->endOfMonth()->toDateTimeString(),
                ];
            case config('globals.BALANCE_PERIODS.PAYROLL_MONTH'):
                $end = $dateCopy->days($payrollClosingDate)->endOfDay()->toDateTimeString();
                $start = $this->getPayrollMonthStartBasedOnMonthEnd($end);

                return [
                    $start,
                    $end,
                ];
            case config('globals.BALANCE_PERIODS.CALENDAR_YEAR'):
                return [
                    $dateCopy->startOfYear()->toDateTimeString(),
                    $dateCopy->endOfYear()->toDateTimeString(),
                ];
            default:
                return [null, null];
        }
    }

    private function createLeaveBalance($employee, $companyLeaveTypePolicy, $start, $end)
    {
        // Step 6: When creating a balance for a past period (previous year/month)
        // it sets the FULL original balance instead of prorating it
        $originalBalance = $this->calculateBalance($employee, $companyLeaveTypePolicy);
        $transferredBalance = $this->getTransferredBalance($employee, $companyLeaveTypePolicy);
        $isLeaveTypeProratedMonthly = $companyLeaveTypePolicy->prorated_monthly;
        $totalProratedBalance = 0;

        if ($isLeaveTypeProratedMonthly) {
            if ($start > now()->toDateTimeString()) {
                $balance = 0;
            } elseif ($end <= now()->toDateTimeString()) {
                // Step 7: For past periods, sets the full annual balance (e.g., 120 hours)
                // instead of prorating it properly
                $balance = $originalBalance;
                $totalProratedBalance = $originalBalance;
            } else {
                $balance = $transferredBalance + ($originalBalance / 12);
                $totalProratedBalance = $originalBalance / 12;
            }
        } else {
            $balance = $originalBalance + $transferredBalance;
        }

        return $this->employeeLeaveBalanceRepository->add([
            'company_leave_type_id' => $companyLeaveTypePolicy->companyLeaveType->id,
            'company_leave_type_policy_id' => $companyLeaveTypePolicy->id,
            'employee_id' => $employee->id,
            'balance' => $balance,
            'start' => $start,
            'end' => $end,
            'total_prorated_balance' => $totalProratedBalance,
            'transferred_balance' => $transferredBalance,
        ]);
    }

    public function updateProratedBalance($employee, $companyLeaveTypePolicy, $leaveBalance, $date)
    {
        if (Carbon::parse($date)->toDateString() != now()->toDateString()) {
            return;
        }

        $originalBalance = $this->calculateBalance($employee, $companyLeaveTypePolicy);

        $balance = $leaveBalance->balance;
        $monthlyIncrement = $originalBalance / 12;
        $balance += $monthlyIncrement;

        $transferredBalance = $this->getTransferredBalance($employee, $companyLeaveTypePolicy);
        $balance += $transferredBalance;

        // Subtract the debit_balance from the balance
        $balance = max(0, $balance - $leaveBalance->debit_balance);

        $leaveBalance->update([
            'balance' => $balance,
            'total_prorated_balance' => $leaveBalance->total_prorated_balance + $originalBalance / 12,
            'debit_balance' => max(0, $leaveBalance->debit_balance - ($originalBalance / 12)),
        ]);
    }

    public function deductTransferredBalance($leaveBalance)
    {
        $expiredTransferredBalance = $leaveBalance->transferred_balance - $leaveBalance->used_transferred_balance;

        $leaveBalance->update([
            'balance' => $leaveBalance->balance - $expiredTransferredBalance,
            'expired_transferred_balance' => $expiredTransferredBalance,
        ]);
    }

    private function calculateBalance($employee, $companyLeaveTypePolicy)
    {
        $balance = $companyLeaveTypePolicy->base_balance;

        $isEligibleForExtraBalance = $this->isEligibleForExtraBalance($employee, $companyLeaveTypePolicy);

        if ($isEligibleForExtraBalance) {
            // Step 12: For eligible employees (>10 years experience or >50 years old),
            // adds 9 extra days (72 hours) to annual balance
            $balance += 9 * 8; // Add 9 days of extra hours
        }

        return $balance;
    }

    public function getTransferredBalance($employee, $companyLeaveTypePolicy): int
    {
        $previousBalance = $this->getPreviousBalance($employee, $companyLeaveTypePolicy);
        $transferredBalance = 0;

        if (
            $companyLeaveTypePolicy->companyLeaveType->balance_period == config('globals.BALANCE_PERIODS.CALENDAR_YEAR')
            &&
            $companyLeaveTypePolicy->allow_balance_transfer
            &&
            now()->startOfYear()->toDateString() == now()->toDateString()
            &&
            (isset($previousBalance) && $previousBalance?->created_at < now()->toDateString()) // check if the balance isn't created by the cron job now
        ) {
            Log::info('BALANCE_DEBUG: All conditions met for transfer');
            $balanceDoesntExpire = $companyLeaveTypePolicy->transferred_balance_usable_until == 0;
            $previousTransferredBalance = $balanceDoesntExpire ? ($previousBalance->transferred_balance - $previousBalance->used_transferred_balance) : 0;
            $transferredBalance = min($companyLeaveTypePolicy->max_transfer_balance, $previousBalance->balance + $previousTransferredBalance);
        }

        return $transferredBalance;
    }

    private function isEligibleForExtraBalance($employee, $policy)
    {
        $employeeAge = Carbon::parse($employee?->employeeInfo?->birth_date)->age ?? 0;
        $yearsOfExperience = $employee?->employeeInfo?->number_of_years_of_experience ?? 0;
        $companyAnnualLeaveId = $employee->company->annual_leave_id;

        return ($yearsOfExperience >= 10 || $employeeAge > 50) &&
            $policy->company_leave_type_id === $companyAnnualLeaveId;
    }

    private function getPreviousBalance($employee, $companyLeaveTypePolicy)
    {
        if (Carbon::parse($employee->created_at)->todateString() >= now()->startOfYear()->toDateString()) {
            return null;
        }

        $prevBalance = $this->oldEmployeeLeaveBalanceRepository->leaveBalanceForEmployee(
            $employee->id,
            now()->subYear()->toDateString(),
            $companyLeaveTypePolicy->id
        );

        return $prevBalance;
    }
}
