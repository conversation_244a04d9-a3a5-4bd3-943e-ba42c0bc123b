<?php

namespace App\Services\TimeTracking\BusinessServices;

use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\Repository;
use App\Services\IBusinessService;
use App\Traits\DataPreparation;
use App\Traits\V1\EmployeeRequestsTrait;
use App\Traits\WorkflowTrait;
use App\Util\AttendanceUtil;
use Illuminate\Support\Facades\Log;
use stdClass;
use Workflow\WorkflowStub;
use App\Jobs\AssignApprovalsJob;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;

class DeductionActionsService implements IBusinessService
{
    use DataPreparation, EmployeeRequestsTrait, WorkflowTrait, PrepareAssignRequestCycleDataTrait;

    private $attendanceDeductionRepository;

    private $attendanceDeduction;

    private $employeeId;

    private Repository $requestCycleRepository;

    private Repository $employeeRepository;

    private $workflow;

    public function __construct(
        private ApplyRequestCycleRulesService $applyRequestCycleRulesService,
        private AssignApprovalRequestToEmployeesService $assignApprovalRequestToEmployeesService,
        private AddRequestsToEmployeeRequest $addRequestsToEmployeeRequest,
        private PayrollsRepository $payrollsRepository

    ) {
        $this->attendanceDeductionRepository = Repository::getRepository('AttendanceDeduction');
        $this->requestCycleRepository = Repository::getRepository('RequestCycle');
        $this->employeeRepository = Repository::getRepository('Employee');

    }

    public function isValid(array $request, stdClass &$output): bool
    {

        $this->attendanceDeduction = $this->attendanceDeductionRepository->getDeductionApprovalCycle($request['id']);
        if (! isset($this->attendanceDeduction)) {
            $output->Error = ['Invalid attendance deduction Id', 'المعرف الخاص بالخصم غير صحيح'];

            return false;
        }

        if ($request['action'] == 'waive') {
            $hasWaiveRequestBefore = ! is_null($this->attendanceDeduction->employeeRequests->first());
            if ($hasWaiveRequestBefore) {
                $output->Error = ['There is a waived request has been generated before', 'تم انشاء طلب الغاء قبل ذلك'];

                return false;
            }
        }

        // this condition for the case of the waive request because the workflow is not created yet
        if ($request['action'] != 'waive' || $request['action'] != 'cancel') {
            if ($this->attendanceDeduction->status != 'applied') {
                $output->Error = ['This request is already completed', 'هذا الطلب مكتمل بالفعل'];

                return false;
            }
        }

        $this->employeeId = config('globals.user')->employee_id;
        if (is_null($this->employeeId)) {
            $output->Error = ['There is no employee id for this user', 'هذا المستخدم ليس لديه معرف خاص بالموظف'];

            return false;
        }

        $payrollOfDeduction = $this->payrollsRepository->payrollCoversDate($this->attendanceDeduction->date);
        if (isset($payrollOfDeduction) && $payrollOfDeduction->status == 'finalized') {
            $output->Error = ['You can not take an action on this request because the payroll is finalized', 'لا يمكنك اتخاذ اجراء على هذا الطلب ﻷنه تم الانتهاء من حساب المرتبات'];

            return false;
        }

        return true;
    }

    public function perform(array $request, stdClass &$output): void
    {
        if (! $this->isValid($request, $output)) {
            return;
        }

        if ($request['action'] == 'waive') {
            $this->waiveDeduction($request, $output);
        } else {
            $this->actionOnDeduction($output, $request['action']);
        }
        $employeeRequest = $this->attendanceDeduction->employeeRequest;
        if ($employeeRequest) {
            $updatedRequest = $this->requestShouldBeHidden($employeeRequest);
            Log::info('Request hidden status updated: '.($updatedRequest->is_hidden ? 'Hidden' : 'Visible').' for request ID: '.$updatedRequest->id);
        }

    }

    public function actionOnDeduction(stdClass &$output, $actionType): void
    {
        try {
            $roleIds = config('globals.user')->roles->pluck('id')->toArray();
            $userCanTakeAnAction = $this->canTakeAnAction($roleIds, $this->attendanceDeduction->id, config('globals.REQUEST_WORKFLOW_TYPES.EDIT_ATTENDANCE_DEDUCTION'), $actionType);
            if (! $userCanTakeAnAction) {
                $output->Error = ['You are not allowed to take this action', 'غير مسموح لك باتخاذ هذا الاجراء'];

                return;
            }
            $deductionValue = isset($output->deduction_value) ? $output->deduction_value : $this->attendanceDeduction?->updated_value ?? $this->attendanceDeduction->deduction_value;
            $reason = isset($output->reason) ? $output->reason : $this->attendanceDeduction?->employeeRequests?->first()->comment ?? null;

            $this->doAnAction($actionType);
            $updatedData = [
                'comment' => $reason,
                'updated_by' => config('globals.user')?->employee_id,
                'request_name' => 'waive_deduction',
            ];
            if($this->checkRequestIsCompleted($this->attendanceDeduction->employeeRequest)){
                $finalStatus = $this->getFinalStatus($this->attendanceDeduction->employeeRequest, $actionType);
                $this->updateRequest($this->attendanceDeduction->employeeRequest, $finalStatus, $updatedData);
                $updatedData = [
                    'updated_value' => null,
                ];
                if($finalStatus == 'cancelled'){
                    $this->updateEntity($this->attendanceDeduction, 'applied', $updatedData);
                    $this->addLogsForCancelledDeduction($this->attendanceDeduction);
                }
                elseif($finalStatus == 'approved'){
                    $this->updateEntity($this->attendanceDeduction, $deductionValue == 0 ? 'waived' : 'applied', [
                        'updated_by' => config('globals.user')->employee_id,
                        'updated_value' => $deductionValue
                    ]);
                }
                elseif($finalStatus == 'rejected'){
                    $this->updateEntity($this->attendanceDeduction, 'applied', $updatedData);
                }
            }

        } catch (\Exception $e) {
            Log::info($e);
            $output->Error = ['An error Occured', 'لقد حدث خطأ ما'];

            return;
        }

    }

    public function waiveDeduction(array $request, stdClass &$output)
    {
        $employee = $this->employeeRepository->getById($this->attendanceDeduction->employee_id);

        $attendanceDeductionEntity = $this->attendanceDeduction->withoutRelations();
        $userId = config('globals.user')->employee_id;
        $waiveDeductionRequestData = $this->prepareEmployeeRequestData($userId, $attendanceDeductionEntity, config('globals.EMPLOYEE_REQUEST_NAMES.WAIVE_DEDUCTION'));
        $waiveDeductionRequestData['request_data']['comment'] = isset($request['comment']) ? $request['comment'] : '';
        $this->addRequestsToEmployeeRequest->perform($waiveDeductionRequestData, $output);

        $requesterRoleIds = config('globals.user')->roles->pluck('id')->toArray();
        AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('waive_late_deduction', $this->attendanceDeduction, $requesterRoleIds));
        unset($output->employee_request);
        // dispatch(new WaiveDeductionRequestPushNotificationJob($approvingEmployees, config('globals.user'), $employee))->afterCommit(); // done

    }

    public function getRequestData()
    {
        return [
            'requestable_id' => $this->attendanceDeduction->id,
            'requestable_type' => config('globals.REQUEST_WORKFLOW_TYPES.EDIT_ATTENDANCE_DEDUCTION'),
            'role_ids' => auth()->user()->roles->pluck('id')->toArray(),
        ];
    }

    public function updateEmployeeRequest($attendanceOvertime, $reason, $actionType)
    {
        $employeeRequest = $attendanceOvertime->employeeRequests->first();

        $updatedData = [
            'comment' => $reason,
            'updated_by' => config('globals.user')?->employee_id,
            'request_name' => 'waive_deduction',
        ];
        if ($actionType == 'cancel') {
            $updatedData['status'] = config('globals.REQUEST_STATUSES.CANCELLED');
        }

        $employeeRequest->update($updatedData);
        $employeeRequest->save();
    }

    public function addLogsForCancelledDeduction($deduction): void
    {
        $employee = auth()->user()->employee;

        activity(AttendanceUtil::CANCELLED_EDIT_DEDUCTION)
            ->on($deduction)
            ->causedBy($employee)
            ->withProperties(['data' => $deduction])
            ->event('cancelled')
            ->log('employee id '.$employee->id.' cancelled edit deduction request');
    }
}
