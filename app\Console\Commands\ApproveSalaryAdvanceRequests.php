<?php

namespace App\Console\Commands;

use App\Models\SalaryAdvance;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ApproveSalaryAdvanceRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:approve-salary-advance-requests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $salaryAdvanceRequests = SalaryAdvance::where('status', 'rejected')
            ->whereHas('employeeRequest', function ($query) {
                $query->where('status', 'approved');
            })
            ->get();

            $this->info('salary advance requests: ' . $salaryAdvanceRequests->count());
            foreach ($salaryAdvanceRequests as $salaryAdvanceRequest) {
                $this->info('approving salary advance request: ' . $salaryAdvanceRequest->id);
                $salaryAdvanceRequest->update(['status' => 'paid_in_full']);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error: ' . $e);
        }
    }
}
