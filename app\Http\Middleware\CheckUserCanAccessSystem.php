<?php

namespace App\Http\Middleware;

use App\Traits\EmployeeRule;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckUserCanAccessSystem
{
    use EmployeeRule;

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $employee = auth()->user()->employee;
        //        if ($this->isEmployeeTerminated($employee) || $this->isEmployeeProbationEnd($employee)) {
        if ($this->isEmployeeTerminated($employee) || ($this->isCompanyDeactivated($employee) && ! $this->isItImpersonateAction())) {
            return response()->json(['message' => 'You are not allowed to access the system.'], 401);
        }

        return $next($request);
    }
}
