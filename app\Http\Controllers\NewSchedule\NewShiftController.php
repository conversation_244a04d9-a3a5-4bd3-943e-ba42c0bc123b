<?php

namespace App\Http\Controllers\NewSchedule;

use App\DomainData\NewShiftDto;
use App\Http\Controllers\Controller;
use App\Services\NewSchedule\BusinessServices\LinkTitlesBranchesWithShiftService;
use App\Services\NewSchedule\CrudServices\NewShiftCrudService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use stdClass;

class NewShiftController extends Controller
{
    use NewShiftDto;

    public function __construct(
        protected NewShiftCrudService $service,
        protected LinkTitlesBranchesWithShiftService $linkTitlesBranchesWithShiftService
    ) {}

    public function create(array $request, stdClass &$output): void
    {
        $rules = $this->getRules();

        $rules = $this->titlesAndBranchesRules($rules);

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $this->service->create($request, $output);

    }

    public function update(array $request, stdClass &$output): void
    {
        $rules = $this->getRules();
        $rules = $this->titlesAndBranchesRules($rules);

        $rules['check_warning'] = 'boolean';
        $rules['id'] = 'required|numeric';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->update($request, $output);

    }

    public function getAll(array $request, stdClass &$output): void
    {

        $rules['branch_id'] = 'numeric';
        $rules['from_date'] = 'date_format:Y-m-d|nullable';
        $rules['to_date'] = 'date_format:Y-m-d|nullable';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['branch_id'])) {
            $request['branch_id'] = config('globals.branchId');
        }

        $this->service->getAll($request, $output);

    }

    public function getFiltered(array $data, stdClass &$output): void
    {
        $validator = Validator::make($data, $this->getFilteredShiftsRules());
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $validatedData = $validator->validate();
        $this->service->getShiftsFiltered($validatedData, $output);
    }

    public function delete(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|numeric';
        $rules['check_warning'] = 'boolean';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $this->service->delete($request, $output);

    }

    public function getClosestShiftsToEmployee(array $request, stdClass &$output): void
    {
        $rules['employee_id'] = ['required', 'integer'];
        $rules['clock_in'] = ['required', 'date_format:Y-m-d H:i:s'];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $this->service->getClosestShiftsToEmployee($request, $output);
    }

    // public function assignTitlesBranchesToShift(array $request, \stdClass &$output) :void
    // {
    //     $rules = $this->titlesAndBranchesRules();

    //     $validator = \Validator::make($request, $rules);
    //     if ($validator->fails()) {
    //         $output->Error = $validator->messages();
    //         return;
    //     }

    //     $request = $validator->validate();

    //     $this->linkTitlesBranchesWithShiftService->perform($request, $output);

    // }

    // public function titlesAndBranchesRules() :array
    // {
    //     $rules['shift_id'] = 'required|numeric';
    //     $rules['branches'] = 'required|array';
    //     $rules['branches.*.id'] = 'required|numeric';
    //     $rules['branches.*.planned_headcount'] = 'numeric';
    //     $rules['branches.*.titles'] = 'array';
    //     $rules['branches.*.titles.*.id'] = 'required|numeric';
    //     $rules['branches.*.titles.*.planned_headcount'] = 'numeric';

    //     return $rules;
    // }

    public function getFilteredShiftsRules(): array
    {
        return [
            'branch_id' => ['required', 'exists:branches,id', Rule::exists('branch_employee', 'branch_id')
                ->where(function ($query) {
                    $query->where('employee_id', auth()->user()->employee_id);
                })],
            'from' => 'required|date',
            'to' => 'required|date|after:date_from',
        ];
    }
}
