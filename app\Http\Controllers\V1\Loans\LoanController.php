<?php

namespace App\Http\Controllers\V1\Loans;

use App\Exports\OutstandingExport;
use App\Exports\PaidInFullExport;
use App\Exports\PendingExport;
use App\Http\Controllers\NewController;
use App\Http\Requests\CreateLoanPolicyRequest;
use App\Http\Requests\EditLoanPolicyRequest;
use App\Http\Requests\GetLoanAndSalaryAdvanceByFilter;
use App\Http\Requests\V1\Loans\AddLoanRequest;
use App\Http\Requests\V1\Loans\AddLoanByManagerRequest;
use App\Http\Requests\V1\Loans\AddSalaryAdvanceByManagerRequest;
use App\Http\Requests\V1\Loans\BulkDisburseRequest;
use App\Http\Requests\V1\Loans\DisburseRequest;
use App\Http\Requests\V1\Loans\EditLoanRequest;
use App\Http\Resources\LoanAndSalaryAdvanceResourceCollection;
use App\Http\Resources\LoanCollection;
use App\Http\Resources\LoanWarningResource;
use App\Http\Resources\PolicyCollectionResource;
use App\Http\Resources\SalaryAdvanceProfileCollection;
use App\Repositories\V1\Loans\SalaryAdvanceRepository;
use App\Services\V1\Loans\LoanService;
use App\Services\V1\Loans\SalaryAdvanceService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Requests\V1\PayrollHub\LoanOrSalaryAdvanceDecisionRequest;
use App\Repositories\NewEmployeeRepository;

class LoanController extends NewController
{
    private $salaryAdvanceService;

    private SalaryAdvanceRepository $salaryAdvanceRepository;

    public function __construct(
        private NewEmployeeRepository $newEmployeeRepository,
        LoanService $loanService)
    {
        parent::__construct($loanService);
        $this->salaryAdvanceRepository = new SalaryAdvanceRepository;
        $this->salaryAdvanceService = new SalaryAdvanceService($this->salaryAdvanceRepository);
    }

    public function getEmployeeLoan(Request $request)
    {
        // Fetch the loans and salary advances
        $loans = $this->service->getEmployeeLoan($request);
        $salaryAdvances = $this->salaryAdvanceService->getEmployeeSalaryAdvance($request);

        // i want to count only the loans with status disbursed or ready_to_disburse
        $outstandingLoans = $loans->filter(function ($loan) {
            return in_array($loan->status, ['disbursed', 'ready_to_disburse']);
        });
        $outstandingSalaryAdvance = $salaryAdvances->filter(function ($salaryAdvances) {
            return in_array($salaryAdvances->status, ['disbursed', 'ready_to_disburse']);
        });
        $outstandingLoansCount = $outstandingLoans->count();
        $outstandingSalaryAdvancesCount = $outstandingSalaryAdvance->count();

        return getResponseStructure([
            'data' => [
                'loans' => new LoanCollection($loans),
                'salary_advances' => new SalaryAdvanceProfileCollection($salaryAdvances),
                'outstanding_loans_count' => $outstandingLoansCount,
                'outstanding_salary_advance_count' => $outstandingSalaryAdvancesCount,
            ],
        ], HttpStatusCodeUtil::OK);
    }

    public function getMaxLoanTenors(Request $request)
    {
        try {
            $maxTenor = $this->service->getMaxLoanTenors($request);

            return getResponseStructure(['data' => ['max_loan_tenor' => $maxTenor]], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }
    }

    public function getMaxLoanAmount(Request $request)
    {
        $loans = $this->service->getMaxLoanAmount($request);

        return getResponseStructure(['data' => ['max_loan_amount' => $loans]], HttpStatusCodeUtil::OK);
    }

    public function canRequestLoan(Request $request)
    {
        $employeeId = $request->input('employee_id');
        $employee = $employeeId
            ? $this->newEmployeeRepository->findOrFail($employeeId)
            : auth()->user()->employee;
        $result = $this->service->employeeCanRequestLoan($employee);

        if (!$result['can_request']) {
            return response()->json([
                'Error' => [$result['error']],
                'Warning' => []
            ], HttpStatusCodeUtil::UNPROCESSABLE_ENTITY);
        }

        return getResponseStructure(['data' => ['can_request_loan' => true]], HttpStatusCodeUtil::OK);
    }

    public function loanTerminationWarning()
    {
        $remainingLoanAmount = $this->service->terminationWarning();

        return getResponseStructure(['data' => ['remaining_loan_amount' => $remainingLoanAmount]], HttpStatusCodeUtil::OK);
    }

    public function repayLoanOnTermination(int $loanId)
    {
        DB::beginTransaction();
        try {
            $this->service->repayLoanOnTermination($loanId);
            DB::commit();

            return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);

            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }
    }

    public function notRepayLoanOnTermination(int $loanId)
    {
        DB::beginTransaction();
        try {
            $this->service->notRepayLoanOnTermination($loanId);
            DB::commit();

            return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);

            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }
    }

    public function requestLoan(AddLoanRequest $request)
    {
        $data = $request->validated();
        $loanRequest = [];
        DB::beginTransaction();
        try {
            $loanRequest = $this->service->requestLoan($data);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }

        return getResponseStructure(['data' => $loanRequest],
            HttpStatusCodeUtil::OK, 'Loan requested successfully');

    }

    public function disburseLoan($id, DisburseRequest $request)
    {
        $request = $request->validated();
        DB::beginTransaction();
        try {
            $loanRequest = $this->service->disburseLoan($id, $request);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }

        return getResponseStructure(['data' => $loanRequest],
            HttpStatusCodeUtil::OK, 'Loan requested successfully');

    }

    public function enableLoansPolicy()
    {
        $this->service->enableLoanPolicyToggle();

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function addLoanPolicy(CreateLoanPolicyRequest $request)
    {
        $request->validated();
        DB::beginTransaction();
        try {
            $loanPolicy = $this->service->createLoanPolicy($request);
            DB::commit();

            return getResponseStructure(['data' => $loanPolicy], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }

    }

    public function titleHasLoanPolicyWarning(Request $request)
    {
        // Extract 'titles' from query parameters instead of the request body
        $titles = $request->query('titles', []); // Get query parameters, default to an empty array if not provided

        if (empty($titles)) {
            return getResponseStructure(['message' => 'No titles provided'], HttpStatusCodeUtil::BAD_REQUEST);
        }

        try {
            $assignedTitles = $this->service->loanPolicyWarning($titles);
            DB::commit();

            return getResponseStructure(['data' => $assignedTitles], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }
    }

    public function editLoanPolicy(EditLoanPolicyRequest $request, int $id)
    {
        $request->validated();
        DB::beginTransaction();
        try {
            $loanPolicy = $this->service->editLoanPolicy($request, $id);
            DB::commit();

            return getResponseStructure(['data' => $loanPolicy], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }

    }

    public function getLoanPolicies()
    {
        $policies = $this->service->showLoanAndSalaryAdvancePolicies();

        return getResponseStructure(['data' => new PolicyCollectionResource($policies)], HttpStatusCodeUtil::OK);
    }

    public function approveLoan(int $id)
    {
        DB::beginTransaction();
        try {
            $loanRequest = $this->service->approveLoan($id);
            DB::commit();

            return getResponseStructure(['data' => $loanRequest], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }
    }

    public function cancelLoan(int $id)
    {
        DB::beginTransaction();
        try {
            $loanRequest = $this->service->forceCancelLoan($id);
            DB::commit();

            return getResponseStructure(['data' => $loanRequest], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }
    }

    public function rejectLoan(int $id)
    {
        DB::beginTransaction();
        try {
            $loanRequest = $this->service->rejectLoan($id);
            DB::commit();

            return getResponseStructure(['data' => $loanRequest], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }
    }

    public function editLoan(int $id, EditLoanRequest $request)
    {
        $request = $request->validated();

        DB::beginTransaction();
        try {
            $loanRequest = $this->service->editLoan($id, $request);
            DB::commit();

            return getResponseStructure(['data' => $loanRequest], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }
    }

    public function getLoansAndSalaryAdvancesByFilter(GetLoanAndSalaryAdvanceByFilter $request)
    {
        $data = $request->validated();
        $data['page_size'] = $data['page_size'] ?? 10;
        $loansAndSalaryAdvanceData = $this->service->getLoanAndSalaryAdvanceByFilter($data);
        $paginatedData = (new LoanAndSalaryAdvanceResourceCollection($loansAndSalaryAdvanceData))->response()->getData();

        return getResponseStructure([
            'data' => $paginatedData->data,
            'pagination' => $paginatedData->meta,
        ], HttpStatusCodeUtil::OK, 'Loans and salary advances filtered successfully');
    }

    public function getReadyToDisburseLoans()
    {
        $classifiedLoans = $this->service->getReadyToDisburseLoansForTerminatedAndActiveEmployees();

        return getResponseStructure(['data' => [
            'terminated_employee_loans' => LoanWarningResource::collection($classifiedLoans['terminated_employee_loans']),
            'active_employee_loans' => LoanWarningResource::collection($classifiedLoans['active_employee_loans']),
        ]], HttpStatusCodeUtil::OK);
    }

    public function disburseAllLoans()
    {
        DB::beginTransaction();
        try {
            $this->service->disburseAllReadyToDisburseLoans();
            DB::commit();

            return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }
    }

    public function bulkDisburse(BulkDisburseRequest $request)
    {
        $data = $request->validated();
        DB::beginTransaction();
        try {
            foreach ($data['entities'] as $entity) {
                if ($entity['type'] == 'loan') {
                    $this->service->disburseLoan($entity['id'], ['disbursement_date' => $entity['date']]);
                } else {
                    $this->salaryAdvanceService->disburseSalaryAdvance($entity['id'], ['disbursement_date' => $entity['date']]);
                }
            }
            DB::commit();

            return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }
    }

    public function export(GetLoanAndSalaryAdvanceByFilter $request)
    {
        $request->validated();
        $status = $request['statuses'][0];
        $data = $this->service->getLoanAndSalaryAdvanceByFilter($request);
        $filteredData = new LoanAndSalaryAdvanceResourceCollection($data);
        if (in_array($status, ['outstanding', 'ready_to_disburse', 'disbursed'])) {
            return Excel::download(new OutstandingExport($filteredData), 'outstanding_loans_and_salary_advances.xlsx');
        } elseif ($status === 'pending') {
            return Excel::download(new PendingExport($filteredData), 'pending_loans_and_salary_advances.xlsx');
        } elseif ($status === 'paid_in_full') {
            return Excel::download(new PaidInFullExport($filteredData), 'paid_in_full_loans_and_salary_advances.xlsx');
        } else {
            return response()->json(['error' => 'Invalid export type'], 400);
        }

    }

    public function getLoansById(Request $request)
    {
        $loansAndSalaryAdvanceData = $this->service->getLoanAndSalaryAdvanceByFilter($request);
        $paginatedData = (new LoanAndSalaryAdvanceResourceCollection($loansAndSalaryAdvanceData))->response()->getData();

        return getResponseStructure([
            'data' => $paginatedData->data,
        ], HttpStatusCodeUtil::OK, 'Loans and salary advances filtered successfully');
    }

    public function addLoanByManager(AddLoanByManagerRequest $request)
    {
        $data = $request->validated();
        $loanRequest = [];
        DB::beginTransaction();
        try {
            $employeeId = $data['employee_id'];
            $loanRequest = $this->service->requestLoan($data, $employeeId);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }

        return getResponseStructure(['data' => $loanRequest],
            HttpStatusCodeUtil::OK, 'Loan requested successfully by manager');
    }

    public function addSalaryAdvanceByManager(AddSalaryAdvanceByManagerRequest $request)
    {
        $data = $request->validated();
        $salaryAdvanceRequest = [];
        DB::beginTransaction();
        try {
            $employeeId = $data['employee_id'];
            $salaryAdvanceRequest = $this->salaryAdvanceService->requestSalaryAdvance($data, $employeeId);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Sentry\captureException($e);
            Log::info($e);
            throw $e;
        }

        return getResponseStructure(['data' => $salaryAdvanceRequest],
            HttpStatusCodeUtil::OK, 'Salary advance requested successfully by manager');
    }
}
