<?php

namespace App\Http\Requests\V1\Holidays;

use Illuminate\Foundation\Http\FormRequest;

class PayoutHolidayBalanceCompensationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'amount' => ['required_without:compensation_days', 'numeric'],
            'compensation_days' => ['required_without:amount', 'numeric'],
        ];
    }
}
