<?php

namespace App\Http\Controllers\Schedule;

use App\DomainData\FilterDto;
use App\DomainData\RestEmployeeDto;
use App\Http\Controllers\Controller;
use App\Services\Schedule\CrudServices\RestEmployeeCrudService;

class RestEmployeeController extends Controller
{
    use FilterDto, RestEmployeeDto;

    public function __construct(
        private RestEmployeeCrudService $service
    ) {}

    public function createMany(array $request, \stdClass &$output): void
    {
        $validator = \Validator::make($request, [
            'entity_array' => 'required|array',
            'related_objects' => 'array',
            'related_objects.*' => 'in:employee',
            'check_warning' => 'boolean',

        ]);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        $rules = $this->getRules(['date', 'employee_id']);

        foreach ($request['entity_array'] as $key => $entity) {
            $validator = \Validator::make($entity, $rules);
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }
        }
        $this->service->createMany($request, $output);

    }

    public function create(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['date', 'employee_id']);
        $rules['check_warning'] = 'boolean';
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->create($request, $output);

        if (isset($request['check_warning']) && $request['check_warning'] && isset($output->Warning) && count($output->Warning)) {
            $output->no_need_commit = true;
            $output->request_body = $request;
        }

    }

    public function update(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['date', 'employee_id']);
        $rules['id'] = 'required|numeric';
        $rules['related_objects'] = ['array'];
        $rules['related_objects.*'] = ['in:employee'];
        $rules['check_warning'] = 'boolean';
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->update($request, $output);
        if (isset($request['check_warning']) && $request['check_warning'] && isset($output->Warning) && count($output->Warning)) {
            $output->no_need_commit = true;
            $output->request_body = $request;
        }
    }

    public function getByFilter(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules(['page', 'filters', 'related_objects.*', 'related_objects_count.*',  'page_size']);
        $rules['branch_id'] = 'required|numeric';

        if (! isset($request['branch_id'])) {
            $request['branch_id'] = config('globals.branchId');
        } else {
            config(['globals.branchId' => $request['branch_id']]);
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['related_objects'])) {
            $request['related_objects'] = [];
        }
        if (! isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $this->service->getByFilter($request, $output);
    }

    public function getById(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules(['related_objects.*', 'related_objects_count.*']);
        $rules['id'] = 'required|numeric';
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['related_objects'])) {
            $request['related_objects'] = [];
        }
        if (! isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $this->service->getById($request, $output);
    }

    public function delete(array $request, \stdClass &$output): void
    {
        $rules['ids'] = ['required', 'array'];
        $rules['ids.*'] = ['required', 'numeric'];
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $this->failMessages($validator->messages());

            return;
        }

        $request = $validator->validate();

        $this->service->delete($request, $output);
    }
}
