<?php

namespace App\Console\Commands;

use App\FeatureToggles\Unleash;
use App\Services\LeaveManagement\LeaveTypesSetupService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LeaveTypesSetup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'leave:types:setup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = ' ';

    public function __construct(private LeaveTypesSetupService $oldLeaveTypesSetupService, private \App\Services\V1\LeaveManagement\LeaveTypesSetupService $leaveTypesSetupService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        // Log::info('Rest Days cron job is started at: ' . date('Y-m-d H:i:s'));

        DB::beginTransaction();
        try {
            $unleash = app(Unleash::class);
            if ($unleash->isLeaveBalanceChangesFlagEnabled()) {
                $this->leaveTypesSetupService->setup();

            } else {
                $this->oldLeaveTypesSetupService->setup();
            }
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
