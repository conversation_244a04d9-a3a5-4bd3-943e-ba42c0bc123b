<?php

namespace App\Console\Commands;

use App\Models\EntityTag;
use App\Models\PublicHolidaysAttendance;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RemovePublicHolidayCompensation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-public-holiday-compensation {company_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Removing public holiday compensations');
            DB::beginTransaction();
            $companyId = $this->argument('company_id');
            $publicHolidayAttendanceCount = PublicHolidaysAttendance::where('company_id', $companyId)->count();
            $this->info('Public holiday compensations count: '.$publicHolidayAttendanceCount);

            $entityTagCount = EntityTag::where('company', $companyId)->where('tag', 'public_holiday')->count();
            $this->info('Entity tag count: '.$entityTagCount);

            PublicHolidaysAttendance::where('company_id', $companyId)->delete();
            EntityTag::where('company', $companyId)->where('tag', 'public_holiday')->delete();

            $publicHolidayAttendanceCount = PublicHolidaysAttendance::where('company_id', $companyId)->count();
            $this->info('Public holiday compensations count after: '.$publicHolidayAttendanceCount);

            $entityTagCount = EntityTag::where('company', $companyId)->where('tag', 'public_holiday')->count();
            $this->info('Entity tag count after: '.$entityTagCount);

            DB::commit();
            $this->info('Public holiday compensations removed successfully');
        } catch (\Exception $e) {
            $this->error('An error occurred while removing public holiday compensations');
            $this->error($e->getMessage());
            Log::error($e->getMessage());
            DB::rollBack();
        }
    }
}
