<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\CompanyDefaultLeaveType;
use App\Models\Employee;
use App\Repositories\Repository;
use App\Services\LeaveManagement\BusinessServices\BalanceAggregatorService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RevertPublicHolidayCompensationForCompany extends Command
{
    public function __construct(
        private BalanceAggregatorService $balanceAggregatorService
    ) {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'revert:public-holiday-compensation {company_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
            $companyId = $this->argument('company_id');
            $employees = Employee::where('company_id', $companyId)
                ->withWhereHas('publicHolidaysAttendancePayout', function ($q) {
                    $q->whereNotIn('public_holiday_id', [7, 8, 9]);
                })
                ->withWhereHas('publicHolidaysAttendance', function ($q) {
                    $q->whereNotIn('public_holiday_id', [7, 8, 9]);
                })
                ->get();
            foreach ($employees as $employee) {
                foreach ($employee->publicHolidaysAttendancePayout as $payout) {
                    $compensation = $payout->compensation_payed_out;
                    $currentRemainingDays = $payout->publicHolidaysAttendance->remaining_days;
                    $currentUsedAsPay = $payout->publicHolidaysAttendance->days_used_as_pay;
                    $payout->publicHolidaysAttendance->update(['remaining_days' => $currentRemainingDays + $compensation]);
                    $payout->publicHolidaysAttendance->update(['days_used_as_pay' => $currentUsedAsPay - $compensation]);
                    $payout->delete();

                }
                $leaveType = CompanyDefaultLeaveType::where('company_id')->where('key', 'public_holiday_leave_type_id')->first();
                $leaveBalance = $employeeLeaveBalanceRepository->getBalanceByLeaveTypeIdAndDate($employee->id, now()->toDateString(), $leaveType->company_leave_type_id);
                $dummyOutput = new \stdClass;
                $this->balanceAggregatorService->perform(['id' => $leaveBalance->id, 'employee_id' => $employee->id, 'is_public_holiday' => true], $dummyOutput);
                echo 'Employee ID: '.$employee->id."\n";
            }
            DB::commit();
        } catch (Exception $e) {
            // dd($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
