<?php

namespace App\Console\Commands;

use App\Services\EmployeeChange\FillBranchIdsService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FillBranchIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill:branch_ids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = ' ';

    public function __construct(private FillBranchIdsService $fillBranchIdsService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        // Log::info('Rest Days cron job is started at: ' . date('Y-m-d H:i:s'));

        DB::beginTransaction();
        try {
            $this->fillBranchIdsService->run();
            DB::commit();
        } catch (Exception $e) {
            // \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
