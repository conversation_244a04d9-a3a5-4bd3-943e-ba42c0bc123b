<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class NationalIdRule implements Rule
{
    protected $nationality;

    public function __construct($nationality)
    {
        $this->nationality = strtolower($nationality ?? '');
    }

    public function passes($attribute, $value): bool
    {
        if (!isset($this->nationality) || strtolower($this->nationality ??'') !== 'egyptian') {
            return true;
        }

        return !empty($value) && preg_match('/^\d{14}$/', $value);
    }

    public function message(): string
    {
        return __('validation.egyptian_national_id');
    }
}
