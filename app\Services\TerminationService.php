<?php

namespace App\Services;

use App\Exceptions\UnprocessableException;
use App\Jobs\V1\FillEmployeeBaseBalancesJob;
use App\FeatureToggles\Unleash;
use App\Models\StateMachines\RequestCancelled;
use App\Repositories\EmployeeHireHistoryRepository;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\Repository;
use App\Repositories\TerminationRequestRepository;
use App\Traits\V1\EmployeeRequestsTrait;
use App\Repositories\V1\EmployeeRequestRepository;
use App\Repositories\V1\Holidays\PublicHolidayAbsenceRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveBalancesRepository;
use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use App\Services\CompanySetup\BusinessServices\UpdateBranchManagerService;
use App\Services\CompanySetup\CrudServices\RegisterationValidationCrudService;
use App\Services\CompanySetup\CrudServices\UserCrudService;
use App\Services\CompanySetup\EmployeesService;
use App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService;
use App\Services\LeaveManagement\FillEmployeeBalancesService;
use App\Services\PayrollSetup\SystemSettingsService;
use App\Services\V1\Attendance\GenerateTimecardsService;
use App\Traits\DataPreparation;
use App\Traits\QueriesHelper;
use App\Traits\V2\WorkTypesTrait;
use App\Traits\WorkflowTrait;
use Carbon\Carbon;
use Exception;
use Google\Cloud\Core\Exception\BadRequestException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Workflow\WorkflowStub;
use App\Jobs\AssignApprovalsJob;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;

class TerminationService extends BaseService
{
    use DataPreparation, QueriesHelper, WorkflowTrait, WorkTypesTrait, EmployeeRequestsTrait, PrepareAssignRequestCycleDataTrait;

    private Repository $employeeRepository;

    private Repository $branchRepository;

    private Repository $employeeLeaveRequestRepository;

    private Repository $cicoRepository;

    private Repository $attendanceDeductionRepository;

    private Repository $attendanceOvertimeRepository;

    private Repository $oldEmployeeLeaveBalanceRepository;

    private EmployeeRequestRepository $employeeRequestRepository;

    private Repository $attendanceRepository;

    private Repository $timecardRepository;

    private NewEmployeeRepository $newEmployeeRepository;

    private Repository $entityTagRepository;

    private TerminationRequestRepository $terminationRequestRepository;

    private EmployeeHireHistoryRepository $employeeHireHistoryRepository;

    private PayrollsRepository $payrollsRepository;

    private WorkflowApprovalCycleRepository $workflowApprovalCycleRepository;

    private EmployeeLeaveBalancesRepository $employeeLeaveBalancesRepository;

    public function __construct(
        private UpdateBranchManagerService                     $updateBranchManagerService,
        private UserCrudService                                $userCrudService,
        private RegisterationValidationCrudService             $registerationValidationCrudService,
        private FillEmployeeBalancesService                    $oldFillEmployeeBalancesService,
        private EmployeeLeaveRequestCrudService                $employeeLeaveRequestCrudService,
        private SystemSettingsService                          $systemSettingsService,
        private EmployeesService                               $employeesService,
        private V1\LeaveManagement\FillEmployeeBalancesService $fillEmployeeBalancesService,
        private PublicHolidayAbsenceRepository                 $publicHolidayAbsenceRepository,
    )
    {
        $this->branchRepository = Repository::getRepository('Branch');
        $this->employeeLeaveRequestRepository = Repository::getRepository('EmployeeLeaveRequest');
        $this->cicoRepository = Repository::getRepository('Cico');
        $this->attendanceDeductionRepository = Repository::getRepository('AttendanceDeduction');
        $this->attendanceOvertimeRepository = Repository::getRepository('AttendanceOvertime');
        $this->oldEmployeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
        $this->timecardRepository = Repository::getRepository('Timecard');
        $this->newEmployeeRepository = new NewEmployeeRepository;
        $this->attendanceRepository = Repository::getRepository('Attendance');
        $this->entityTagRepository = Repository::getRepository('EntityTag');
        $this->employeeRequestRepository = new EmployeeRequestRepository;
        $this->terminationRequestRepository = new TerminationRequestRepository;
        $this->employeeHireHistoryRepository = new EmployeeHireHistoryRepository;
        $this->payrollsRepository = new PayrollsRepository;
        $this->employeeRepository = new Repository('Employee');
        $this->workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $this->employeeLeaveBalancesRepository = new EmployeeLeaveBalancesRepository;

    }

    public function empolyeeExist(int $employeeId)
    {
        $employee = $this->newEmployeeRepository->find($employeeId);

        if (!isset($employee)) {
            throw new UnprocessableException(trans('messages.employee_not_found'));
        }

        if (!isset($employee->employeeInfo)) {
            throw new UnprocessableException(trans('messages.employee_info_not_found'));
        }

        return $employee;

    }

    public function requestTerminate(mixed $payload): void
    {
        DB::beginTransaction();

        try {
            $employeeId = $payload['employee_id'];
            $terminateDate = Carbon::parse($payload['terminate_date']);
            $terminate_reason = $payload['terminate_reason'];
            $terminate_type = $payload['terminate_type'];

            // Check if employee exists
            $employee = $this->empolyeeExist($employeeId);
            if (!$employee) {
                throw new \Exception('Employee does not exist.');
            }
            $pendingTerminationRequest = $this->terminationRequestRepository->getPendingTerminationRequest($employeeId);

            if (isset($pendingTerminationRequest)) {
                throw new UnprocessableException(trans('messages.termination_request_exist'));
            }

            // Get the latest attendance record
            $attendance = $this->attendanceRepository->getLatestAttendance($employeeId);

            if (!isset($attendance) || Carbon::parse($attendance['date'])->lte($terminateDate)) {
                $this->removeEmployeeRelatedData($employeeId, $terminateDate);

                // Update employee's termination date
                $employee->employeeInfo->termination_date = $terminateDate;
                $employee->employeeInfo->save();

                // Save the termination request
                $terminationRequest = $this->terminationRequestRepository->add([
                    'employee_id' => $employeeId,
                    'terminate_date' => $terminateDate,
                    'terminate_reason' => $terminate_reason,
                    'terminate_type' => $terminate_type,
                    'status' => 'pending',
                ]);
                if (!$terminationRequest) {
                    throw new \Exception('Failed to save termination request.');
                }

                // Save the request in the employee_requests table
                $employeeRequest = $this->employeeRequestRepository->add([
                    'company_id' => config('globals.company')->id,
                    'requested_by' => config('globals.user')->employee_id,
                    'requestable_id' => $terminationRequest->id,
                    'requestable_type' => 'termination_request',
                    'request_name' => 'termination_request',
                    'comment' => null,
                    'status' => 'pending',
                    'employee_id' => $employeeId,
                    'date' => now(),
                ]);
                $requesterRoleIds = config('globals.user')->roles->pluck('id')->toArray();
                
                

                AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('termination', $terminationRequest, $requesterRoleIds));
                //                $employee->status = 'termination_pending';
                $employee->save();
                DB::commit();
            } else {
                throw new UnprocessableException('There is an attenadence before this termination date');
            }

        } catch (Exception $e) {
            DB::rollBack();
            // Log the exception or handle it as necessary
            Log::error('Request Terminate failed: ' . $e->getMessage());
            throw $e;
        }
    }

    private function removeEmployeeRelatedData(mixed $employeeId, mixed $terminateDate): void
    {
        try {
            $this->removeApprovedLeaves($employeeId, $terminateDate);
            $this->removeEmployeeTimeCards($employeeId, $terminateDate);
            $this->removeEmployeeUnverifiedCicos($employeeId, $terminateDate);
            $this->removePublicHolidaysAbsences($employeeId, $terminateDate);
        } catch (\Exception $e) {
            Log::error('Failed to remove employee related data: ' . $e->getMessage());
            throw $e;
        }
    }

    private function removeApprovedLeaves(mixed $employeeId, mixed $terminateDate): void
    {
        $terminateDate = Carbon::parse($terminateDate);

        $approvedRequests = $this->employeeLeaveRequestRepository->getAllLeaveRequestsIntersectingOrAfterDate($employeeId, $terminateDate);
        foreach ($approvedRequests as $request) {
            try {
                $intersectingDaysCount = 0;
                $companyLeaveTypeId = $request->company_leave_type_id;
                $requestFrom = Carbon::parse($request->from);
                $requestTo = Carbon::parse($request->to);

                if ($requestFrom <= $terminateDate && $requestTo > $terminateDate) {
                    $intersectingDays = $requestTo->diffInDays($terminateDate);
                    $intersectingDaysCount += $intersectingDays;
                    $request->to = $terminateDate;
                    if (!$request->save()) {
                        throw new \Exception('Failed to update leave request.');
                    }
                }

                if ($requestFrom > $terminateDate) {
                    $requestId = $request->id;
                    $requestData['requestable_id'] = $requestId;
                    $requestData['requestable_type'] = 'employee_leave_request';
                    $employeeRequest = $this->employeeRequestRepository->getRequestByRequestableId($requestData);
                    if ($employeeRequest) {
                        if (!$employeeRequest->delete()) {
                            throw new \Exception('Failed to delete employee request.');
                        }
                    }
                    if (!$request->delete()) {
                        throw new \Exception('Failed to delete leave request.');
                    }
                }
                $this->updateEmployeeLeaveBalance($employeeId, $companyLeaveTypeId, $terminateDate, $intersectingDaysCount);
            } catch (\Exception $e) {
                Log::error('Failed to process leave request ID ' . $request->id . ': ' . $e->getMessage());
                throw $e;
            }
        }
    }

    private function updateEmployeeLeaveBalance(mixed $employeeId, int $companyLeaveTypeId, mixed $terminateDate, int $intersectingDaysCount): void
    {
        try {
            $leaveBalance = $this->oldEmployeeLeaveBalanceRepository->getBalanceByLeaveTypeIdAndDate($employeeId, $terminateDate, $companyLeaveTypeId);
            if ($leaveBalance) {
                $leaveBalance->balance += ($intersectingDaysCount * 8);
                if (!$leaveBalance->save()) {
                    throw new \Exception('Failed to update leave balance.');
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to update leave balance for employee ID ' . $employeeId . ': ' . $e->getMessage());
            throw $e;
        }
    }

    private function removeEmployeeTimeCards(mixed $employeeId, mixed $terminateDate): void
    {
        try {
            // Retrieve timecard IDs that need to be deleted
            $timecardIds = $this->timecardRepository->getTimecardIdsAfterDate($employeeId, $terminateDate->format('Y-m-d'));
            // Prepare entities for deletion of associated tags
            $entities = array_map(function ($id) {
                return [
                    'id' => $id,
                    'type' => 'time_card',
                ];
            }, $timecardIds);
            // Delete associated entity tags
            $this->entityTagRepository->deleteEntityTags($entities);

            // Delete timecards after the termination date
            $this->timecardRepository->deleteTimecardsAfterDate($employeeId, $terminateDate);

        } catch (\Exception $e) {
            // Log the error for debugging purposes
            Log::error('Failed to remove employee timecards: ' . $e->getMessage());
            throw $e;
        }
    }

    public function fetchPendingTerminationRequest(int $employeeId)
    {
        // Check for existing pending termination request
        $existingRequest = $this->terminationRequestRepository
            ->getPendingTerminationRequest($employeeId);

        return $existingRequest;
    }

    // Function to handle the rest of the termination logic
    public function directTerminate(array $payload): void
    {
        DB::beginTransaction();

        try {
            $employeeId = $payload['employee_id'];
            $terminateDate = Carbon::parse($payload['terminate_date']);
            $terminateReason = $payload['terminate_reason'];
            $terminateType = $payload['terminate_type'];

            // Check if employee exists
            $employee = $this->empolyeeExist($employeeId);

            // Check for existing pending termination request
            $pendingRequest = $this->fetchPendingTerminationRequest($employeeId);

            if (isset($pendingRequest)) {

                // Cancel the existing pending request
                $pendingRequest->status->transitionTo(RequestCancelled::class);
                $data = [
                    'requestable_id' => $pendingRequest->id,
                    'requestable_type' => 'termination_request',
                ];

                $employeeRequest = $this->employeeRequestRepository->getRequestByRequestableId($data);

                $employeeRequest->status->transitionTo(RequestCancelled::class);

                if ($pendingRequest->workflow_id != null) {
                    $workflow = WorkflowStub::load($pendingRequest->workflow_id);
                    $workflow->setIsCompleted(true);
                }

            }

            $attendance = $this->attendanceRepository->getLatestAttendance($employeeId);

            if (!isset($attendance) || Carbon::parse($attendance['date'])->lte($terminateDate)) {
                // Create a new termination request with status 'approved'
                $terminationRequest = $this->terminationRequestRepository->add([
                    'employee_id' => $employeeId,
                    'terminate_date' => $terminateDate,
                    'terminate_reason' => $terminateReason,
                    'terminate_type' => $terminateType,
                    'status' => 'approved',
                ]);

                $employeeRequest = $this->employeeRequestRepository->add([
                    'company_id' => config('globals.company')->id,
                    'requested_by' => config('globals.user')->employee_id,
                    'requestable_id' => $terminationRequest->id,
                    'requestable_type' => 'termination_request',
                    'request_name' => 'termination_request',
                    'comment' => null,
                    'status' => 'approved',
                    'employee_id' => $employeeId,
                    'date' => now(),
                ]);

                // Update employee's termination details
                $employee->employeeInfo->termination_date = $terminateDate;
                if ($terminateDate->lte(date('Y-m-d'))) {
                    $employee->status = 'terminated';
                    $employee->save();
                } else {
                    $employee->status = 'termination_pending';
                    $employee->save();
                }
                $employee->push();

                // Remove related data after termination date
                $this->removeEmployeeRelatedData($employeeId, $terminateDate);

                DB::commit();
            } else {
                throw new UnprocessableException('There is an attenadence before this termination date');
            }
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Direct Termination failed: ' . $e->getMessage());
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function checkUnverifiedAttendanceAndLeaves(mixed $payload)
    {
        $employeeId = $payload['employee_id'];
        $from = $payload['from'];
        $unverifiedCicos = $this->cicoRepository->getAllUnverifiedClockInsAfterDateForEmployee($from, $employeeId);
        $employeeLeaves = $this->employeeLeaveRequestRepository->getAllLeaveRequestsIntersectingOrAfterDateWithoutRestDays($employeeId, $from);
        $leavesCount = count($employeeLeaves);
        $cicosNumber = count($unverifiedCicos);
        $warning = [
            'unverifiedCicos' => $cicosNumber,
            'leavesCount' => $leavesCount,
            'lastLeaveRequest' => $employeeLeaves->first(),
        ];

        return $warning;
    }

    public function employeeRehire(int $id, mixed $payload)
    {
        $employeeId = $id;
        $rehireDate = Carbon::parse($payload['rehire_date'])->format('Y-m-d');
        $employee = $this->empolyeeExist($employeeId);

        if (!$employee) {
            throw new UnprocessableException(trans('messages.employee_not_found'));
        }

        $companyId = $employee->company_id;
        $currentPayroll = $this->payrollsRepository->getLastDraftedPayroll($companyId);
        $joinDate = $employee->employeeInfo->join_date;
        if (!$currentPayroll) {
            throw new UnprocessableException('No valid payroll found for the company.');
        }

        $terminationDate = $employee->employeeInfo->termination_date;
        $formattedTerminationDate = Carbon::parse($terminationDate)->format('Y-m-d');

        if ($currentPayroll['start'] > $formattedTerminationDate) {
            $terminationPayroll = $this->payrollsRepository->payrollCoversDate($formattedTerminationDate);
            if (!isset($terminationPayroll) || $terminationPayroll['end'] < $rehireDate) {
                $this->employeeHireHistoryRepository->add([
                    'employee_id' => $employeeId,
                    'company_id' => $companyId,
                    'rehire_date' => $rehireDate,
                    'termination_date' => $formattedTerminationDate,
                    'previous_hire_date' => $joinDate,
                ]);
                $employee->status = 'active';
                $employee->employeeInfo->termination_date = null;
                $employee->employeeInfo->join_date = $rehireDate;
                $employee->employeeInfo->save();
                $employee->save();

            } else {
                throw new UnprocessableException(trans('messages.rehire_date_not_valid') . $terminationPayroll['end']);
            }

        } else {
            throw new UnprocessableException(trans('messages.same_payroll_rehire'));
        }

    }

    public function fixLeaveBalancesForRehiredEmployee($employeeId)
    {
        $employee = $this->newEmployeeRepository->findOrFail($employeeId);
        if ($employee->status == 'terminated') {
            throw new UnprocessableException('Employee is terminated');
        }
        $this->employeeLeaveBalancesRepository->deleteByKeys(['employee_id' => $employee->id]); // delete old leave balances
        dispatch(new FillEmployeeBaseBalancesJob([$employee]));
        $dummyOutput = new \stdClass;
        $this->employeeLeaveRequestCrudService->addProratedRequest(['employee_id' => $employee->id], $dummyOutput);
    }

    public function lastVerifiedAttendance(int $employeeId)
    {
        $latestTimeCardForEmployee = $this->timecardRepository->getLatestTimecardForEmployee($employeeId);

        if (!$latestTimeCardForEmployee) {
            return null;
        }

        return Carbon::parse($latestTimeCardForEmployee['from'])->format('Y-m-d');
    }

    //    public function cancelTermination($employeeID)
    //    {
    //        DB::beginTransaction();
    //
    //        try {
    //            $terminationRequest = $this->terminationRequestRepository->getPendingTerminationRequest($employeeID);
    //
    //            if (!isset($terminationRequest)) {
    //                throw new BadRequestException(trans('messages.termination_request_done'));
    //            }
    //
    //            if ($terminationRequest->status != config('globals.REQUEST_STATUSES.PENDING')) {
    //                throw new BadRequestException(trans('messages.termination_not_approved'));
    //            }
    //
    //            $terminationRequest->status->transitionTo(RequestCancelled::class);
    //            $requestId = $terminationRequest->id;
    //            $requestData['requestable_id'] = $requestId;
    //            $requestData['requestable_type'] = "termination_request";
    //
    //            $employeeRequest = $this->employeeRequestRepository->getRequestByRequestableId($requestData);
    //
    //            if ($employeeRequest) {
    //                $employeeRequest->status->transitionTo(RequestCancelled::class);
    //                $employeeRequest->save();
    //
    //                $employee = $this->employeeRepository->find($employeeID);
    //                $employee->employeeInfo->termination_date = null;
    //                $employee->employeeInfo->save();
    //            }
    //
    //            if ($terminationRequest->workflow_id != null) {
    //                $workflow = WorkflowStub::load($terminationRequest->workflow_id);
    //                $workflow->setIsCompleted(true);
    //            }
    //
    //            DB::commit();
    //
    //        } catch (\Exception $e) {
    //            DB::rollBack();
    //            throw $e;
    //        }
    //    }

    public function approveTermination($id)
    {
        $request = $this->terminationRequestRepository->findOrFail($id);
        $this->actionOnTermination($request, 'approve');

    }

    public function rejectTermination($id)
    {
        $request = $this->terminationRequestRepository->findOrFail($id);

        $this->actionOnTermination($request, 'reject');

        $title = $request?->employee->title;
        $employeeId = $request?->employee->id;
        if (in_array($title->workTypePolicy->work_days_type, $this->getAssignableTypes($title->company_id))) {
            $this->assignStaticShifts($title, $employeeId);
        }

    }

    public function actionOnTermination($request, $actionType): void
    {
        try {
            $roleIds = config('globals.user')->roles->pluck('id')->toArray();
            $userCanTakeAnAction = $this->canTakeAnAction($roleIds, $request->id, config('globals.REQUEST_WORKFLOW_TYPES.TERMINATION'));
            if (!$userCanTakeAnAction) {
                throw new UnprocessableException(trans('messages.can_not_take_this_action'));
            }

            if ($request->status != config('globals.REQUEST_STATUSES.PENDING')) {
                throw new UnprocessableException(trans('messages.workflow_is_completed'));
            }

            $this->doAnAction($actionType);
            if($this->checkRequestIsCompleted($request->employeeRequest)) {
                $finalStatus = $this->getFinalStatus($request->employeeRequest, $actionType);
                $this->updateEntity($request, $finalStatus);
                $this->updateRequest($request->employeeRequest, $finalStatus);
                $request->employee->status = $finalStatus == 'approved' ? ($request->termination_date <= date('Y-m-d') ? 'terminated' : 'termination_pending') : $request->employee->status;
                if($finalStatus == 'rejected') {
                    $request->employee->employeeInfo->termination_date = null;
                    $request->employee->employeeInfo->save();
                }
                $request->employee->save();
            }
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException(trans('messages.can_not_take_this_action'));
        }

    }

    public function canApprove($employeeApproves)
    {
        $emploeeId = auth()->user()->employee_id;

        foreach ($employeeApproves as $employeeApproval) {
            $employeeIds = array_column($employeeApproval->employees, 'id');
            if (in_array($emploeeId, $employeeIds) and $employeeApproval->pivot->operator == 'or') {
                if ($employeeApproval->pivot->status == config('globals.REQUEST_STATUSES.PENDING')) {
                    return true;
                } else { // if the status is approved or rejected then don't show approve or reject button
                    return false;
                }

            }
        }

        foreach ($employeeApproves as $employeeApproval) {
            $employeeIds = array_column($employeeApproval->employees, 'id');
            if (in_array($emploeeId, $employeeIds)) {
                if ($employeeApproval->pivot->status == config('globals.REQUEST_STATUSES.PENDING')) {
                    return true;
                } else { // if the status is approved or rejected then don't show approve or reject button
                    return false;
                }
            }

            if (!in_array($emploeeId, $employeeIds) && $employeeApproval->pivot->status == config('globals.REQUEST_STATUSES.PENDING')) {
                return false;
            }
        }

        return false;
    }

    public function getRequestData($request)
    {
        return [
            'requestable_id' => $request->id,
            'requestable_type' => config('globals.REQUEST_WORKFLOW_TYPES.TERMINATION'),
            'role_ids' => auth()->user()->roles->pluck('id')->toArray(),
        ];
    }

    public function directCancelTermination(int $id)
    {
        $terminationRequest = $this->terminationRequestRepository->getTerminationRequest($id);
        if (!isset($terminationRequest)) {
            throw new BadRequestException(trans('messages.termination_request_done'));
        }
        // Prepare request data for employee request lookup
        $requestId = $terminationRequest->id;
        $requestData = [
            'requestable_id' => $requestId,
            'requestable_type' => 'termination_request',
        ];
        $employee = $this->employeeRepository->find($id);
        $title = $employee->title;

        $actionHasBeenTaken = $this->workflowApprovalCycleRepository->actionHasBeenTaken($requestId, 'termination_request');
        // Retrieve and update employee request status
        $employeeRequest = $this->employeeRequestRepository->getRequestByRequestableId($requestData);
        if (auth()->user()->hasPermissionTo('terminate_employee', 'user-api') && ($terminationRequest->status == 'approved')) {
            // Retrieve and update termination request status
            $terminationRequest->status->transitionTo(RequestCancelled::class);
            if ($employeeRequest) {
                $employeeRequest->status->transitionTo(RequestCancelled::class);
                $employeeRequest->save();

                if ($employee) {
                    $employee->employeeInfo->termination_date = null;
                    $employee->employeeInfo->save();
                    $employee->status = 'active';
                    $employee->save();
                }
            }

            if (in_array($title->workTypePolicy->work_days_type, $this->getAssignableTypes($title->company_id))) {
                $this->assignStaticShifts($title, $id);
            }

        } elseif (auth()->user()->employee->id == $employeeRequest->requested_by && !$actionHasBeenTaken) {

            if ($terminationRequest->status != config('globals.REQUEST_STATUSES.PENDING')) {
                throw new BadRequestException(trans('messages.termination_not_approved'));
            }

            $terminationRequest->status->transitionTo(RequestCancelled::class);

            $employeeRequest = $this->employeeRequestRepository->getRequestByRequestableId($requestData);

            if ($employeeRequest) {
                $employeeRequest->status->transitionTo(RequestCancelled::class);
                $employeeRequest->save();

                $employee->employeeInfo->termination_date = null;
                $employee->employeeInfo->save();
            }
            if (in_array($title->workTypePolicy->work_days_type, $this->getAssignableTypes($title->company_id))) {
                $this->assignStaticShifts($title, $id);
            }
        } else {
            throw new UnprocessableException(trans('messages.can_not_cancel_termination'));
        }
    }

    private function removeEmployeeUnverifiedCicos(mixed $employeeId, mixed $terminateDate)
    {
        $this->cicoRepository->removeAllUnverifiedClockInsAfterDateForEmployee($terminateDate, $employeeId);
    }

    private function removePublicHolidaysAbsences($employeeId, $terminateDate)
    {
        $publicHolidayAbsences = $this->publicHolidayAbsenceRepository->getAllPublicHolidaysAbsencesAfterDateForEmployee($employeeId, $terminateDate);
        foreach ($publicHolidayAbsences as $publicHolidayAbsence) {
            if ($publicHolidayAbsence->from > $terminateDate) {
                $publicHolidayAbsence->delete();
            } else {
                $publicHolidayAbsence->to = $terminateDate;
                $publicHolidayAbsence->save();
            }
        }
    }

    public function assignStaticShifts($title, $employeeId): void
    {
        $titles = [[
            'title_id' => $title->id,
            'work_type_policy_id' => $title->workTypePolicy->id,
            'start_time' => $title->workTypePolicy->start_time,
            'end_time' => $title->workTypePolicy->end_time,
            'rest_days' => $title->workTypePolicy->rest_days,
            'employee_id' => $employeeId,
            'work_days_type' => $title->workTypePolicy->work_days_type,
            'any_location_type' => $title->workTypePolicy->any_location_type,
        ]];
        $nextDay = Carbon::tomorrow()->format('Y-m-d');

        $generateTimecardsService = new GenerateTimecardsService;
        $generateTimecardsService->generateTimecards($titles, $nextDay);
    }
}
