<?php

namespace App\Http\Controllers\V1\Attendance;

use App\FeatureToggles\Unleash;
use App\Http\Controllers\Controller;
use App\Http\Requests\AttendanceDeductionRequest;
use App\Http\Requests\AttendanceOvertimeReasonRequest;
use App\Http\Requests\V1\Attendance\AttendanceFiltersRequest;
use App\Repositories\IRepository;
use App\Repositories\Repository;
use App\Services\TimeTracking\BusinessServices\GetCicosFilterService;
use App\Services\V1\Attendance\NewAttendanceService;
use App\Util\HttpStatusCodeUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class NewAttendanceController extends Controller
{
    private Repository $entityTagRepository;

    private $unleash;

    // use DataPreparation;
    private IRepository $timecardRepository;

    private IRepository $cicoRepository;

    public function __construct(
        private NewAttendanceService $newAttendanceService,
        private GetCicosFilterService $getCicosFilterService

    ) {
        $this->timecardRepository = Repository::getRepository('Timecard');
        $this->cicoRepository = Repository::getRepository('Cico');
        $this->unleash = app(Unleash::class);
    }

    public function getUnverifiedTimecard(int $id)
    {
        $timecard = $this->timecardRepository->find($id);
        if (! $timecard) {
            return getResponseStructure(['data' => null], HttpStatusCodeUtil::NOT_FOUND, 'Timecard not found');
        }
        $employeeId = $timecard->employee_id;
        $date = Carbon::parse($timecard->from)->format('Y-m-d');
        $lastUnverifiedClockInTheDate = $this->cicoRepository->getUnverifiedClockInsInDateForEmployee($date, $employeeId);
        if (! $lastUnverifiedClockInTheDate) {
            return getResponseStructure(['data' => null], HttpStatusCodeUtil::OK, "No unverified timecard for employee $employeeId on $date");
        }
        $lastUnverifiedClockOutTheDate = null;
        if (! is_null($lastUnverifiedClockInTheDate->paired_clock_id)) {
            $lastUnverifiedClockOutTheDate = $this->cicoRepository->find($lastUnverifiedClockInTheDate->paired_clock_id);
        }

        return getResponseStructure(['data' => [
            'clock_in_id' => $lastUnverifiedClockInTheDate?->id,
            'clock_out_id' => $lastUnverifiedClockOutTheDate?->id ?? null,
            'clock_in_time' => $lastUnverifiedClockInTheDate?->date ?? null,
            'clock_out_time' => $lastUnverifiedClockOutTheDate?->date ?? null,
        ],

        ],
            HttpStatusCodeUtil::OK, "Unverified timecard for employee $employeeId on $date");
    }

    public function addOvertimeReason(int $id, AttendanceOvertimeReasonRequest $request)
    {
        $data = $request->validated();

        $this->newAttendanceService->addOvertimeReason($id, $data);

        return getResponseStructure(['data' => null], HttpStatusCodeUtil::OK, 'Reason added successfully');
    }

    public function editAttendanceDeduction(int $id, AttendanceDeductionRequest $request)
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            $this->newAttendanceService->editAttendanceDeduction($id, $data);
            DB::commit();

            return getResponseStructure(['data' => null], HttpStatusCodeUtil::OK, 'Deduction edited successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }

    }

    public function getUnverifiedByFilters(AttendanceFiltersRequest $request)
    {
        $data = $request->validated();
        $output = new \stdClass;

        if ($this->unleash->getUnscheduledShiftsFeatureFlag()) {
            $data['is_unscheduled'] = true;
        }

        $this->getCicosFilterService->perform($data, $output);
        $paginatedRepsonse = ($output->cicos)->response()->getData();

        return getResponseStructure(['data' => $paginatedRepsonse->data, 'pagination' => $paginatedRepsonse->meta], HttpStatusCodeUtil::OK, 'Unverified attendance filtered successfully');
        // return getResponseStructure(['data' => $cicos->data->data, 'pagination' => $cicos->data->meta], HttpStatusCodeUtil::OK, "Unverified attendance filtered successfully");
    }

    // public function approveAttendanceDeduction(int $id, AttendanceDeductionRequest $request)
    // {
    //     // $this->attendanceCrudService->approveAttendanceDeduction($id);
    //     return getResponseStructure(['data' => null], HttpStatusCodeUtil::OK, "Deduction approved successfully");
    // }
    // public function rejectAttendanceDeduction(int $id)
    // {
    //     // $this->attendanceCrudService->rejectAttendanceDeduction($id);
    //     return getResponseStructure(['data' => null], HttpStatusCodeUtil::OK, "Deduction rejected successfully");
    // }

    // public function approveAttendanceOvertime(int $id, AttendanceOvertimeRequest $request)
    // {
    //     // $this->attendanceCrudService->approveAttendanceOvertime($id);
    //     return getResponseStructure(['data' => null], HttpStatusCodeUtil::OK, "Overtime approved successfully");
    // }
    // public function rejectAttendanceOvertime(int $id)
    // {
    //     // $this->attendanceCrudService->rejectAttendanceOvertime($id);
    //     return getResponseStructure(['data' => null], HttpStatusCodeUtil::OK, "Overtime rejected successfully");
    // }

}
