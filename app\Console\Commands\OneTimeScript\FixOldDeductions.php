<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Attendance;
use App\Models\AttendanceDeduction;
use App\Models\Company;
use App\Repositories\V1\EntityTagRepository;
use App\Traits\CICOHelper;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixOldDeductions extends Command
{
    use CICOHelper;

    private $entityTagRepository;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:attendance_deductions {--company_id=company_id} {--start_date=start_date} {--end_date=end_date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $companyId = $this->option('company_id');
        $start_date = $this->option('start_date');
        $end_date = $this->option('end_date');
        $this->entityTagRepository = new EntityTagRepository;

        DB::beginTransaction();
        try {
            $this->fixDeductions($companyId, $start_date, $end_date);
            DB::commit();
        } catch (Exception $e) {
            // dd($e);
            \Sentry\captureException($e);
            Log::error($e);
            DB::rollBack();
        }
    }

    private function fixDeductions($companyId, $startDate, $endDate)
    {

        config(['globals.company' => Company::find($companyId)]);
        $deductionsInPeriod = AttendanceDeduction::where('company_id', $companyId)->whereHas('attendance', function ($q) use ($startDate, $endDate) {
            $q->whereBetween('date', [$startDate, $endDate]);
        })->get();

        foreach ($deductionsInPeriod as $deduction) {
            $deduction->delete();
        }

        $attendances = Attendance::where('company_id', $companyId)
            ->whereBetween('date', [$startDate, $endDate])
            ->whereHas('entityTags', function ($q) {
                $q->where('tag', 'late');
            })
            ->whereDoesntHave('attendanceDeductions')
            ->orderBy('date', 'asc')
            ->get();

        foreach ($attendances as $attendance) {
            config(['globals.user' => $attendance->employee->user]);
            if (isset($attendance->clockIn->date) && isset($attendance->slotable->from)) {
                $clockIn = Carbon::parse($attendance->clockIn->date);
                $actualStart = Carbon::parse($attendance->slotable->from);
                if ($actualStart->lte($clockIn)) {
                    $clockInDifferenceTime = Carbon::parse($attendance->clockIn->date)->diffInMinutes($attendance->slotable->from);
                    $deduction = $this->createAttendanceDeductionIfExist($attendance->employee, $clockInDifferenceTime, $attendance);
                    if (isset($deduction) && $deduction->deduction_value > 0) {
                        echo 'Attendance deduction created for employee: '.$attendance->employee->id.' for attendance: '.$attendance->id.' was late '.$clockInDifferenceTime.' with deduction: '.$deduction->deduction_value.' on date: '.$attendance->date.PHP_EOL;
                    }
                }
            }
        }
    }
}
