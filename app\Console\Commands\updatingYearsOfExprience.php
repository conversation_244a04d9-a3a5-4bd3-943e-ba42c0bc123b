<?php

namespace App\Console\Commands;

use App\Models\Employee;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class updatingYearsOfExprience extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:years-of-exprience';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $employees = Employee::all();
            foreach ($employees as $employee) {
                $numberOfYearsOfExperience = $employee->employeeInfo?->number_of_years_of_experience;
                if ($numberOfYearsOfExperience !== null) {
                    // Increment the number of years of experience by 1
                    $employee->employeeInfo->number_of_years_of_experience = $numberOfYearsOfExperience + 1;
                    // Save the changes
                    $employee->employeeInfo->save();
                }
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
        }
    }
}
