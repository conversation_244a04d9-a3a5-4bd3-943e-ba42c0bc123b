<?php

namespace App\DomainData;

trait PermissionDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'name' => 'required|string',
            'guard_name' => 'string',
            'company_id' => 'nullable|numeric',
            'permission_group_id' => 'integer',
            'description' => 'string',
            'name_en' => 'string',
            'name_ar' => 'string',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializePermissionDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
