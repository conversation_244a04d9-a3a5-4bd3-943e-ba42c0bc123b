<?php

namespace App\Console\Commands\OneTimeScript;

use App\Enums\EntityTags\AttendanceTags;
use App\Models\Company;
use App\Models\EmployeeLeaveRequest;
use App\Models\Timecard;
use App\Repositories\Repository;
use App\Services\PayrollSetup\MonthlySummariesService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FillUnaccountedDaysWithRestDays extends Command
{
    private $employeeLeaveBalanceRepository;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill:unaccounted_days:with_restdays {company_id} {from} {to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reject all pending requests.';

    /**
     * Execute the console command.
     */
    public function handle(

    ) {
        $monthlySummariesService = app(MonthlySummariesService::class);
        $this->employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
        DB::beginTransaction();
        try {
            $companyId = $this->argument('company_id');
            $company = Company::with('restDayLeaveType.companyLeaveTypePolicy')->find($companyId);
            config(['globals.company' => $company]);
            config(['globals.scope_branch_ids' => $company->branches->pluck('id')->toArray()]);
            config(['globals.scope_department_ids' => $company->departments->pluck('id')->toArray()]);
            config(['globals.scope_sub_department_ids' => $company->subDepartments->pluck('id')->toArray()]);
            config(['globals.scope_key' => 'company']);
            $fromDate = date($this->argument('from'));
            $toDate = date($this->argument('to'));
            $numRestDaysAdded = 0;
            $unaccountedDays = $monthlySummariesService->allUnaccountedDays(['payroll_from_date' => $fromDate, 'payroll_to_date' => $toDate]);
            echo 'Unaccounted Days: '.count($unaccountedDays)."\n";
            foreach ($unaccountedDays as $unaccountedDay) {
                //                $restDayRequest = [
                //                    'employee_id' => $unaccountedDay->id,
                //                    'company_leave_type_id' => $company->rest_day_leave_id,
                //                    'company_leave_type_policy_id' => $company->restDayLeaveType?->companyLeaveTypePolicy?->id,
                //                    'from' => Carbon::parse($unaccountedDay->unaccounted_date)->startOfDay()->toDateTimeString(),
                //                    'to' => Carbon::parse($unaccountedDay->unaccounted_date)->endOfDay()->toDateTimeString(),
                //                    'status' => 'approved',
                //                    'net_quantity' => config('globals.WORKED_HOURS_PER_DAY'),
                //                    'branch_id' => $unaccountedDay->branch_id,
                //                ];
                //                $leave = EmployeeLeaveRequest::create($restDayRequest);
                //                $leaveBalance = $employeeLeaveBalanceRepository->getBalanceOfLeave($leave);
                //                if ($leaveBalance) {
                //                    $leaveBalance->update(['balance' => max(0, $leaveBalance->balance - $leave->net_quantity)]);
                //                }
                $this->addRestDay($unaccountedDay->id, $unaccountedDay->unaccounted_date, $company->restDayLeaveType, $unaccountedDay->branch_id);
                $numRestDaysAdded++;
            }

            $absentTimecards = Timecard::whereHas('employee', function ($query) use ($company) {
                $query->where('company_id', $company->id);
            })->whereHas('entityTags', function ($query) {
                $query->whereIn('tag', [AttendanceTags::ABSENT->value, AttendanceTags::ABSENT_WITHOUT_PERMISSION->value]);
            })->whereDate('from', '>=', $fromDate)->whereDate('to', '<=', $toDate)->get();
            echo 'Absent Timecards: '.count($absentTimecards)."\n";

            foreach ($absentTimecards as $absentTimecard) {
                $this->addRestDay($absentTimecard->employee_id, $absentTimecard->from, $company->restDayLeaveType, $absentTimecard->branch_id);
                $numRestDaysAdded++;
            }
            $absentTimecards->each->delete();
            echo 'Rest Days Added: '.$numRestDaysAdded."\n";
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
        }

    }

    private function addRestDay($employeeId, $date, $companyLeaveType, $branchId)
    {
        $restDayRequest = [
            'employee_id' => $employeeId,
            'company_leave_type_id' => $companyLeaveType->id,
            'company_leave_type_policy_id' => $companyLeaveType->companyLeaveTypePolicy->id,
            'from' => Carbon::parse($date)->startOfDay()->toDateTimeString(),
            'to' => Carbon::parse($date)->endOfDay()->toDateTimeString(),
            'status' => 'approved',
            'net_quantity' => config('globals.WORKED_HOURS_PER_DAY'),
            'branch_id' => $branchId,
        ];
        $leave = EmployeeLeaveRequest::create($restDayRequest);
        $leaveBalance = $this->employeeLeaveBalanceRepository->getBalanceOfLeave($leave);
        if ($leaveBalance) {
            $leaveBalance->update(['balance' => max(0, $leaveBalance->balance - $leave->net_quantity)]);
        }
    }
}
