<?php

namespace App\Console\Commands;

use App\Models\Timecard;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateTimecardsDate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-timecards-date';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $timecards = Timecard::where('updated_at', '>=', '2024-12-20 21:50:00')
                ->where('updated_at', '<=', '2024-12-20 21:52:00')
                ->get();

            foreach ($timecards as $timecard) {
                $oldFromDate = Carbon::parse($timecard->from)->format('Y-m-d');
                $oldToDate = Carbon::parse($timecard->to)->format('Y-m-d');
                $fromTime = Carbon::parse($timecard->from)->toTimeString();
                $toTime = Carbon::parse($timecard->to)->toTimeString();
                if ($oldToDate > $oldFromDate) {
                    $timecard->to = '2024-12-02 '.$toTime;
                } else {
                    $timecard->to = '2024-12-01 '.$toTime;
                }
                $timecard->from = '2024-12-01 '.$fromTime;
                $timecard->save();
            }
            DB::commit();
        } catch (\Exception $e) {
            $this->error($e);
            DB::rollBack();
        }
    }
}
