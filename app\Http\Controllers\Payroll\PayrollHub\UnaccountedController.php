<?php

namespace App\Http\Controllers\Payroll\PayrollHub;

use App\Exceptions\UnprocessableException;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\PayrollHub\UnaccountedBulkActionRequest;
use App\Services\V1\PayrollHub\AttendanceBulkActionService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UnaccountedController extends NewController
{
    public function __construct(
        protected AttendanceBulkActionService $attendanceBulkActionService,
    )
    {
    }

    public function addBulkAttendance(UnaccountedBulkActionRequest $request)
    {
        try {
            DB::transaction(function () use ($request) {
                $this->attendanceBulkActionService->bulkAddAttendanceForUnaccounted($request->validated());
            });
            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Attendance added successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function addBulkAbsent(UnaccountedBulkActionRequest $request)
    {
        try {
            DB::transaction(function () use ($request) {
                $this->attendanceBulkActionService->addBulkAbsentForUnaccounted($request->validated());
            });
            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Attendance added successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function addBulkRestDays(UnaccountedBulkActionRequest $request)
    {
        try {
            DB::transaction(function () use ($request) {
                $this->attendanceBulkActionService->bulkAddRestDay($request->validated());
            });
            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Attendance added successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function addBulkLeave(UnaccountedBulkActionRequest $request)
    {
        try {
            $this->attendanceBulkActionService->addBulkLeave($request->validated());

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'Attendance added successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

}