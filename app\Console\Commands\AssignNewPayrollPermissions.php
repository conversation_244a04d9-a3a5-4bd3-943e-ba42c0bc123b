<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AssignNewPayrollPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:assign-new-payroll-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign new payroll-related permissions to roles that already have the manage-payroll permission, and ensure roles with manage_payroll_settings permission get manage_payroll permission';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $permissions = [
            [
                'name' => 'manage_payroll_settings',
                'guard_name' => 'user-api',
                'description_ar' => 'إدارة إعدادات الرواتب',
                'description_en' => 'Manage payroll settings',
                'name_ar' => 'إدارة إعدادات الرواتب',
                'name_en' => 'Manage payroll settings',
                'permission_group_id' => 1,
            ],
            [
                'name' => 'manage_employee_salary_info',
                'guard_name' => 'user-api',
                'description_ar' => 'إدارة معلومات رواتب الموظف',
                'description_en' => 'Manage employee salary info',
                'name_ar' => 'إدارة معلومات رواتب الموظف',
                'name_en' => 'Manage employee salary info',
                'permission_group_id' => 1,
            ],
            [
                'name' => 'view_employee_salary_info',
                'guard_name' => 'user-api',
                'description_ar' => 'عرض معلومات رواتب الموظف',
                'description_en' => 'View employee salary info',
                'name_ar' => 'عرض معلومات رواتب الموظف',
                'name_en' => 'View employee salary info',
                'permission_group_id' => 1,
            ],
            [
                'name' => 'finalize_payroll',
                'guard_name' => 'user-api',
                'description_ar' => 'تقفيل المرتبات',
                'description_en' => 'Finalize payroll',
                'name_ar' => 'تقفيل المرتبات',
                'name_en' => 'Finalize payroll',
                'permission_group_id' => 1,
            ],
        ];

        $permissionInstances = [];
        foreach ($permissions as $permissionData) {
            $permissionInstances[] = Permission::findOrCreate($permissionData['name'], $permissionData['guard_name']);
        }

        $managePayrollPermission = Permission::where('name', 'manage_payroll')
            ->where('guard_name', 'user-api')
            ->first();

        if (! $managePayrollPermission) {
            $this->error('Permission "manage_payroll" does not exist.');

            return 1;
        }

        $setupPayrollPermission = Permission::where('name', 'manage_payroll_settings')
            ->where('guard_name', 'user-api')
            ->first();

        if (! $setupPayrollPermission) {
            $this->error('Permission "manage_payroll_settings" does not exist.');

            return 1;
        }

        $rolesWithSetupPayroll = Role::whereHas('permissions', function ($query) use ($setupPayrollPermission) {
            $query->where('permission_id', $setupPayrollPermission->id);
        })->get();

        foreach ($rolesWithSetupPayroll as $role) {
            if (! $role->hasPermissionTo('manage_payroll', 'user-api')) {
                $role->givePermissionTo('manage_payroll');
                $this->info('Added "manage_payroll" permission to role: '.$role->name);
            }
        }

        $rolesWithManagePayroll = Role::whereHas('permissions', function ($query) use ($managePayrollPermission) {
            $query->where('permission_id', $managePayrollPermission->id);
        })->get();

        foreach ($rolesWithManagePayroll as $role) {
            foreach ($permissionInstances as $permission) {
                $role->givePermissionTo($permission);
            }
            $this->info('Added new permissions to role: '.$role->name);
        }

        $this->info('Permissions successfully added to all roles with "manage_payroll" permission!');

        return 0;
    }
}
