<?php

namespace App\Http\Requests\V1\Employee;

use App\Enums\Employee\EmployeeExportCategoryEnum;
use App\Enums\Employee\EmployeeExportFieldEnum;
use App\Rules\UniqueEmployeeExportTemplateNameRule;
use Illuminate\Foundation\Http\FormRequest;

class CreateEmployeeExportTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $categoryValues = EmployeeExportCategoryEnum::values();

        $rules = [
            'name' => [
                'required',
                'string',
                'max:255',
                new UniqueEmployeeExportTemplateNameRule
            ],
        ];

        foreach ($categoryValues as $category) {
            if ($category === EmployeeExportCategoryEnum::ESSENTIAL->value) {
                continue;
            }

            $categoryFields = EmployeeExportFieldEnum::getFieldsForCategory(EmployeeExportCategoryEnum::from($category));

            $rules[$category] = 'required|array';

            foreach ($categoryFields as $field) {
                $rules["{$category}.{$field}"] = 'required|boolean';
            }
        }

        return $rules;
    }

    public function messages(): array
    {
        $messages = [
            'name.required' => trans('validation.template_name_required'),
            'name.unique' => trans('validation.template_name_exists'),
        ];

        foreach (EmployeeExportCategoryEnum::values() as $category) {
            if ($category === EmployeeExportCategoryEnum::ESSENTIAL->value) {
                continue;
            }

            $messages["{$category}.required"] = trans('validation.category_fields_required', ['category' => $category]);
            $messages["{$category}.array"] = trans('validation.category_fields_array', ['category' => $category]);

            $categoryFields = EmployeeExportFieldEnum::getFieldsForCategory(EmployeeExportCategoryEnum::from($category));

            foreach ($categoryFields as $field) {
                $messages["{$category}.{$field}.required"] = trans('validation.field_required', [
                    'field' => $field,
                    'category' => $category
                ]);
                $messages["{$category}.{$field}.boolean"] = trans('validation.field_boolean', [
                    'field' => $field,
                    'category' => $category
                ]);
            }
        }

        return $messages;
    }

}
