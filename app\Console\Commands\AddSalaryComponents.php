<?php

namespace App\Console\Commands;

use App\Models\SalaryComponent;
use App\Models\SalaryComponentsCategory;
use Illuminate\Console\Command;

class AddSalaryComponents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:salary-components {company_id} {attendance_deduction} {attendance_overtime}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command to add salary categories and components to specific company
    you have to pass the company id and (1 or 0) for each category to be added or not';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companyId = $this->argument('company_id');
        $attendanceOvertime = $this->argument('attendance_deduction');
        $attendanceDeduction = $this->argument('attendance_overtime');

        if ($attendanceOvertime == 1) {
            $overtimeSalaryComponentsCategory = SalaryComponentsCategory::updateOrCreate([
                'company_id' => $companyId,
                'name' => 'Attendance Overtime',
            ],
                [
                    'is_addition' => 1,
                    'order' => null,
                    'is_taxable' => 1,
                ]);

            if (isset($overtimeSalaryComponentsCategory)) {
                SalaryComponent::updateOrCreate([
                    'company_id' => $companyId,
                    'name' => 'overtime',
                ],
                    [
                        'salary_components_category_id' => $overtimeSalaryComponentsCategory->id,
                        'is_variable' => 0,
                        'order' => null,
                        'is_automated' => 1,
                    ]);
            }
        }

        if ($attendanceDeduction == 1) {
            $deductionSalaryComponentsCategory = SalaryComponentsCategory::updateOrCreate([
                'company_id' => $companyId,
                'name' => 'Attendance Deduction',
            ],
                [
                    'is_addition' => 0,
                    'order' => null,
                    'is_taxable' => 1,
                ]);

            if (isset($deductionSalaryComponentsCategory)) {
                SalaryComponent::updateOrCreate([
                    'company_id' => $companyId,
                    'name' => 'late_deduction',
                ],
                    [
                        'salary_components_category_id' => $deductionSalaryComponentsCategory->id,
                        'is_variable' => 0,
                        'order' => null,
                        'is_automated' => 1,
                    ]);

                SalaryComponent::updateOrCreate([
                    'company_id' => $companyId,
                    'name' => 'absence_deduction',
                ],
                    [
                        'salary_components_category_id' => $deductionSalaryComponentsCategory->id,
                        'is_variable' => 0,
                        'order' => null,
                        'is_automated' => 1,
                    ]);
            }
        }

    }
}
