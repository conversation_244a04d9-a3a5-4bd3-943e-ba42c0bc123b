<?php

namespace App\Http\Requests\V1\PayrollHub;

use App\Rules\EmployeeIdRule;
use Illuminate\Foundation\Http\FormRequest;

class ConvertAbsenceToLeaveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'employee_id' => [
                'required',
                'integer',
                new EmployeeIdRule,
            ],
            'timecard_id' => [
                'integer',
                'required',
                'exists:timecards,id',
            ],
            'from' => [
                'required',
                'date',
                'date_format:Y-m-d H:i:s',
            ],
            'to' => [
                'required',
                'date',
                'date_format:Y-m-d H:i:s',
            ],
            'balance_id' => [
                'required',
                'integer',
            ],
            'note' => [
                'string',
                'nullable',
            ],
        ];
    }
}
