<?php

namespace App\Services\V1\ExportTemplate;

use App\Enums\Employee\EmployeeExportCategoryEnum;
use App\Http\Resources\V1\ExportTemplate\ExportTemplateResource;
use App\Repositories\V1\Employee\EmployeeExportTemplateRepository;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class ExportTemplateService
{
    public function __construct(
        private EmployeeExportTemplateRepository $employeeExportTemplateRepository
    ) {}

    public function getEmployeeExportTemplates(): AnonymousResourceCollection
    {
        $templates = $this->employeeExportTemplateRepository->get();
        return ExportTemplateResource::collection($templates);
    }

    public function createEmployeeExportTemplate(array $data): ExportTemplateResource
    {
        $employeeId = config('globals.user')?->employee_id;

        $fields = [];
        foreach (EmployeeExportCategoryEnum::values() as $category) {
            if ($category === EmployeeExportCategoryEnum::ESSENTIAL->value) {
                continue;
            }

            if (isset($data[$category]) && is_array($data[$category])) {
                $fields[$category] = $data[$category];
            }
        }

        $templateData = [
            'name' => $data['name'],
            'is_default' => false,
            'fields' => $fields,
            'created_by' => $employeeId,
        ];

        $template = $this->employeeExportTemplateRepository->add($templateData);

        return new ExportTemplateResource($template);
    }

    public function deleteEmployeeExportTemplate(int $id): bool
    {
        $employeeId = config('globals.user')?->employee_id;

        $template = $this->employeeExportTemplateRepository->find($id);

        if (!$template) {
            return false;
        }

        if ($template->is_default || $template->created_by !== $employeeId) {
            return false;
        }

        return $this->employeeExportTemplateRepository->delete([$id]);
    }
}
