<?php

namespace App\Enums;

enum LoanPolicyFilterEnum: string
{
    case LOAN = 'loan';
    case SALARY_ADVANCE = 'salary_advance';

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get values as a comma-separated string for validation rules
     */
    public static function valuesAsString(): string
    {
        return implode(',', self::values());
    }
}
