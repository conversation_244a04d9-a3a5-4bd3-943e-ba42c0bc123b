<?php

namespace App\Enums\Employee;

use App\Traits\EnumToArray;

enum EmployeeExportPresetTemplateEnum: string
{
    use EnumToArray;

    case BASIC_DETAILS = 'basic_details';
    case EMPLOYMENT_DETAILS = 'employment_details';

    public static function getLabel(self $value): string
    {
        return match ($value) {
            self::BASIC_DETAILS => 'Basic Details',
            self::EMPLOYMENT_DETAILS => 'Employment Details',
        };
    }

    public static function getCategories(self $value): array
    {
        return match ($value) {
            self::BASIC_DETAILS => [
                EmployeeExportCategoryEnum::BASIC,
                EmployeeExportCategoryEnum::CONTACT,
                EmployeeExportCategoryEnum::EDUCATION,
            ],
            self::EMPLOYMENT_DETAILS => [
                EmployeeExportCategoryEnum::EMPLOYMENT,
            ],
        };
    }

    public static function all(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }
}
