<?php

namespace App\Services\V1\RequestOvertime;

use App\Exceptions\UnprocessableException;
use App\Models\Company;
use App\Models\OvertimeGroup;
use App\Models\StateMachines\RequestCancelled;
use App\Repositories\NewAttendanceSettingRepository;
use App\Repositories\V1\EmployeeRequestRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveBalancesRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use App\Repositories\V1\Missions\MissionRepository;
use App\Repositories\V1\RequestOvertime\RequestOvertimeRepository;
use App\Services\BaseService;
use App\Traits\WorkflowTrait;
use Carbon\Carbon;
use Workflow\WorkflowStub;
use App\Jobs\AssignApprovalsJob;
use App\Traits\V1\EmployeeRequestsTrait;
use App\Traits\V1\NotificationRedirection;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;

class RequestOvertimeService extends BaseService
{
    use WorkflowTrait, EmployeeRequestsTrait, NotificationRedirection, PrepareAssignRequestCycleDataTrait;

    public function __construct(
        private RequestOvertimeRepository $requestOvertimeRepository,
        private NewAttendanceSettingRepository $attendanceSettingRepository,
        private EmployeeLeaveBalancesRepository $employeeLeaveBalancesRepository,
        private EmployeeLeaveRequestRepository $employeeLeaveRequestRepository,
        private MissionRepository $missionRepository,
        private EmployeeRequestRepository $employeeRequestRepository

    ) {}

    public function requestOvertime($data)
    {
        $this->canRequest($data);
        $request = $this->requestOvertimeRepository->add($data);
        $requesterRoleIds = auth()->user()->roles->pluck('id')->toArray();

        AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('overtime_request', $request, $requesterRoleIds));

    }

    public function actionOnRequest($requestId, $actionType)
    {
        $request = $this->requestOvertimeRepository->findOrFail($requestId);

        $roleIds = config('globals.user')->roles->pluck('id')->toArray();
        $userCanTakeAnAction = $this->canTakeAnAction($roleIds, $request->id, config('globals.REQUEST_WORKFLOW_TYPES.OVERTIME_REQUEST'), $actionType);
        if (! $userCanTakeAnAction) {
            throw new UnprocessableException(trans('messages.can_not_take_this_action'));
        }
        if ($request->status != 'pending') {
            throw new UnprocessableException(trans('messages.workflow_is_completed'));
        }

        $this->doAnAction($actionType);
        if($this->checkRequestIsCompleted($request->employeeRequest)){
            $finalStatus = $this->getFinalStatus($request->employeeRequest, $actionType);
            $this->updateRequest($request->employeeRequest, $finalStatus);
            $this->updateEntity($request, $finalStatus);
            $this->redirectNotificationsAfterRequestFinalized($request, $finalStatus);
        }
    }

    public function cancelExpiredRequest($employeeId)
    {
        $pendingRequest = $this->requestOvertimeRepository->getRequestByDateAndStatus($employeeId, Carbon::now()->format('Y-m-d'), ['pending']);
        $this->requestOvertimeRepository->cancelExpiredRequest($employeeId);
        if (isset($pendingRequest)) {
            $data = [
                'requestable_id' => $pendingRequest->id,
                'requestable_type' => config('globals.REQUEST_WORKFLOW_TYPES.OVERTIME_REQUEST'),
            ];
            $employeeRequest = $this->employeeRequestRepository->getRequestByRequestableId($data);
            $employeeRequest->status->transitionTo(RequestCancelled::class);
            $employeeRequest->save();
        }

    }

    public function getOvertimeRequest($employeeId)
    {
        return $this->requestOvertimeRepository->findByKeys(['employee_id' => $employeeId]);
    }

    public function getApprovedRequestByDate($employeeId, $date)
    {
        return $this->requestOvertimeRepository->getFirstByAttributes([
            'employee_id' => $employeeId,
            'date' => $date,
        ]);
    }

    public function addBalanceToOvertimeLeave($data)
    {
        $company = Company::find(auth()->user()->company_id);
        $overtimeLeaveBalance = $this->employeeLeaveBalancesRepository->getLeaveTypeBalanceOrCreate($data['employee_id'], $company->OvertimeLeaveType);
        $overtimeLeaveBalance->update(['balance' => $overtimeLeaveBalance->balance + ($data['minutes'] / 60 + $data['hours'])]);

    }

    // #################################

    private function isCompanyApplyOvertimePolicy()
    {
        $applyOvertimePolicy = $this->attendanceSettingRepository->getSettingsByCompanyIdAndKey('apply_overtime', auth()->user()->employee->company_id);
        if (isset($applyOvertimePolicy) && $applyOvertimePolicy->is_used) {
            return true;
        }

        return false;
    }

    private function isCompanyApplyOvertimeOnThisTitle($titleId)
    {
        return OvertimeGroup::whereHas('titles', function ($q) use ($titleId) {
            $q->where('title_id', $titleId);
        })->exists();
    }

    private function canRequest($data)
    {
        if (! $this->isCompanyApplyOvertimePolicy() || ! $this->isCompanyApplyOvertimeOnThisTitle(auth()->user()->employee->title_id)) {
            throw new UnprocessableException(trans('messages.can_not_take_this_action'));
        }

        $pendingRequest = $this->requestOvertimeRepository->getRequestByDateAndStatus($data['employee_id'], $data['date'], ['pending', 'approved']);
        if ($pendingRequest) {
            throw new UnprocessableException(trans('messages.already_have_pending_request'));
        }

        $partialLeaveExists = $this->employeeLeaveRequestRepository->getEmployeePartialLeaveAroundDate($data['employee_id'], $data['date']) !== null;
        if ($partialLeaveExists) {
            throw new UnprocessableException(trans('messages.partial_leave_exists'));
        }
    }
}
