<?php

namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller;
use App\Repositories\IRepository;
use App\Repositories\Repository;
use App\Services\V1\ManagerService;
use App\Util\HttpStatusCodeUtil;

class ManagerController extends Controller
{
    private Repository $entityTagRepository;

    // use DataPreparation;
    private IRepository $timecardRepository;

    private IRepository $cicoRepository;

    public function __construct(
        private ManagerService $managerService,
        // private GetTimecardsFilterService $timecardsFilterService,
        // private GetCicosFilterService     $cicosFilterService,
        // private GetLeavesFiltersService   $leavesFiltersService,
        // private AttendanceCrudService       $attendanceCrudService,
        // private EmployeeLeaveRequestCrudService  $employeeLeaveRequestCrudService,

    ) {}

    public function homePageDetails()
    {

        $data = $this->managerService->homePageDetails();

        return getResponseStructure(['data' => $data], HttpStatusCodeUtil::OK);
    }
}
