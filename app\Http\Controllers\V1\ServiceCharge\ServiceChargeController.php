<?php

namespace App\Http\Controllers\V1\ServiceCharge;

use App\Exceptions\UnprocessableException;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\ServiceCharge\AddServiceChargeGroupRequest;
use App\Http\Requests\V1\ServiceCharge\GetServiceChargeRequest;
use App\Http\Requests\V1\ServiceCharge\SaveServiceChargeRequest;
use App\Http\Requests\V1\ServiceCharge\TogglePolicyRequest;
use App\Http\Requests\V1\ServiceCharge\UpdateBranchesRequest;
use App\Http\Requests\V1\ServiceCharge\UpdateGeneralSettingsRequest;
use App\Http\Resources\V1\ServiceCharge\BranchGroupResource;
use App\Http\Resources\V1\ServiceCharge\BranchResource;
use App\Http\Resources\V1\ServiceCharge\GeneralSettingsResource;
use App\Services\V1\ServiceCharge\ServiceChargeService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ServiceChargeController extends NewController
{
    public function __construct(
        protected ServiceChargeService $serviceChargeService)
    {
    }

    public function togglePolicy(TogglePolicyRequest $request)
    {
        try {
            return DB::transaction(function () use ($request) {

                $this->serviceChargeService->settingsChangedEffect();
                $this->serviceChargeService->toggleServiceChargesPolicy($request->validated('flag'));

                return getResponseStructure(
                    ['data' => []],
                    HttpStatusCodeUtil::OK,
                    'policy changed successfully');
            });

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function getGeneralSettings()
    {
        try {
            $settings = $this->serviceChargeService->getGeneralSettings();

            return getResponseStructure(
                ['data' => new GeneralSettingsResource((object) $settings)],
                HttpStatusCodeUtil::OK,
                'General settings retrieved successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function updateGeneralSettings(UpdateGeneralSettingsRequest $request)
    {
        try {
            return DB::transaction(function () use ($request) {
                $this->serviceChargeService->settingsChangedEffect();
                $this->serviceChargeService->setupServiceCharge($request->validated());

                return getResponseStructure(
                    ['data' => []],
                    HttpStatusCodeUtil::OK,
                    'policy changed successfully');
            });

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function listGroups()
    {
        try {
            $group = $this->serviceChargeService->listGroups();

            return getResponseStructure(
                ['data' => BranchGroupResource::collection($group)],
                HttpStatusCodeUtil::OK,
                'group add successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function addGroup(AddServiceChargeGroupRequest $request)
    {
        try {
            return DB::transaction(function () use ($request) {
                $this->serviceChargeService->settingsChangedEffect();
                $group = $this->serviceChargeService->createBranchGroup($request->validated());

                return getResponseStructure(
                    ['data' => $group],
                    HttpStatusCodeUtil::OK,
                    'group add successfully');
            });

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function editGroup(AddServiceChargeGroupRequest $request, int $id)
    {
        try {
            return DB::transaction(function () use ($request, $id) {
                $this->serviceChargeService->settingsChangedEffect();
                $this->serviceChargeService->updateBranchGroup($id, $request->validated());

                return getResponseStructure(
                    ['data' => []],
                    HttpStatusCodeUtil::OK,
                    'group updated successfully');
            });

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function deleteGroup($id)
    {
        try {
            return DB::transaction(function () use ($id) {
                $this->serviceChargeService->settingsChangedEffect();
                $this->serviceChargeService->deleteGroup($id);

                return getResponseStructure(
                    ['data' => []],
                    HttpStatusCodeUtil::OK,
                    'group deleted successfully');
            });

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function listBranches()
    {
        try {
            $branches = $this->serviceChargeService->listBranches();

            return getResponseStructure(
                ['data' => BranchResource::collection($branches)],
                HttpStatusCodeUtil::OK,
                'branches retrieved successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function editBranches(UpdateBranchesRequest $request)
    {
        try {
            return DB::transaction(function () use ($request) {

                $this->serviceChargeService->settingsChangedEffect();
                $this->serviceChargeService->syncBranches($request->validated());

                return getResponseStructure(
                    ['data' => []],
                    HttpStatusCodeUtil::OK,
                    'branches updated successfully');
            });

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function getSettingsWithSalaries(GetServiceChargeRequest $request)
    {
        try {
            $data = $this->serviceChargeService->getSettingsWithSalaries($request->validated());

            return getResponseStructure(
                ['data' => $data],
                HttpStatusCodeUtil::OK,
                'settings retrieved successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function saveSettingsWithSalaries(SaveServiceChargeRequest $request)
    {

        return DB::transaction(function () use ($request) {

            $this->serviceChargeService->saveSettingsWithSalaries($request->validated());

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'settings saved successfully');
        });

    }
}
