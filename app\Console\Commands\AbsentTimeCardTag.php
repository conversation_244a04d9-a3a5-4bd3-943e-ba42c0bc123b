<?php

namespace App\Console\Commands;

use App\Services\TimeTracking\CrudServices\EntityTagCrudService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AbsentTimeCardTag extends Command
{
    public function __construct(private EntityTagCrudService $entityTagCrudService)
    {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trigger:absent:timeCards';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        DB::beginTransaction();
        try {
            $this->entityTagCrudService->addAbsentTagForTimeCards();
            DB::commit();
        } catch (Exception $e) {
            Log::error('absent cron job failed at: '.date('Y-m-d H:i:s').' with error: '.$e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
