<?php

namespace App\Console\Commands;

use App\Models\AttendanceDeduction;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RemoveCompanyAttendanceDeductions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-company-attendance-deductions {company_id} {from_date} {to_date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $companyId = $this->argument('company_id');
            $fromDate = $this->argument('from_date');
            $toDate = $this->argument('to_date');
            $attendanceDeductions = AttendanceDeduction::where('company_id', $companyId)
                ->whereDate('date', '>=', $fromDate)->whereDate('date', '<=', $toDate)->get();
            foreach ($attendanceDeductions as $attendanceDeduction) {
                foreach ($attendanceDeduction->workflowApprovalCycles as $workflowApprovalCycle) {
                    activity()->withoutLogs(function () use ($workflowApprovalCycle) {
                        $workflowApprovalCycle->delete();
                    });
                }
                activity()->withoutLogs(function () use ($attendanceDeduction) {
                    $attendanceDeduction->delete();
                });
            }
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();
        }
    }
}
