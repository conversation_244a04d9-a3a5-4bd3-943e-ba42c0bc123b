<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Employee;
use App\Models\PublicHolidaysAttendance;
use App\Services\V1\Holidays\PublicHolidaysAttendanceService;
use App\Services\V1\Holidays\PublicHolidaysService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixOldHolidaysCompensations extends Command
{
    public function __construct(
        private PublicHolidaysService $publicHolidaysService,
        private PublicHolidaysAttendanceService $publicHolidaysAttendanceService,
    ) {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:old:compensations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $holidaysCompensationBalances = PublicHolidaysAttendance::with('employee.title.publicHolidaysPolicy')->get();
            // change each days_used_as_pay and days_used_as_leave based on employee.title.publicHolidaysPolicy->compensation_holidays_rate or  compensation_pay_rate
            foreach ($holidaysCompensationBalances as $holidaysCompensationBalance) {
                $compensationHolidaysRate = $holidaysCompensationBalance->employee->title?->publicHolidaysPolicy?->compensation_holidays_rate ?? 1;
                $compensationPayRate = $holidaysCompensationBalance->employee->title?->publicHolidaysPolicy?->compensation_pay_rate ?? 1;
                $holidaysCompensationBalance->days_used_as_leave = min($holidaysCompensationBalance->days_used_as_leave * $compensationHolidaysRate, $holidaysCompensationBalance->holiday_compensation);
                $holidaysCompensationBalance->save();
            }
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
            //            dd($e->getMessage());
        }
    }
}
