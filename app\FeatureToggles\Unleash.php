<?php

namespace App\FeatureToggles;

use Unleash\Client\Configuration\Context;
use Unleash\Client\Configuration\UnleashContext;
use Unleash\Client\UnleashBuilder;

class Unleash
{
    public $client;

    public function __construct()
    {
        $this->client = UnleashBuilder::create()
            ->withAppName(env('UNLEASH_APP_NAME') ?? '')
            ->withAppUrl(env('UNLEASH_URL') ?? '')
            ->withInstanceId(env('UNLEASH_INSTANCE_ID') ?? '')
            ->withHeader('Authorization', env('UNLEASH_AUTHORIZATION') ?? '')
            ->build();
    }

    public function isEnabled(string $featureName, ?Context $context = null, bool $default = false): bool
    {
        return $this->client->isEnabled($featureName, $context, $default);
    }

    public function getAbsenceLateEditsFeatureFlag()
    {
        return $this->client->isEnabled('sa7el-attendance-policies-temp');

    }

    public function getUnscheduledShiftsFeatureFlag(): bool
    {
        return $this->isEnabled('unscheduled-perm') ?? false;
    }

    public function getNewWorkTypesFeatureFlag($authCompanyId = null): bool
    {
        $companyId = $authCompanyId ?? config('globals.company')->id;
        $context = new UnleashContext;
        $context->setCustomProperty('companyId', $companyId);

        return $this->client->isEnabled('new-work-types', $context) ?? false;
    }

    public function getSickLeaveComponentEditsFeatureFlag(): bool
    {
        return true;

    }

    public function isStaticShiftsFlagEnabled()
    {
        return $this->isEnabled('static-shifts-temp');

    }

    public function isLeaveBalanceChangesFlagEnabled()
    {
        return $this->isEnabled('leave-balance-changes');
    }

    public function isEmployeeChangesEnabled() // deprecated
    {
        return $this->isEnabled('employee_profile_changes');
    }

    public function isWorkerAppEnabled()
    {
        $companyId = config('globals.company')->id;
        $context = new UnleashContext;
        $context->setCustomProperty('companyId', $companyId);

        return $this->isEnabled('new_worker_app', $context);
    }

    public function isMissionsFlagEnabled()
    {
        return $this->isEnabled('missions');
    }

    public function isRequestOvertimeHoursEnabled($companyId = null)
    {
        $companyId = $companyId ?? config('globals.company')->id;
        $context = new UnleashContext;
        $context->setCustomProperty('companyId', $companyId);

        return $this->isEnabled('request-overtime-hours', $context);
    }

    public function isPartialLeaveEnabled()
    {
        return $this->isEnabled('partial-day-leave-temp');
    }

    public function isProbationEnabled()
    {
        return $this->isEnabled('probation-temp');
    }

    public function isShiftViewFlagEnabled()
    {
        return $this->isEnabled('shift-view');
    }

    public function isDeviceIDResetEnabled()
    {
        return $this->isEnabled('device_id_feat');
    }

    public function isPayrollHubEnabled($company_id = null): bool
    {
        $companyId = $company_id ?? config('globals.company')->id;
        $context = new UnleashContext;
        $context->setCustomProperty('companyId', $companyId);

        return $this->isEnabled('payroll-hub-temp', $context);
    }
}
