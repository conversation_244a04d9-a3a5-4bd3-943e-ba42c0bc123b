<?php

namespace App\Console\Commands;

use App\Services\V1\Files\ChangeFilesStatusService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ChangeFilesStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'change:files_statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = ' ';

    public function __construct(private ChangeFilesStatusService $changeFilesStatusService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        DB::beginTransaction();
        try {
            $this->changeFilesStatusService->run();
            DB::commit();
        } catch (Exception $e) {
            // \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
