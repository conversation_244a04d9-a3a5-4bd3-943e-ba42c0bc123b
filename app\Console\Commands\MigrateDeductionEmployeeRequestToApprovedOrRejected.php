<?php

namespace App\Console\Commands;

use App\Models\EmployeeRequest;
use DB;
use Illuminate\Console\Command;

class MigrateDeductionEmployeeRequestToApprovedOrRejected extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-deduction-employee-request-to-approved-or-rejected';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $deductionEmployeeRequests = EmployeeRequest::with('requestable')
                ->where('requestable_type', 'attendance_deduction')
                ->where('status', '!=', 'pending')
                ->get();

            echo 'Total Deduction Employee Requests: '.$deductionEmployeeRequests->count().PHP_EOL;

            foreach ($deductionEmployeeRequests as $deductionEmployeeRequest) {

                if ($deductionEmployeeRequest->status == 'waived') {
                    echo 'Deduction Employee Request is already waived for request id: '.$deductionEmployeeRequest->id.PHP_EOL;
                    $deductionEmployeeRequest->update(['status' => 'approved']);
                } else {
                    $deduction = $deductionEmployeeRequest->requestable;
                    $employeeApproves = $deduction->employeeApproves;
                    if (count($employeeApproves) == 0) {
                        echo 'Deduction Employee Request id: '.$deductionEmployeeRequest->id.' has no employee approves'.PHP_EOL;
                        $deductionEmployeeRequest->update(['status' => 'rejected']);

                        continue;
                    }
                    echo 'Deduction Employee Request id: '.$deductionEmployeeRequest->id.' has '.$employeeApproves->count().' employee approves'.PHP_EOL;
                    $employeeApproves->each(function ($employeeApprove) use ($deductionEmployeeRequest) {
                        if ($employeeApprove->pivot->status === 'rejected') {
                            echo 'Deduction Employee Request id: '.$deductionEmployeeRequest->id.' is rejected'.PHP_EOL;
                            $deductionEmployeeRequest->update(['status' => 'rejected']);
                        } else {
                            echo 'Deduction Employee Request id: '.$deductionEmployeeRequest->id.' is approved after applied'.PHP_EOL;
                            $deductionEmployeeRequest->update(['status' => 'approved']);
                        }

                    });
                }
            }
            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            // dd($e);

        }
    }
}
