<?php

namespace App\Repositories;

use App\Enums\EntityTags\AttendanceTags;
use App\Enums\EntityTags\ClockInTags;
use App\Enums\Missions\MissionsEnum;
use App\Enums\LoanPolicyFilterEnum;
use App\Models\Employee;
use App\Traits\QueriesHelper;
use App\Traits\V2\WorkTypesTrait;
use App\Util\EmployeeUtil;
use App\Util\LoansUtil;
use App\Util\ScopeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NewEmployeeRepository extends BaseRepository
{
    use QueriesHelper, WorkTypesTrait;

    public function model(): string
    {
        return Employee::class;
    }

    public function getImportantDataByFilters($data)
    {
        return $this->model
            ->select('id', 'first_name_en', 'first_name_ar', 'second_name_en', 'second_name_ar', 'title_id', 'employee_number')
            ->when(isset($data['search_value']), function ($q) use ($data) {
                $q->where(function ($query) use ($data) {
                    $query->where('name_ar', 'LIKE', '%'.$data['search_value'].'%')
                        ->orWhere('name_en', 'LIKE', '%'.$data['search_value'].'%');
                });
            })
            ->whereIn('title_id', $data['title_ids'])
            ->whereIn('branch_id', $data['branch_ids'])
            ->with('title:id,name_en,name_ar,color')
            ->get();
    }

    public function getEmployeesMissingHiringDateCount()
    {
        return $this->model->whereHas('employeeInfo', function ($q) {
            $q->whereNull('join_date');
        })->count();
    }

    public function missingSalaryDataCounter($data)
    {
        // $q = $this->model
        //     ->join('employees_info', 'employees_info.employee_id', '=', 'employees.id')
        //     ->when(isset($data['payroll_from_date']), function ($q) use ($data) {
        //         $q->where('employees_info.termination_date', '>=', $data['payroll_from_date'])
        //             ->orWhereNull('employees_info.termination_date');
        //     })
        //     ->where(function ($q) {
        //         $q->doesntHave('employeeSalaries')
        //             ->orWhereHas('employeeSalaries', function ($q) {
        //                 $q->where(function ($q) {
        //                     $q->where('net_salary', 0)->orWhereNull('net_salary');
        //                 });
        //             });
        //     });
        // $this->appendScopeQuery($q);

        // return $q->count();

        $q = $this->model
            ->join('employees_info as i', 'employees.id', '=', 'i.employee_id')
            ->leftJoin('employee_salaries as s', 'employees.id', '=', 's.employee_id')
            ->where('employees.company_id', auth()->user()->company_id)
            ->whereNull('s.id')

            ->when(isset($data['payroll_from_date']), function ($q) use ($data) {
                $q->where(function ($query) use ($data) {
                    $query->whereNull('i.termination_date')
                        ->orWhere('i.termination_date', '>=', $data['payroll_from_date']);
                });
            })
            ->select('employees.*', 'i.*', 's.*');
        $this->appendScopeQuery($q);

        return $q->count();
    }

    public function employeeWithPhoneNumberWithoutGlobalScopes($phone)
    {
        return $this->model->withoutGlobalScopes()->where('phone', $phone)->first();
    }

    public function getByPhone($phone)
    {
        return $this->model->where('phone', $phone)->with('emergencyContacts', 'employeeInfo')->first();
    }

    public function getIdByPhone($phone): ?int
    {
        return $this->model->where('phone', $phone)->value('id');
    }

    public function findFirstEmployeeWithAttributesWithoutGlobalScopes(array $attributes)
    {
        return $this->model
            ->select('id', 'revoke_date', 'deleted_at', 'company_id', 'status')
            ->withoutGlobalScopes()
            ->where($attributes)
            ->with('employeeInfo', function ($q) {
                $q->select('employee_id', 'termination_date');
            })
            ->first();
    }

    public function findFirstByAnyKey($data)
    {
        return $this->model
            ->where(function ($query) use ($data) {
                $query->where('phone', $data['phone'])
                    ->orWhere('name_ar', $data['name_ar'])
                    ->orWhere('employee_number', $data['employee_number']);
                if (!empty($data['national_id'])) {
                    $query->orWhere('national_id', $data['national_id']);
                }
            })
            ->first();
    }

    public function getLastEmployeeNumber(): ?int
    {

        return $this->model
            ->where('company_id', auth()->user()->company_id)
            ->value(DB::raw('MAX(CAST(employee_number AS UNSIGNED))'));

    }

    public function getEmployeesWithCompanyScope()
    {
        return $this->model->withWhereHas('user.customRoles.scopes', function ($q) {
            $q->where('key', ScopeUtil::COMPANY_SCOPE);
        })->get();
    }

    public function getActiveEmployeesWithPastTerminationDate()
    {
        return $this->model
            ->whereHas('employeeInfo', function ($q) {
                $q->where('termination_date', '<=', date('Y-m-d'));
            })
            ->where('status', 'termination_pending')
            ->get();
    }

    public function getCountOfTerminatedEmployeesWithOutstandingLoans()
    {
        $q = $this->model
            ->where('status', 'terminated')
            ->where(function ($q) {
                $q->whereHas('loans', function ($q) {
                    $q->whereIn('status', [LoansUtil::DISBURSED, LoansUtil::READY_TO_DISBURSE]);
                });
            });
        $this->appendScopeQuery($q);

        return $q->count();
    }

    public function getEmployeeUnderScope($employee, $scope, $search = null, $loadTimeCards = true, $loadLeaveRequests = true)
    {
        /*  we need to extract the logic of this query to a new method
         without the TimeCards and LeaveRequests and use that method in all the place that doesn't
         require these two fields */
        // Define the base query with required fields and relationships
        $query = $this->model
            ->select(
                'employees.id',
                'employees.name_ar',
                'employees.name_en',
                'employees.employee_number',
                'employees.first_name',
                'employees.second_name',
                'employees.third_name',
                'employees.fourth_name',
                'employees.name',
                'employees.address',
                'employees.phone',
                'employees.gender',
                'employees.status',
                'employees.title_id',
                'employees.branch_id',
                'employees.company_id'
            ) 
            ->with([
                'employeeInfo' => function ($q) {
                    $q->select('employee_id', 'termination_date');
                },
                'title' => function ($q) {
                    $q->with('department');
                },
                'branches',
            ])
            ->when($loadTimeCards, function ($q) {
                $q->with('timeCards');
            })
            ->when($loadLeaveRequests, function ($q) {
                $q->with('employeeLeaveRequests');
            })
            ->where('employees.id', '!=', $employee->id)
            ->where('employees.status', '!=', EmployeeUtil::STATUSES['TERMINATED']);

        if (isset($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('employees.name_ar', 'LIKE', '%'.$search.'%')
                    ->orWhere('employees.name_en', 'LIKE', '%'.$search.'%')
                    ->orWhere('employees.employee_number', 'LIKE', '%'.$search.'%')
                    ->orWhere('employees.phone', 'LIKE', '%'.$search.'%');
            });
        }
        // Apply scope-based filters
        $managedBranches = $employee->branches->pluck('id')->toArray();
        $managedDepartments = $employee->managedDepartments->pluck('id')->toArray();
        $managedSubDepartments = $employee->managedSubDepartments->pluck('id')->toArray();
        switch ($scope) {
            case ScopeUtil::BRANCH_SCOPE:
                $query->whereIn('branch_id', $managedBranches);
                break;

            case ScopeUtil::DEPARTMENT_SCOPE:
                $query->whereIn('branch_id', $managedBranches)
                    ->whereHas('title', function ($q) use ($managedDepartments) {
                        $q->whereIn('department_id', $managedDepartments);
                    });
                break;

            case ScopeUtil::SUB_DEPARTMENT_SCOPE:
                $query->whereIn('branch_id', $managedBranches)
                    ->whereHas('title', function ($q) use ($managedSubDepartments) {
                        $q->whereIn('sub_department_id', $managedSubDepartments);
                    });
                break;
        }
        return $query->get();
    }

    public function getEmployeeUnderScopeForLoans($employee, $scope, $policyType = null)
    {
        $pendingStatuses = [LoansUtil::PENDING, LoansUtil::DISBURSED, LoansUtil::READY_TO_DISBURSE];

        $query = $this->model
            ->select(
                'employees.id',
                'employees.name_ar',
                'employees.name_en',
                'employees.employee_number',
                'employees.title_id',
                'employees.branch_id',
                'employees.status'
            )
            ->with([
                'employeeSalary:employee_id,net_salary',
                'title:id,name_ar,name_en,loan_policy_id,salary_advance_policy_id',
                'title.salaryAdvancePolicy:id,allowed_request_day'
            ])
            ->withExists([
                'loans as isPendingLoan' => function ($query) use ($pendingStatuses) {
                    $query->whereIn('status', $pendingStatuses);
                },
                'salaryAdvances as isPendingSalaryAdvance' => function ($query) use ($pendingStatuses) {
                    $query->whereIn('status', $pendingStatuses);
                }
            ])
            ->where('employees.id', '!=', $employee->id)
            ->where('employees.status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->whereHas('title', function ($q) use ($policyType) {
                if ($policyType === LoanPolicyFilterEnum::LOAN->value) {
                    $q->whereNotNull('loan_policy_id');
                } elseif ($policyType === LoanPolicyFilterEnum::SALARY_ADVANCE->value) {
                    $q->whereNotNull('salary_advance_policy_id');
                } else {
                    $q->where(function ($titleQuery) {
                        $titleQuery->whereNotNull('loan_policy_id')
                            ->orWhereNotNull('salary_advance_policy_id');
                    });
                }
            });

        $managedBranches = $employee->branches->pluck('id')->toArray();
        $managedDepartments = $employee->managedDepartments->pluck('id')->toArray();
        $managedSubDepartments = $employee->managedSubDepartments->pluck('id')->toArray();

        switch ($scope) {
            case ScopeUtil::BRANCH_SCOPE:
                $query->whereIn('branch_id', $managedBranches);
                break;

            case ScopeUtil::DEPARTMENT_SCOPE:
                $query->whereIn('branch_id', $managedBranches)
                    ->whereHas('title', function ($q) use ($managedDepartments) {
                        $q->whereIn('department_id', $managedDepartments);
                    });
                break;

            case ScopeUtil::SUB_DEPARTMENT_SCOPE:
                $query->whereIn('branch_id', $managedBranches)
                    ->whereHas('title', function ($q) use ($managedSubDepartments) {
                        $q->whereIn('sub_department_id', $managedSubDepartments);
                    });
                break;
        }

        return $query->get();
    }

    public function getAttendanceMetrics($fromDate, $toDate, $employeeId)
    {
        $clockInTags = ClockInTags::values();
        $missionTags = MissionsEnum::MissionTypes;
        $company = auth()->user()->company;

        $employee = $this->model
            ->where('id', $employeeId)
            ->with([
                'attendance' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('date', '>=', $fromDate)
                        ->whereDate('date', '<=', $toDate);
                },
                'employeeLeaveRequests' => function ($q) use ($fromDate, $toDate, $employeeId) {
                    $q->whereDate('from', '<=', $toDate)
                        ->whereDate('to', '>=', $fromDate)
                        ->where('status', 'approved')
                        ->where('type', '!=', 'prorated')
                        ->whereHas('companyLeaveType', function ($query) use ($employeeId) {
                            $query->where('company_id', function ($subquery) use ($employeeId) {
                                $subquery->select('company_id')
                                    ->from('employees')
                                    ->where('id', $employeeId);
                            });
                        });
                },
                'penalties' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('date', '>=', $fromDate)
                        ->whereDate('date', '<=', $toDate)
                        ->where('status', 'approved');
                },
                'attendanceOvertimes' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('date', '>=', $fromDate)
                        ->whereDate('date', '<=', $toDate);
                },
                'attendanceDeductions' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('date', '>=', $fromDate)
                        ->whereDate('date', '<=', $toDate)
                        ->where('deduction_value', '!=', 0)
                        ->where('status', 'applied');
                },
                'excusesBalance' => function ($q) use ($employeeId, $fromDate, $toDate) {
                    $q->where('employee_id', $employeeId)
                        ->whereHas('payroll', function ($query) use ($fromDate, $toDate) {
                            $query->whereDate('start', '>=', $fromDate)
                                ->whereDate('start', '<=', $toDate);
                        })
                        ->latest();
                },
                'publicHolidayAbsences' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('from', '<=', $toDate)
                        ->whereDate('to', '>=', $fromDate);
                },
            ])
            ->withCount([
                'attendance as attendance_count' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('date', '>=', $fromDate)
                        ->whereDate('date', '<=', $toDate);
                },
                'timecards as absence_count' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('from', '<=', $toDate)
                        ->whereDate('to', '>=', $fromDate)
                        ->whereHas('entityTags', function ($query) {
                            $query->whereIn('tag', ['absent', 'absent_without_permission']);
                        });
                },
                'employeeLeaveRequests as custom_leave_count' => function ($q) use ($company, $employeeId, $fromDate, $toDate) {
                    $q->where('status', 'approved')
                        ->whereDate('from', '<=', $toDate)
                        ->whereDate('to', '>=', $fromDate)
                        ->where('status', 'approved')
                        ->where('type', '!=', 'prorated')
                        ->whereHas('companyLeaveType', function ($query) use ($company, $employeeId) {
                            $query->where('company_id', function ($subquery) use ($employeeId) {
                                $subquery->select('company_id')
                                    ->from('employees')
                                    ->where('id', $employeeId);
                            })
                                ->whereNotIn('id', [$company->public_holiday_leave_type_id, $company->rest_day_leave_id]);
                        });
                },
                'employeeLeaveRequests as rest_day_count' => function ($q) use ($company, $employeeId, $fromDate, $toDate) {
                    $q->whereDate('from', '<=', $toDate)
                        ->whereDate('to', '>=', $fromDate)
                        ->whereHas('companyLeaveType', function ($query) use ($company, $employeeId) {
                            $query->where('company_id', function ($subquery) use ($employeeId) {
                                $subquery->select('company_id')
                                    ->from('employees')
                                    ->where('id', $employeeId);
                            })
                                ->where('id', $company->rest_day_leave_id);
                        });
                },
                'attendance as work_public_holiday' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('date', '>=', $fromDate)
                        ->whereDate('date', '<=', $toDate)
                        ->whereHas('entityTags', function ($query) {
                            $query->where('tag', 'public_holiday');
                        });
                },
                'attendance as no_clock_out' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('date', '>=', $fromDate)
                        ->whereDate('date', '<=', $toDate)
                        ->WhereHas('clockOut', function ($query) {
                            $query->where('source', 'automated');
                        });
                },
                'attendance as extra_work_days' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('date', '>=', $fromDate)
                        ->whereDate('date', '<=', $toDate)
                        ->whereHas('entityTags', function ($query) {
                            $query->where('tag', 'extra_workday');
                        })
                        ->where('extra_work_day_status', 'approved');
                },
                'publicHolidayAbsences as public_holiday_absence_count' => function ($q) use ($employeeId, $fromDate, $toDate) {
                    $q->whereDate('from', '<=', $toDate)
                        ->whereDate('to', '>=', $fromDate)
                        ->where('employee_id', $employeeId);
                },
                'timecards as missions' => function ($q) use ($missionTags, $fromDate, $toDate) {
                    $q->whereDate('from', '<=', $toDate)
                        ->whereDate('to', '>=', $fromDate)
                        ->whereHas('timecardType', function ($query) use ($missionTags) {
                            $query->whereIn('name', $missionTags);
                        });
                },
            ])
            ->withSum(['attendanceOvertimes' => function ($q) use ($fromDate, $toDate) {
                $q->whereDate('date', '>=', $fromDate)
                    ->whereDate('date', '<=', $toDate)
                    ->where('status', 'approved');
            }], 'overtime_minutes')
            ->withSum(['penalties as penalty_days_sum' => function ($q) use ($fromDate, $toDate) {
                $q->whereDate('date', '>=', $fromDate)
                    ->whereDate('date', '<=', $toDate)
                    ->where('status', 'approved')
                    ->where('unit', 'days');
            }], 'amount')
            ->withSum(['penalties as penalty_cash_sum' => function ($q) use ($fromDate, $toDate) {
                $q->whereDate('date', '>=', $fromDate)
                    ->whereDate('date', '<=', $toDate)
                    ->where('status', 'approved')
                    ->where('unit', 'cash');
            }], 'amount')
            ->first();

        if ($employee) {
            $employee->total_penalty_days_value = $employee->penalty_days_sum;
            $employee->total_penalty_cash_value = $employee->penalty_cash_sum;
            $employee->total_overtime_value = $employee->attendance_overtimes_sum_overtime_minutes;

            if ($employee->excusesBalance()->exists()) {
                $latestExcuseBalance = $employee->excusesBalance()->latest('created_at')->first();
                $employee->excuse_balance_diff = $latestExcuseBalance->base_hours - $latestExcuseBalance->remaining_hours;
            } else {
                $employee->excuse_balance_diff = 0;
            }
        }

        return $employee;
    }

    public function getAttendanceDetails($employeeId, $fromDate, $toDate)
    {
        return $this->model
            ->where('id', $employeeId)
            ->with([
                'attendance' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('date', '>=', $fromDate)
                        ->whereDate('date', '<=', $toDate)
                        ->with([
                            'entityTags',
                            'attendanceOvertimes',
                            'slotable',
                            'attendanceDeductions' => function ($deductionQuery) {
                                $deductionQuery->where('deduction_value', '!=', 0);
                            },
                        ]);
                },
                'employeeLeaveRequests' => function ($q) use ($fromDate, $toDate, $employeeId) {
                    $q->whereDate('from', '<=', $toDate)
                        ->whereDate('to', '>=', $fromDate)
                        ->whereHas('companyLeaveType', function ($query) use ($employeeId) {
                            $query->where('company_id', function ($subquery) use ($employeeId) {
                                $subquery->select('company_id')
                                    ->from('employees')
                                    ->where('id', $employeeId);
                            });
                        })
                        ->with('companyLeaveType');
                },
                'publicHolidayAbsences' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('from', '<=', $toDate)
                        ->whereDate('to', '>=', $fromDate);
                },
                'cicos' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('date', '>=', $fromDate)
                        ->whereDate('date', '<=', $toDate)
                        ->where('status', 'unverified');
                },
                'timecards' => function ($q) use ($fromDate, $toDate) {
                    $q->whereDate('from', '<=', $toDate)
                        ->whereDate('to', '>=', $fromDate)
                        ->with([
                            'entityTags',
                            'timecardType',
                        ]);
                },
            ])
            ->get();
    }

    public function getShiftViewEmployeesByFilters($data)
    {
        /*

         * */
        $lang = '_'.(config('globals.lang') ?? 'ar');
        $q = $this->model
            ->select('employees.id',
                'employee_number',
                'first_name'.$lang,
                'second_name'.$lang,
                'branch_id',
                'title_id',
                'employees.company_id as company_id')
            ->with('title:id,name_en,name_ar,color,work_type_policy_id', 'title.workTypePolicy:id,work_days_type,rest_days_type', 'branch:id,name_en,name_ar', 'profilePicture:attachment_url,attachable_id,attachable_type')
            ->where('branch_id', $data['branch_id'])
            ->where('status', '!=', EmployeeUtil::STATUSES['TERMINATED'])
            ->whereHas('employeeInfo', function ($q) use ($data) {
                $q->whereDate('join_date', '<', $data['date'])
                    ->where(function ($q) use ($data) {
                        // The following logic filters employees to ensure that only those with a
                        // termination_date within the specified range or with a null termination_date are included
                        $q->whereNull('termination_date')
                            // Include employees whose termination_date is within the date range
                            ->orWhere(function ($q) use ($data) {
                                $q->whereDate('termination_date', '>=', $data['date']);
                            });
                    });
            })
            ->when($data['type'] === 'shift' && ! empty($data['shift_id']), function ($q) use ($data) {
                $q->whereDoesntHave('timecards', function ($q) use ($data) {
                    $q->where('shift_id', $data['shift_id'])
                        ->whereDate('from', $data['date']);
                });
            })
            ->when($data['type'] === 'rest_day' && empty($data['shift_id']), function ($q) use ($data) {
                $q->whereDoesntHave('employeeLeaveRequests', function ($q) use ($data) {
                    $q->whereDate('from', $data['date'])
                        ->where('status', 'approved');
                });
            })
            ->when(isset($data['title_ids']), function ($q) use ($data) {
                $q->whereIn('title_id', $data['title_ids']);
            })
            ->when(isset($data['department_ids']), function ($q) use ($data) {
                $q->whereHas('title', function ($q) use ($data) {
                    $q->whereIn('department_id', $data['department_ids']);
                });
            })
            ->when(isset($data['search_value']), function ($q) use ($data) {
                $this->appendEmployeeTextSearchQuery($q, $data['search_value']);
            });

        $this->appendScopeQuery($q);

        return $q->get();
    }

    public function restDayBlockerValidation($employeeIds, $date, $restDayLeaveId)
    {
        return $this->model
            ->whereIn('id', $employeeIds)
            ->where(function ($query) use ($date, $restDayLeaveId) {
                $query->whereHas('employeeLeaveRequests', function ($q) use ($date, $restDayLeaveId) {
                    $q->where('from', $date)
                        ->where('company_leave_type_id', $restDayLeaveId);
                })
                    ->orWhereHas('attendance', function ($q) use ($date) {
                        $q->where('date', $date);
                    })
                    ->orWhere(function ($query) use ($date, $restDayLeaveId) {
                        $query->whereDoesntHave('employeeLeaveBalances', function ($q) use ($date, $restDayLeaveId) {
                            $q->where('start', '<=', $date)
                                ->where('end', '>=', $date)
                                ->where('company_leave_type_id', $restDayLeaveId);
                        });
                    });
            })
            ->get();
    }

    public function getEmployeesWithWorkTypePolicySettings(array $employeeIds)
    {
        return $this->model
            ->select('employees.id',
                'branch_id',
                'title_id',
                'work_type_policies.id as work_type_policy_id',
                'employees.company_id as company_id',
                'titles.work_type_policy_id',
                'work_type_policies.work_days_type as work_days_type',
                'work_type_policies.rest_days_type as rest_days_type')
            ->whereIn('employees.id', $employeeIds)
            ->join('titles', 'employees.title_id', '=', 'titles.id')
            ->join('work_type_policies', 'titles.work_type_policy_id', '=', 'work_type_policies.id')
            ->get();
    }

    public function getEmployeesAttendanceDetails($from, $to, $company_id, $branch_id, $search_value = null, $department_id = null, $title_id = null)
    {
        return $this->model
            ->where('company_id', $company_id)
            ->where('branch_id', $branch_id)
            ->when(isset($department_id), function ($q) use ($department_id) {
                $q->whereHas('title', function ($q) use ($department_id) {
                    $q->where('department_id', $department_id);
                });
            })
            ->when(isset($title_id), function ($q) use ($title_id) {
                $q->where('title_id', $title_id);
            })
            ->when(isset($search_value), function ($q) use ($search_value) {
                $this->appendEmployeeTextSearchQuery($q, $search_value);
            })
            ->with([
                'company',
                'branch',
                'title',
                'title.department',
                'employeeInfo',
                'attendance' => function ($q) use ($from, $to) {
                    $q->whereDate('date', '>=', $from)
                        ->whereDate('date', '<=', $to)
                        ->with([
                            'entityTags',
                            'attendanceOvertimes' => function ($overtimeQuery) {
                                $overtimeQuery->where('status', 'approved');
                            },
                            'attendanceDeductions' => function ($deductionQuery) {
                                $deductionQuery->where('status', 'applied')
                                    ->where('deduction_value', '!=', 0);
                            },
                            'branch',
                            'slotable',
                            'employee',
                        ]);
                },
                'employeeLeaveRequests' => function ($q) use ($from, $to) {
                    $q->whereDate('from', '<=', $to)
                        ->whereDate('to', '>=', $from)
                        ->whereIn('status', ['approved', 'pending'])
                        ->whereNull('partial_leave_type')
                        ->where('type', '!=', 'prorated')
                        ->with('companyLeaveType');
                },
                'timecards' => function ($q) use ($from, $to) {
                    $q->whereDate('from', '<=', $to)
                        ->whereDate('to', '>=', $from)
                        ->with('entityTags');
                },
                'cicos' => function ($q) use ($from, $to) {
                    $q->whereDate('date', '>=', $from)
                        ->whereDate('date', '<=', $to)
                        ->where('status', 'unverified')
                        ->where(function ($query) {
                            $query->where('in_out', 'in')
                                ->orWhere('in_out', 'out');
                        });
                },
                'missionRequests' => function ($q) use ($from, $to) {
                    $q->whereDate('from', '<=', $to)
                        ->whereDate('to', '>=', $from)
                        ->where('status', 'pending');
                },
            ])
            ->withWhereHas('employeeInfo', function ($q) use ($to) {
                $q->whereDate('join_date', '<=', $to)
                    ->where(function ($q) use ($to) {
                        $q->whereNull('termination_date')
                            ->orWhereDate('termination_date', '>=', $to);
                    });
            })
            ->get();
    }

    public function getEmployeeWorkingHours($employeeId)
    {
        return $this->model
            ->where('employees.id', $employeeId)
            ->join('titles', 'employees.title_id', '=', 'titles.id')
            ->select('titles.working_hours')
            ->first()->working_hours;
    }

    public function getEmployeeDetails($employeeId)
    {
        return $this->model
            ->where('id', $employeeId)
            ->with('title', function ($query) {
                $query->with('attendanceGroup');
            })
            ->with(['branch', 'profilePicture'])
            ->first();
    }

    public function getEmployeeAbsences($filters)
    {
        return $this->model
            ->withWhereHas('timecards', function ($query) use ($filters) {
                $query->whereDate('from', '>=', $filters['from_date'])
                    ->whereDate('to', '<=', $filters['to_date'])
                    ->withWhereHas('entityTags', function ($query) {
                        $query->whereIn('tag', [AttendanceTags::ABSENT, AttendanceTags::ABSENT_WITHOUT_PERMISSION]);
                    })
                    ->orderBy('from');
            })
            ->where('id', $filters['employee_id'])
            ->with('title', function ($query) {
                $query->with('attendanceGroup');
            })
            ->with('branch')
            ->with(['profilePicture'])
            ->with('cicos', function ($query) use ($filters) {
                $query
                    ->whereDate('date', '>=', $filters['from_date'])
                    ->whereDate('date', '<=', $filters['to_date'])
                    ->where('status', 'unverified')
                    ->where('in_out', 'in')
                    ->with(['branch', 'clockOut']);
            })
            ->first();
    }

    public function getCompanyAbsences($filters)
    {
        $query = $this->model
            ->withWhereHas('timecards', function ($query) use ($filters) {
                $query->whereDate('from', '>=', $filters['from_date'])
                    ->whereDate('to', '<=', $filters['to_date'])
                    ->withWhereHas('entityTags', function ($query) {
                        $query->whereIn('tag', [AttendanceTags::ABSENT, AttendanceTags::ABSENT_WITHOUT_PERMISSION]);
                    });
            })
            ->where('company_id', $filters['company_id'])
            ->withWhereHas('title', function ($query) use ($filters) {
                $query->when(isset($filters['department_ids']), function ($query) use ($filters) {
                    return $query->whereIn('titles.department_id', $filters['department_ids']);
                })
                    ->with('attendanceGroup');
            })

            ->withWhereHas('branch', function ($query) use ($filters) {
                $query->when(isset($filters['branch_ids']), function ($query) use ($filters) {
                    return $query->whereIn('branches.id', $filters['branch_ids']);
                });
            })
            ->with('profilePicture')
            ->when(isset($filters['search_value']), function ($query) use ($filters) {
                return $query->where(function ($query) use ($filters) {
                    $query->where('employees.name_en', 'like', '%'.$filters['search_value'].'%')
                        ->orWhere('employees.name_ar', 'like', '%'.$filters['search_value'].'%')
                        ->orWhere('employees.employee_number', 'like', '%'.$filters['search_value'].'%');
                });
            });
        $this->appendScopeQuery($query);

        return $query;
    }

    public function countNewlyHiredEmployees($filters)
    {
        $query = $this->model
            ->where('status', EmployeeUtil::STATUSES['NEW_HIRE'])
            ->join('employees_info', 'employees_info.employee_id', '=', 'employees.id')
            ->where('employees_info.join_date', '>=', $filters['from_date'])
            ->where('employees_info.join_date', '<=', $filters['to_date']);
        $this->appendScopeQuery($query, $filters);

        return $query->count();
    }

    public function countOnProbationEmployees($filters)
    {
        $query = $this->model
            ->join('titles', 'employees.title_id', '=', 'titles.id')
            ->join('employees_info', 'employees_info.employee_id', '=', 'employees.id')
            ->whereRaw('DATE_ADD(employees_info.join_date, INTERVAL titles.probation_period MONTH) >= ?', [$filters['from_date']])
            ->where('employees_info.join_date', '<=', $filters['to_date']);
        $this->appendScopeQuery($query, $filters);

        return $query->count();

    }

    public function countNumberOfActiveEmployees($filters)
    {
        $query = $this->model
            ->join('employees_info', 'employees_info.employee_id', '=', 'employees.id')
            ->whereNot('status', EmployeeUtil::STATUSES['TERMINATED'])
            ->where('employees_info.join_date', '<=', $filters['to_date']);
        $this->appendScopeQuery($query, $filters);
        return $query->count();
    }

    public function getExistingEmployeeNumbersForBulkValidation(): array
    {
        return $this->model
            ->select('employee_number')
            ->whereNotNull('employee_number')
            ->where('employee_number', '!=', '')
            ->pluck('employee_number')
            ->flip()
            ->toArray();
    }

    public function getExistingNationalIdsForBulkValidation(): array
    {
        return $this->model
            ->withoutGlobalScopes()
            ->select('national_id')
            ->whereNotNull('national_id')
            ->where('national_id', '!=', '')
            ->pluck('national_id')
            ->flip()
            ->toArray();
    }

    public function getExistingPhonesForBulkValidation(): array
    {
        return $this->model
            ->withoutGlobalScopes()
            ->select('phone')
            ->whereNotNull('phone')
            ->where('phone', '!=', '')
            ->pluck('phone')
            ->flip()
            ->toArray();
    }

    public function getEmployeesWithCurrentValuesForBulk()
    {
        return $this->model
            ->withoutGlobalScopes()
            ->select('employees.id', 'employees.phone', 'employees.employee_number', 'employees.national_id', 'employees.company_id')
            ->join('employees_info', 'employees.id', '=', 'employees_info.employee_id')
            ->addSelect('employees_info.passport_number', 'employees_info.email as work_email', 'employees_info.personal_email')
            ->with('emergencyContacts:id,employee_id,name,phone,relation')
            ->whereNotNull('employees.phone')
            ->where('employees.phone', '!=', '')
            ->whereNull('employees.deleted_at')
            ->get();
    }
}
