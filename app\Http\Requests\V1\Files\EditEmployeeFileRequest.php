<?php

namespace App\Http\Requests\V1\Files;

use App\Rules\IsArabic;
use App\Rules\IsEnglish;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;

class EditEmployeeFileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name_en' => ['required_without:employee_file_category_id', 'string', new IsEnglish],
            'name_ar' => ['required_without:employee_file_category_id', 'string', new IsArabic],
            'employee_file_category_id' => [
                'integer',
                (new Exists('employee_file_categories', 'id'))->where(function ($query) {
                    $query->where('company_id', auth()->user()->company_id)
                        ->whereNull('deleted_at');
                }),
            ],
            'employee_id' => [
                'integer',
                (new Exists('employees', 'id'))->where(function ($query) {
                    $query->where('company_id', auth()->user()->company_id)
                        ->whereNull('deleted_at');
                }),
            ],
            'expiry_date' => 'date_format:Y-m-d',
            'existing_attachment_ids' => 'array',
            'existing_attachment_ids.*' => [
                'required',
                'integer',
                (new Exists('attachments', 'id'))->where(function ($query) {
                    $query
                        ->whereNull('deleted_at');
                }),
            ],
            'attachments' => 'array|max:5',
            'attachments.*' => 'required|mimes:pdf,doc,docx,png,jpg,jpeg|max:'.config('globals.MAX_FILE_SIZE_KB'),
        ];

    }
}
