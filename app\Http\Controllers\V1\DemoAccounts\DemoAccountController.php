<?php

namespace App\Http\Controllers\V1\DemoAccounts;

use App\Exceptions\UnprocessableException;
use App\Http\Controllers\NewController;
use App\Http\Requests\CreateDemoRequest;
use App\Http\Resources\DemoAccountResource;
use App\Jobs\InternalDashboard\CreateDemoCompany;
use App\Services\V1\InternalDashboard\CustomerSuccess\CustomerSuccessCompanyService;
use App\Services\V1\InternalDashboard\DemoAccount\DemoAccountService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\Log;

class DemoAccountController extends NewController
{
    public function __construct(
        protected DemoAccountService            $demoAccountService,
        protected CustomerSuccessCompanyService $customerSuccessCompanyService
    )
    {
    }

    public function addDemoAccount(CreateDemoRequest $request)
    {
        try {
            CreateDemoCompany::dispatch(
                $this->demoAccountService,
                $this->customerSuccessCompanyService,
                $request->validated()
            );

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'created successfully'
            );

        } catch (\Exception $e) {
            Log::error('Error in addDemoAccount: ' . $e->getMessage());
            throw new UnprocessableException($e->getMessage());
        }
    }

    //    public function deleteDemoAccount()
    //    {
    //        return getResponseStructure(
    //            ['data' => []],
    //            HttpStatusCodeUtil::OK,
    //            'updated successfully'
    //        );
    //    }

    public function listDemoAccounts()
    {
        $companies = $this->demoAccountService->listDemoAccounts();

        return getResponseStructure(
            ['data' => DemoAccountResource::collection($companies)],
            HttpStatusCodeUtil::OK,
            'retrieved successfully'
        );
    }
}
