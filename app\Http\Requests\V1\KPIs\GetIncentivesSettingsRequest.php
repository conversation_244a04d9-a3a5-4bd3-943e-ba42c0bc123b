<?php

namespace App\Http\Requests\V1\KPIs;

use Illuminate\Foundation\Http\FormRequest;

class GetIncentivesSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'month' => [
                'required',
                'string',
            ],
            'year' => [
                'required',
                'string',
            ],
            'branch_id' => [
                'integer',
            ],
            'payout_frequency' => [
                'string',
                'in:monthly,quarterly,bi_annually,annually',
            ],
            'employee_id' => [
                'integer',
                'nullable',
            ],
        ];
    }
}
