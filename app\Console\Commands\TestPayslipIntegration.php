<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\V1\Payroll\PayslipServiceV2;
use App\Repositories\V1\Payroll\PayslipRepositoryV2;
use App\Repositories\NewCompanyRepository;
use ZipArchive;
use Exception;

class TestPayslipIntegration extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'payslip:test-integration 
                            {--company_id=1 : Company ID to test with}
                            {--payroll_id=1 : Payroll ID to test with}
                            {--month=12 : Month to test with}
                            {--year=2024 : Year to test with}
                            {--lang=en : Language to test with (en/ar)}';

    /**
     * The console command description.
     */
    protected $description = 'Test PayslipService integration - Load template, store temp PDFs, create ZIP';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== PayslipService Integration Test ===');
        $this->info('Testing the three core requirements:');
        $this->info('1. Load payslip PDF template');
        $this->info('2. Store temp PDF for each payslip');
        $this->info('3. Collect all PDFs in one ZIP folder');
        $this->newLine();

        try {
            // Create service instance
            $payslipRepository = new PayslipRepositoryV2();
            $companyRepository = new NewCompanyRepository();
            $payslipService = new PayslipServiceV2($payslipRepository, $companyRepository);

            $this->info('✓ PayslipService initialized successfully');

            // Get test parameters
            $testData = [
                'company_id' => (int) $this->option('company_id'),
                'payroll_id' => (int) $this->option('payroll_id'),
                'month' => (int) $this->option('month'),
                'year' => (int) $this->option('year'),
                'print_lang' => $this->option('lang')
            ];

            $this->info('Test data: ' . json_encode($testData));
            $this->newLine();

            // Test 1: Check if we can get payslip data
            $this->info('=== Test 1: Getting payslip data ===');
            $payslips = $payslipService->getPayslips($testData);

            if ($payslips->isEmpty()) {
                $this->warn('⚠ No payslip data found. Please check your test data parameters.');
                $this->warn('Make sure the company_id, payroll_id, month, and year exist in your database.');
                return 1;
            }

            $this->info("✓ Found {$payslips->count()} payslip records");
            $this->info('Sample employee: ' . $payslips->first()->employee_name);
            $this->newLine();

            // Test 2: Generate and store payslips (this tests all 3 requirements)
            $this->info('=== Test 2: Generate and store payslips ===');
            $this->info('This will test all three requirements:');
            $this->info('- Loading payslip PDF template');
            $this->info('- Storing temp PDF for each payslip');
            $this->info('- Collecting all PDFs in one ZIP folder');
            $this->newLine();

            $progressBar = $this->output->createProgressBar($payslips->count());
            $progressBar->setFormat('Generating PDFs: %current%/%max% [%bar%] %percent:3s%%');
            $progressBar->start();

            $result = $payslipService->generateAndStorePayslips($testData);

            $progressBar->finish();
            $this->newLine(2);

            $this->info('✓ Payslips generated successfully!');
            $this->info("Total employees: {$result['total_employees']}");
            $this->info("ZIP file path: {$result['path']}");

            // Test 3: Verify ZIP file exists and has content
            $this->newLine();
            $this->info('=== Test 3: Verify ZIP file ===');
            $storagePath = $payslipService->getStoredPayslipsPath($testData);
            $fullPath = storage_path('app/' . $storagePath);

            if (file_exists($fullPath)) {
                $fileSize = filesize($fullPath);
                $this->info("✓ ZIP file exists: {$fullPath}");
                $this->info('✓ File size: ' . number_format($fileSize) . ' bytes');

                // Test ZIP contents
                $zip = new ZipArchive();
                if ($zip->open($fullPath) === TRUE) {
                    $numFiles = $zip->numFiles;
                    $this->info("✓ ZIP contains {$numFiles} PDF files");

                    // List first few files
                    $this->info('Sample files in ZIP:');
                    for ($i = 0; $i < min(3, $numFiles); $i++) {
                        $fileName = $zip->getNameIndex($i);
                        $this->info("  - {$fileName}");
                    }
                    $zip->close();
                } else {
                    $this->error('✗ Could not open ZIP file for verification');
                    return 1;
                }
            } else {
                $this->error("✗ ZIP file not found at: {$fullPath}");
                return 1;
            }

            $this->newLine();
            $this->info('=== Integration Test Results ===');
            $this->info('✓ Requirement 1: Load payslip PDF template - SUCCESS');
            $this->info('✓ Requirement 2: Store temp PDF for each payslip - SUCCESS');
            $this->info('✓ Requirement 3: Collect all PDFs in one ZIP folder - SUCCESS');
            $this->newLine();
            $this->info('🎉 All tests passed! PayslipService integration is working correctly.');

            return 0;

        } catch (Exception $e) {
            $this->newLine();
            $this->error('✗ Test failed with error: ' . $e->getMessage());

            if ($this->option('verbose')) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }

            return 1;
        }
    }
}