<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SalaryAdvanceProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status,
            'type' => $this->type,
            'amount' => $this->amount,
            'request_date' => \Carbon\Carbon::parse($this->created_at)->format('Y-m-d'),
            'payout_date' => $this->disbursement_date,
            'comment' => $this->comment,
        ];
    }
}
