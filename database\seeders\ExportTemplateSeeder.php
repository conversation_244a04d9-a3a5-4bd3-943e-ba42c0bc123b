<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Services\V1\InternalDashboard\CustomerSuccess\CustomerSuccessCompanyService;
use Illuminate\Database\Seeder;

class ExportTemplateSeeder extends Seeder
{
    public function run(): void
    {
        $companies = Company::all();
        $customerSuccessCompanyService = app(CustomerSuccessCompanyService::class);

        foreach ($companies as $company) {
            $customerSuccessCompanyService->createExportTemplatesForCompany($company->id);
        }
    }
}
