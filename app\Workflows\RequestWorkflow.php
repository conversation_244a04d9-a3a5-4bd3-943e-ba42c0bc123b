<?php

namespace App\Workflows;

use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use Illuminate\Support\Facades\Log;
use Workflow\ActivityStub;
use Workflow\SignalMethod;
use Workflow\Workflow;
use Workflow\WorkflowStub;

abstract class RequestWorkflow extends Workflow
{
    public $queue = 'workflow';

    public $connection = 'workflow';

    public int $tries = 250;

    public $is_completed = false;

    public $finalStatus = 'pending';

    public $requestWorkflowApprovals;

    abstract public function getFinalStatus($data);

    abstract public function updateRequestStatus($requestObj);

    public function execute($data)
    {
        try {
            Log::info('request workflow is started');

            yield ActivityStub::make(AssignRequestActivity::class, $data);

            yield WorkflowStub::await(fn () => ($this->is_completed));

            $this->getFinalStatus($data);

            if (isset($data['request']->migration_request)) {
                unset($data['request']->migration_request);
            }

            $this->updateRequestStatus($data['request']);

        } catch (\Exception $e) {
            Log::info('error in request workflow');
            Log::info($e);
            // \Sentry\captureException($e);

        }

    }

    #[SignalMethod]
    public function checkApprovalCycleStatus($data, $isSuperAdmin = false)
    {
        Log::info('Signal is received');
        Log::info('is super admin: '.$isSuperAdmin);

        if ($isSuperAdmin && ! $this->userInApprovalCycle($data)) {
            $this->is_completed = true;
        } else {
            $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
            $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle($data);
            $this->requestWorkflowApprovals = $this->getApprovals($approvalCycle);
            $this->checkIsCompleted();
        }
    }

    public function checkIsCompleted()
    {
        $orExists = count($this->requestWorkflowApprovals['or']) > 0;
        $thenExists = count($this->requestWorkflowApprovals['then']) > 0;
        Log::info('Check is completed');
        if (($orExists && ! in_array(config('globals.REQUEST_STATUSES.PENDING'), $this->requestWorkflowApprovals['or'])) ||
        ($thenExists && in_array(config('globals.REQUEST_STATUSES.REJECTED'), $this->requestWorkflowApprovals['then'])) ||
        ($thenExists && in_array(config('globals.REQUEST_STATUSES.CANCELLED'), $this->requestWorkflowApprovals['then'])) ||
        ($thenExists && ! in_array(config('globals.REQUEST_STATUSES.PENDING'), $this->requestWorkflowApprovals['then']))) {
            $this->is_completed = true;
        }
    }

    public function isWorkflowRequestRejected()
    {
        Log::info('Check is Workflow has rejected');
        if (
            (count($this->requestWorkflowApprovals['then']) > 0 && in_array('rejected', $this->requestWorkflowApprovals['then'])) ||
            (count($this->requestWorkflowApprovals['or']) > 0 && in_array('rejected', $this->requestWorkflowApprovals['or']))) {
            return true;
        }

        return false;

    }

    public function isWorkflowRequestCancelled()
    {
        Log::info('Check is Workflow has cancelled');
        $cancelledStatus = config('globals.REQUEST_STATUSES.CANCELLED');

        if (
            (count($this->requestWorkflowApprovals['then']) > 0 && in_array($cancelledStatus, $this->requestWorkflowApprovals['then'])) ||
            (count($this->requestWorkflowApprovals['or']) > 0 && in_array($cancelledStatus, $this->requestWorkflowApprovals['or']))) {
            return true;
        }

        return false;
    }

    public function getApprovals($approvalCycle)
    {
        $approvals = [
            'then' => [],
            'or' => [],
        ];

        foreach ($approvalCycle as $approval) {
            if ($approval->operator == 'then') {
                $approvals['then'][] = $approval->status;
            } elseif ($approval->operator == 'or') {
                $approvals['or'][] = $approval->status;
            }
        }

        return $approvals;
    }

    public function getRequest($data)
    {
        return [
            'requestable_id' => $data['request']->id,
            'requestable_type' => $data['workflow_type'],
        ];
    }

    public function userInApprovalCycle($data)
    {
        Log::info('userInApprovalCycle role ids: '.json_encode($data['role_ids']));
        // $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle($data);
        Log::info('cycle role ids: '.json_encode($approvalCycle->pluck('role_id')->toArray()));
        $user = $approvalCycle->whereIn('role_id', $data['role_ids'])->first();

        return ! is_null($user);
    }
}
