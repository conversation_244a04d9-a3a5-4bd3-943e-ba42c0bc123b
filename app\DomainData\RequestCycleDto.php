<?php

namespace App\DomainData;

trait RequestCycleDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'company_id' => 'required|integer',
            'title_id' => 'required|integer',
            'operator' => 'required|string|in:then,or',
            'type' => 'required|string|in:attendance_overtime,attendance_deduction',
            'order' => 'integer',

        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeRequestCycleDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
