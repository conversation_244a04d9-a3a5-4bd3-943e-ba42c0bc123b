<?php

namespace App\Console\Commands;

use App\Models\EmployeeLeaveBalance;
use App\Repositories\V1\Leaves\EmployeeLeaveBalancesRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixDuplicateLeaveBalances extends Command
{
    protected $signature = 'leave-balances:fix-duplicates';

    protected $description = 'Fix duplicate leave balances for employees';

    private $employeeLeaveBalanceRepository;

    public function __construct()
    {
        parent::__construct();
        $this->employeeLeaveBalanceRepository = new EmployeeLeaveBalancesRepository;
    }

    public function handle()
    {
        $this->info('Starting to fix duplicate leave balances...');

        try {
            $originalBalances = DB::table('employee_leave_balances as e')
                ->select(DB::raw('min(id) as min_id, company_leave_type_id, employee_id, e.start'))
                ->whereNull('deleted_at')
                ->groupBy('employee_id', 'company_leave_type_id', 'e.start')
                ->get();
            $allAnnualLeaveBalances = DB::table('employee_leave_balances as e')
                ->select('id', 'company_leave_type_id', 'employee_id', 'e.start')
                ->whereNull('deleted_at')
                ->get();

            $ids = $allAnnualLeaveBalances->pluck('id')->diff($originalBalances->pluck('min_id'));
            $numDeleted = EmployeeLeaveBalance::whereIn('id', $ids)->delete();

            $this->info("Fixed duplicate leave balances and deleted {$numDeleted}");
            Log::info("Fixed duplicate leave balances and deleted {$numDeleted}");
            $this->info("Fixed duplicate leave balances and deleted {$numDeleted}");

        } catch (\Exception $e) {
            $this->error('Error occurred: '.$e->getMessage());
        }
    }
}
