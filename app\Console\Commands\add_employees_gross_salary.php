<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Models\EmployeePayrollSummary;
use App\Models\PayRollPolicy;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Services\V1\Payroll\ConvertNetToGrossService;
use App\Util\PayrollUtil;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class add_employees_gross_salary extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:net_and_gross_salary';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();

            $employees = Employee::withWhereHas('employeeSalary')->get();
            // $convertNetToGrossService = new ConvertNetToGrossService();
            // $count = 0;
            foreach ($employees as $employee) {
                $salary = $employee->employeeSalary;
                $salary->gross_salary = $employee->employeeSalary->basic_salary;
                $salary->net_salary = $employee->employeeSalary->basic_salary;
                $salary->save();

                // $netSalary = $employee->employeeSalary->basic_salary;
                // if($netSalary <= 0)
                //     continue;

                // $socialInsuranceSalary = $employee->employeeSalary->social_insurance_salary ?? null;
                // $salary = $employee->employeeSalary;

                // try{
                //     $payrollPolicy = $this->getTaxInsurancePolicy($employee->id, $employee->company_id);
                //     $result = $convertNetToGrossService->calculate($netSalary, $socialInsuranceSalary, $payrollPolicy);
                // }
                // catch (\Exception $e) {
                //     if($e->getMessage() != 'Could not find the right gross salary')
                //         throw new \Exception($e->getMessage());
                //     echo "Could not find the right gross salary for employee: {$employee->id} with net salary {$netSalary} with social insurance salary  {$socialInsuranceSalary} with policy {$payrollPolicy}\n";
                //     $count += 1;
                // }
                // $salary->gross_salary = $result['grossSalary'];
                // $salary->net_salary = $netSalary;
                // $salary->save();
            }
            // echo "count of employees with no gross salary: $count\n";

            $allEmployeesSummary = EmployeePayrollSummary::all();
            foreach ($allEmployeesSummary as $employeeSummary) {
                $employeeSummary->gross_salary = $employeeSummary->basic_salary;
                $employeeSummary->final_net_salary = $employeeSummary->net_salary;
                $employeeSummary->net_salary = $employeeSummary->basic_salary;
                $employeeSummary->save();
            }
            DB::commit();
        } catch (\Exception $e) {
            if ($e->getMessage()) {
                DB::rollBack();
            }
            $this->error($e->getMessage());
        }
    }

    public function getTaxInsurancePolicy($employeeId, $companyId)
    {
        $payrollRepository = new PayrollsRepository;
        $payroll = $payrollRepository->getLastDraftedPayroll($companyId);
        if (! $payroll) {
            $payroll = $payrollRepository->getLastFinalizedPayroll($companyId);
            if (! $payroll) {
                return PayrollUtil::TAX_AND_SOCIAL_INSURANCE_POLICIES['CALCULATION_INCLUDED_DEDUCTED'];
            }
        }

        $employeePolicy = PayRollPolicy::where('payable_id', $employeeId)
            ->where('payable_type', 'employee')
            ->where('payroll_id', $payroll->id)
            ->first();
        $companyPolicy = PayRollPolicy::where('payable_id', $companyId)
            ->where('payable_type', 'company')
            ->where('payroll_id', $payroll->id)
            ->first();

        if (! $companyPolicy && ! $employeePolicy) {
            return PayrollUtil::TAX_AND_SOCIAL_INSURANCE_POLICIES['CALCULATION_INCLUDED_DEDUCTED'];
        }

        $isCompanyPolicyCustom = isset($companyPolicy->policy) && $companyPolicy->policy === PayrollUtil::TAX_AND_SOCIAL_INSURANCE_POLICIES['CUSTOM'];

        return ($isCompanyPolicyCustom && isset($employeePolicy?->policy)) ? $employeePolicy?->policy : $companyPolicy?->policy;

    }
}
