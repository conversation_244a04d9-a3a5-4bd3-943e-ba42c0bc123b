<?php

return [
    'employee_code' => 'Employee Code',
    'employee_name_ar' => 'Employee Name (Arabic)',
    'employee_name_en' => 'Employee Name (English)',
    'job_title' => 'Job Title',
    'department' => 'Department',
    'location' => 'Location',
    'primary_phone' => 'Primary Phone Number',

    'nationality' => 'Nationality',
    'national_id' => 'National ID',
    'passport_number' => 'Passport Number',
    'gender' => 'Gender',
    'place_of_birth' => 'Place of Birth',
    'address' => 'Address',
    'military_status' => 'Military Status',
    'date_of_birth' => 'Date of Birth',
    'religion' => 'Religion',
    'marital_status' => 'Marital Status',
    'number_of_children' => 'Number of Children',
    'years_of_experience' => 'Years of Experience',

    'primary_phone_country_code' => 'Primary Phone Country Code',
    'secondary_phone' => 'Secondary Phone Number',
    'secondary_phone_country_code' => 'Secondary Phone Country Code',
    'work_email' => 'Work Email',
    'personal_email' => 'Personal Email',
    'emergency_contact_details' => 'Emergency Contact Details',
    'emergency_contact_name' => 'Emergency Contact Name',
    'emergency_contact_relation' => 'Emergency Contact Relation',
    'emergency_contact_phone' => 'Emergency Contact Phone',
    'emergency_contact_phone_country_code' => 'Emergency Contact Phone Country Code',

    'degree_type' => 'Degree Type',
    'degree_name' => 'Degree Name',
    'institution_name' => 'Institution Name',
    'graduation_year' => 'Graduation Year',

    'hiring_date' => 'Hiring Date',
    'employment_type' => 'Employment Type',
    'latest_contract_start_date' => 'Latest Contract Start Date',
    'latest_contract_duration' => 'Latest Contract Duration',
    'latest_contract_end_date' => 'Latest Contract End Date',


    'essential' => 'Essential Fields',
    'basic' => 'Basic Details',
    'contact' => 'Contact Details',
    'education' => 'Education Details',
    'employment' => 'Employment Details',

    'male' => 'Male',
    'female' => 'Female',

    'completed_service' => 'Completed Service',
    'exempted' => 'Exempted',
    'postponed' => 'Postponed',
    'student' => 'Student',

    'muslim' => 'Muslim',
    'christian' => 'Christian',
    'other' => 'Other',

    'single' => 'Single',
    'married' => 'Married',
    'divorced' => 'Divorced',
    'widowed' => 'Widowed',

    'full_time' => 'Full Time',
    'part_time' => 'Part Time',
    'consultant' => 'Consultant',
    'out_sourced' => 'Out Sourced',

    'technical_diploma' => 'Technical Diploma',
    'high_school' => 'High School',
    'bachelor' => 'Bachelor',
    'masters' => 'Masters',
    'phd' => 'PhD',
    'diploma' => 'Diploma',

    'open' => 'Open',
    '12_months' => '12 Months',
    '6_months' => '6 Months',
    '3_months' => '3 Months',
    'custom' => 'Custom',

    'afghan' => 'Afghan',
    'albanian' => 'Albanian',
    'algerian' => 'Algerian',
    'american' => 'American',
    'andorran' => 'Andorran',
    'angolan' => 'Angolan',
    'antiguan_barbuda' => 'Antiguan and Barbudan',
    'argentine' => 'Argentine',
    'armenian' => 'Armenian',
    'australian' => 'Australian',
    'austrian' => 'Austrian',
    'azerbaijani' => 'Azerbaijani',
    'bahamian' => 'Bahamian',
    'bahraini' => 'Bahraini',
    'bangladeshi' => 'Bangladeshi',
    'barbadian' => 'Barbadian',
    'belarusian' => 'Belarusian',
    'belgian' => 'Belgian',
    'belizian' => 'Belizian',
    'beninese' => 'Beninese',
    'bhutanese' => 'Bhutanese',
    'bolivian' => 'Bolivian',
    'bosnian' => 'Bosnian',
    'botswanan' => 'Botswanan',
    'brazilian' => 'Brazilian',
    'british' => 'British',
    'bruneian' => 'Bruneian',
    'bulgarian' => 'Bulgarian',
    'burkinabe' => 'Burkinabe',
    'burundian' => 'Burundian',
    'cabo_verdean' => 'Cabo Verdean',
    'cambodian' => 'Cambodian',
    'cameroonian' => 'Cameroonian',
    'canadian' => 'Canadian',
    'central_african' => 'Central African',
    'chadian' => 'Chadian',
    'chilean' => 'Chilean',
    'chinese' => 'Chinese',
    'colombian' => 'Colombian',
    'comorian' => 'Comorian',
    'congolese' => 'Congolese',
    'costa_rican' => 'Costa Rican',
    'croatian' => 'Croatian',
    'cuban' => 'Cuban',
    'cypriot' => 'Cypriot',
    'czech' => 'Czech',
    'danish' => 'Danish',
    'djiboutian' => 'Djiboutian',
    'dominican' => 'Dominican',
    'dutch' => 'Dutch',
    'east_timorese' => 'East Timorese',
    'ecuadorean' => 'Ecuadorean',
    'egyptian' => 'Egyptian',
    'eritrean' => 'Eritrean',
    'estonian' => 'Estonian',
    'ethiopian' => 'Ethiopian',
    'fijian' => 'Fijian',
    'finnish' => 'Finnish',
    'french' => 'French',
    'gabonese' => 'Gabonese',
    'gambian' => 'Gambian',
    'georgian' => 'Georgian',
    'german' => 'German',
    'ghanaian' => 'Ghanaian',
    'greek' => 'Greek',
    'grenadian' => 'Grenadian',
    'guatemalan' => 'Guatemalan',
    'guinean' => 'Guinean',
    'guyanese' => 'Guyanese',
    'haitian' => 'Haitian',
    'honduran' => 'Honduran',
    'hungarian' => 'Hungarian',
    'icelander' => 'Icelander',
    'indian' => 'Indian',
    'indonesian' => 'Indonesian',
    'iranian' => 'Iranian',
    'iraqi' => 'Iraqi',
    'irish' => 'Irish',
    'israeli' => 'Israeli',
    'italian' => 'Italian',
    'jamaican' => 'Jamaican',
    'japanese' => 'Japanese',
    'jordanian' => 'Jordanian',
    'kazakh' => 'Kazakh',
    'kenyan' => 'Kenyan',
    'kiribati' => 'Kiribati',
    'korean' => 'Korean',
    'kuwaiti' => 'Kuwaiti',
    'kyrgyz' => 'Kyrgyz',
    'laotian' => 'Laotian',
    'latvian' => 'Latvian',
    'lebanese' => 'Lebanese',
    'lesothan' => 'Lesothan',
    'liberian' => 'Liberian',
    'libyan' => 'Libyan',
    'liechtenstein' => 'Liechtenstein',
    'lithuanian' => 'Lithuanian',
    'luxembourgish' => 'Luxembourgish',
    'macedonian' => 'Macedonian',
    'malagasy' => 'Malagasy',
    'malawian' => 'Malawian',
    'malaysian' => 'Malaysian',
    'maldivian' => 'Maldivian',
    'malian' => 'Malian',
    'malta' => 'Malta',
    'mauritian' => 'Mauritian',
    'mexican' => 'Mexican',
    'moldovan' => 'Moldovan',
    'mongolian' => 'Mongolian',
    'moroccan' => 'Moroccan',
    'mozambican' => 'Mozambican',
    'namibian' => 'Namibian',
    'nepalese' => 'Nepalese',
    'new_zealand' => 'New Zealand',
    'nigerian' => 'Nigerian',
    'north_korean' => 'North Korean',
    'norwegian' => 'Norwegian',
    'omani' => 'Omani',
    'pakistani' => 'Pakistani',
    'panamanian' => 'Panamanian',
    'peruvian' => 'Peruvian',
    'philippine' => 'Philippine',
    'polish' => 'Polish',
    'portuguese' => 'Portuguese',
    'qatari' => 'Qatari',
    'romanian' => 'Romanian',
    'russian' => 'Russian',
    'rwandan' => 'Rwandan',
    'saudi' => 'Saudi',
    'senegalise' => 'Senegalise',
    'singaporean' => 'Singaporean',
    'south_african' => 'South African',
    'south_korean' => 'South Korean',
    'spanish' => 'Spanish',
    'sri_lankan' => 'Sri Lankan',
    'swedish' => 'Swedish',
    'swiss' => 'Swiss',
    'syrian' => 'Syrian',
    'taiwanese' => 'Taiwanese',
    'tajik' => 'Tajik',
    'tanzanian' => 'Tanzanian',
    'thai' => 'Thai',
    'tunisian' => 'Tunisian',
    'turkish' => 'Turkish',
    'ukrainian' => 'Ukrainian',
    'uruguayan' => 'Uruguayan',
    'vietnamese' => 'Vietnamese',
    'yemeni' => 'Yemeni',
    'zimbabwean' => 'Zimbabwean',
];
