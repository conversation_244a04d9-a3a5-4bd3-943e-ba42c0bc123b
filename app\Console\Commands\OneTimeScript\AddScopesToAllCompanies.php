<?php

namespace App\Console\Commands\OneTimeScript;

use App\Services\V1\CompanySetup\FillDefaultScopesService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddScopesToAllCompanies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill:default:scopes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $fillDefaultScopesService = new FillDefaultScopesService;
            $fillDefaultScopesService->fill();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
