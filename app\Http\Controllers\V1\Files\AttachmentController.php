<?php

namespace App\Http\Controllers\V1\Files;

use App\Http\Controllers\NewController;
use App\Services\V1\Files\AttachmentService;
use App\Util\HttpStatusCodeUtil;

class AttachmentController extends NewController
{
    public function __construct(private AttachmentService $attachmentService) {}

    public function downloadAttachment(int $id)
    {

        $data = $this->attachmentService->downloadAttachment($id);

        return $this->response(['data' => $data],
            HttpStatusCodeUtil::OK, 'Company File Uploaded successfully');
    }
}
