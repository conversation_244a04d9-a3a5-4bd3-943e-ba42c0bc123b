<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class EmployeeInfo extends BaseModel
{
    use HasFactory;

    protected $guarded = ['created_at', 'updated_at', 'id'];

    protected $table = 'employees_info';

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function getOnProbationAttribute()
    {
        return Carbon::now()->diffInMonths($this->join_date) < $this->employee->title?->probation_period ? true : false;
    }
    public function getAgeAttribute()
    {
        return $this->birth_date ? Carbon::parse($this->birth_date)->age : null;
    }

    public function getLengthOfServiceAttribute()
    {
        if (!$this->join_date) {
            return null;
        }

        $diff = Carbon::parse($this->join_date)->diff(Carbon::now());
        return [
            'years' => $diff->y,
            'months' => $diff->m,
            'days' => $diff->d,
        ];
    }
}
