<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\ApprovalCycle;
use App\Models\AttendanceOvertime;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ReRouteOvertimesToEmployee extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reroute:overtimes {--company_id=company_id} {--branch_id=branch_id} {--employee_id=:employee_id(required)} {--start_date=start_date} {--end_date=end_date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add title working hours to titles table';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $companyId = $this->option('company_id');
        $branchId = $this->option('branch_id');
        $employeeId = $this->option('employee_id');
        $start_date = $this->option('start_date');
        $end_date = $this->option('end_date');

        if ($this->confirm("Do you want to reroute overtimes to employee id  $employeeId ?")) {
            DB::beginTransaction();
            try {
                $this->rerouteOvertimes($companyId, $branchId, $employeeId, $start_date, $end_date);
                DB::commit();
            } catch (Exception $e) {
                //// dd($e);
                \Sentry\captureException($e);
                Log::error($e);
                DB::rollBack();
            }
        } else {
            $this->info('Custom command aborted.');
        }
    }

    public function rerouteOvertimes($companyId, $branchId, $employeeId, $start_date, $end_date)
    {
        $overtimes = AttendanceOvertime::where('company_id', $companyId)
            ->where('status', 'pending')
            ->where('date', '>=', $start_date)
            ->where('date', '<=', $end_date)
            ->where(function ($q) use ($branchId) {
                $q->whereHas('employee', function ($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })
                    ->orWhere('branch_id', $branchId);
            })
            ->get();

        $newApprovalCycle = [
            'status' => 'pending',
            'status_date' => Carbon::now()->toDateString(),
            'employee_id' => $employeeId,
            'company_id' => $companyId,
            'order' => 1,
            'operator' => 'or',
        ];

        foreach ($overtimes as $overtime) {
            foreach ($overtime->approvalCycle as $cycle) { // this $overtime->approvalCycle()->delete() doesn't work with polymorphic rerlationship
                $cycle->delete();
            }
            $newApprovalCycle['approval_id'] = $overtime->id;
            $newApprovalCycle['approval_type'] = 'attendance_overtime';
            ApprovalCycle::create($newApprovalCycle);
        }
    }
}
