<?php

namespace App\Traits\V1;

use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use Illuminate\Support\Facades\Log;

trait EmployeeRequestsTrait
{
    public function isHiddenRequest($request)
    {
        return $request->is_hidden;
    }

    public function requestShouldBeHidden(&$request)
    {
        $request->update(['is_hidden' => $this->checkRequestIsCompleted($request)]);

        return $request->refresh();
    }

    public function checkRequestIsCompleted($request, $requester = null)
    {
        try {
            if ($requester && $requester->user) {
                return $requester->user->roles->contains('is_super_admin', 1) || $requester->user->hasPermissionTo('manage_all_requests', 'user-api');
            }
            if ($this->isAuthorizedToForceApprove()) {
                return true;
            }

            $requestWorkflowApprovals = $this->getRequestWorkflowApprovals($request);

            $orExists = count($requestWorkflowApprovals['or']) > 0;
            $thenExists = count($requestWorkflowApprovals['then']) > 0;
            if (($orExists && ! in_array(config('globals.REQUEST_STATUSES.PENDING'), $requestWorkflowApprovals['or'])) ||
            ($thenExists && in_array(config('globals.REQUEST_STATUSES.REJECTED'), $requestWorkflowApprovals['then'])) ||
            ($thenExists && in_array(config('globals.REQUEST_STATUSES.CANCELLED'), $requestWorkflowApprovals['then'])) ||
            ($thenExists && ! in_array(config('globals.REQUEST_STATUSES.PENDING'), $requestWorkflowApprovals['then']))) {
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Error in checkRequestIsCompleted: '.$e->getMessage());

            return false;
        }
    }

    public function updateRequest($request, $finalStatus = 'pending', $updatedData = [])
    {
        if (auth()->user() && $this->isAuthorizedToForceApprove()) {
            $updatedData = [...$updatedData, 'decider_admin_id' => config('globals.user')->employee_id, 'decider_admin_action' => $finalStatus];
        } elseif (! auth()->user() && $request->requestedBy && $request->requestedBy->user) {
            if ($request->requestedBy->user->roles->contains('is_super_admin', 1) || $request->requestedBy->user->hasPermissionTo('manage_all_requests', 'user-api')) {
                $updatedData = [...$updatedData, 'decider_admin_id' => $request->requestedBy->id, 'decider_admin_action' => $finalStatus];
            }
        }
        $request->update(['status' => $finalStatus, 'is_hidden' => true, ...$updatedData]);
    }

    public function updateEntity($entity, $finalStatus = 'pending', $updatedData = [])
    {
        Log::info('in updateEntity the entity is '.$entity->id.' and the final status is '.$finalStatus);
        $updatedData = array_merge(['updated_at' => now(), 'status' => $finalStatus], $updatedData);
        $entity->update($updatedData);
    }

    public function getFinalStatus($request, $actionType)
    {
        $requestStatuses = [
            'approve' => config('globals.REQUEST_STATUSES.APPROVED'),
            'reject' => config('globals.REQUEST_STATUSES.REJECTED'),
            'cancel' => config('globals.REQUEST_STATUSES.CANCELLED'),
        ];

        $request->refresh();
        if ($this->isAuthorizedToForceApprove()) {
            return $requestStatuses[$actionType];
        }

        $requestWorkflowApprovals = $this->getRequestWorkflowApprovals($request);
        Log::info('requestWorkflowApprovals is '.json_encode($requestWorkflowApprovals));
        if (in_array(config('globals.REQUEST_STATUSES.CANCELLED'), $requestWorkflowApprovals['then'])
        || in_array(config('globals.REQUEST_STATUSES.CANCELLED'), $requestWorkflowApprovals['or'])) {
            return config('globals.REQUEST_STATUSES.CANCELLED');
        } elseif (count($requestWorkflowApprovals['or']) > 0 && ! in_array('pending', $requestWorkflowApprovals['or'])) {
            return config('globals.REQUEST_STATUSES.' . strtoupper($requestWorkflowApprovals['or'][0]));
        } elseif (count($requestWorkflowApprovals['then']) > 0
        && in_array('rejected', $requestWorkflowApprovals['then'])) {
            return config('globals.REQUEST_STATUSES.REJECTED');
        } else {
            return config('globals.REQUEST_STATUSES.APPROVED');
        }
    }

    private function userInApprovalCycle($request)
    {
        $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle($request);
        $user = $approvalCycle->whereIn('role_id', auth()->user()->roles->pluck('id')->toArray())->first();

        return ! is_null($user);
    }

    private function getRequestWorkflowApprovals($request)
    {
        $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $data = [
            'requestable_id' => $request->requestable_id,
            'requestable_type' => $request->requestable_type,
        ];
        $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle($data);
        $requestWorkflowApprovals = [
            'then' => [],
            'or' => [],
        ];
        foreach ($approvalCycle as $approval) {
            if ($approval->operator == 'then') {
                $requestWorkflowApprovals['then'][] = $approval->status;
            } elseif ($approval->operator == 'or') {
                $requestWorkflowApprovals['or'][] = $approval->status;
            }
        }

        return $requestWorkflowApprovals;
    }
}
