<?php

namespace App\DomainData;

trait PreSetRolesDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'name' => 'required|string',
            'description' => 'string',
            'permissions' => 'json',
            'industry_id' => 'required|numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializePreSetRolesDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
