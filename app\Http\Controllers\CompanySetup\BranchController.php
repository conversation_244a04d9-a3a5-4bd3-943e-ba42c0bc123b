<?php

namespace App\Http\Controllers\CompanySetup;

use App\DomainData\BranchDto;
use App\DomainData\FilterDto;
use App\Http\Controllers\Controller;
use App\Services\CompanySetup\BusinessServices\GetBranchSetupService;
use App\Services\CompanySetup\BusinessServices\GetBranchTitlesService;
use App\Services\CompanySetup\CrudServices\BranchCrudService;

class BranchController extends Controller
{
    use BranchDto, FilterDto;

    public function __construct(
        private BranchCrudService $service,
        private GetBranchSetupService $getBranchSetupService,
        private GetBranchTitlesService $getBranchTitlesService
    ) {}

    public function createMany(array $request, \stdClass &$output): void
    {
        $validator = \Validator::make($request, [
            'entity_array' => 'required|array',
            'related_objects' => 'array',
            'related_objects.*' => 'in:area',
        ]);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        $rules = $this->getRules(['name', 'start_time', 'end_time', 'area_id']);

        foreach ($request['entity_array'] as $key => $entity) {
            $validator = \Validator::make($entity, $rules);
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }
        }

        $this->service->createMany($request, $output);

    }

    public function create(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['name', 'start_time', 'end_time', 'area_id']);
        $rules['related_objects'] = ['array'];
        $rules['related_objects.*'] = ['in:area'];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->create($request, $output);

    }

    public function update(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(
            ['name',
                'location',
                'address',
                'gps_address',
                'work_days',
                'work_days.*',
                'start_time',
                'end_time',
                'number_shifts',
                'hours_per_shift',
                'number_of_workers',
                'max_hours_week',
                'radius',
                'setup_counter',
                'employee_id',
                'area_id']);
        $rules['id'] = 'required|numeric';
        $rules['related_objects'] = ['array'];
        $rules['related_objects.*'] = ['in:area,employee'];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->update($request, $output);
    }

    public function getByFilter(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules(['page', 'filters', 'related_objects.*', 'related_objects_count.*',  'page_size']);

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['related_objects'])) {
            $request['related_objects'] = [];
        }
        if (! isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $this->service->getByFilter($request, $output);
    }

    public function getById(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules(['related_objects.*', 'related_objects_count.*']);
        $rules['id'] = 'required|numeric';
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['related_objects'])) {
            $request['related_objects'] = [];
        }
        if (! isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $this->service->getById($request, $output);
    }

    public function delete(array $request, \stdClass &$output): void
    {
        $rules['ids'] = ['required', 'array'];
        $rules['ids.*'] = ['required', 'numeric'];
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $this->failMessages($validator->messages());

            return;
        }

        $request = $validator->validate();

        $this->service->delete($request, $output);
    }

    public function getBranchSetup(array $request, \stdClass &$output): void
    {
        $request['company_id'] = config('globals.company')->id;
        $request['is_admin'] = config('globals.user')->is_admin;
        $request['employee_id'] = config('globals.user')->employee_id;
        $this->getBranchSetupService->perform($request, $output);
    }

    public function getBranchTitleShifts(array $request, \stdClass &$output): void
    {
        $rules['branch_id'] = 'required|numeric';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->getBranchTitlesService->perform($request, $output);
    }

    public function getEmployeeBranches(array $request, \stdClass &$output): void
    {
        $this->service->getBranchesByEmployee($request, $output);
    }

    public function getCompanyBranches(array $request, \stdClass &$output): void
    {
        $this->service->getCompanyBranches($output);
    }
}
