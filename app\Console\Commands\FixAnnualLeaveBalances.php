<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\EmployeeLeaveBalance;
use App\Models\EmployeeLeaveRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class FixAnnualLeaveBalances extends Command
{
    protected $signature = <<<'SIGN'
leaves:recalculate-annual
    {company_id : The ID of the company}
    {--chunk=500 : Employees processed per batch}
    {--year= : Calendar year to recalculate (YYYY, defaults to current)}
    {--dry-run : Compute balances but do not persist}
SIGN;

    protected $description = 'Recompute annual-leave balances for a given year, subtracting all approved annual-leave requests in that same year.';

    public function handle(): int
    {
        $company = Company::find($this->argument('company_id'));

        if (! $company) {
            $this->error('Company not found.');

            return self::FAILURE;
        }

        $annualLeaveTypeId = $company->annual_leave_id;
        if (! $annualLeaveTypeId) {
            $this->warn('Company has no annual-leave type configured. Nothing to do.');

            return self::SUCCESS;
        }

        $chunkSize = (int) $this->option('chunk');
        $year = (int) ($this->option('year') ?: now()->year);
        $isDryRun = $this->option('dry-run');
        $updatedRows = 0;

        $this->info("Recalculating {$year} annual-leave balances for company #{$company->id} (chunk = {$chunkSize})");
        if ($isDryRun) {
            $this->comment('Running in dry-run mode – no DB writes will occur.');
        }

        // Stream employee IDs in memory-friendly chunks
        $company->employees()
            ->select('id')
            ->orderBy('id')
            ->chunk($chunkSize, function (Collection $employees) use ($annualLeaveTypeId, $year, $isDryRun, &$updatedRows) {
                $ids = $employees->pluck('id');

                // Sum of approved leave taken in the target year
                $approvedByEmployee = EmployeeLeaveRequest::query()
                    ->select('employee_id', DB::raw('SUM(net_quantity) as taken'))
                    ->whereIn('employee_id', $ids)
                    ->where('company_leave_type_id', $annualLeaveTypeId)
                    ->where('status', 'approved')
                    ->whereYear('from', $year)   // ← adjust to your date column
                    ->whereYear('to', $year)   // ← adjust to your date column
                    ->groupBy('employee_id')
                    ->pluck('taken', 'employee_id');   // [employee_id => taken]

                // Fetch balances for that same year
                $balances = EmployeeLeaveBalance::query()
                    ->where('company_leave_type_id', $annualLeaveTypeId)
                    ->whereIn('employee_id', $ids)
                    ->whereYear('end', $year)             // ← assumes a `year` column in balances
                    ->get()
                    ->keyBy('employee_id');

                DB::transaction(function () use ($balances, $approvedByEmployee, $isDryRun, &$updatedRows) {

                    foreach ($balances as $empId => $balance) {
                        $old = $balance->balance;
                        $taken = $approvedByEmployee[$empId] ?? 0;
                        $new = max(0, $old - $taken);  // never negative

                        if (! $isDryRun) {
                            $balance->update(['balance' => $new]);
                        }

                        $this->line(sprintf(
                            'Employee %d — old: %.2f, taken: %.2f, new: %.2f%s',
                            $empId,
                            $old,
                            $taken,
                            $new,
                            $isDryRun ? ' (simulated)' : ''
                        ));
                        $updatedRows++;
                    }
                });
            });

        $this->info(($isDryRun ? 'Simulated' : 'Updated')." {$updatedRows} balances for {$year}.");

        return self::SUCCESS;
    }
}
