<?php

namespace App\Http\Controllers\CompanySetup;

use App\DomainData\EmployeeImageDto;
use App\DomainData\FilterDto;
use App\Http\Controllers\Controller;
use App\Services\CompanySetup\BusinessServices\EmployeeImageUploadService;
use App\Services\CompanySetup\CrudServices\EmployeeImageCrudService;

class EmployeeImageController extends Controller
{
    use EmployeeImageDto, FilterDto;

    public function __construct(
        private EmployeeImageCrudService $service,
        private EmployeeImageUploadService $employeeImageUploadService
    ) {}

    public function upload(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['type']);
        $rules['image'] = ['required', 'mimes:jpeg,bmp,jpg,png'];
        $rules['id'] = ['nullable', 'numeric'];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->employeeImageUploadService->perform($request, $output);
    }

    public function get(array $request, \stdClass &$output): void
    {
        $rules['type'] = 'string|in:LEFT,FRONT,RIGHT';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['related_objects'])) {
            $request['related_objects'] = [];
        }
        if (! isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $employeeId = config('globals.user')->employee_id;
        if (is_null($employeeId)) {
            return;
        }

        $request['page'] = 1;
        $request['page_size'] = 3;
        $request['filters'] = [
            'employee_images' => [
                'employee_id' => $employeeId,
                'operator' => 'and',
            ],
        ];

        if (isset($request['type'])) {
            $request['filters']['employee_images']['type'] = $request['type'];
        }

        $this->service->getByFilter($request, $output);
    }
}
