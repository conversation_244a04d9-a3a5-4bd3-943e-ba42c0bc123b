<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Employee;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixDemoCompanyIssues extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-demo-company-issues';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Fixing demo company issues...');
            DB::beginTransaction();

            Employee::where('company_id', 216)->update(['enable_impersonation' => 1]);

            $this->info('Demo company issues fixed successfully');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Failed to fix demo company issues: '.$e->getMessage());
        }
    }
}
