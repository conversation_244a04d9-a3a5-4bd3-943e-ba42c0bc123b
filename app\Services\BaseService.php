<?php

namespace App\Services;

use App\Repositories\BaseRepository;
use Closure;
use Illuminate\Contracts\Queue\EntityNotFoundException;
use Throwable;

abstract class BaseService
{
    protected BaseRepository $repository;

    protected string $entity;

    /**
     * Create a new controller instance.
     */
    public function __construct(BaseRepository $baseRepository)
    {
        $this->repository = $baseRepository;
    }

    /**
     * @return mixed
     *
     * @throws EntityNotFoundException
     */
    public function find($id, array $in = [])
    {
        return $this->findOrFail($id, $in);
    }

    public function all(): mixed
    {
        return $this->repository->all();
    }

    public function findByKey($key, $value)
    {
        return $this->repository->findByKey($key, $value);
    }

    public function findByKeys(array $attributes)
    {
        return $this->repository->findByKeys($attributes);
    }

    public function findFirstByKeys(array $attributes)
    {
        return $this->repository->findFirstByKeys($attributes);
    }

    /**
     * @param  mixed  $data
     * @return mixed
     *
     * @throws Throwable
     */
    public function add($data)
    {
        return $this->repository->add($data);
    }

    /**
     * @return mixed
     *
     * @throws Throwable
     */
    public function firstOrCreate(array $search, array $create = [])
    {
        return $this->repository->firstOrCreate($search, $create);
    }

    /**
     * @return mixed
     *
     * @throws Throwable
     */
    public function insert(array $data)
    {
        return $this->repository->insert($data);
    }

    /**
     * @return mixed
     *
     * @throws EntityNotFoundException|Throwable
     */
    public function update(int $id, array $data)
    {
        $this->findOrFail($id);

        return $this->repository->update($id, $data);
    }

    /**
     * @return mixed
     *
     * @throws EntityNotFoundException
     * @throws Throwable
     */
    public function updateWithoutFilter(int $id, array $data)
    {
        $this->findOrFail($id);

        return $this->repository->updateWithoutFilter($id, $data);
    }

    /**
     * @return mixed
     *
     * @throws Throwable
     */
    public function delete(array $ids)
    {
        return $this->repository->delete($ids);
    }

    /**
     * @return mixed
     *
     * @throws EntityNotFoundException
     */
    public function findOrFail($id, array $in = [])
    {
        if (count($in) && ! in_array($id, $in)) {
            throw new EntityNotFoundException('', $id);
        }

        if (! $entity = $this->repository->find($id)) {
            throw new EntityNotFoundException('', $id);
        }

        return $entity;
    }

    /**
     * @param  mixed|int  $attempts
     * @return mixed
     *
     * @throws Throwable
     */
    public function applyInTransaction(Closure $callback, int $attempts = 1)
    {
        $connection = $this->repository->getDBConnection();

        return $connection->transaction($callback, $attempts);
    }

    /**
     * @return mixed
     */
    public function updateOrCreate(array $attributes, array $values = [])
    {
        return $this->repository->updateOrCreate($attributes, $values);
    }

    /**
     * @return array
     */
    public function updateOrCreateMany(array $attributes, array $values = [])
    {
        return $this->repository->updateOrCreateMany($attributes, $values);
    }

    public function getFirstByAttributes(array $attributes)
    {
        return $this->repository->getFirstByAttributes($attributes);
    }

    public function createMany(array $records): array
    {
        return $this->repository->createMany($records);
    }

    public function findFirstByKey($key, $value, $relations = [])
    {
        return $this->repository->findFirstByKey($key, $value, $relations);
    }
    
    public function insertMany(array $records): bool
    {
        if (empty($records)) {
            return true;
        }

        $now = now();
        foreach ($records as &$record) {
            $record['created_at'] = $now;
            $record['updated_at'] = $now;
            $record['deleted_at'] = null;
        }

        return $this->repository->insert($records);
    }
}
