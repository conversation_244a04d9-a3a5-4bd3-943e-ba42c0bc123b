<?php

namespace Tests\Feature;

use Tests\DatabaseSetupTrait;
use Tests\TestCase;
use Illuminate\Support\Facades\DB;

class SampleDatabaseTest extends TestCase
{
    use DatabaseSetupTrait;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Setup the database for testing (disables foreign key checks)
        $this->setupDatabase();
    }

    public function testDatabaseConnection(): void
    {
        // This test verifies that we can connect to the database
        $this->assertTrue(DB::connection()->getPdo() !== null);
        
        // Create some test data
        // DB::table('your_table')->insert([...]);
        
        // The cleanupDatabase() method will be called in tearDown()
        // to clean up any data created during this test
    }
    
    public function testCreateAndCleanupData(): void
    {
        // Example of creating test data
        $testTableName = 'users'; // Replace with a table that exists in your database
        
        // Get current count
        $initialCount = DB::table($testTableName)->count();
        
        // Insert a test record if the table exists
        try {
            DB::table($testTableName)->insert([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                // Add other required fields as needed
            ]);
            
            // Verify the record was inserted
            $afterInsertCount = DB::table($testTableName)->count();
            $this->assertEquals($initialCount + 1, $afterInsertCount);
            
            // The cleanupDatabase() method will be called in tearDown()
            // to clean up any data created during this test
        } catch (\Exception $e) {
            // If table doesn't exist, just pass the test
            $this->assertTrue(true, "Table {$testTableName} doesn't exist or couldn't be modified");
        }
    }
    
    public function testRunMigrations(): void
    {
        // Uncommenting this will run migrations and reset the database
        // Only do this if you want a fresh database for your tests
        // $this->runMigrations();
        
        $this->assertTrue(true);
    }
} 