name: Deployment

on:
  push:
    branches: [ "staging_trunk" ]

jobs:
  tests:
    name: Run tests
    runs-on: ubuntu-latest
    steps:
        - name: Setup PHP with PECL extension
          uses: shivammathur/setup-php@v2
          with:
            php-version: "8.2"  
        - uses: actions/checkout@v2
        - name: Copy .env
          run: cp .env.example .env
        # - name: Install Dependencies
        #   run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress
        # - name: Directory Permissions
        #   run: chmod -R 777 storage bootstrap/cache
        # - name: Wait for MySQL to be ready
        #   run: |
        #     until mysqladmin ping -h"${{ secrets.DEV_DB_HOST }}" --silent; do
        #       echo 'waiting for mysql to be available...'
        #       sleep 1
        #     done

        # - name: Create JWT Secret key
        #   env:
        #     DB_CONNECTION: mysql
        #     DB_HOST: ${{ secrets.DEV_DB_HOST }}
        #     DB_PORT: 3306
        #     DB_DATABASE: bluworks_qa
        #     DB_USERNAME: bluworks_stg
        #     DB_PASSWORD: ${{ secrets.MYSQL_DEV_ROOT_PASSWORD }}
        #     CACHE_DRIVER: array
        #     SESSION_DRIVER: array
        #     QUEUE_DRIVER: sync
        #   run: php artisan jwt:secret

        # - name: Create Application key
        #   env:
        #     DB_CONNECTION: mysql
        #     DB_HOST: ${{ secrets.DEV_DB_HOST }}
        #     DB_PORT: 3306
        #     DB_DATABASE: bluworks_qa
        #     DB_USERNAME: bluworks_stg
        #     DB_PASSWORD: ${{ secrets.MYSQL_DEV_ROOT_PASSWORD }}
        #     CACHE_DRIVER: array
        #     SESSION_DRIVER: array
        #     QUEUE_DRIVER: sync
        #   run: php artisan key:generate

        #   #      - name: Run migrations
        #   #        env:
        #   #          DB_CONNECTION: mysql
        #   #          DB_HOST: ${{ secrets.DEV_DB_HOST }}
        #   #          DB_PORT: 3306
        #   #          DB_DATABASE: bluworks_qa
        #   #          DB_USERNAME: bluworks_stg
        #   #          DB_PASSWORD: ${{ secrets.MYSQL_DEV_ROOT_PASSWORD }}
        #   #          CACHE_DRIVER: array
        #   #          SESSION_DRIVER: array
        #   #          QUEUE_DRIVER: sync
        #   #        run: php artisan migrate:fresh --path=database/migrations

        # - name: Execute tests (Unit and Feature tests) via PHPUnit
        #   env:
        #     DB_CONNECTION: mysql
        #     DB_HOST: ${{ secrets.DEV_DB_HOST }}
        #     DB_PORT: 3306
        #     DB_DATABASE: bluworks_qa
        #     DB_USERNAME: bluworks_stg
        #     DB_PASSWORD: ${{ secrets.MYSQL_DEV_ROOT_PASSWORD }}
        #     CACHE_DRIVER: array
        #     SESSION_DRIVER: array
        #     QUEUE_DRIVER: sync
        #   run: php artisan test  tests/Feature/V1/Running/Loans
      
  staging-deployment:
    needs: tests

    runs-on: ubuntu-latest

    steps:
    - name: Set output
      id: vars
      run: echo ::set-output name=short_ref::${GITHUB_REF#refs/*/}

    - name: Check output
      run: echo ${{ steps.vars.outputs.short_ref }}
      
    - name: Install SSH Key
      run: |
        install -m 600 -D /dev/null ~/.ssh/id_rsa
        echo "${{ secrets.STG_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.STG_SSH_HOST }} > ~/.ssh/known_hosts
    - name: ssh
      run: ssh bluworks@${{ secrets.STG_SSH_HOST }} "cd ${{secrets.STG_DIR}} && ${{vars.STAGING_DEPLOYMENT_COMMANDS}} && exit"



    - name: delete current branch if exists
      continue-on-error: true
      run: ssh bluworks@${{ secrets.STG_SSH_HOST }} "cd ${{secrets.STG_DIR}} && git checkout development && git branch -D ${{ steps.vars.outputs.short_ref }}"

    - name: checkout new branch
      run: ssh bluworks@${{ secrets.STG_SSH_HOST }} "cd ${{secrets.STG_DIR}} && echo updateing && git remote update && echo fetching 1 && git fetch origin && echo fetching &&git fetch  --all && echo checkingout && git checkout --track origin/${{ steps.vars.outputs.short_ref }} && git pull && composer install --no-dev --no-interaction --no-progress --optimize-autoloader --classmap-authoritative &&  php artisan route:cache && php artisan view:cache && php artisan event:cache && php artisan migrate --force && php artisan queue:restart && exit"
