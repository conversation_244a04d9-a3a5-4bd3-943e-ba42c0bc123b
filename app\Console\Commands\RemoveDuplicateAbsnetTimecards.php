<?php

namespace App\Console\Commands;

use App\Models\Timecard;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RemoveDuplicateAbsnetTimecards extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-duplicate-absent-timecards';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove duplicate absent timecards (same employee_id, from, to) by clearing tags and deleting extras';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Searching for duplicate absent timecards...');

        DB::transaction(function () {
            // Find groups of absent timecards for active employees having duplicates
            $duplicates = Timecard::whereHas('entityTags', function ($q) {
                $q->where('tag', 'absent');
            })
                ->whereHas('employee', function ($q) {
                    $q->where('status', '!=', 'terminated');
                })
                ->select(['employee_id', 'from', 'to', DB::raw('COUNT(*) as cnt')])
                ->whereDate('from', '>=', '2025-03-01')
                ->groupBy('employee_id', 'from', 'to')
                ->having('cnt', '>', 1)
                ->get();

            foreach ($duplicates as $dup) {
                $this->info("Found {$dup->cnt} duplicates for employee {$dup->employee_id} ({$dup->from} → {$dup->to})");

                // Retrieve all matching timecards, ordered by creation
                $timecards = Timecard::where('employee_id', $dup->employee_id)
                    ->where('from', $dup->from)
                    ->where('to', $dup->to)
                    ->orderBy('id')
                    ->get();

                // Keep the first (original) timecard
                $timecards->shift();

                foreach ($timecards as $duplicate) {
                    // Delete associated entityTags first
                    $removed = $duplicate->entityTags()->delete();
                    $this->info("  • Removed {$removed} 'absent' tag(s) from timecard #{$duplicate->id}");

                    // Then delete the duplicate timecard record
                    $duplicate->delete();
                    $this->info("  ✓ Deleted duplicate timecard #{$duplicate->id}");
                }
            }
        });

        $this->info('Duplicate cleanup complete.');

        return 0;
    }
}
