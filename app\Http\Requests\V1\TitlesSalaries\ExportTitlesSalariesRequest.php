<?php

namespace App\Http\Requests\V1\TitlesSalaries;

use Illuminate\Foundation\Http\FormRequest;

class ExportTitlesSalariesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {

        return [
            'department_ids' => [
                'array',
            ],
            'department_ids.*' => [
                'integer',
            ],
            'title_ids' => [
                'array',
            ],
            'title_ids.*' => [
                'integer',
            ],
            'search_value' => [
                'string',
            ],
        ];
    }
}
