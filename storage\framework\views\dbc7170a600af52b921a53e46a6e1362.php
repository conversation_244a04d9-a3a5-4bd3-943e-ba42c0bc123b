<!DOCTYPE html>
<html dir="<?php echo e($direction ?? 'ltr'); ?>" lang="<?php echo e($language ?? 'en'); ?>" class="<?php echo e($direction == 'rtl' ? 'rtl' : 'ltr'); ?>">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo e(shapeArabic(__('salary_components.payslip'))); ?> -
        <?php echo e(isset($employee['name']) ? shapeArabic($employee['name']) : ''); ?>

    </title>
    <!-- Arabic Font CDN Injection -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap"
        rel="stylesheet">

    <style>
        /* Arabic Font Injection via CDN */
        @import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');

        /* Modern CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            direction: rtl;
            unicode-bidi: bidi-override;
            box-sizing: border-box;
        }

        body {
            font-family: 'Amiri', 'DejaVu Sans', sans-serif;
            direction:
                <?php echo e($direction ?? 'ltr'); ?>

            ;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background: #ffffff;
        }

        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background: #ffffff;
        }

        /* Header Section - Row One: Company Name and Blueworks Logo */
        .header-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px 0;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .blueworks-logo {
            font-size: 24px;
            font-weight: bold;
            color: #4285f4;
        }

        /* Line Break */
        .line-break {
            height: 1px;
            background-color: #ddd;
            margin: 20px 0;
            border: none;
        }

        /* Section One: Employee Info */
        .section-one {
            margin-bottom: 20px;
        }

        .payslip-month {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .employee-name {
            font-size: 16px;
            margin-bottom: 15px;
        }

        .employee-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .employee-code {
            font-size: 14px;
        }

        .employee-title {
            font-size: 14px;
        }

        /* Section Two: Categories (Iterative) */
        .section-two {
            margin-bottom: 30px;
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .category-name {
            font-weight: bold;
            font-size: 16px;
        }

        .amount-keyword {
            font-weight: 600;
            font-size: 16px;
        }

        .dashed-line {
            border-top: 1px dashed #ccc;
            margin: 10px 0;
            height: 1px;
        }

        .component-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .component-name {
            font-weight: bold;
        }

        .component-amount {
            font-weight: normal;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(128, 128, 128, 0.6);
            padding: 10px 15px;
            margin-top: 10px;
            font-weight: bold;
        }

        /* Section Three: Taxes & Social Insurance */
        .section-three {
            margin-bottom: 30px;
        }

        .section-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .section-title-text {
            font-weight: bold;
            font-size: 16px;
        }

        .tax-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .tax-label {
            font-weight: normal;
        }

        .tax-amount {
            font-weight: normal;
        }

        /* Section Four: Net Pay */
        .section-four {
            margin-bottom: 30px;
        }

        .net-pay-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(128, 128, 128, 0.3);
            padding: 15px;
            font-weight: bold;
            font-size: 18px;
        }

        /* Section Five: Employee Signature */
        .section-five {
            margin-top: 40px;
        }

        .signature-row {
            display: flex;
            justify-content: flex-end;
            align-items: flex-start;
        }

        .signature-container {
            text-align: right;
        }

        .signature-label {
            margin-bottom: 10px;
            font-weight: normal;
        }

        .signature-box {
            border: 1px solid #333;
            width: 200px;
            height: 60px;
            background: transparent;
        }

        /* Arabic Text Support */
        .arabic-text {
            font-family: 'Amiri', 'DejaVu Sans', sans-serif;
            direction: rtl;
            unicode-bidi: bidi-override;
            text-align: right;
            font-weight: 500;
        }

        .normal-text {
            font-family: 'DejaVu Sans', sans-serif;
            direction: ltr;
        }

        .number {
            font-family: 'DejaVu Sans', sans-serif !important;
            direction: ltr !important;
            font-weight: 600;
        }

        /* RTL Layout Adjustments */
        .rtl .header-row {
            flex-direction: row-reverse;
        }

        .rtl .employee-details {
            flex-direction: row-reverse;
        }

        .rtl .category-header {
            flex-direction: row-reverse;
        }

        .rtl .component-row {
            flex-direction: row-reverse;
        }

        .rtl .total-row {
            flex-direction: row-reverse;
        }

        .rtl .section-title {
            flex-direction: row-reverse;
        }

        .rtl .tax-row {
            flex-direction: row-reverse;
        }

        .rtl .net-pay-row {
            flex-direction: row-reverse;
        }

        .rtl .signature-row {
            justify-content: flex-start;
        }

        .rtl .signature-container {
            text-align: left;
        }

        /* Page Break */
        .new-page {
            page-break-after: always;
        }
    </style>
</head>

<body>
    <?php
        // Global translation variables with Arabic reshaping
        $amountText = __('salary_components.amount');
        if (containsArabic($amountText)) {
            $amountText = shapeArabic($amountText);
        }

        $currencyText = __('salary_components.currency');
        if (containsArabic($currencyText)) {
            $currencyText = shapeArabic($currencyText);
        }

        $totalText = __('salary_components.total');
        if (containsArabic($totalText)) {
            $totalText = shapeArabic($totalText);
        }

        // Determine font class based on language
        $fontClass = $language == 'ar' ? 'arabic-text' : 'normal-text';
        $isRTL = $direction == 'rtl';
    ?>

    <div class="container">
        
        <div class="header-row">
            <div class="company-name">
                <?php
                    $companyName = $company_name ?? 'Company Name';
                    $companyNameDisplay = containsArabic($companyName) ? shapeArabic($companyName) : $companyName;
                ?>
                <span class="<?php echo e($fontClass); ?>">
                    <?php echo e($companyNameDisplay); ?>

                </span>
            </div>
            <div class="blueworks-logo">blueworks.</div>
        </div>

        
        <hr class="line-break">

        
        <div class="section-one">
            
            <div class="payslip-month">
                <?php
                    $payslipForText = __('salary_components.payslip_for');
                    if (containsArabic($payslipForText)) {
                        $payslipForText = shapeArabic($payslipForText);
                    }
                ?>
                <span class="<?php echo e($fontClass); ?>">
                    <?php echo e($payslipForText); ?>

                </span>
                <span class="normal-text"><?php echo e(containsArabic($month ?? '') ? shapeArabic($month ?? '') : $month ?? ''); ?>

                    <?php echo e($year ?? ''); ?></span>
            </div>

            
            <div class="employee-name">
                <?php
                    $employeeNameText = __('salary_components.employee_name');
                    if (containsArabic($employeeNameText)) {
                        $employeeNameText = shapeArabic($employeeNameText);
                    }

                    $employeeName = $employee['name'] ?? '';
                    if (containsArabic($employeeName)) {
                        $employeeName = shapeArabic($employeeName);
                    }
                ?>
                <span class="<?php echo e($fontClass); ?>">
                    <?php echo e($employeeNameText); ?>:
                </span>
                <span class="<?php echo e($fontClass); ?>">
                    <?php echo e($employeeName); ?>

                </span>
            </div>

            
            <div class="employee-details">
                <div class="employee-code">
                    <?php
                        $codeText = __('salary_components.employee_code');
                        if (containsArabic($codeText)) {
                            $codeText = shapeArabic($codeText);
                        }
                    ?>
                    <span class="<?php echo e($fontClass); ?>">
                        <?php echo e($codeText); ?>:
                    </span>
                    <span class="number">#<?php echo e($employee['code'] ?? ''); ?></span>
                </div>
                <div class="employee-title">
                    <?php
                        $titleText = __('salary_components.job_title');
                        if (containsArabic($titleText)) {
                            $titleText = shapeArabic($titleText);
                        }

                        $employeeTitle = $employee['title'] ?? '';
                        if (containsArabic($employeeTitle)) {
                            $employeeTitle = shapeArabic($employeeTitle);
                        }
                    ?>
                    <span class="<?php echo e($fontClass); ?>">
                        <?php echo e($titleText); ?>:
                    </span>
                    <span class="<?php echo e($fontClass); ?>">
                        <?php echo e($employeeTitle); ?>

                    </span>
                </div>
            </div>
        </div>

        
        <hr class="line-break">

        
        <?php if(isset($categories) && is_array($categories)): ?>
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category_name => $components): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="section-two">
                    
                    <div class="category-header">
                        <div class="category-name">
                            <?php
                                $categoryKey = 'salary_components.' . trim($category_name) ?? '';
                                $translatedCategoryName = __($categoryKey) ?: $category_name;
                                if (containsArabic($translatedCategoryName)) {
                                    $translatedCategoryName = shapeArabic($translatedCategoryName);
                                }
                            ?>
                            <span class="<?php echo e($fontClass); ?>">
                                <?php echo e($translatedCategoryName); ?>

                            </span>
                        </div>
                        <div class="amount-keyword">
                            <span class="<?php echo e($fontClass); ?>">
                                <?php echo e($amountText); ?>

                            </span>
                        </div>
                    </div>

                    
                    <div class="dashed-line"></div>

                    
                    <?php $categoryTotal = 0; ?>
                    <?php if(is_array($components)): ?>
                        <?php $__currentLoopData = $components; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $component): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $componentKey = 'salary_components.' . $component['name'] ?? '';
                                $translatedComponentName = __($componentKey) ?: ($component['name'] ?? '');
                                if (containsArabic($translatedComponentName)) {
                                    $translatedComponentName = shapeArabic($translatedComponentName);
                                }
                                $amount = floatval(str_replace(',', '', $component['amount'] ?? '0'));
                                $categoryTotal += $amount;
                            ?>
                            <div class="component-row">
                                <div class="component-name">
                                    <span class="<?php echo e($fontClass); ?>">
                                        <?php echo e($translatedComponentName); ?>

                                    </span>
                                </div>
                                <div class="component-amount">
                                    <span class="number">
                                        <?php echo e($component['amount'] ?? '0'); ?>

                                        <span class="<?php echo e($fontClass); ?>">
                                            <?php echo e($currencyText); ?>

                                        </span>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>

                    
                    <div class="total-row">
                        <div>
                            <span class="<?php echo e($fontClass); ?>">
                                <?php echo e($totalText); ?> <?php echo e($translatedCategoryName); ?>

                            </span>
                        </div>
                        <div>
                            <span class="number">
                                <?php echo e(number_format($categoryTotal, 2)); ?>

                                <span class="<?php echo e($fontClass); ?>">
                                    <?php echo e($currencyText); ?>

                                </span>
                            </span>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>

        
        <div class="new-page"></div>

        
        <div class="section-three">
            
            <div class="section-title">
                <div class="section-title-text">
                    <?php
                        $taxInsuranceText = __('salary_components.taxes_social_insurance');
                        if (containsArabic($taxInsuranceText)) {
                            $taxInsuranceText = shapeArabic($taxInsuranceText);
                        }
                    ?>
                    <span class="<?php echo e($fontClass); ?>">
                        <?php echo e($taxInsuranceText); ?>

                    </span>
                </div>
                <div class="amount-keyword">
                    <span class="<?php echo e($fontClass); ?>">
                        <?php echo e($amountText); ?>

                    </span>
                </div>
            </div>

            
            <div class="tax-row">
                <div class="tax-label">
                    <?php
                        $taxesText = __('salary_components.taxes');
                        if (containsArabic($taxesText)) {
                            $taxesText = shapeArabic($taxesText);
                        }
                    ?>
                    <span class="<?php echo e($fontClass); ?>">
                        <?php echo e($taxesText); ?>

                    </span>
                </div>
                <div class="tax-amount">
                    <span class="number">
                        -<?php echo e($tax_amount ?? '0'); ?>

                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($currencyText); ?>

                        </span>
                    </span>
                </div>
            </div>

            
            <div class="tax-row">
                <div class="tax-label">
                    <?php
                        $socialInsuranceText = __('salary_components.social_insurance');
                        if (containsArabic($socialInsuranceText)) {
                            $socialInsuranceText = shapeArabic($socialInsuranceText);
                        }
                    ?>
                    <span class="<?php echo e($fontClass); ?>">
                        <?php echo e($socialInsuranceText); ?>

                    </span>
                </div>
                <div class="tax-amount">
                    <span class="number">
                        -<?php echo e($insurance_amount ?? '0'); ?>

                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($currencyText); ?>

                        </span>
                    </span>
                </div>
            </div>

            
            <div class="total-row">
                <div>
                    <?php
                        $totalTaxInsurance = floatval(str_replace(',', '', $tax_amount ?? '0')) + floatval(str_replace(',', '', $insurance_amount ?? '0'));
                    ?>
                    <span class="<?php echo e($fontClass); ?>">
                        <?php echo e($totalText); ?>

                    </span>
                </div>
                <div>
                    <span class="number">
                        <?php echo e(number_format($totalTaxInsurance, 2)); ?>

                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($currencyText); ?>

                        </span>
                    </span>
                </div>
            </div>
        </div>

        
        <div class="section-four">
            
            <div class="net-pay-row">
                <div>
                    <?php
                        $netPayText = __('salary_components.net_pay');
                        if (containsArabic($netPayText)) {
                            $netPayText = shapeArabic($netPayText);
                        }
                    ?>
                    <span class="<?php echo e($fontClass); ?>">
                        <?php echo e($netPayText); ?>

                    </span>
                </div>
                <div>
                    <span class="number">
                        <?php echo e($employee['net_salary'] ?? '0'); ?>

                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($currencyText); ?>

                        </span>
                    </span>
                </div>
            </div>
        </div>

        
        <div class="section-five">
            
            <div class="signature-row">
                <div class="signature-container">
                    <div class="signature-label">
                        <?php
                            $signatureText = __('salary_components.employee_signature');
                            if (containsArabic($signatureText)) {
                                $signatureText = shapeArabic($signatureText);
                            }
                        ?>
                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($signatureText); ?>:
                        </span>
                    </div>
                    <div class="signature-box"></div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
<?php /**PATH C:\Herd\www\API\resources\views/admin/pdf-templates/payslip.blade.php ENDPATH**/ ?>