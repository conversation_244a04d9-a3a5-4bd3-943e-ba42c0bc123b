<!DOCTYPE html>
<html dir="<?php echo e($direction ?? 'ltr'); ?>" lang="<?php echo e($language ?? 'en'); ?>">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo e(shapeArabic(__('salary_components.payslip'))); ?> -
        <?php echo e(isset($employee['name']) ? shapeArabic($employee['name']) : ''); ?>

    </title>
    <?php if($language === 'ar'): ?>
        <link
            href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap"
            rel="stylesheet">
    <?php endif; ?>
    <style>
        /* Base styles with better font support */
        body {
            font-family:
                <?php if($language === 'ar'): ?>
                'Noto Kufi Arabic', 'Amiri', <?php endif; ?> 'DejaVu Sans', sans-serif;
            direction:
                <?php echo e($direction ?? 'ltr'); ?>

            ;
            text-align:
                <?php echo e($direction == 'rtl' ? 'right' : 'left'); ?>

            ;
            font-size: 14px;
            line-height: 1.6;
            margin: 20px;
            color: #333;
        }

        .container {
            width: 100%;
            margin: 0 auto;
        }

        /* Table-based layouts */
        .header-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .header-table td {
            padding: 0;
            vertical-align: middle;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            text-align: left;
        }

        .company-logo {
            font-size: 24px;
            font-weight: bold;
            color: #4285f4;
            text-align: right;
        }

        /* Line break */
        .line-break {
            height: 1px;
            background-color: #ddd;
            margin: 15px 0;
            border: none;
        }

        /* Section One - Employee Info */
        .section-one {
            margin-bottom: 20px;
        }

        .payslip-month {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .employee-name {
            font-size: 16px;
            margin-bottom: 8px;
        }

        .employee-details-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
        }

        .employee-details-table td {
            padding: 0;
            vertical-align: top;
        }

        /* Section Two - Categories */
        .section-two {
            margin-bottom: 20px;
        }

        .category-header-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }

        .category-header-table td {
            padding: 0;
            vertical-align: middle;
        }

        .category-name {
            font-weight: bold;
            font-size: 16px;
            text-align: left;
        }

        .amount-keyword {

            text-align: right;
            direction: rtl;
        }

        .dashed-line {
            border-top: 1px dashed #ccc;
            margin: 8px 0;
            height: 1px;
        }

        .component-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 5px;
        }

        .component-table td {
            padding: 2px 0;
            vertical-align: top;
        }

        .component-name {
            font-weight: bold;
            text-align: left;
        }

        .component-amount {
            font-weight: normal;
            text-align: right;
        }

        .total-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 5px;
            background-color: rgba(200, 200, 200, 0.8);
            margin-top: 8px;
        }

        .total-table td {
            padding: 8px 10px;
            font-weight: bold;
            vertical-align: middle;
        }

        .total-left {
            text-align: left;
        }

        .total-right {
            text-align: right;
        }

        /* Section Three - Taxes & Social Insurance */
        .section-three {
            margin-bottom: 20px;
        }

        .section-title-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }

        .section-title-table td {
            padding: 0;
            vertical-align: middle;
        }

        .section-title-text {
            font-weight: bold;
            font-size: 16px;
            text-align: left;
        }

        .tax-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 5px;
        }

        .tax-table td {
            padding: 2px 0;
            vertical-align: top;
        }

        .tax-left {
            text-align: left;
        }

        .tax-right {
            text-align: right;
        }

        /* Section Four - Net Pay */
        .section-four {
            margin-bottom: 20px;
        }

        .net-pay-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 5px;
            background-color: rgba(200, 200, 200, 0.8);
        }

        .net-pay-table td {
            padding: 12px 15px;
            font-weight: bold;
            font-size: 18px;
            vertical-align: middle;
        }

        .net-pay-left {
            text-align: left;
        }

        .net-pay-right {
            text-align: right;
        }

        /* Section Five - Signature */
        .section-five {
            margin-top: 40px;
        }

        .signature-table {
            width: 100%;
            border-collapse: collapse;
        }

        .signature-table td {
            padding: 0;
            vertical-align: top;
        }

        .signature-container {
            text-align:
                <?php echo e($direction == 'rtl' ? 'left' : 'right'); ?>

            ;
        }

        .signature-label {
            margin-bottom: 10px;
            font-weight: normal;
        }

        .signature-box {
            border: 1px solid #333;
            width: 200px;
            height: 60px;
            margin: 0
                <?php echo e($direction == 'rtl' ? 'auto 0 0' : '0 0 auto'); ?>

            ;
        }

        /* Arabic Text Support with better fonts */
        .arabic-text {
            font-family: 'Noto Kufi Arabic', 'Amiri', 'DejaVu Sans', 'Arial Unicode MS', 'Tahoma', sans-serif;
            direction: rtl;
            unicode-bidi: bidi-override;
            text-align: right;
            font-feature-settings: 'liga' 1, 'kern' 1;
        }

        .normal-text {
            font-family:
                <?php if($language === 'ar'): ?>
                'Noto Kufi Arabic', 'Amiri', <?php endif; ?> 'DejaVu Sans', 'Arial Unicode MS', 'Tahoma', sans-serif;
        }

        .number {
            font-family: 'DejaVu Sans', sans-serif !important;
            direction: ltr !important;
        }

        /* RTL adjustments for tables */
        .rtl .company-name {
            text-align: right;
        }

        .rtl .company-logo {
            text-align: left;
        }

        .rtl .category-name,
        .rtl .section-title-text,
        .rtl .tax-left,
        .rtl .net-pay-left {
            text-align: right;
        }

        .rtl .amount-keyword,
        .rtl .component-amount,
        .rtl .total-right,
        .rtl .tax-right,
        .rtl .net-pay-right {
            text-align: left;
        }

        .rtl .signature-container {
            text-align: left;
        }

        .new-page {
            page-break-after: always;
        }
    </style>
</head>

<body>
    <?php
        // Global translation variables - declared once at the top
        $amountText = __('salary_components.amount');
        if (containsArabic($amountText)) {
            $amountText = shapeArabic($amountText);
        }

        $currencyText = __('salary_components.currency');
        if (containsArabic($currencyText)) {
            $currencyText = shapeArabic($currencyText);
        }

        $totalText = __('salary_components.total');
        if (containsArabic($totalText)) {
            $totalText = shapeArabic($totalText);
        }

        $fontClass = $language == 'ar' ? 'arabic-text' : 'normal-text';
    ?>

    <div class="container">
        
        <table class="header-table">
            <tr>
                <td class="company-name">
                    <?php
                        $companyName = $company_name ?? 'Company Name';
                        $companyNameDisplay = containsArabic($companyName) ? shapeArabic($companyName) : $companyName;
                    ?>
                    <span class="<?php echo e($fontClass); ?>">
                        <?php echo e($companyNameDisplay); ?>

                    </span>
                </td>
                <td class="company-logo">blueworks.</td>
            </tr>
        </table>

        
        <hr class="line-break">

        
        <div class="section-one">
            
            <div class="payslip-month">
                <?php
                    $payslipForText = __('salary_components.payslip_for');
                    if (containsArabic($payslipForText)) {
                        $payslipForText = shapeArabic($payslipForText);
                    }
                ?>
                <span class="<?php echo e($fontClass); ?>">
                    <?php echo e($payslipForText); ?>

                </span>
                <span class="normal-text"><?php echo e(containsArabic($month ?? '') ? shapeArabic($month ?? '') : $month ?? ''); ?>

                    <?php echo e($year ?? ''); ?></span>
            </div>

            
            <div class="employee-name">
                <?php
                    $employeeNameText = __('salary_components.employee_name');
                    if (containsArabic($employeeNameText)) {
                        $employeeNameText = shapeArabic($employeeNameText);
                    }

                    $employeeName = $employee['name'] ?? '';
                    if (containsArabic($employeeName)) {
                        $employeeName = shapeArabic($employeeName);
                    }
                ?>
                <span class="<?php echo e($fontClass); ?>">
                    <?php echo e($employeeNameText); ?>:
                </span>
                <span class="<?php echo e($fontClass); ?>">
                    <?php echo e($employeeName); ?>

                </span>
            </div>

            
            <table class="employee-details-table">
                <tr>
                    <td>
                        <?php
                            $codeText = __('salary_components.employee_code');
                            if (containsArabic($codeText)) {
                                $codeText = shapeArabic($codeText);
                            }
                        ?>
                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($codeText); ?>:
                        </span>
                        <span class="number">#<?php echo e($employee['code'] ?? ''); ?></span>
                    </td>
                    <td style="text-align: right;">
                        <?php
                            $titleText = __('salary_components.job_title');
                            if (containsArabic($titleText)) {
                                $titleText = shapeArabic($titleText);
                            }

                            $employeeTitle = $employee['title'] ?? '';
                            if (containsArabic($employeeTitle)) {
                                $employeeTitle = shapeArabic($employeeTitle);
                            }
                        ?>
                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($titleText); ?>:
                        </span>
                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($employeeTitle); ?>

                        </span>
                    </td>
                </tr>
            </table>
        </div>

        
        <hr class="line-break">

        
        <?php if(isset($categories) && is_array($categories)): ?>
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoryName => $categoryData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="section-two">
                    
                    <table class="category-header-table">
                        <tr>
                            <td class="category-name">
                                <?php
                                    $categoryKey = 'salary_components.' . trim($categoryName) ?? '';
                                    $translatedCategoryName = __($categoryKey) ?: $categoryName;
                                    if (containsArabic($translatedCategoryName)) {
                                        $translatedCategoryName = shapeArabic($translatedCategoryName);
                                    }
                                ?>
                                <span class="<?php echo e($fontClass); ?>">
                                    <?php echo e($translatedCategoryName); ?>

                                </span>
                            </td>
                            <td class="amount-keyword">
                                <span class="<?php echo e($fontClass); ?>">
                                    <?php echo e($amountText); ?>

                                </span>
                            </td>
                        </tr>
                    </table>

                    
                    <div class="dashed-line"></div>

                    
                    <?php $categoryTotal = 0; ?>
                    <?php if(is_array($categoryData['components'])): ?>
                        <?php $__currentLoopData = $categoryData['components']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $component): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $componentKey = 'salary_components.' . $component['name'] ?? '';
                                $translatedComponentName = __($componentKey) ?: ($component['name'] ?? '');
                                if (containsArabic($translatedComponentName)) {
                                    $translatedComponentName = shapeArabic($translatedComponentName);
                                }
                                $amount = floatval(str_replace(',', '', $component['amount'] ?? '0'));
                                $categoryTotal += $amount;
                            ?>
                            <table class="component-table">
                                <tr>
                                    <td class="component-name">
                                        <span class="<?php echo e($fontClass); ?>">
                                            <?php echo e($translatedComponentName); ?>

                                        </span>
                                    </td>
                                    <td class="component-amount">
                                        <span class="number">
                                            <?php echo e($categoryData['is_addition'] ? '' : '-'); ?><?php echo e($component['amount']); ?>

                                            <span class="<?php echo e($fontClass); ?>">
                                                <?php echo e($currencyText); ?>

                                            </span>
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>

                    
                    <table class="total-table">
                        <tr>
                            <td class="total-left">
                                <span class="<?php echo e($fontClass); ?>">
                                    <?php echo e($totalText); ?> <?php echo e($translatedCategoryName); ?>

                                </span>
                            </td>
                            <td class="total-right">
                                <span class="number">
                                    <?php echo e(number_format($categoryTotal, 2)); ?>

                                    <span class="<?php echo e($fontClass); ?>">
                                        <?php echo e($currencyText); ?>

                                    </span>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>

        
        <div class="new-page"></div>

        
        <div class="section-three">
            
            <table class="section-title-table">
                <tr>
                    <td class="section-title-text">
                        <?php
                            $taxInsuranceText = __('salary_components.taxes_social_insurance');
                            if (containsArabic($taxInsuranceText)) {
                                $taxInsuranceText = shapeArabic($taxInsuranceText);
                            }
                        ?>
                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($taxInsuranceText); ?>

                        </span>
                    </td>
                    <td class="amount-keyword">
                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($amountText); ?>

                        </span>
                    </td>
                </tr>
            </table>

            
            <table class="tax-table">
                <tr>
                    <td class="tax-left">
                        <?php
                            $taxesText = __('salary_components.taxes');
                            if (containsArabic($taxesText)) {
                                $taxesText = shapeArabic($taxesText);
                            }
                        ?>
                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($taxesText); ?>

                        </span>
                    </td>
                    <td class="tax-right">
                        <span class="number">
                            -<?php echo e($tax_amount ?? '0'); ?>

                            <span class="<?php echo e($fontClass); ?>">
                                <?php echo e($currencyText); ?>

                            </span>
                        </span>
                    </td>
                </tr>
            </table>

            
            <table class="tax-table">
                <tr>
                    <td class="tax-left">
                        <?php
                            $socialInsuranceText = __('salary_components.social_insurance');
                            if (containsArabic($socialInsuranceText)) {
                                $socialInsuranceText = shapeArabic($socialInsuranceText);
                            }
                        ?>
                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($socialInsuranceText); ?>

                        </span>
                    </td>
                    <td class="tax-right">
                        <span class="number">
                            -<?php echo e($insurance_amount ?? '0'); ?>

                            <span class="<?php echo e($fontClass); ?>">
                                <?php echo e($currencyText); ?>

                            </span>
                        </span>
                    </td>
                </tr>
            </table>

            
            <table class="total-table">
                <tr>
                    <td class="total-left">
                        <?php
                            $totalTaxInsurance = floatval(str_replace(',', '', $tax_amount ?? '0')) + floatval(str_replace(',', '', $insurance_amount ?? '0'));
                        ?>
                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($totalText); ?>

                        </span>
                    </td>
                    <td class="total-right">
                        <span class="number">
                            <?php echo e(number_format($totalTaxInsurance, 2)); ?>

                            <span class="<?php echo e($fontClass); ?>">
                                <?php echo e($currencyText); ?>

                            </span>
                        </span>
                    </td>
                </tr>
            </table>
        </div>

        
        <div class="section-four">
            <table class="net-pay-table">
                <tr>
                    <td class="net-pay-left">
                        <?php
                            $netPayText = __('salary_components.net_pay');
                            if (containsArabic($netPayText)) {
                                $netPayText = shapeArabic($netPayText);
                            }
                        ?>
                        <span class="<?php echo e($fontClass); ?>">
                            <?php echo e($netPayText); ?>

                        </span>
                    </td>
                    <td class="net-pay-right">
                        <span class="number">
                            <?php echo e($employee['net_salary'] ?? '0'); ?>

                            <span class="<?php echo e($fontClass); ?>">
                                <?php echo e($currencyText); ?>

                            </span>
                        </span>
                    </td>
                </tr>
            </table>
        </div>

        
        <div class="section-five">
            <table class="signature-table">
                <tr>
                    <td></td>
                    <td class="signature-container">
                        <div class="signature-label">
                            <?php
                                $signatureText = __('salary_components.employee_signature');
                                if (containsArabic($signatureText)) {
                                    $signatureText = shapeArabic($signatureText);
                                }
                            ?>
                            <span class="<?php echo e($fontClass); ?>">
                                <?php echo e($signatureText); ?>:
                            </span>
                        </div>
                        <div class="signature-box"></div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</body>

</html><?php /**PATH C:\Herd\www\API\resources\views/admin/pdf-templates/payslip.blade.php ENDPATH**/ ?>