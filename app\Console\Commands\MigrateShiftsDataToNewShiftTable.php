<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use App\Models\Cico;
use App\Models\Company;
use App\Models\NewShift;
use App\Models\Shift;
use App\Models\Timecard;
use App\Models\TimecardType;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Console\Command\Command as CommandAlias;
use Symfony\Component\Console\Helper\ProgressBar;

class MigrateShiftsDataToNewShiftTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shift:new';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {

        if ($this->confirm('Do you wish to continue?', true)) {

            //            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            DB::table('new_shifts')->truncate();
            DB::table('shift_title_branch')->truncate();
            DB::table('timecard_types')->truncate();
            DB::table('timecards')->truncate();
            DB::table('cicos')->truncate();
            //            DB::statement('SET FOREIGN_KEY_CHECKS=1;');

            DB::beginTransaction();
            try {
                $companies = Company::all();
                $bar1 = new ProgressBar($this->output, count($companies));
                $bar1->setFormat(' %current%/%max% [%bar%] %percent:3s%%');
                $bar1->start();

                foreach ($companies as $company) {
                    $this->migrateData($company);
                    $bar1->advance();
                }
                $bar1->finish();

                $bar1->finish();
                $this->output->newLine();

                $attendances = Attendance::query()->whereNull('deleted_at')->get();
                $bar2 = new ProgressBar($this->output, count($attendances));
                $bar2->setFormat(' %current%/%max% [%bar%] %percent:3s%%');
                $bar2->start();
                foreach ($attendances as $attendance) {
                    $this->migrateAttendanceToCico($attendance);
                    $bar2->advance();
                }
                $bar2->finish();
                $this->output->newLine();

                DB::commit();
            } catch (\Exception $e) {
                DB::rollback();
                $this->error(' An error occurred: '.$e->getMessage());

                return CommandAlias::FAILURE;
            }
            $this->line('Migration completed successfully');
        }

        return CommandAlias::SUCCESS;
    }

    public function migrateAttendanceToCico(Attendance $attendance): void
    {
        $ci = null;
        if ($attendance->clock_in != null) {
            $ci = Cico::query()->create([
                'employee_id' => $attendance->employee_id,
                'branch_id' => $attendance->branch_id,
                'cico_by_id' => $attendance->clock_in_by ?? $attendance->employee_id,
                'date' => Carbon::createFromFormat('Y-m-d H:i:s', $attendance->date.' '.$attendance->clock_in),
                'lat' => $attendance->clock_in_lat,
                'long' => $attendance->clock_in_long,
                'in_out' => 'in',
                'source' => 'app',
                'status' => 'verified',
                'created_at' => $attendance->created_at,
                'updated_at' => $attendance->updated_at,
            ]);
            $attendance->update([
                'ci_id' => $ci->id,
            ]);
        }
        if ($attendance->clock_out != null) {
            $co = Cico::query()->create([
                'employee_id' => $attendance->employee_id,
                'branch_id' => $attendance->branch_id,
                'cico_by_id' => $attendance->clock_out_by ?? $attendance->employee_id,
                'date' => Carbon::createFromFormat('Y-m-d H:i:s', $attendance->date.' '.$attendance->actual_start),
                'lat' => $attendance->clock_out_lat,
                'long' => $attendance->clock_out_long,
                'in_out' => 'out',
                'source' => 'app',
                'status' => 'verified',
                'created_at' => $attendance->created_at,
                'updated_at' => $attendance->updated_at,
            ]);
            $attendance->update([
                'co_id' => $co->id,
            ]);
            if ($ci) {
                $ci->update([
                    'paired_clock_id' => $co->id,
                ]);
                $co->update([
                    'paired_clock_id' => $ci->id,
                ]);
            }
        }
        if ($attendance->clock_in == null && $attendance->clock_out == null) {
            $attendance->delete();
        }
    }

    public function getShiftWeekDuration($date, $day, $fromTime, $endTime): ?string
    {
        $endTime = Carbon::createFromFormat('Y-m-d H:i:s', $date.' '.$endTime);
        $fromTime = Carbon::createFromFormat('Y-m-d H:i:s', $date.' '.$fromTime);

        if ($endTime->lessThan($fromTime)) {
            $endTime->addDay();
        }

        $duration = floatval($fromTime->diffInMinutes($endTime)) / 60;
        $map = [
            'SUNDAY' => $duration,
            'MONDAY' => $duration,
            'TUESDAY' => $duration,
            'WEDNESDAY' => $duration,
            'THURSDAY' => $duration,
            'FRIDAY' => $duration,
            'SATURDAY' => $duration,
            null => null,
        ];

        return $map[$day];
    }

    /**
     * @return array
     */
    public function getShiftWeekFromTime($day, $fromTime)
    {
        $map = [
            'SUNDAY' => $fromTime,
            'MONDAY' => $fromTime,
            'TUESDAY' => $fromTime,
            'WEDNESDAY' => $fromTime,
            'THURSDAY' => $fromTime,
            'FRIDAY' => $fromTime,
            'SATURDAY' => $fromTime,
            null => null,
        ];

        return $map[$day];
    }

    public function migrateData(Company $company): void
    {

        $timeCardType = TimecardType::create([
            'name' => 'regular',
            'company_id' => $company->id,
            'is_default' => 1,
            'created_at' => now(),
            'updated_at' => now(),

        ]);
        foreach ($company->branches as $companyBranch) {
            $shifts = Shift::whereNotNull('schedule_id')
                ->whereNull('deleted_at')
                ->where('company_id', $companyBranch->company_id)
                ->where('branch_id', $companyBranch->id)
                ->get();

            $groupedShifts = $shifts->groupBy(function ($shift) {
                return strtolower($shift->name).'-'.$shift->schedule_id;
            });

            foreach ($groupedShifts as $shiftGroup) {
                $newShift = [
                    'name' => null,
                    'from_date' => null,
                    'to_date' => null,
                    'company_id' => null,
                    'sun_from_time' => null,
                    'sun_duration' => null,
                    'mon_from_time' => null,
                    'mon_duration' => null,
                    'tue_from_time' => null,
                    'tue_duration' => null,
                    'wed_from_time' => null,
                    'wed_duration' => null,
                    'thu_from_time' => null,
                    'thu_duration' => null,
                    'fri_from_time' => null,
                    'fri_duration' => null,
                    'sat_from_time' => null,
                    'sat_duration' => null,
                    'created_at' => null,
                    'updated_at' => null,
                    'deleted_at' => null,
                ];
                $timeCards = [];
                $oldShiftsIds = [];
                foreach ($shiftGroup as $index => $shift) {
                    $titleShiftsBranches = [];
                    foreach ($shift->titleShifts as $titleShift) {
                        foreach ($titleShift->titleShiftEmployees as $titleShiftEmployee) {
                            $parts = explode(',', $shift->branch->location);
                            $latitude = trim($parts[0]);
                            $longitude = trim($parts[1]);

                            $from = Carbon::createFromFormat('Y-m-d H:i:s', $shift->date.' '.$shift->start_time);
                            $to = Carbon::createFromFormat('Y-m-d H:i:s', $shift->date.' '.$shift->end_time);

                            $timeCards[] = [
                                'employee_id' => $titleShiftEmployee->employee_id,
                                'branch_id' => $shift->branch_id,
                                'shift_id' => null,
                                'created_by_id' => $titleShiftEmployee->employee_id,
                                'from' => $from,
                                'to' => $to,
                                'timecard_type_id' => $timeCardType->id,
                                'required_ci_lat' => $latitude,
                                'required_ci_long' => $longitude,
                                'required_co_lat' => $latitude,
                                'required_co_long' => $longitude,
                                'required_co_branch_id' => $shift->branch_id,
                                'required_ci_branch_id' => $shift->branch_id,
                                'created_at' => $shift->created_at,
                                'updated_at' => $shift->updated_at,
                                'start_dayOfWeek' => Carbon::parse($shift->date)->format('D'),
                            ];
                        }
                        $titleShiftsBranches[] = [
                            'branch_id' => $shift->branch_id,
                            'new_shift_id' => null,
                            'title_id' => $titleShift->title_id,
                            'planned_headcount' => $titleShift->employee_count,
                            'created_at' => $titleShift->created_at,
                            'updated_at' => $titleShift->updated_at,
                        ];

                    }
                    $dayOfWeek = strtolower(substr($shift->shift_day, 0, 3));
                    if ($index === 0) {
                        $newShift['from_date'] = $shift->date;
                        $newShift['created_at'] = $shift->created_at;
                    }
                    if ($index === $shiftGroup->count() - 1) {
                        $newShift['to_date'] = $shift->date;
                        $newShift['updated_at'] = $shift->updated_at;
                    }
                    $oldShiftsIds[] = $shift->id;
                    $newShift['name'] = $shift->name;
                    $newShift['company_id'] = $shift->company_id;
                    $newShift['colorhex'] = $this->getColorCode($shift->color);
                    $newShift[$dayOfWeek.'_from_time'] = $this->getShiftWeekFromTime($shift->shift_day, $shift->start_time);
                    $newShift[$dayOfWeek.'_duration'] = $this->getShiftWeekDuration($shift->date, $shift->shift_day, $shift->start_time, $shift->end_time);
                }
                $newShift = NewShift::create($newShift);
                if ($timeCards) {
                    foreach ($timeCards as $key => $unique) {
                        $timeCards[$key]['shift_id'] = $newShift->id;
                    }
                    Timecard::insert($timeCards);
                }
                $shiftAttendances = Attendance::query()
                    ->whereNull('deleted_at')
                    ->whereIn('shift_id', $oldShiftsIds)->get();
                foreach ($shiftAttendances as $shiftAttendance) {
                    $timeCard = Timecard::query()
                        ->where('employee_id', $shiftAttendance->employee_id)
                        ->where('from', Carbon::createFromFormat('Y-m-d H:i:s', $shiftAttendance->date.' '.$shiftAttendance->actual_start))
                        ->where('to', Carbon::createFromFormat('Y-m-d H:i:s', $shiftAttendance->date.' '.$shiftAttendance->actual_end))
                        ->first();
                    if ($timeCard) {
                        Attendance::query()->where('id', $shiftAttendance->id)->update(['slotable_id' => $timeCard->id, 'slotable_type' => 'time_card']);
                    }
                }
                $groupedArray = [];
                foreach ($titleShiftsBranches as $item) {
                    $branchId = $item['branch_id'];
                    $titleId = $item['title_id'];
                    $plannedHeadcount = $item['planned_headcount'];
                    $createdAt = $item['created_at'];
                    $updatedAt = $item['updated_at'];

                    $newShiftId = $newShift->id;
                    $key = $branchId.'_'.$titleId.'_'.$newShiftId;

                    if (! isset($groupedArray[$key])) {
                        $groupedArray[$key] = [
                            'branch_id' => $branchId,
                            'title_id' => $titleId,
                            'planned_headcount' => 0,
                            'created_at' => $createdAt,
                            'updated_at' => $updatedAt,
                            'new_shift_id' => $newShiftId,
                        ];
                    }
                    $groupedArray[$key]['planned_headcount'] += $plannedHeadcount;
                }
                $finalArray = array_values($groupedArray);
                DB::table('shift_title_branch')->insert($finalArray);
            }
        }

        $attendancesWithoutShifts = Attendance::query()
            ->whereNull('deleted_at')
            ->whereNull('shift_id')
            ->whereNotNull('clock_in')
            ->whereNotNull('clock_out')
            ->where('company_id', $company->id)
            ->get();

        foreach ($attendancesWithoutShifts as $attendancesWithoutShift) {
            $parts = explode(',', $attendancesWithoutShift->branch->location);
            $latitude = trim($parts[0]);
            $longitude = trim($parts[1]);
            $timeCardWithoutShift = Timecard::create([
                'employee_id' => $attendancesWithoutShift->employee_id,
                'branch_id' => $attendancesWithoutShift->branch_id,
                'shift_id' => null,
                'created_by_id' => $attendancesWithoutShift->employee_id,
                'from' => Carbon::createFromFormat('Y-m-d H:i:s', $attendancesWithoutShift->date.' '.$attendancesWithoutShift->actual_start),
                'to' => Carbon::createFromFormat('Y-m-d H:i:s', $attendancesWithoutShift->date.' '.$attendancesWithoutShift->actual_end),
                'timecard_type_id' => $timeCardType->id,
                'required_ci_lat' => $latitude,
                'required_ci_long' => $longitude,
                'required_co_lat' => $latitude,
                'required_co_long' => $longitude,
                'required_co_branch_id' => $attendancesWithoutShift->branch_id,
                'required_ci_branch_id' => $attendancesWithoutShift->branch_id,
                'created_at' => $attendancesWithoutShift->created_at,
                'updated_at' => $attendancesWithoutShift->updated_at,
                'start_dayOfWeek' => Carbon::parse($attendancesWithoutShift->date)->format('D'),
            ]);
            Attendance::query()->where('id', $attendancesWithoutShift->id)->update(['slotable_id' => $timeCardWithoutShift->id, 'slotable_type' => 'time_card']);

        }
    }

    public function getColorCode(?string $colorName): ?string
    {
        $colors = [
            'blue' => '0000FF',
            'green' => '008000',
            'orange' => 'FFA500',
            'red' => 'FF0000',
            'purple' => '800080',
            'cian' => '00FFFF',
            null => null,
        ];

        return $colors[$colorName];
    }
}
