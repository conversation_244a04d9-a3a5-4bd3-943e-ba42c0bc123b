<?php

namespace App\Console\Commands;

use App\Imports\GrossAndNetImport;
use App\Repositories\PayrollRepositories\SalaryRangeRepository;
use App\Util\PayrollUtil;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class ConvertGrossToNet extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:convert-gross-to-net {file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $this->info('Convert Gross To Net');
            $file = $this->argument('file');
            $filePath = storage_path($file);

            if (! file_exists($filePath)) {
                $this->error("File not found: $filePath");

                return 1;
            }

            Excel::import(new GrossAndNetImport, $filePath);
            $this->info('Employee salaries converted successfully');

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error: '.$e->getMessage());
        }
    }
    // $netSalary = 20356.29;
    // //$netSalary = 17350;
    //$this->calculate($netSalary, 0, 0);
    // Example usage
    // $monthlyGrossSalary = 15000; // Example monthly gross salary
    // $socialInsuranceSalary = 10372;
    // // $grossAfterInsuranceAndExm = 15000 - 1199 - 1666.67;
    // $monthlyNetSalary = $this->calculateMonthlyNetSalary($monthlyGrossSalary, $socialInsuranceSalary) ;

    // echo 'Monthly Gross Salary: EGP ' . $monthlyGrossSalary . "\n";
    // echo 'Monthly Net Salary: EGP '.round($monthlyNetSalary, 2)."\n";

    //    public function GuessTheGross($netSalary)
    //     {
    //         $guessingValues = [];
    //         $maxElement = ceil($netSalary * 30 / 100);
    //         $initialElement = 0;
    //         while($initialElement <= $maxElement)
    //             {
    //             $guessingValues[] = $initialElement;
    //             $initialElement += 2;
    //         }
    //         $finalGross = $this->binarySearch($guessingValues, $netSalary);
    //         // echo "The gross salary is: EGP " . $finalGross . "\n";
    //     }

    // public function binarySearch(array $sortedArray, $target)
    // {
    //     $low = 0;
    //     $high = count($sortedArray) - 1;
    //     $step = 1;
    //     while ($low <= $high) {
    //         echo 'step: '.$step." --------------------\n";

    //         $mid = (int) floor(($low + $high) / 2);
    //         $expectedGross = $target + $sortedArray[$mid];
    //         $expectedNet = $this->calculateMonthlyNetSalary($expectedGross);
    //         // echo "mid: " . $mid . "\n";
    //         // echo "expected gross: EGP " . $expectedGross . "\n";
    //         echo 'expected net: EGP '.ceil($expectedNet)."\n";
    //         if (ceil($expectedNet) == $target || (ceil($expectedNet) >= $target - 1 && ceil($expectedNet) < $target + 1)) {
    //             return $sortedArray[$mid]; // Target found, return the value instead of the index
    //         }
    //         // if (ceil($expectedNet) > $target) {
    //         //     return -2; // Target found, return the value instead of the index
    //         // }

    //         if ($expectedNet < $target) {
    //             $low = $mid + 1; // Target is in the upper half
    //         } else {
    //             $high = $mid - 1; // Target is in the lower half
    //         }
    //         $step++;

    //     }

    //     return -1; // Target not found
    // }

    // public function calculateAnnualTax($annualTaxBase)
    // {
    //     $salaryRangeRepository = new SalaryRangeRepository();
    //     $salaryRange = $salaryRangeRepository->getSalaryRangeWithTaxBrackets($annualTaxBase, 'desc');

    //     $tax = 0;

    //     foreach ($salaryRange->taxBrackets as $bracket) {
    //         if ($annualTaxBase > $bracket->min) {
    //             $tax += ($annualTaxBase - $bracket->min) * ($bracket->percentage / 100);
    //             $annualTaxBase = $bracket->min;
    //         }
    //     }
    // $tax = 0;

    // if ($annualTaxBase > 1200000) {
    //     $tax += ($annualTaxBase - 1200000) * 0.275;
    //     $annualTaxBase = 1200000;
    // }
    // if ($annualTaxBase > 400000) {
    //     $tax += ($annualTaxBase - 400000) * 0.25;
    //     $annualTaxBase = 400000;
    // }
    // if ($annualTaxBase > 200000) {
    //     $tax += ($annualTaxBase - 200000) * 0.225;
    //     $annualTaxBase = 200000;
    // }
    // if ($annualTaxBase > 70000) {
    //     $tax += ($annualTaxBase - 70000) * 0.20;
    //     $annualTaxBase = 70000;
    // }
    // if ($annualTaxBase > 55000) {
    //     $tax += ($annualTaxBase - 55000) * 0.15;
    //     $annualTaxBase = 55000;
    // }
    // if ($annualTaxBase > 40000) {
    //     $tax += ($annualTaxBase - 40000) * 0.10;
    //     $annualTaxBase = 40000;
    // }

    // // No tax for income up to EGP 40,000
    //     return $tax;
    // }

    //used in calculation of net salary from gross salary
    // public function calculateMonthlyNetSalary($monthlyGrossSalary, $socialInsuranceSalary = null)
    // {

    //     $socialInsuranceEmployeeShare = $this->calculateSocialInsuranceEmployeeShare($monthlyGrossSalary, $socialInsuranceSalary);

    //     $grossWithoutInsuranceAndExm = $monthlyGrossSalary - $socialInsuranceEmployeeShare - 1666.67;
    //     // Calculate the annual tax base (monthly gross salary * 12)
    //     $annualGrossSalary = $grossWithoutInsuranceAndExm * 12;
    //     // $annualGrossSalary = 145612.8;
    //     // Calculate the annual tax based on the annual tax base
    //     $annualTax = $this->calculateAnnualTax($annualGrossSalary);
    //     // echo "annual tax: EGP " . $annualTax . "\n";

    //     // Calculate the monthly tax by dividing the annual tax by 12
    //     $monthlyTax = $annualTax / 12;
    //     echo 'Monthly Tax: '.round($monthlyTax, 2)."\n";

    //     $totalGross = $grossWithoutInsuranceAndExm + $socialInsuranceEmployeeShare + 1666.67;
    //     echo 'Gross Salary: '.ceil($totalGross)."\n";

    //     // Calculate the monthly net salary by subtracting the monthly tax from the monthly gross salary
    //     $monthlyNetSalary = $totalGross - $monthlyTax - $socialInsuranceEmployeeShare - ((0.5 / 1000) * $totalGross);
    //     return $monthlyNetSalary;
    // }

    // public function calculateMonthlyNetSalary($monthlyGrossSalary)
    // {
    //     $socialInsuranceEmployeeShare = $this->calculateSocialInsuranceEmployeeShare($monthlyGrossSalary);
    //     echo 'Social Insurance Employee Share: '.round($socialInsuranceEmployeeShare, 2)."\n";

    //     $familyTaxExemption = $this->calculateFamilyTaxExemption($monthlyGrossSalary);

    //     $grossWithoutInsuranceAndFamilyExemption = $monthlyGrossSalary - $socialInsuranceEmployeeShare - $familyTaxExemption;

    //     $annualGrossSalary = $grossWithoutInsuranceAndFamilyExemption * 12;

    //     $annualTax = $this->calculateAnnualTax($annualGrossSalary);

    //     $monthlyTax = $annualTax / 12;
    //     echo 'Monthly Tax: '.round($monthlyTax, 2)."\n";

    //     echo 'Gross Salary: '.ceil($monthlyGrossSalary)."\n";

    //     // Calculate the monthly net salary by subtracting the monthly tax from the monthly gross salary
    //     $monthlyNetSalary = $monthlyGrossSalary - $monthlyTax - 1386 - ((5 / 10000) * $monthlyGrossSalary);
    //     return $monthlyNetSalary;
    // }
    /////////////////////////////////

    // public function calculate($netSalary, $socialInsuranceSalary, $payrollPolicy)
    // {
    //     $addonsValues = $this->getAddonsValues($netSalary);
    //     $result = $this->guessTheRightGrossSalary($addonsValues, $netSalary);
    //     $result['expectedNetSalary'] = ceil($result['expectedNetSalary']);
    //     $result['grossSalary'] = ceil($result['grossSalary']);
    //     $result['monthlyTax'] = round($result['monthlyTax'], 2);
    //     // dd($result);

    // }

    // private function getAddonsValues($netSalary)
    // {
    //     $values = [];
    //     $max = ceil($netSalary * 30 / 100);
    //     $start = 0;
    //     while ($start <= $max) {
    //         $values[] = $start;
    //         $start += 2;
    //     }

    //     return $values;
    // }

    // public function guessTheRightGrossSalary($addonsValues, $netSalary)
    // {
    //     $low = 0;
    //     $high = count($addonsValues) - 1;
    //     $step = 1;
    //     while ($low <= $high) {

    //         $mid = (int) floor(($low + $high) / 2);
    //         $expectedGrossSalary = $netSalary + $addonsValues[$mid];

    //         $result = $this->calculateMonthlyNetSalary($expectedGrossSalary);

    //         $expectedNetSalary = $result['expectedNetSalary'];

    //         if (ceil($expectedNetSalary) == $netSalary || (ceil($expectedNetSalary) >= $netSalary - 1 && ceil($expectedNetSalary) < $netSalary + 1)) {
    //             $result['grossSalary'] = $expectedGrossSalary;

    //             return $result;
    //         }

    //

    //            if ($expectedNetSalary < $netSalary) {
    //             $low = $mid + 1;
    //         } else {
    //             $high = $mid - 1;
    //         }
    //        $step++;

    //     }
    //     throw new \Exception('Could not find the right gross salary');
    // }

    //used in calculation of net salary from gross salary
    // public function calculateAnnualTax($annualTaxBase)
    // {
    //     $salaryRangeRepository = new SalaryRangeRepository();
    //     $salaryRange = $salaryRangeRepository->getSalaryRangeWithTaxBrackets($annualTaxBase, 'desc');

    //     $tax = 0;
    //     foreach ($salaryRange->taxBrackets as $bracket) {
    //         if ($annualTaxBase > $bracket->min) {
    //             $tax += ($annualTaxBase - $bracket->min) * ($bracket->percentage / 100);
    //             $annualTaxBase = $bracket->min;
    //         }
    //     }

    //     return $tax;
    // }

    // public function calculateMonthlyNetSalary($monthlyGrossSalary)
    // {
    //     $socialInsuranceEmployeeShare = $this->calculateSocialInsuranceEmployeeShare($monthlyGrossSalary);

    //     $familyTaxExemption = $this->calculateFamilyTaxExemption($monthlyGrossSalary);

    //     $grossWithoutInsuranceAndFamilyExemption = $monthlyGrossSalary - $socialInsuranceEmployeeShare - $familyTaxExemption;

    //     $annualGrossSalary = $grossWithoutInsuranceAndFamilyExemption * 12;

    //     $annualTax = $this->calculateAnnualTax($annualGrossSalary);

    //     $monthlyTax = $annualTax / 12;
    //     echo 'Monthly Tax: '.round($monthlyTax, 2)."\n";

    //     echo 'Gross Salary: '.ceil($monthlyGrossSalary)."\n";

    //     $monthlyNetSalary = $monthlyGrossSalary - $monthlyTax - $socialInsuranceEmployeeShare - (PayrollUtil::MARTYR_FUND * $monthlyGrossSalary);

    //     $result['expectedNetSalary'] = $monthlyNetSalary;
    //    $result['monthlyTax'] = $monthlyTax;

    //     return $result;
    // }

    //used in calculation of net salary from gross salary
    // public function calculateFamilyTaxExemption()
    // {
    //     return PayrollUtil::FAMILY_TAX_EXEMPTION / 12;
    // }

    //used in calculation of net salary from gross salary
    // public function calculateSocialInsuranceEmployeeShare($grossSalary, $socialInsuranceSalary = null)
    // {
    //     if (! is_null($socialInsuranceSalary)) {
    //         return $socialInsuranceSalary * (PayrollUtil::SOCIAL_INSURANCE_EMPLOYEE_SHARE_PERCENTAGE / 100);
    //     }
    //    $salaryThatSocialInsuranceIsCalculatedOn = $grossSalary > PayrollUtil::MAXIMUM_INSURANCE_AMOUNT ? PayrollUtil::MAXIMUM_INSURANCE_AMOUNT : $grossSalary;

    //     return $salaryThatSocialInsuranceIsCalculatedOn * (PayrollUtil::SOCIAL_INSURANCE_EMPLOYEE_SHARE_PERCENTAGE / 100);
    //}
}
