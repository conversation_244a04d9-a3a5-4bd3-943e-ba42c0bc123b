<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Employee;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class SyncDataForRequestaPageRevamp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:requests_page:data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        DB::beginTransaction();
        try {

            Artisan::call('app:fix-pending-workflows');
            echo 'Employee ID has been filled in employee requests.';

            Artisan::call('app:fill-employee-requests-employee-id');
            echo 'Employee ID has been filled in employee requests.';

            Artisan::call('app:add-penalties-to-employee-requests');
            echo 'Penalties have been added to employee requests.';

            Artisan::call('sync:leave_status:with_requests');
            echo 'Leave status has been synced with employee requests.';

            // to add change employee requests status to be rejected approved instead of waived and applied

            Artisan::call('app:migrate-deduction-employee-request-to-approved-or-rejected');
            echo 'Employee requests of deductions have been migrated to approved or rejected instead of applied waived.';

            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
