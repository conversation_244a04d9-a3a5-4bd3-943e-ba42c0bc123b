<?php

namespace App\Handlers;

use App\Util\HttpStatusCodeUtil;
use Illuminate\Http\Request;
use Throwable;

class NotFoundExceptionHandler extends BaseExceptionHandler
{
    /**
     * {@inheritDoc}
     */
    public static function getResponseStatusCode(Throwable $exception): int
    {
        return HttpStatusCodeUtil::NOT_FOUND;
    }

    /**
     * {@inheritDoc}
     */
    public static function getPayload(Request $request, Throwable $exception): array
    {
        $payload = parent::getPayload($request, $exception);
        $payload['errors']['message'] = ! empty($exception->getMessage()) ? $exception->getMessage() : 'Not found';

        return $payload;
    }
}
