<?php

namespace App\Services\V1\Employee;

use App\Exceptions\UnprocessableException;
use App\Repositories\V1\Employee\EmployeeContractRepository;
use App\Services\BaseService;

class EmployeeContractService extends BaseService
{

    public function __construct(EmployeeContractRepository $repository)
    {
        $this->repository = $repository;
    }

    public function getByEmployeeId(int $employeeId)
    {
        return $this->repository->getByEmployeeId($employeeId);
    }
}