<?php

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ResetNumberOfDeviceIdChanges extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reset:num:device-id-changes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'reset number of times device id has been changed to 0 ';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            DB::table('users')->update(['num_device_id_changed' => 0]);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();
        }
    }
}
