<?php

use App\Http\Controllers\NewCompanySetup\CompanySetupController;
use App\Http\Controllers\V1\CompanySetup\CompanySetupController as V1CompanySetupController;

use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'admin'], function () {
    Route::get('industries', [CompanySetupController::class, 'getIndustries']);

    Route::group(['prefix' => 'company'], function () {
        Route::post('basic-info', [CompanySetupController::class, 'setCompanyBasicInfo']);
        Route::get('basic-info', [CompanySetupController::class, 'getCompanyBasicInfo']);

        Route::get('setup-counter', [CompanySetupController::class, 'getCompanySetupCounter']);
        Route::post('setup-counter', [CompanySetupController::class, 'setCompanySetupCounter']);

        Route::group(['prefix' => 'departments'], function () {
            Route::get('', [CompanySetupController::class, 'getCompanyDepartments']);
            Route::post('', [CompanySetupController::class, 'addDepartment']);
            Route::put('/{id}', [CompanySetupController::class, 'editDepartment']);
            Route::delete('/{id}', [CompanySetupController::class, 'deleteDepartment']);
        });

        Route::group(['prefix' => 'areas'], function () {
            Route::get('', [CompanySetupController::class, 'getCompanyAreas']);
            Route::post('', [CompanySetupController::class, 'addArea']);
            Route::put('/{id}', [CompanySetupController::class, 'editArea']);
            Route::delete('/{id}', [CompanySetupController::class, 'deleteArea']);
        });

        Route::group(['prefix' => 'branches'], function () {
            Route::get('', [CompanySetupController::class, 'getCompanyBranches']);
            Route::post('', [CompanySetupController::class, 'addBranch']);
            Route::put('/{id}', [CompanySetupController::class, 'editBranch']);
            Route::delete('/{id}', [CompanySetupController::class, 'deleteBranch']);
        });

        Route::group(['prefix' => 'titles'], function () {
            Route::get('', [CompanySetupController::class, 'getCompanyTitles']);
            Route::post('', [CompanySetupController::class, 'addTitle']);
            Route::put('/{id}', [CompanySetupController::class, 'editTitle']);
            Route::delete('/{id}', [CompanySetupController::class, 'deleteTitle']);
            Route::post('assignRole', [CompanySetupController::class, 'assignRoleToTitle']);
        });

        Route::group(['prefix' => 'leaves'], function () {
            Route::get('', [V1CompanySetupController::class, 'getCompanyLeavesSetup']);
            Route::post('', [V1CompanySetupController::class, 'addCompanyLeaveSetup']);
            Route::put('/{id}', [V1CompanySetupController::class, 'editCompanyLeaveSetup']);
            Route::delete('/{id}', [V1CompanySetupController::class, 'deleteCompanyLeaveTypeWithPolicy']);
        });

        Route::group(['prefix' => 'policies'], function () {
            Route::get('', [CompanySetupController::class, 'getCompanyAttendancePolicies']);
            Route::post('', [CompanySetupController::class, 'setCompanyAttendancePolicies']);
            Route::post('setAttendanceToggles', [CompanySetupController::class, 'setAttendanceToggles']);

        });

        Route::group(['prefix' => 'attendance-deductions'], function () {
            Route::get('', [CompanySetupController::class, 'getAttendanceDeductionPolicies']);
            Route::post('', [CompanySetupController::class, 'setAttendanceDeductionPolicies']);
            Route::put('/{id}', [CompanySetupController::class, 'editAttendanceDeductionPolicies']);
            Route::delete('/{id}', [CompanySetupController::class, 'deleteAttendanceDeductionPolicy']);
        });

        Route::group(['prefix' => 'attendance-overtimes'], function () {
            Route::get('', [CompanySetupController::class, 'getAttendanceOvertimePolicies']);
            Route::post('', [CompanySetupController::class, 'setAttendanceOvertimePolicies']);
            Route::put('/{id}', [CompanySetupController::class, 'editAttendanceOvertimePolicies']);
            Route::delete('/{id}', [CompanySetupController::class, 'deleteAttendanceOvertimePolicy']);
        });

        Route::group(['prefix' => 'transfer'], function () {
            Route::post('/to-branch', [CompanySetupController::class, 'transferEmployee']);
        });

        Route::group(['prefix' => 'roles'], function () {
            Route::get('', [CompanySetupController::class, 'getCompanyRoles']);
            Route::get('/{id}/employees/', [CompanySetupController::class, 'getEmployeesAssignedToRole']);
            Route::post('', [CompanySetupController::class, 'addRole']);
            Route::put('{id}', [CompanySetupController::class, 'editRole']);
            Route::delete('/{id}', [CompanySetupController::class, 'deleteRole']);
        });

        Route::group(['prefix' => 'permissions'], function () {
            Route::get('', [CompanySetupController::class, 'getAllPermissions']);
        });

        Route::group(['prefix' => 'request-cycle'], function () { // deprecated #TODO remove this
            Route::get('', [CompanySetupController::class, 'getRequestCycles']);
            Route::post('', [CompanySetupController::class, 'addRequestCycle']);
        });

        Route::group(['prefix' => 'penalties'], function () {
            Route::get('', [CompanySetupController::class, 'getPenaltyGroups']);
            Route::post('', [CompanySetupController::class, 'addPenaltyGroupWithPolicies']);
            Route::put('/{id}', [CompanySetupController::class, 'editPenaltyGroupWithPolicies']);
            Route::delete('/{id}', [CompanySetupController::class, 'deletePenaltyGroup']);
        });
    })->middleware(['permission:manage_company']);

    Route::group(['prefix' => 'company'], function () {

        Route::group(['prefix' => 'employees'], function () {
            Route::get('', [CompanySetupController::class, 'getEmployeesByFilter']);
            Route::get('/{id}', [CompanySetupController::class, 'getEmployee']);
            Route::put('/{id}', [CompanySetupController::class, 'editEmployee']);
            Route::post('', [CompanySetupController::class, 'addEmployee']);
        })->middleware(['permission:add_employee|manage_company']);
        Route::get('get-company-country-codes', [CompanySetupController::class, 'getCompanyCountryCodes']);
    });
});
