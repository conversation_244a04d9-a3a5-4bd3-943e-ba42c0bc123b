<?php

//namespace App\Http\Middleware;
//
//use Closure;
//use Illuminate\Http\Request;
//use Illuminate\Support\Facades\Log;
//use Symfony\Component\HttpFoundation\Response;
//
//class Datadog
//{
//    /**
//     * Handle an incoming request.
//     *
//     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
//     */
//    public function handle(Request $request, Closure $next): Response
//    {
//        Log::info('Datadog middleware');
//        $route = $request->method().' '.$request->path();
//        if (function_exists('\DDTrace\trace_method')) {
//            $span = GlobalTracer::get()->getActiveSpan();
//            $span?->setResource($route);
//            $span?->setTag('employee_id', auth()->user()?->employee?->id);
//        }
//
//        return $next($request);
//    }
//}
