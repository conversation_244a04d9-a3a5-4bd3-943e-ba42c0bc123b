<?php

namespace App\Http\Requests\V1\RequestGroups;

use App\Rules\RoleInWorkflowRule;
use Illuminate\Foundation\Http\FormRequest;

class AddWorkFlowsToRequestGroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'workflows' => 'required|array',
            'workflows.*.type' => 'required|string',
            'workflows.*.workflow' => 'array',
            'workflows.*.workflow.*.role_id' => ['integer', new RoleInWorkflowRule],
            'workflows.*.workflow.*.operator' => 'string',
            'workflows.*.workflow.*.order' => 'integer',
        ];
    }
}
