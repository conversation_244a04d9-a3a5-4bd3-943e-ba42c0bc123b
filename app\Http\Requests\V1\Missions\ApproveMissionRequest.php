<?php

namespace App\Http\Requests\V1\Missions;

use Illuminate\Foundation\Http\FormRequest;

class ApproveMissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'with_attendance' => [
                'required',
                'boolean',
            ],
            'clock_in' => [
                'required_if:with_attendance,true',
                'date_format:Y-m-d H:i:s',
            ],
            'clock_out' => [
                'required_if:with_attendance,true',
                'date_format:Y-m-d H:i:s',
                'after_or_equal:clock_in',
            ],
        ];

    }
}
