<?php

namespace App\Handlers\MonthlyAttendanceTags;

use Illuminate\Support\Carbon;

class UnAccountedTagHandler implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        return [
            'name' => $employeeAttendance->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($employeeAttendance->from)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null): int
    {
        if (isset($tags[$employeeId]['tags']['unaccounted'])) {
            $attendanceRecords = $tags[$employeeId]['tags']['unaccounted']['data'];
            $dates = array_column($attendanceRecords, 'date');
            $uniqueDates = array_unique($dates);

            return count($uniqueDates);
        } else {
            return 1;
        }

    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return false;
    }

    public function handleTagUnit(): string
    {
        return 'days';
    }
}
