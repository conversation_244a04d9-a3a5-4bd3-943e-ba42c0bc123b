<?php

namespace App\Http\Controllers\Schedule;

use App\DomainData\FilterDto;
use App\DomainData\ScheduleDto;
use App\Http\Controllers\Controller;
use App\Services\Schedule\BusinessServices\CopyPreviousWeekScheduleService;
use App\Services\Schedule\BusinessServices\PublishScheduleService;
use App\Services\Schedule\BusinessServices\ValidateEmployeeScheduleService;
use App\Services\Schedule\CrudServices\RestEmployeeCrudService;
use App\Services\Schedule\CrudServices\ScheduleCrudService;

class ScheduleController extends Controller
{
    use FilterDto, ScheduleDto;

    public function __construct(
        private ScheduleCrudService $service,
        private PublishScheduleService $publishScheduleService,
        private ValidateEmployeeScheduleService $validateEmployeeScheduleService,
        private CopyPreviousWeekScheduleService $copyPreviousWeekScheduleService,
        private RestEmployeeCrudService $restEmployeeCrudService
    ) {}

    public function publishSchedule(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['start_date', 'end_date', 'branch_id']);
        $rules['related_objects'] = ['array'];
        $rules['notify'] = 'nullable|in:NO,ALL,ONLY';

        if (! isset($request['branch_id']) && ! is_null(config('globals.branchId'))) {
            $request['branch_id'] = config('globals.branchId');
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['notify'])) {
            $request['check_warning'] = true;
        }

        $this->publishScheduleService->perform($request, $output);

        if (! isset($request['notify']) && $request['check_warning'] && (! isset($output->Warning) || count($output->Warning) == 0)) {
            $output->no_need_commit = true;
            $output->request_body = $request;
        }
    }

    public function getByDate(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['start_date', 'end_date', 'branch_id']);
        $rules['related_objects'] = ['array'];

        if (! isset($request['branch_id']) && ! is_null(config('globals.branchId'))) {
            $request['branch_id'] = config('globals.branchId');
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->getByDate($request, $output);
    }

    public function getValidationOnEmployee(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['start_date', 'end_date', 'branch_id']);

        if (! isset($request['branch_id']) && ! is_null(config('globals.branchId'))) {
            $request['branch_id'] = config('globals.branchId');
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->validateEmployeeScheduleService->perform($request, $output);
    }

    public function copyPreviousWeek(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['start_date', 'branch_id']);

        if (! isset($request['branch_id'])) {
            $request['branch_id'] = config('globals.branchId');
        } else {
            config(['globals.branchId' => $request['branch_id']]);
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request['with_employees'] = false;

        $this->copyPreviousWeekScheduleService->perform($request, $output);
    }

    public function fillDefaultRests(array $request, \stdClass &$output)
    {
        $rules['branch_id'] = 'required|integer';
        $rules['start_day'] = 'required|date';
        $rules['end_day'] = 'required|date';
        $rules['check_warning'] = 'required|boolean'; // check_warning is will be true if the client didnt send it

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();
        $this->restEmployeeCrudService->fillDefaultRests($request, $output);

        if (isset($request['check_warning']) && $request['check_warning'] && isset($output->Warning) && count($output->Warning)) {
            $output->no_need_commit = true;
            $output->request_body = $request;
        }
    }

    public function copyPreviousWeekWithEmployees(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['start_date', 'branch_id']);

        if (! isset($request['branch_id'])) {
            $request['branch_id'] = config('globals.branchId');
        } else {
            config(['globals.branchId' => $request['branch_id']]);
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request['with_employees'] = true;

        $this->copyPreviousWeekScheduleService->perform($request, $output);
    }
}
