<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Employee;
use App\Models\TerminationRequest;
use App\Services\CompanySetup\EmployeesService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ChangeEmployeesStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'change:employees_status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $employees = Employee::with('employeeInfo', 'company.latestPayroll', 'title')->get();
            foreach ($employees as $employee) {
                $terminationRequest = TerminationRequest::where('employee_id', $employee->id)->latest()->first();
                $payroll = $employee->company?->latestPayroll->first() ?? null;
                //termination date is past, so this employee is terminated
                if (isset($employee->employeeInfo) && isset($terminationRequest) && !is_null($employee->employeeInfo->termination_date)
                    && $employee->employeeInfo->termination_date <= date('Y-m-d') && $terminationRequest->status == 'approved') {
                    $employee->status = 'terminated';
                    $employee->save();
                } //there is a termination date in the future, so employee will get terminated
                elseif (isset($employee->employeeInfo) && isset($terminationRequest) && !is_null($employee->employeeInfo->termination_date)
                    && $employee->employeeInfo->termination_date > date('Y-m-d') && $terminationRequest->status == 'approved') {
                    $employee->status = 'termination_pending';
                    $employee->save();
                } //in case there is no termination date, so employee could be new_hire or active-
                else {
                    $joinDate = $employee->employeeInfo?->join_date ?? null;
                    if (EmployeesService::getEmployeeStatus($joinDate, $payroll) == 'active' && $employee->on_probation) {
                        $employee->status = 'on_probation';
                    } else {
                        $employee->status = EmployeesService::getEmployeeStatus($joinDate, $payroll);
                    }
//                    $employee->status = EmployeesService::getEmployeeStatus($joinDate, $payroll);
//                    $employee->save();
                }
            }
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
