<?php

namespace App\Services\V1\InternalDashboard\CustomerSuccess;

use App\Imports\AreasBranchesImport;
use App\Imports\DeptTitlesImport;
use App\Models\BranchEmployee;
use App\Models\ModelHasRole;
use App\Models\Permission;
use App\Models\RegisterationValidation;
use App\Models\Role;
use App\Models\RoleScope;
use App\Repositories\NewAreaRepository;
use App\Repositories\NewAttendanceSettingRepository;
use App\Repositories\NewCompanyRepository;
use App\Repositories\NewDepartmentRepository;
use App\Repositories\NewEmployeeInfoRepository;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\NewRoleRepository;
use App\Repositories\NewTitleRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\Repository;
use App\Repositories\V1\BranchRepository;
use App\Repositories\V1\CountriesRepository;
use App\Repositories\V1\IndustryRepository;
use App\Repositories\V1\SubDepartmentRepository;
use App\Repositories\V1\WorkFlow\ScopeRepository;
use App\Services\BaseService;
use App\Util\BasicInfoUtil;
use App\Enums\Employee\EmployeeExportFieldEnum;
use App\Enums\Employee\EmployeeExportPresetTemplateEnum;
use App\Models\EmployeeExportTemplate;
use App\Repositories\V1\Employee\EmployeeExportTemplateRepository;
use App\FeatureToggles\Unleash;
use Maatwebsite\Excel\Facades\Excel;
use App\Services\V1\LeaveManagement\LeaveTypesSetupService;

class CustomerSuccessCompanyService extends BaseService
{
    private $departmentRepository;

    private $newRoleRepository;

    private $titleRepository;

    private $areaRepository;

    private $branchRepository;

    private $systemSettingRepository;

    private $timecardTypeRepository;

    private $scopeRepository;

    private $employeeRepository;

    private $userRepository;

    private $employeeInfoRepository;

    private $subDepartmentRepository;

    private $industryRepository;

    public function __construct(
        NewCompanyRepository $newCompanyRepository,
        private NewAttendanceSettingRepository $newAttendanceSettingRepository,
        private CountriesRepository $countriesRepository,
        private EmployeeExportTemplateRepository $employeeExportTemplateRepository,
        private LeaveTypesSetupService $leaveTypesSetupService
    ) {
        parent::__construct($newCompanyRepository);
        $this->departmentRepository = new NewDepartmentRepository;
        $this->newRoleRepository = new NewRoleRepository;
        $this->titleRepository = new NewTitleRepository;
        $this->areaRepository = new NewAreaRepository;
        $this->branchRepository = new BranchRepository;
        $this->systemSettingRepository = new SystemSettingRepository;
        $this->scopeRepository = new ScopeRepository;
        $this->timecardTypeRepository = Repository::getRepository('TimecardType');
        $this->employeeRepository = new NewEmployeeRepository;
        $this->userRepository = Repository::getRepository('User');
        $this->employeeInfoRepository = new NewEmployeeInfoRepository;
        $this->subDepartmentRepository = new SubDepartmentRepository;
        $this->industryRepository = new IndustryRepository;
    }

    public function createCompany($request)
    {
        $company = $this->createCompanyRecord($request);
        $companyRecordId = $company->id;

        $this->addCompanySettings($companyRecordId);

        // Create Spatie roles
        $this->createRole($request, $companyRecordId);
        $role = Role::where('company_id', $companyRecordId)->first();
        $roleId = $role->id;
        $this->createSuperAdminRole($companyRecordId);
        $superAdminRoleId = Role::where('company_id', $companyRecordId)->where('name', 'Super Admin')->first()?->id;

        $this->createWorkerRole($companyRecordId);
        $workerRole = Role::where('company_id', $companyRecordId)->where('name', 'worker')->first();
        $workerRoleId = $workerRole->id;

        // Create departments and titles and get main title ID
        $titles = $this->createDepartmentsWithTitles($request, $companyRecordId, $roleId, $workerRoleId);
        $mainTitleId = $titles['main_title_id'];

        $area = $this->createArea($request, $companyRecordId);
        $areaId = $area->id;

        $branch = $this->createBranch($request, $companyRecordId, $areaId);
        $branchId = $branch->id;

        $this->createSystemSettings($request, $companyRecordId);

        $this->createTimecardType($request, $companyRecordId);

        $this->assignPermissions($role);

        $this->createScopes($request, $companyRecordId, $roleId, $workerRoleId, $superAdminRoleId);
        // Use the mainTitleId for employee creation
        $employee = $this->createEmployee($request, $companyRecordId, $branchId, $mainTitleId);
        $employeeId = $employee->id;

        $user = $this->createUser($request, $companyRecordId, $employeeId);
        $userId = $user->id;

        $this->createRegistrationValidation($request, $companyRecordId, $userId);

        $this->assignRoleToUser($roleId, $userId);

        $this->assignBranchToEmployee($employeeId, $branchId);

        $this->createEmployeeInfo($request, $employeeId);

        $this->createDefaultExportTemplates($companyRecordId);
        // Setup leave types for the company
        $this->leaveTypesSetupService->setup(true, true, $companyRecordId);

        return [
            'company_id' => $companyRecordId,
            'title_ids' => $titles['title_ids'],
            'branch_id' => $branchId,
        ];
    }

    private function generateUniqueCompanyCode()
    {
        do {
            $code = str_pad(mt_rand(0, 9999), 4, '0', STR_PAD_LEFT);
        } while ($this->repository->findByKey('company_code', $code)->count() > 0);

        return $code;
    }

    private function createCompanyRecord($request)
    {
        $data = [
            'name' => $request['company']['name_ar'],
            'industry_id' => $request['industries']['id'],
            'company_code' => $this->generateUniqueCompanyCode(),
            'monthly_disbursement_day' => $request['company']['monthly_disbursement_day'],
            'tax_exemption' => 0,
            'tax_equation' => 0,
            'is_ready' => 1,
            'name_en' => $request['company']['name_en'],
            'name_ar' => $request['company']['name_ar'],
            'status' => 'active',
            'default_probation_period' => $request['company']['default_probation'],
            'default_working_hours' => $request['company']['default_working_hours'],
            'country_id' => $request['company']['country_id'],

            'use_foodics' => 0,
        ];
        if (isset($request['company']['payroll_setup_state'])) {
            $data['payroll_setup_state'] = $request['company']['payroll_setup_state'];
        }

        // Only add monthly_end_day if not using dynamic_closing_day
        if (! $request['company']['dynamic_closing_day']) {
            $data['monthly_end_day'] = $request['company']['monthly_end_day'] ?? 30;
        }

        return $this->repository->add($data);
    }

    private function createDepartmentsWithTitles($request, $companyRecordId, $roleId, $workerRoleId)
    {
        $mainTitleId = null;
        $titleIds = [];

        $titleNames = [];
        foreach ($request['departments'] as $departmentData) {
            $department = $this->departmentRepository->firstOrCreate([
                'name' => $departmentData['name_ar'],
                'company_id' => $companyRecordId,
                'name_en' => $departmentData['name_en'],
                'name_ar' => $departmentData['name_ar'],
            ]);

            // Assign titles to the department
            if (isset($departmentData['titles'])) {
                foreach ($departmentData['titles'] as $titleData) {
                    $titleNames[] = $titleData['name_ar'];
                    $title = $this->titleRepository->firstOrCreate([
                        'name' => $titleData['name_ar'],
                        'company_id' => $companyRecordId,
                        'name_en' => $titleData['name_en'],
                        'name_ar' => $titleData['name_ar'],
                    ], [
                        'color' => '#32CD32',
                        'department_id' => $department->id,
                        'is_higher_management' => $titleData['is_main'] ? 1 : 0,
                        'probation_period' => $request['company']['default_probation'],
                        'working_hours' => $request['company']['default_working_hours'],
                        'role_id' => $titleData['is_main'] ? $roleId : $workerRoleId,
                    ]);
                    $titleIds[] = $title->id;

                    // Capture the main title ID
                    if ($titleData['is_main']) {
                        $mainTitleId = $title->id;
                    }
                }
            }

            if (isset($departmentData['sub_departments'])) {
                foreach ($departmentData['sub_departments'] as $subDepartmentData) {
                    $subDepartment = $this->subDepartmentRepository->firstOrCreate([
                        'company_id' => $companyRecordId,
                        'name_en' => $subDepartmentData['name_en'],
                        'name_ar' => $subDepartmentData['name_ar'],
                    ], [
                        'department_id' => $department->id,
                    ]);
                    if (isset($subDepartmentData['sub_titles'])) {
                        foreach ($subDepartmentData['sub_titles'] as $subTitleData) {
                            if (isset($titleNames[$subTitleData['name_ar']])) {
                                continue;
                            }
                            $titleNames[] = $subTitleData['name_ar'];
                            $subTitle = $this->titleRepository->firstOrCreate([
                                'name' => $subTitleData['name_ar'],
                                'company_id' => $companyRecordId,
                                'name_en' => $subTitleData['name_en'],
                                'name_ar' => $subTitleData['name_ar'],
                            ], [
                                'color' => $subTitleData['color'],
                                'department_id' => $department->id,
                                'sub_department_id' => $subDepartment->id,
                                'probation_period' => $subTitleData['probation'],
                                'working_hours' => $subTitleData['working_hours'],
                                'role_id' => $workerRoleId,
                            ]);
                            $titleIds[] = $subTitle->id;
                        }
                    }
                }
            }
        }

        return [
            'main_title_id' => $mainTitleId,
            'title_ids' => $titleIds,
        ];
    }

    private function createRole($request, $companyRecordId)
    {
        return $this->newRoleRepository->insert([
            'name' => $request['spatie_roles']['name_ar'],
            'company_id' => $companyRecordId,
            'guard_name' => 'user-api',
            'name_en' => $request['spatie_roles']['name_en'],
            'name_ar' => $request['spatie_roles']['name_ar'],
            'is_system_role' => 0,
        ]);
    }

    private function createSuperAdminRole($companyRecordId)
    {
        return $this->newRoleRepository->insert([
            'name' => 'Super Admin',
            'company_id' => $companyRecordId,
            'guard_name' => 'user-api',
            'name_en' => 'Super Admin',
            'name_ar' => 'سوبر ادمن',
            'is_system_role' => 1,
            'is_super_admin' => 1,
        ]);
    }

    private function createArea($request, $companyRecordId)
    {
        return $this->areaRepository->add([
            'name' => $request['areas']['name_ar'],
            'company_id' => $companyRecordId,
            'name_en' => $request['areas']['name_en'],
            'name_ar' => $request['areas']['name_ar'],
        ]);
    }

    private function createBranch($request, $companyRecordId, $areaId)
    {
        return $this->branchRepository->add([
            'name' => $request['branches']['name_ar'],
            'company_id' => $companyRecordId,
            'location' => $request['branches']['location'],
            'name_en' => $request['branches']['name_en'],
            'name_ar' => $request['branches']['name_ar'],
            'work_days' => json_encode(['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY']),
            'radius' => $request['branches']['radius'],
            'area_id' => $areaId,
            'start_day' => $request['branches']['start_day'],
        ]);
    }

    private function createSystemSettings($request, $companyRecordId)
    {
        if (! $request['company']['dynamic_closing_day']) {
            $this->systemSettingRepository->add([
                'company_id' => $companyRecordId,
                'key' => 'payroll_monthly_closing_day',
                'value' => $request['company']['monthly_end_day'] ?? null,
                'as_of_date' => date('Y-m-d'),
            ]);
        }

        $this->systemSettingRepository->add([
            'company_id' => $companyRecordId,
            'key' => 'payroll_disbursement_day',
            'value' => $request['company']['monthly_disbursement_day'],
            'as_of_date' => date('Y-m-d'),
        ]);

        $this->systemSettingRepository->add([
            'company_id' => $companyRecordId,
            'key' => 'allow_service_charge',
            'value' => 0,
            'as_of_date' => date('Y-m-d'),
        ]);

        $this->systemSettingRepository->add([
            'company_id' => $companyRecordId,
            'key' => 'employee_code_type',
            'value' => $request['employee_code_type'],
            'as_of_date' => date('Y-m-d'),
        ]);

        if ($request['company']['dynamic_closing_day']) {
            $this->systemSettingRepository->add([
                'as_of_date' => date('Y-m-d'),
                'key' => BasicInfoUtil::APPLY_DYNAMIC_CLOSING_DAY,
                'value' => 1,
                'company_id' => $companyRecordId,
            ]);
        }
    }

    private function createTimecardType($request, $companyRecordId)
    {
        $this->timecardTypeRepository->create([
            'name' => 'regular',
            'company_id' => $companyRecordId,
            'is_default' => 1,
        ]);
    }

    private function assignPermissions($role)
    {
        $permissionAddEmployee = Permission::all()->pluck('id')->toArray();
        $role->syncPermissions($permissionAddEmployee);

    }

    private function createScopes($request, $companyRecordId, $roleId, $workerRoleId, $superAdminRoleId)
    {
        $scopes = [
            [
                'key' => 'me',
                'name_ar' => 'نطاق فردى',
                'name_en' => 'Me Scope',
                'description_ar' => 'نطاق فردى',
                'description_en' => 'Me Scope',
            ],
            [
                'key' => 'branch',
                'name_ar' => 'نطاق الفروع',
                'name_en' => 'Branch Scope',
                'description_ar' => 'نطاق الفروع',
                'description_en' => 'Branch Scope',
            ],
            [
                'key' => 'sub_department',
                'name_ar' => 'نطاق الإدارات الفرعية',
                'name_en' => 'Sub Department Scope',
                'description_ar' => 'نطاق الإدارات الفرعية',
                'description_en' => 'Sub Department Scope',
            ],
            [
                'key' => 'department',
                'name_ar' => 'نطاق الإدارات',
                'name_en' => 'Department Scope',
                'description_ar' => 'نطاق الإدارات',
                'description_en' => 'Department Scope',
            ],
            [
                'key' => 'company',
                'name_ar' => 'نطاق الشركة',
                'name_en' => 'Company Scope',
                'description_ar' => 'نطاق الشركة',
                'description_en' => 'Company Scope',
            ],
        ];

        foreach ($scopes as $scope) {
            $createdScope = $this->scopeRepository->add([
                'company_id' => $companyRecordId,
                'key' => $scope['key'],
                'name_ar' => $scope['name_ar'],
                'name_en' => $scope['name_en'],
                'description_ar' => $scope['description_ar'],
                'description_en' => $scope['description_en'],
            ]);

            if ($scope['key'] == 'company') {
                $scopeId = $createdScope->id;
                RoleScope::create([
                    'role_id' => $roleId,
                    'scope_id' => $scopeId,
                ]);
                RoleScope::create([
                    'role_id' => $superAdminRoleId,
                    'scope_id' => $scopeId,
                ]);
            }
            if ($scope['key'] == 'me') {
                RoleScope::create([
                    'role_id' => $workerRoleId,
                    'scope_id' => $createdScope->id,
                ]);
            }
        }
    }

    private function createEmployee($request, $companyRecordId, $branchId, $titleId)
    {
        $countryCode = $request['employee']['country_code'] ?? '+20';

        $formattedPhone = $countryCode.
            (str_starts_with($request['employee']['phone'], '0') && $countryCode == '+20'
                ? substr($request['employee']['phone'], 1)
                : $request['employee']['phone']);

        return $this->employeeRepository->add([
            'employee_number' => $request['employee']['employee_number'] ?? '0001',
            'first_name' => $request['employee']['first_name_ar'],
            'second_name' => $request['employee']['second_name_ar'],
            'third_name' => $request['employee']['third_name_ar'],
            'fourth_name' => $request['employee']['fourth_name_ar'] ?? null,
            'fifth_name' => $request['employee']['fifth_name_ar'] ?? null,
            'first_name_en' => $request['employee']['first_name_en'],
            'second_name_en' => $request['employee']['second_name_en'],
            'third_name_en' => $request['employee']['third_name_en'],
            'fourth_name_en' => $request['employee']['fourth_name_en'] ?? null,
            'fifth_name_en' => $request['employee']['fifth_name_en'] ?? null,
            'first_name_ar' => $request['employee']['first_name_ar'],
            'second_name_ar' => $request['employee']['second_name_ar'],
            'third_name_ar' => $request['employee']['third_name_ar'],
            'fourth_name_ar' => $request['employee']['fourth_name_ar'] ?? null,
            'fifth_name_ar' => $request['employee']['fifth_name_ar'] ?? null,
            'name' => $request['employee']['first_name_ar'].' '.$request['employee']['second_name_ar'].' '.$request['employee']['third_name_ar'],
            'name_ar' => $request['employee']['first_name_ar'].' '.$request['employee']['second_name_ar'].' '.$request['employee']['third_name_ar'],
            'name_en' => $request['employee']['first_name_en'].' '.$request['employee']['second_name_en'].' '.$request['employee']['third_name_en'],
            'phone' => $formattedPhone,
            'national_id' => $request['employee']['nid'] ?? null,
            'gender' => $request['employee']['gender'],
            'on_probation' => 0,
            'title_id' => $titleId,
            'status' => 'active',
            'branch_id' => $branchId,
            'company_id' => $companyRecordId,
            'is_trackable' => $request['employee']['is_trackable'],
            'enable_impersonation' => 0,
            'work_hours_day' => 8,
        ]);
    }

    private function createUser($request, $companyRecordId, $employeeId)
    {
        return $this->userRepository->create([
            'name' => $request['employee']['first_name_ar'].' '.$request['employee']['second_name_ar'].' '.$request['employee']['third_name_ar'],
            'is_admin' => 0,
            'password' => bcrypt('123456'),
            'company_id' => $companyRecordId,
            'employee_id' => $employeeId,
        ]);
    }

    private function createRegistrationValidation($request, $companyRecordId, $userId)
    {
        RegisterationValidation::create([
            'name' => $request['employee']['first_name_ar'].' '.$request['employee']['second_name_ar'].' '.$request['employee']['third_name_ar'],
            'phone' => $request['employee']['phone'],
            'verification_code' => '1111',
            'is_verified_phone' => 1,
            'is_verified_email' => 1,
            'expire_date' => '2027-12-31',
            'company_id' => $companyRecordId,
            'user_id' => $userId,
        ]);
    }

    private function assignRoleToUser($roleId, $userId)
    {
        ModelHasRole::create([
            'role_id' => $roleId,
            'model_id' => $userId,
            'model_type' => 'App\Models\User',
        ]);
    }

    private function assignBranchToEmployee($employeeId, $branchId)
    {
        BranchEmployee::create([
            'employee_id' => $employeeId,
            'branch_id' => $branchId,
        ]);
    }

    private function createEmployeeInfo($request, $employeeId)
    {
        $this->employeeInfoRepository->add([
            'employee_id' => $employeeId,
            'join_date' => $request['employee_infos']['join_date'],
            'gender' => $request['employee']['gender'],
            'work_hours_day' => 8,
            'nationality' => $request['employee']['nationality'],
            'passport_number' => $request['employee']['passport_number'] ?? null,
        ]);
    }

    public function createWorkerRole($companyRecordId)
    {
        return $this->newRoleRepository->insert([
            'name' => 'worker',
            'company_id' => $companyRecordId,
            'guard_name' => 'user-api',
            'name_en' => 'Worker',
            'name_ar' => 'عامل',
            'is_system_role' => 0,
        ]);
    }

    public function addCompanySettings($companyId)
    {
        $attendanceSettings = [
            ['key' => 'apply_overtime', 'value' => 0, 'description' => 'apply_overtime', 'company_id' => $companyId, 'is_used' => 1],
            ['key' => 'automated_clock_out', 'value' => 720, 'company_id' => $companyId, 'description' => 'apply_automated_clock_out', 'is_used' => 1],
            ['key' => 'allowed_minutes_to_check_in_before', 'value' => 60, 'company_id' => $companyId, 'description' => 'allowed_minutes_to_check_in_before', 'is_used' => 1],
            ['key' => 'allowed_minutes_to_clock_out_early', 'value' => 15, 'company_id' => $companyId, 'description' => 'allowed_minutes_to_clock_out_early', 'is_used' => 1],
            ['key' => 'clock_out_deadline', 'value' => 15, 'company_id' => $companyId, 'description' => 'clock_out_deadline', 'is_used' => 1],
            ['key' => 'apply_late_deduction', 'value' => 0, 'company_id' => $companyId, 'description' => 'apply_late_deduction', 'is_used' => 1],
            ['key' => 'apply_absence_deduction', 'value' => 15, 'company_id' => $companyId, 'description' => 'apply_absence_deduction', 'is_used' => 1],

        ];

        $this->newAttendanceSettingRepository->createMany($attendanceSettings);
    }

    public function listCompanies($data)
    {
        $search = $data['search'] ?? null;
        $pageSize = $data['page_size'];

        return $this->repository->getCompaniesWithEmployeesCount($search, $pageSize);
    }

    public function getIndustries()
    {
        return $this->industryRepository->all();
    }

    public function importAreasBranches($data)
    {
        $companyId = $data['company_id'];
        $company = $this->repository->find($companyId);
        $existingAreas = $company->areas->pluck('name_en', 'id')->toArray();
        $import = new AreasBranchesImport($companyId, $existingAreas);
        Excel::import($import, $data->file('file'));
    }

    public function importDeptTitles($data)
    {
        $companyId = $data['company_id'];
        $import = new DeptTitlesImport($companyId);
        Excel::import($import, $data->file('file'));
    }

    public function getCountries()
    {
        return $this->countriesRepository->all();
    }

    private function createDefaultExportTemplates($companyId)
    {
        $unleash = app(Unleash::class);

        if (!$unleash->isEnhancedEmployeeProfileEnabled()) {
            return;
        }

        $this->createExportTemplatesForCompany($companyId);
    }

    public function createExportTemplatesForCompany($companyId)
    {
        foreach (EmployeeExportPresetTemplateEnum::cases() as $presetTemplate) {
            $templateName = EmployeeExportPresetTemplateEnum::getLabel($presetTemplate);

            $existingTemplate = EmployeeExportTemplate::where('name', $templateName)
                ->where('company_id', $companyId)
                ->first();

            if ($existingTemplate) {
                continue;
            }

            $fields = [];
            $presetCategories = EmployeeExportPresetTemplateEnum::getCategories($presetTemplate);

            foreach ($presetCategories as $category) {
                $categoryFields = EmployeeExportFieldEnum::getFieldsForCategory($category);
                $fields[$category->value] = [];

                foreach ($categoryFields as $field) {
                    $fields[$category->value][$field] = true;
                }
            }

            $this->employeeExportTemplateRepository->add([
                'name' => $templateName,
                'is_default' => true,
                'fields' => $fields,
                'company_id' => $companyId,
            ]);
        }
    }
}
