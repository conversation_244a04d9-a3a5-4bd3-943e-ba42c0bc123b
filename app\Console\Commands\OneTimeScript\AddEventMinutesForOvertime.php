<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Title;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddEventMinutesForOvertime extends Command
{
    protected $signature = 'event-minutes:add 
        {company_id : ID of the company}
        {event_name : Name of the event}
        {start_date : Start date (Y-m-d)}
        {end_date : End date (Y-m-d)}
        {working_minutes : Working minutes (non-negative integer}';

    protected $description = 'Add event for all company titles with datetime validation';

    public function handle()
    {
        // Retrieve arguments
        $companyId = $this->argument('company_id');
        $eventName = trim($this->argument('event_name'));
        $rawStartDate = $this->argument('start_date');
        $rawEndDate = $this->argument('end_date');
        $workingMinutesArg = $this->argument('working_minutes');

        // Validate company exists
        if (! DB::table('companies')->where('id', $companyId)->exists()) {
            $this->error("Company with ID $companyId does not exist.");

            return 1;
        }

        // Validate event name
        if (empty($eventName)) {
            $this->error('Event name cannot be empty.');

            return 1;
        }

        // Validate and parse dates
        try {
            $startDate = Carbon::parse($rawStartDate)->startOfDay();
            $endDate = Carbon::parse($rawEndDate)->endOfDay();
        } catch (\Exception $e) {
            $this->error('Invalid date format. Please use Y-m-d format.');

            return 1;
        }

        if ($startDate->gt($endDate)) {
            $this->error('Start date must be before or equal to end date.');

            return 1;
        }

        // Validate working minutes
        if (! ctype_digit((string) $workingMinutesArg) || $workingMinutesArg < 0) {
            $this->error('Working minutes must be a non-negative integer.');

            return 1;
        }
        $workingMinutes = (int) $workingMinutesArg;

        // Retrieve titles
        $titles = Title::where('company_id', $companyId)->get();

        if ($titles->isEmpty()) {
            $this->error("No titles found for company ID: $companyId");

            return 1;
        }

        // Prepare data with progress bar
        $data = [];
        $progressBar = $this->output->createProgressBar($titles->count());
        $this->info("\nPreparing data for {$titles->count()} titles...");
        $progressBar->start();

        foreach ($titles as $title) {
            $data[] = [
                'title_id' => $title->id,
                'event_name' => $eventName,
                'start_date' => $startDate->toDateTimeString(),
                'end_date' => $endDate->toDateTimeString(),
                'working_minutes' => $workingMinutes,
            ];
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // Show confirmation with formatted dates
        $this->info('You are about to add the following event:');
        $this->table(
            ['Field', 'Value'],
            [
                ['Company ID', $companyId],
                ['Event Name', $eventName],
                ['Start Date', $startDate->format('Y-m-d H:i:s')],
                ['End Date', $endDate->format('Y-m-d H:i:s')],
                ['Working Minutes', $workingMinutes],
                ['Affected Titles', $titles->count()],
            ]
        );

        if (! $this->confirm('Do you want to proceed with these changes?', false)) {
            $this->info('Operation cancelled.');

            return 0;
        }

        // Execute transaction
        DB::beginTransaction();
        try {
            $this->info("\nInserting records...");
            DB::table('event_titles_minutes')->insert($data);
            DB::commit();

            $this->newLine();
            $this->info(count($data).' records inserted successfully!');

            return 0;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error inserting records: '.$e->getMessage());

            return 1;
        }
    }
}
