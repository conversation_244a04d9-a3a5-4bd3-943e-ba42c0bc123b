<?php

namespace App\CsvEntity;

use App\FeatureToggles\Unleash;
use App\Traits\DateTimeHelper;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

class EmployeeCsvEntity implements SkipsEmptyRows, WithCustomValueBinder, WithHeadingRow, WithHeadings
{
    use DateTimeHelper;

    protected $codeType;

    private $unleash;

    public function setEmployeeCodeType(string $employeeCodeType): void
    {
        $this->codeType = $employeeCodeType;
        $this->unleash = app(Unleash::class);
    }

    public function headings(): array
    {
        $headings = [];

        if ($this->codeType === 'manual') {
            $headings[] = 'Code';
        }

        $headings = array_merge($headings, [
            'Employee First Name',
            'Employee Second Name',
            'Employee Third Name',
            'Employee Fourth Name',
            'Employee Fifth Name',
            'National ID #',
            'Country Code',
            'Phone Number',
            'Title',
            'Main Branch',
            'Additional Branches',
            'Direct Manager',
            'Hiring Date',
            'Gender',
            'Number of Years of Experience',
            'Birth Date',
            'Address',
            'Email',
            'Passport Number',
            'Secondary Phone',
            'Nationality',
            'Military Status',
        ]);

        return [$headings];
    }

    // Importing
    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
    }

    public function AppendToEmployeeArray($inputArray): array
    {
        $targetEmployee = [];

        if ($this->codeType === 'manual') {
            $targetEmployee['employee_number'] = (string)$inputArray['code'];
        }
        $phone = $inputArray['phone_number'];
        if (!str_starts_with($phone, '0') && auth()->user()->company->country->dial_code == '+20') {
            $phone = '0' . $phone;
        }

        $targetEmployee = array_merge($targetEmployee, [
            'first_name_ar' => $inputArray['employee_first_name_arabic'],
            'second_name_ar' => $inputArray['employee_second_name_arabic'],
            'third_name_ar' => $inputArray['employee_third_name_arabic'],
            'fourth_name_ar' => $inputArray['employee_fourth_name_arabic'],
            'fifth_name_ar' => $inputArray['employee_fifth_name_arabic'],
            'first_name_en' => $inputArray['employee_first_name_english'],
            'second_name_en' => $inputArray['employee_second_name_english'],
            'third_name_en' => $inputArray['employee_third_name_english'],
            'fourth_name_en' => $inputArray['employee_fourth_name_english'],
            'fifth_name_en' => $inputArray['employee_fifth_name_english'],
            'national_id' => $inputArray['national_id'],
            'country_code' => $inputArray['country_code'],
            'phone' => $phone,
            'title' => $inputArray['title'],
            'branch' => $inputArray['main_branch'],
            'address' => $inputArray['address'],
            'join_date' => $this->parseDateFromExcel($inputArray['hiring_date']),
            'career_start_date' => $this->parseDateFromExcel($inputArray['experience_start_date']),
            'gender' => $inputArray['gender'],
            'number_of_years_of_experience' => $inputArray['years_of_experience'],
            'birth_date' => $this->parseDateFromExcel($inputArray['birth_date']),
            'email' => $inputArray['email'],
            'passport_number' => $inputArray['passport_number'],
            'secondary_phone' => $inputArray['secondary_phone'],
            'nationality' => $inputArray['nationality'],
            'military_status' => $inputArray['military_status'],
            'notes' => $inputArray['notes'],
            'training_certification_status' => $inputArray['training_certification_status'] ?? null,
        ]);

        return $targetEmployee;
    }
}
