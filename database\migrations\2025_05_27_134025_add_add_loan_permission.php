<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use App\Models\PermissionGroup;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $loansGroup = PermissionGroup::where('name_en', 'Loans')->first();

        if ($loansGroup) {
            Permission::create([
                'name' => 'add_loan',
                'guard_name' => 'user-api',
                'description_en' => 'Add Loan for Employee',
                'description_ar' => 'إضافة قرض للموظف',
                'name_ar' => 'إضافة قرض للموظف',
                'name_en' => 'Add Loan for Employee',
                'permission_group_id' => $loansGroup->id,
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Permission::where('name', 'add_loan')
            ->where('guard_name', 'user-api')
            ->delete();
    }
};
