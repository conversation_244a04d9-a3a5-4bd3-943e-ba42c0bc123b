<?php

namespace App\Traits;

use App\FeatureToggles\Unleash;
use App\Models\Attendance;
use App\Models\EmployeeRequest;
use App\Models\Role;
use App\Models\StateMachines\LoanRequest\RequestReadyToDisburse;
use App\Models\StateMachines\RequestApproved;
use App\Models\StateMachines\RequestCancelled;
use App\Models\StateMachines\RequestRejected;
use App\Models\StateMachines\WaiveRequest\RequestWaived;
use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use App\Util\ScopeUtil;
use App\Workflows\ApprovalWorkflow;
use App\Workflows\EditAttendanceDeductionWorkflow;
use App\Workflows\ExtraWorkdayWorkflow;
use App\Workflows\LeaveWorkflow;
use App\Workflows\LoanAndSalaryAdvanceWorkflow;
use App\Workflows\MissionWorkflow;
use App\Workflows\OvertimeWorkflow;
use App\Workflows\ProbationWorkflow;
use App\Workflows\RequestOvertimeWorkflow;
use App\Workflows\TerminationWorkflow;
use App\Workflows\WaiveWorkflow;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Workflow\WorkflowStub;

trait WorkflowTrait
{
    private $userAction;

    private $request;

    public function initializeRequestWorkflow($request, $employee, $requestType, $workflowType, $requesterRoleIds = [], $timecardsToBeDeleted = [])
    {

        $workflowData = $this->prepareApprovalWorkflowData($request, $employee, $requestType, $workflowType, $requesterRoleIds);

        if ($workflowType == config('globals.REQUEST_WORKFLOW_TYPES.WAIVE_LATE_DEDUCTION')
            && $requestType == config('globals.REQUEST_CYCLE_TYPES.WAIVE_LATE_DEDUCTION')) {
            $workflowData['auto_approve'] = $this->canAutoApprove($employee);
            $workflow = WorkflowStub::make(WaiveWorkflow::class);
        } elseif ($workflowType == config('globals.REQUEST_WORKFLOW_TYPES.LEAVE')) {
            $workflowData['auto_approve'] = $this->canAutoApprove($employee);
            $unleash = app(Unleash::class);
            if ($unleash->isLeaveBalanceChangesFlagEnabled()) {
                $workflowData['is_force_approve'] = ($request->company_leave_type_id == $request->employee->company?->emergencyLeaveType?->id) ?? false; // approve the request without an approval cycle
            }
            $workflow = WorkflowStub::make(LeaveWorkflow::class);
            $workflow->setTimecards($timecardsToBeDeleted);
        } elseif ($workflowType == config('globals.REQUEST_WORKFLOW_TYPES.EDIT_ATTENDANCE_DEDUCTION')
            && $requestType == config('globals.REQUEST_CYCLE_TYPES.EDIT_ATTENDANCE_DEDUCTION')
        ) {
            $workflowData['auto_approve'] = $this->canAutoApprove($employee);
            $workflow = WorkflowStub::make(EditAttendanceDeductionWorkflow::class);
        } elseif ($workflowType == config('globals.REQUEST_WORKFLOW_TYPES.ATTENDANCE_OVERTIME')
        ) {
            $workflow = WorkflowStub::make(OvertimeWorkflow::class);
        } elseif ($workflowType == config('globals.REQUEST_WORKFLOW_TYPES.TERMINATION')) {
            $workflowData['auto_approve'] = $this->canAutoApprove($employee);
            $workflow = WorkflowStub::make(TerminationWorkflow::class);
        } elseif ($workflowType == config('globals.REQUEST_WORKFLOW_TYPES.LOAN') || $workflowType == config('globals.REQUEST_WORKFLOW_TYPES.SALARY_ADVANCE')) {
            //            $workflowData['auto_approve'] = true;
            $workflow = WorkflowStub::make(LoanAndSalaryAdvanceWorkflow::class);
        } elseif ($workflowType == config('globals.REQUEST_WORKFLOW_TYPES.EXTRA_WORKDAY')) {
            $workflow = WorkflowStub::make(ExtraWorkdayWorkflow::class);
        } elseif ($workflowType == config('globals.REQUEST_WORKFLOW_TYPES.MISSIONS')) {
            $workflow = WorkflowStub::make(MissionWorkflow::class);
        } elseif ($workflowType == config('globals.REQUEST_WORKFLOW_TYPES.OVERTIME_REQUEST')) {
            $workflow = WorkflowStub::make(RequestOvertimeWorkflow::class);
        } elseif ($workflowType == config('globals.REQUEST_WORKFLOW_TYPES.PROBATION')) {
            $workflow = WorkflowStub::make(ProbationWorkflow::class);
        } else {
            $workflow = WorkflowStub::make(ApprovalWorkflow::class);
        }
        $workflow->start($workflowData);
    }

    public function linkWorkflowWithRequest($request, $workflowId)
    {
        Log::info('linkWorkflowWithRequest');
        $request->workflow_id = $workflowId;
        $request->save();
    }

    public function prepareApprovalWorkflowData($request, $employee, $requestType, $workflowType, $requesterRoleIds)
    {

        $employeeRequest = EmployeeRequest::where('requestable_id', $request->id)
            ->where('requestable_type', $workflowType)
            ->first();
        $attendanceDate = Carbon::parse($request->extra_work_day_date)->format('Y-m-d');
        $attendance = Attendance::where('date', $attendanceDate)
            ->where('extra_work_day_status', 'pending')
            ->where('employee_id', $employee->id)
            ->first();

        return [
            'request' => $request,
            'employee_request' => $employeeRequest ?? null,
            'employee_id' => $employee->id,
            'title_id' => $employee->title->id,
            'request_type' => $requestType,
            'workflow_type' => $workflowType,
            'requester_role_ids' => $requesterRoleIds,
            'attendance' => $attendance,
        ];
    }

    public function canTakeAnAction($roleIds, $requestId, $requestType, $actionType = null)
    {
        $canTakeAnAction = false;
        $previousAction = true;
        $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle([
            'requestable_id' => $requestId,
            'requestable_type' => $requestType,
        ]);
        Log::info('approvalCycle: '.json_encode($approvalCycle));
        Log::info('roleIds: '.json_encode($roleIds));
        Log::info('requestId: '.$requestId);
        Log::info('requestType: '.$requestType);
        Log::info('actionType: '.$actionType);
        foreach ($approvalCycle as $approval) {
            // in case the employee has the power of the OR operator
            if ($actionType == 'cancel' && in_array($approval->role_id, $roleIds)) {
                if ($approval->status == config('globals.REQUEST_STATUSES.APPROVED')) {
                    $canTakeAnAction = true;
                    $this->userAction = $approval;
                } else {
                    $canTakeAnAction = false;
                }

                break;
            }

            if ($actionType != 'cancel') {

                if (in_array($approval->role_id, $roleIds) && $approval->operator == 'or') {
                    $canTakeAnAction = true;
                    $this->userAction = $approval;
                    Log::info('first condition');
                    break;
                } // in case the employee's role exists in the approval cycle
                elseif (in_array($approval->role_id, $roleIds) && $previousAction && $approval->operator == 'then') {
                    $this->userAction = $approval;
                    $canTakeAnAction = true;
                    Log::info('second condition');
                } // the next two conditions to check on previous actions
                elseif (! in_array($approval->role_id, $roleIds) && $approval->operator == 'then' && $approval->status != config('globals.REQUEST_STATUSES.PENDING')) {
                    $previousAction = true;
                    Log::info('third condition');
                } elseif (! in_array($approval->role_id, $roleIds) && $approval->operator == 'then' && $approval->status == config('globals.REQUEST_STATUSES.PENDING')) {
                    $previousAction = false;
                    Log::info('fourth condition');
                }

                // to check if any employee took action after the current employee, so we prevent him from changing his action
                if (! in_array($approval->role_id, $roleIds) && $approval->operator == 'then' &&
                    $approval->status != config('globals.REQUEST_STATUSES.PENDING') && $canTakeAnAction) {
                    $canTakeAnAction = false;
                    Log::info('fifth condition');
                }

            }
        }

        if ($this->isAuthorizedToForceApprove()) {
            $this->request = EmployeeRequest::where('requestable_type', $requestType)->where('requestable_id', $requestId)->first()->requestable;

            return true;
        }

        return $canTakeAnAction;
    }

    public function isAuthorizedToForceApprove()
    {
        $user = auth()->user();

        $isAuthorizedToForceApprove = config('globals.is_super_admin') || $user->hasPermissionTo('manage_all_requests');

        return $isAuthorizedToForceApprove;
    }

    private function hasSuperAdminRole($roleIds)
    {
        return Role::whereIn('id', $roleIds)->where('is_super_admin', 1)->exists();
    }

    public function employeeCanCancelLeaveForHimSelf($leaveId)
    {
        $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
        $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle([
            'requestable_id' => $leaveId,
            'requestable_type' => config('globals.REQUEST_WORKFLOW_TYPES.LEAVE'),
        ]);

        foreach ($approvalCycle as $approval) {
            if ($approval->status != config('globals.REQUEST_STATUSES.PENDING')) {
                return false;
            }
        }

        return true;
    }

    public function doAnAction($actionType)
    {
        if (isset($this->userAction)) {
            if ($actionType == 'approve') {
                $this->userAction->status->transitionTo(RequestApproved::class);
            } elseif ($actionType == 'reject') {
                $this->userAction->status->transitionTo(RequestRejected::class);
            } elseif ($actionType == 'cancel') {
                $this->userAction->status->transitionTo(RequestCancelled::class);
            }
            $this->userAction->decider_id = config('globals.user')?->employee_id;

            $this->userAction->save();
        }
    }

    private function forceUpdateRequestStatus($actionType, $value = null)
    {
        Log::info('inside forceUpdateRequestStatus request isset '.isset($this->request).' request id: '.$this->request?->id);
        if (! isset($this->request)) {
            return;
        }
        $statusMap = [
            'approve' => RequestApproved::class,
            'reject' => RequestRejected::class,
            'cancel' => RequestCancelled::class,
        ];

        Log::info('$$$$###'.class_basename($this->request->status).'!='.class_basename($statusMap[$actionType]));

        $isAttendanceDeduction = class_basename($this->request) === 'AttendanceDeduction';
        $isLoanOrSalaryAdvance = class_basename($this->request) === 'Loan' || class_basename($this->request) === 'SalaryAdvance';
        Log::info('isAttendanceDeduction: '.$isAttendanceDeduction.' class basename '.class_basename($this->request).' action is '.$actionType);
        Log::info('isLoanOrSalaryAdvance: '.$isLoanOrSalaryAdvance.' class basename '.class_basename($this->request).' action is '.$actionType);

        // ///////// update request status
        if ($isAttendanceDeduction && $actionType == 'approve' && $this->request->status != 'waived' && $value == 0) {
            $this->request->status->transitionTo(RequestWaived::class);
        } elseif ($isLoanOrSalaryAdvance && $actionType == 'approve' && $this->request->status != 'ready_to_disburse') {
            $this->request->status->transitionTo(RequestReadyToDisburse::class);
        } elseif (isset($statusMap[$actionType]) && class_basename($this->request->status) != class_basename($statusMap[$actionType])
        && ! $isAttendanceDeduction) {
            Log::info('not attendance deduction and not loan or salary advance and action is '.$actionType);
            $this->request->status->transitionTo($statusMap[$actionType]);
        }

        // /////////// update employee request status
        if (isset($this->request->employeeRequest)) {
            $this->request->employeeRequest->update([
                'decider_admin_id' => auth()->user()->employee_id,
                'decider_admin_action' => $actionType,
            ]);

        }

    }

    public function linkWorkflowWithAttendance($attendance, $workflowId)
    {
        if (isset($attendance)) {
            $attendance->extra_work_day_workflow_id = $workflowId;
            $attendance->save();
        }
    }

    public function canAutoApprove($employee)
    {
        $userScope = config('globals.scope_key');
        $branchId = $employee->branch_id;
        $departmentId = $employee->title->department_id;
        $subDepartmentId = $employee->title->sub_department_id;

        if ($userScope == ScopeUtil::COMPANY_SCOPE) {
            return true;
        } elseif ($userScope == ScopeUtil::BRANCH_SCOPE && in_array($branchId, config('globals.scope_branch_ids'))) {
            return true;
        } elseif ($userScope == ScopeUtil::DEPARTMENT_SCOPE &&
            in_array($departmentId, config('globals.scope_department_ids'))
            && in_array($branchId, config('globals.scope_branch_ids'))) {
            return true;
        } elseif ($userScope == ScopeUtil::SUB_DEPARTMENT_SCOPE &&
            in_array($subDepartmentId, config('globals.scope_sub_department_ids'))
            && in_array($branchId, config('globals.scope_branch_ids'))) {
            return true;
        }

        return false;
    }
}
