<?php

namespace App\Console\Commands;

use App\Models\AttendanceDeduction;
use App\Models\Workflow;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixPendingWorkflows extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-pending-workflows';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        try {
            DB::beginTransaction();

            function checkIsCompleted($requestWorkflowApprovals)
            {
                if ((count($requestWorkflowApprovals['or']) > 0 && ! in_array('pending', $requestWorkflowApprovals['or'])) ||
                (count($requestWorkflowApprovals['then']) > 0 && in_array('rejected', $requestWorkflowApprovals['then'])) ||
                (count($requestWorkflowApprovals['then']) > 0 && ! in_array('pending', $requestWorkflowApprovals['then']))) {
                    return true;
                } else {
                    return false;
                }
            }

            function getApprovals($approvalCycle)
            {
                $approvals = [
                    'then' => [],
                    'or' => [],
                ];

                foreach ($approvalCycle as $approval) {
                    if ($approval->operator == 'then') {
                        $approvals['then'][] = $approval->status;
                    } elseif ($approval->operator == 'or') {
                        $approvals['or'][] = $approval->status;
                    }
                }

                return $approvals;
            }

            function updateWorkflowStatus($workflow)
            {
                $workflow->status = 'completed';
                $workflow->save();
                echo 'Workflow id '.$workflow->id.' updated'."\n";
            }

            function updateEmployeeRequest($deduction)
            {
                $employeeRequest = $deduction->employeeRequests()->get();

                foreach ($employeeRequest as $request) {
                    $deductionStatus = (string) $deduction->status;
                    $request->status = $deductionStatus;
                    $request->save();
                    echo 'Employee request id '.$request->id.' updated for deduction: '.$deduction->id."\n";
                }
            }

            $deductions = AttendanceDeduction::select('id', 'status', 'company_id', 'workflow_id')
                ->whereHas(
                    'workflow', function ($query) {
                        $query->where('status', '!=', 'completed');
                    }
                )->get();

            echo 'Deductions count: '.$deductions->count()."\n";

            foreach ($deductions as $deduction) {
                $workflow = Workflow::select('id', 'status')
                    ->where('id', $deduction->workflow_id)
                    ->where('status', '!=', 'completed')
                    ->first();

                if (! $workflow) {
                    echo 'Workflow not found for deduction: '.$deduction->id."\n";

                    continue;
                }

                $requestWorkflowApprovals = getApprovals($deduction->workflowApprovalCycles);
                $isCompleted = checkIsCompleted($requestWorkflowApprovals);

                if ($isCompleted) {
                    updateWorkflowStatus($workflow);
                    updateEmployeeRequest($deduction);

                }
            }
            DB::commit();

        } catch (\Exception $e) {
            Db::rollBack();
            Log::info($e->getMessage());
        }

    }
}
