<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class DynamicCompanyDataExport implements FromCollection, ShouldAutoSize, WithHeadings, WithTitle
{
    private $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return collect($this->data);
    }

    public function headings(): array
    {
        return [
            'Company Branches',
            'Company Titles',
            'Nationality',
            'Military Status',
            'Gender',
            'Religion',
            'Marital Status',
            'Employment Type',
            'Degree Type',
            'Contract Duration',
        ];
    }

    public function title(): string
    {
        return 'Dropdown Values';
    }
}
