<?php

namespace App\Console\Commands;

use App\Models\EntityTag;
use App\Models\Timecard;
use Illuminate\Console\Command;

class FixTimeCardEntityTags extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:timeCard:entityTags';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * get all time cards that have entity tags and her attendance also have entity tags
     */
    public function handle(): void
    {
        $timeCardIds = TimeCard::query()
            ->join('attendances', function ($join) {
                $join->on('attendances.slotable_id', '=', 'timecards.id')
                    ->where('attendances.slotable_type', '=', 'time_card')
                    ->whereNull('attendances.deleted_at');
            })
            ->join('entity_tags as et1', function ($join) {
                $join->on('et1.entity_id', '=', 'timecards.id')
                    ->where('et1.entity_type', '=', 'time_card');
            })
            ->join('entity_tags as et2', function ($join) {
                $join->on('et2.entity_id', '=', 'attendances.id')
                    ->where('et2.entity_type', '=', 'attendance');
            })
            ->whereNull('timecards.deleted_at')
            ->pluck('timecards.id')
            ->unique()
            ->toArray();

        EntityTag::whereIn('entity_id', $timeCardIds)
            ->where('entity_type', 'time_card')
            ->delete();

    }
}
