<?php

namespace App\Traits\V1;

use App\Rules\PhoneNumberByCountryRule;
use App\Traits\V1\PhoneHelper;

trait PhoneValidationTrait
{
    use PhoneHelper;
    protected function validateEmergencyContactPhone(string $baseField, string $indexKey = '1'): \Closure
    {
        return function($attribute, $value, $fail) use ($baseField, $indexKey) {
            $parts = explode('.', $attribute);
            $index = $parts[$indexKey];
            $countryCode = $this->input("{$baseField}.{$index}.phone_country_code");

            if ($countryCode) {
                $validator = new PhoneNumberByCountryRule($countryCode);
                if (!$validator->passes($attribute, $value)) {
                    $fail(__('validation.phone_number_format', ['country_code' => $countryCode]));
                }
            }
        };
    }

    protected function validateUniqueEmergencyContacts(string $baseField): \Closure
    {
        return function($attribute, $items, $fail) {
            if (!is_array($items)) {
                return;
            }

            $phoneNumbers = [];
            foreach ($items as $item) {
                if (!isset($item['phone']) || !isset($item['phone_country_code'])) {
                    continue;
                }

                $fullPhone = $this->formatPhoneWithCountryCode($item['phone_country_code'], $item['phone']);

                if (in_array($fullPhone, $phoneNumbers)) {
                    $fail(__('validation.duplicate_emergency_contact', ['phone' => $fullPhone]));
                    return;
                }

                $phoneNumbers[] = $fullPhone;
            }
        };
    }
}
