<?php

namespace App\Http\Controllers\V1\Probation;

use App\Exceptions\UnprocessableException;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\RejectProbationRequest;
use App\Services\V1\Probation\ProbationService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProbationController extends NewController
{
    public function __construct(
        protected ProbationService $probationService
    )
    {
    }

    public function approveProbationRequest($probationId)
    {
        try {
            DB::transaction(function () use ($probationId) {
                $this->probationService->actionOnProbation($probationId, 'approve');
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'probation approved successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function rejectProbationRequest($probationId, RejectProbationRequest $request)
    {
        try {
            DB::transaction(function () use ($request, $probationId) {
                $this->probationService->actionOnProbation($probationId, 'reject', $request->validated());
            });

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'probation rejected successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }
}
