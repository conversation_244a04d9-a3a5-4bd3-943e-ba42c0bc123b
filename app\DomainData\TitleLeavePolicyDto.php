<?php

namespace App\DomainData;

trait TitleLeavePolicyDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'title_id' => 'numeric',
            'leave_policy_id' => 'numeric',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeTitleLeavePolicyDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
