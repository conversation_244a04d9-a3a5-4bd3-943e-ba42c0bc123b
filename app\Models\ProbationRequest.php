<?php

namespace App\Models;

use App\Models\Casts\TimezoneDateTime;
use App\Models\StateMachines\RequestState;
use App\Traits\CompanyRule;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProbationRequest extends BaseModel
{
    use CompanyRule, HasFactory, softDeletes;

    protected $table = 'probation_requests';

    protected $casts = [
        'created_at' => TimezoneDateTime::class,
        'updated_at' => TimezoneDateTime::class,
        'deleted_at' => TimezoneDateTime::class.':nullable',
        'status' => RequestState::class,
    ];

    protected $guarded = ['id', 'created_at', 'updated_at'];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($probationRequest) {
            EmployeeRequest::create([
                'date' => $probationRequest->probation_end_date,
                'employee_id' => $probationRequest->employee_id,
                'status' => $probationRequest->status,
                'comment' => null,
                'request_name' => 'probation_request',
                'requestable_id' => $probationRequest->id,
                'requestable_type' => 'probation_request',
                'requested_by' => null,
                'company_id' => $probationRequest->company_id,
            ]);
        });

    }

    /**
     * Relationship with the Company model.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Relationship with the Employee model.
     */
    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Relationship with the Workflow model.
     */
    public function workflow()
    {
        return $this->belongsTo(Workflow::class);
    }

    public function employeeRequest()
    {
        return $this->morphOne(EmployeeRequest::class, 'requestable')->latest('id');
    }

    public function employeeApproves()
    {
        return $this->morphToMany(Role::class, 'requestable', 'workflow_approval_cycle',
            'requestable_id', 'role_id')
            ->withPivot(['id', 'company_id', 'status', 'date', 'order', 'operator', 'requestable_type', 'request_workflow_id', 'branch_id', 'department_id'])
            ->withTimestamps();
    }

    public function workflowApprovalCycles()
    {
        return $this->morphMany(WorkflowApprovalCycle::class, 'requestable');
    }
}
