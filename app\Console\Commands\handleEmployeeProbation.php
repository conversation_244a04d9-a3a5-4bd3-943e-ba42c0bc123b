<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Services\V1\Probation\ProbationService;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class handleEmployeeProbation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:handle-employee-probation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handle employee probation and create probation requests as needed.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Log::info('Employee probation command started.');

        $probationService = app(ProbationService::class);

        $employees = Employee::where('on_probation', true)
            ->whereHas('title', function ($query) {
                $query->where('probation_period', '>', 0);
            })
            ->with(['title', 'employeeInfo', 'probationRequest', 'title.requestGroup'])
            ->get();

        Log::info('Fetched employees on probation.', ['count' => $employees->count()]);

        foreach ($employees as $employee) {
            Log::info('Checking employee', ['id' => $employee->id, 'name' => $employee->name]);

            if (!$employee->employeeInfo) {
                Log::warning('Skipped: employeeInfo missing.', ['employee_id' => $employee->id]);
                continue;
            }

            if (!$employee->title) {
                Log::warning('Skipped: title missing.', ['employee_id' => $employee->id]);
                continue;
            }

            if (in_array($employee->status, ['terminated', 'termination_pending'])) {
                Log::info('Skipped: employee status is not active.', ['employee_id' => $employee->id, 'status' => $employee->status]);
                continue;
            }

            $joinDate = $employee->employeeInfo->join_date ?? null;
            if (!$joinDate) {
                Log::warning('Skipped: join date missing.', ['employee_id' => $employee->id]);
                continue;
            }

            $endProbationDate = Carbon::parse($joinDate)
                ->addMonths($employee->title->probation_period)
                ->startOfDay();

            Log::info('Calculated probation end date.', [
                'employee_id' => $employee->id,
                'end_date' => $endProbationDate->toDateString()
            ]);

            $probationRequestExists = $employee->probationRequest()
                ->whereDate('probation_end_date', $endProbationDate)
                ->exists();

            if ($probationRequestExists) {
                Log::info('Skipped: probation request already exists.', ['employee_id' => $employee->id]);
                continue;
            }

            if (!isset($employee->title->requestGroup)) {
                Log::warning('Skipped: no request group found.', ['employee_id' => $employee->id]);
                continue;
            }

            $approvalCycleExists = $employee->title
                ->requestGroup
                ->requestWorkflows()
                ->where('type', 'probation_request')
                ->whereNull('deleted_at')
                ->exists();

            if (!$approvalCycleExists) {
                Log::warning('Skipped: no approval workflow found for probation.', ['employee_id' => $employee->id]);
                continue;
            }

            $oneWeekBeforeEnd = $endProbationDate->copy()->subWeek();

            if (now()->greaterThanOrEqualTo($oneWeekBeforeEnd) && now()->lessThan($endProbationDate)) {
                Log::info('Creating probation request.', ['employee_id' => $employee->id]);
                $probationService->createRequest($employee, $endProbationDate);
            } else {
                Log::info('Skipped: today is not one week before probation end.', [
                    'employee_id' => $employee->id,
                    'expected_date' => $oneWeekBeforeEnd->toDateString(),
                    'today' => now()->toDateString(),
                ]);
            }
        }

        Log::info('Employee probation command completed.');
        $this->info('Employee probation handling completed.');
    }

}
