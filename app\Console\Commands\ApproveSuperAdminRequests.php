<?php

namespace App\Console\Commands;

use App\Models\EmployeeRequest;
use App\Traits\V1\NotificationRedirection;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ApproveSuperAdminRequests extends Command
{
    use NotificationRedirection;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:approve-super-admin-requests {company_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update status of requests based on activity logs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("\n🚀 Starting status fix process for super admin requests...");
        $this->line('----------------------------------------');

        try {
            $companyId = $this->argument('company_id') ?? null;

            $requests = EmployeeRequest::whereNotNull('decider_admin_id')
                ->where('date', '>=', '2025-04-01')
                ->where('company_id', $companyId)
                ->where('requestable_type', 'attendance_overtime')
                ->where('status', 'pending')
                ->get();

            $counter = 0;
            DB::beginTransaction();

            foreach ($requests as $request) {

                if (empty($request->requestable)) {
                    $counter++;
                    $this->info('missing entity request id:'.$request->id);

                    continue;
                }

                $request->update(['status' => 'approved']);
                $request->requestable->update(['status' => 'approved']);

            }
            DB::commit();

            $this->info('✅ Fix complete!');
            $this->info("🔢 Total requests processed:   {$requests->count()}");
            $this->info("⚠️  Requests with missing entities: {$counter}");

        } catch (\Exception $e) {
            $this->error("\n❌ Error encountered: ".$e->getMessage());
            $this->warn('⏪ Rolling back database changes...');
            DB::rollBack();

            Log::error('FixRequestsStatus failed: '.$e->getMessage());

            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
