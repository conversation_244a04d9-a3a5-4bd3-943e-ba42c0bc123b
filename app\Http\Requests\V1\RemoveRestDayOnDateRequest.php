<?php

namespace App\Http\Requests\V1;

use App\Rules\EmployeeIdRule;
use Illuminate\Foundation\Http\FormRequest;

class RemoveRestDayOnDateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'employee_ids' => [
                'required',
                'array',
            ],
            'employee_ids.*' => [
                'required',
                'integer',
                new EmployeeIdRule,
            ],
            'date' => [
                'required',
                'date',
                'date_format:Y-m-d',
            ],
        ];
    }
}
