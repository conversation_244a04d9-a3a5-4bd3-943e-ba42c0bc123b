<?php

namespace App\Console\Commands\OneTimeScript;

use App\Exceptions\UnprocessableException;
use App\Models\Title;
use App\Repositories\NewCompanyLeaveTypePolicyRepository;
use App\Repositories\NewCompanyLeaveTypeRepository;
use App\Repositories\V1\Leaves\CompanyDefaultLeaveTypesRepository;
use App\Util\DefaultLeaveTypesUtil;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;

class AddOvertimeLeaveTypeForCompany extends Command
{
    public function __construct(
        private NewCompanyLeaveTypeRepository $newCompanyLeaveTypeRepository,
        private NewCompanyLeaveTypePolicyRepository $newCompanyLeaveTypePolicyRepository,
        private CompanyDefaultLeaveTypesRepository $companyDefaultLeaveTypesRepository
    ) {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:leaves:type:overtime {company_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'add overtime leave type for company';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        try {
            DB::transaction(function () {
                $overtimeLeaveType = [
                    'name' => 'Overtime leaves',
                    'name_en' => 'Overtime leaves',
                    'name_ar' => 'اجازه بدل العمل لوقت اضافي',
                    'is_primary' => 1,
                    'uuid' => str_replace('-', '', Uuid::uuid7()->toString()),
                    'balance_period' => config('globals.BALANCE_PERIODS.CALENDAR_YEAR'),
                    'gender' => 'all',
                    'company_id' => $this->argument('company_id'),
                ];

                $leaveType = $this->newCompanyLeaveTypeRepository->add($overtimeLeaveType);

                $overtimeLeavePolicy = [
                    'company_leave_type_id' => $leaveType->id,
                    'base_balance' => 0,
                    'unit' => 'days',
                    'is_probation_allowed' => 1,
                    'company_id' => $this->argument('company_id'),
                ];
                $leaveTypePolicy = $this->newCompanyLeaveTypePolicyRepository->add($overtimeLeavePolicy);

                $companyTitleIds = Title::all()->pluck('id')->toArray();
                $leaveTypePolicy->titles()->attach($companyTitleIds);

                $companyDefaultLeaveType = [
                    'key' => DefaultLeaveTypesUtil::OVERTIME,
                    'company_leave_type_id' => $leaveType->id,
                    'company_id' => $this->argument('company_id'),

                ];
                $this->companyDefaultLeaveTypesRepository->add($companyDefaultLeaveType);
            });
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }
}
