<?php

namespace App\Enums\EmployeeInfo;

enum DegreeTypeEnum: string
{
    case TECHNICAL_DIPLOMA = 'technical_diploma';
    case HIGH_SCHOOL = 'high_school';
    case BACHELOR = 'bachelor';
    case MASTERS = 'masters';
    case PHD = 'phd';
    case DIPLOMA = 'diploma';

    public static function all(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    public static function getTranslatedValues(): array
    {
        return array_map(fn ($case) => trans('employeeexport.' . $case->value), self::cases());
    }

    public static function getLabel(self $value): string
    {
        return trans('employeeexport.' . $value->value);
    }
}