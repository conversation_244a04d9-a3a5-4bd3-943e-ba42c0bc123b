<?php

namespace App\Console\Commands\OneTimeScript;

use App\Enums\V2\WorkTypes\AnyLocationTypesEnum;
use App\Enums\V2\WorkTypes\RestDaysTypesEnum;
use App\Enums\V2\WorkTypes\WorkTypesEnum;
use App\Models\WorkTypePolicy;
use App\Repositories\Repository;
use App\Util\AttendanceUtil;
use App\Util\UserWorkTypesUtil;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrationToNewWorkTypes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate-old-work-types {--company_id=company_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {

            $this->migrateWorkTypes();
            $this->addConsiderEmployeeOffShiftSetting();
            $this->info('Work types migrated successfully');
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            DB::rollBack();
        }
    }

    private function getRemoteDays($workTypePolicy)
    {
        $weekylRestDays = $workTypePolicy?->rest_days ?? '';
        $weeklyRestDays = explode(',', $weekylRestDays);
        $weeklyInOfficeDays = $workTypePolicy?->work_days ?? '';
        $weeklyInOfficeDays = explode(',', $weeklyInOfficeDays);
        $weekDays = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];

        // weekly remote days are days that doenst exist in weekly rest days and weekly in office days
        $weeklyRemoteDays = array_values(array_diff($weekDays, $weeklyRestDays, $weeklyInOfficeDays));

        // concat remote days to be saved in remote_days column
        return implode(',', $weeklyRemoteDays);
    }

    private function addConsiderEmployeeOffShiftSetting()
    {

        $attendanceSettingRepository = Repository::getRepository('AttendanceSetting');
        $offShiftSetting = $attendanceSettingRepository->findAttendanceSetting(AttendanceUtil::CONSIDER_EMPLOYEE_OFF_SHIFT_AFTER, $this->option('company_id'));
        if (! $offShiftSetting) {
            $data = [
                'key' => AttendanceUtil::CONSIDER_EMPLOYEE_OFF_SHIFT_AFTER,
                'value' => 8 * 60, // 8 hours
                'company_id' => $this->option('company_id'),
                'is_used' => true,
                'description' => 'Consider employee off shift after',
            ];
            $attendanceSettingRepository->create($data);
        }

    }

    private function migrateWorkTypes()
    {
        $workTypes = WorkTypePolicy::where('company_id', $this->option('company_id'))->get();

        foreach ($workTypes as $workType) {
            if (in_array($workType->work_days_type, WorkTypesEnum::all())) { // if the script has already run before then skip
                continue;
            }

            $updatedData = [];
            if ($workType->work_days_type != UserWorkTypesUtil::DYNAMIC_ON_SITE) {
                $updatedData['work_days_type'] = WorkTypesEnum::FIXED_WORKING_HOURS->value;

                $updatedData['rest_days_type'] = RestDaysTypesEnum::FIXED->value;
                $updatedData['rest_days_count'] = null;

                if ($workType->work_days_type == UserWorkTypesUtil::FLEXIBLE_HYBRID) {
                    $updatedData['apply_any_location'] = true;
                    $updatedData['any_location_type'] = AnyLocationTypesEnum::NUM_OF_TIMES->value;
                    $updatedData['remote_days_count'] = $workType->work_days_count;
                } elseif ($workType->work_days_type == UserWorkTypesUtil::FIXED_HYBRID) {
                    $updatedData['apply_any_location'] = true;
                    $updatedData['any_location_type'] = AnyLocationTypesEnum::FIXED_DAYS->value;
                    $updatedData['remote_days'] = $this->getRemoteDays($workType);
                }

            } else {
                $updatedData['work_days_type'] = WorkTypesEnum::SHIFT_BASED->value;

                $updatedData['rest_days_type'] = RestDaysTypesEnum::ROTATIONAL->value;
                $updatedData['rest_days'] = null;

            }

            $workType->update($updatedData);
        }
    }
}
