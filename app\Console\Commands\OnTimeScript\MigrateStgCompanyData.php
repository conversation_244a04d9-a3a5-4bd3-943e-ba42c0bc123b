<?php

namespace App\Console\Commands\OnTimeScript;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateStgCompanyData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-stg-company-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Connect to staging database
        $stagingConnection = DB::connection('staging');

        // Get data from staging database where company_id = 293
        $users = $stagingConnection->table('users')
            ->where('company_id', 293)
            ->get();

        $titles = $stagingConnection->table('titles')
            ->where('company_id', 293)
            ->get();

        $departments = $stagingConnection->table('departments')
            ->where('company_id', 293)
            ->get();

        $subDepartments = $stagingConnection->table('sub_departments')
            ->where('company_id', 293)
            ->get();

        // Connect to local database (using default connection from .env)
        $localConnection = DB::connection();

        DB::transaction(function () use ($users, $titles, $departments, $subDepartments, $localConnection) {
            // Insert departments first since they're referenced by sub-departments
            foreach ($departments as $department) {
                $departmentData = (array) $department;
                unset($departmentData['id']); // Remove ID to auto-generate new one
                $localConnection->table('departments')->insert($departmentData);
            }

            // Insert sub-departments
            foreach ($subDepartments as $subDepartment) {
                $subDepartmentData = (array) $subDepartment;
                unset($subDepartmentData['id']);
                $localConnection->table('sub_departments')->insert($subDepartmentData);
            }

            // Insert titles
            foreach ($titles as $title) {
                $titleData = (array) $title;
                unset($titleData['id']);
                $localConnection->table('titles')->insert($titleData);
            }

            // Insert users
            foreach ($users as $user) {
                $userData = (array) $user;
                unset($userData['id']);
                $localConnection->table('users')->insert($userData);
            }
        });

        $this->info('Data migration completed successfully!');
    }
}
