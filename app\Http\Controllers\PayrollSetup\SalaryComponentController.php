<?php

namespace App\Http\Controllers\PayrollSetup;

use App\Exceptions\UnprocessableException;
use App\Http\Controllers\NewController;
use App\Http\Requests\AddCustomComponentFieldsRequest;
use App\Services\PayrollSetup\EmployeeSalaryComponentMonthsService;
use App\Services\PayrollSetup\PayrollsService;
use App\Services\PayrollSetup\SalaryComponentsService;
use App\Util\HttpStatusCodeUtil;
use Exception;
use Illuminate\Support\Facades\DB;

class SalaryComponentController extends NewController
{
    public function __construct(
        SalaryComponentsService $salaryComponentsService,
        private PayrollsService $payrollsService,
        private EmployeeSalaryComponentMonthsService $employeeSalaryComponentMonthsService
    ) {
        parent::__construct($salaryComponentsService);
    }

    public function addCustomComponentFields(AddCustomComponentFieldsRequest $request)
    {
        $data = $request->all();
        $payroll = $this->payrollsService->payrollCoversDate($data['payroll_start_date']);

        if (! isset($payroll)) {
            throw new UnprocessableException('No payroll record was found for this date');
        }
        if ($payroll->status == 'finalized') {
            throw new UnprocessableException('The payroll for this month has been already finalized');
        }

        $createOrUpdateRequests = [];
        foreach ($data['employees_data'] as $employeeData) {
            $salaryComponentMonthRequest = [
                'employee_id' => $employeeData['employee_id'],
                'salary_component_id' => $employeeData['component_id'],
                'payroll_id' => $payroll->id,
            ];
            $employeeSalaryComponentMonth = $this->employeeSalaryComponentMonthsService->findFirstByKeys($salaryComponentMonthRequest);
            $salaryComponent = $this->service->find($employeeData['component_id']);

            $createOrUpdateRequest = [
                'employee_id' => $employeeData['employee_id'],
                'salary_component_id' => $employeeData['component_id'],
                'amount' => $employeeData['amount'],
                'payroll_id' => $payroll->id,
                'is_taxable' => $salaryComponent->salaryComponentsCategory->is_taxable ?? 0,
            ];
            if (! is_null($employeeSalaryComponentMonth)) {
                $createOrUpdateRequest['id'] = $employeeSalaryComponentMonth->id;
            }
            $createOrUpdateRequests[] = $createOrUpdateRequest;
        }

        $createdRecordsCount = 0;
        DB::beginTransaction();
        try {

            foreach ($createOrUpdateRequests as $createOrUpdateRequest) {
                if (isset($createOrUpdateRequest['id'])) {
                    $this->employeeSalaryComponentMonthsService->update($createOrUpdateRequest['id'], $createOrUpdateRequest);
                } else {
                    $this->employeeSalaryComponentMonthsService->add($createOrUpdateRequest);
                    $createdRecordsCount += 1;
                }
            }
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();

            return getResponseStructure([], HttpStatusCodeUtil::SOMETHING_WENT_WRONG, 'Failed adding custom fields');
        }

        return getResponseStructure(['count' => $createdRecordsCount],
            HttpStatusCodeUtil::OK, 'Employee salary was added successfully');
    }
}
