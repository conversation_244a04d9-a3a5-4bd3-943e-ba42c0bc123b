<?php

namespace App\Console\Commands;

use App\Services\MigrateApprovalCycleService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateApprovalCycle extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:approval-cycle-to-workflow';

    private MigrateApprovalCycleService $migrateApprovalCycleService;

    /**
     * Execute the console command.
     */
    public function handle(MigrateApprovalCycleService $migrateApprovalCycleService): void
    {
        $this->migrateApprovalCycleService = $migrateApprovalCycleService;

        DB::beginTransaction();
        try {
            $this->migrateApprovalCycleService->run();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
