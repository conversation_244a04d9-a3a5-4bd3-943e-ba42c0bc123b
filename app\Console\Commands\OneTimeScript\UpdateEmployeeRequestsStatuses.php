<?php

namespace App\Console\Commands\OneTimeScript;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateEmployeeRequestsStatuses extends Command
{
    protected $requestWorkflowApprovals;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'employee-requests:update-status 
    {type : The request type to update (attendance_deduction, attendance_overtime, employee_leave_request, penalty, mission)} {date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update employee requests status for a specific type from related models';

    protected $typeMap = [
        'attendance_deduction' => 'attendance_deductions',
        'attendance_overtime' => 'attendance_overtimes',
        'employee_leave_request' => 'employee_leave_requests',
        'penalty' => 'penalties',
        'mission' => 'mission_requests',
    ];

    public function handle()
    {
        try {
            $type = $this->argument('type');
            $startDate = $this->argument('date');

            if (! array_key_exists($type, $this->typeMap)) {
                $this->error('Invalid type! Valid options: '.implode(', ', array_keys($this->typeMap)));

                return 1;
            }

            DB::transaction(function () use ($type, $startDate) {
                $this->updateStatusForType($type, $startDate, $this->typeMap[$type]);
            });

            $this->info("Employee request statuses updated for $type!");

            return 0;

        } catch (\Throwable $e) {
            $this->error('Update failed: '.$e->getMessage());
            \Log::error("Employee request update error ({$type}): ".$e->getMessage());

            return 1;
        }
    }

    protected function updateStatusForType(string $type, $startDate, string $table)
    {
        $statusMapping = $type === 'attendance_deduction'
            ? DB::raw("
                CASE r.status
                    WHEN 'applied' THEN 'approved'
                    WHEN 'waived' THEN 'rejected'
                    ELSE NULL
                END
            ")
            : DB::raw('r.status');

        $updated = DB::table('employee_requests as er')
            ->join("$table as r", function ($join) use ($type) {
                $join->on('er.requestable_id', '=', 'r.id')
                    ->where('er.requestable_type', $type);
            })
           // ->where('er.status', 'pending')
            ->whereDate('er.date', '>=', $startDate)
            ->update([
                'er.status' => $statusMapping,
            ]);

        $this->info("Updated $updated records for type: $type");
    }
}
