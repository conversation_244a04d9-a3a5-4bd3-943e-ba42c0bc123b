<?php

namespace App\Http\Controllers\NewCompanySetup;

use App\Constants\DefaultScopesData;
use App\Enums\EmployeeInfo\CountryEnum;
use App\Exceptions\UnprocessableException;
use App\FeatureToggles\Unleash;
use App\Http\Controllers\NewController;
use App\Http\Requests\AddAreaRequest;
use App\Http\Requests\AddBranchRequest;
use App\Http\Requests\AddCycleApproversRequest;
use App\Http\Requests\AddDepartmentRequest;
use App\Http\Requests\AddEmployeeRequest;
use App\Http\Requests\AddPenaltyGroupWithPoliciesRequest;
use App\Http\Requests\AddRoleRequest;
use App\Http\Requests\AddTitleRequest;
use App\Http\Requests\AssignRoleToTitleRequest;
use App\Http\Requests\CreateCompanyLeaveSetupRequest;
use App\Http\Requests\EditAreaRequest;
use App\Http\Requests\EditAttendanceDeductionPoliciesRequest;
use App\Http\Requests\EditBranchRequest;
use App\Http\Requests\EditCompanyLeaveSetupRequest;
use App\Http\Requests\EditDepartmentRequest;
use App\Http\Requests\EditEmployeeRequest;
use App\Http\Requests\EditOvertimePoliciesRequest;
use App\Http\Requests\EditPenaltyGroupWithPoliciesRequest;
use App\Http\Requests\EditRoleRequest;
use App\Http\Requests\EditTitleRequest;
use App\Http\Requests\GetEmployeesByFilterRequest;
use App\Http\Requests\setAttendanceDeductionPoliciesRequest;
use App\Http\Requests\SetAttendanceTogglesRequest;
use App\Http\Requests\SetCompanyAttendancePoliciesRequest;
use App\Http\Requests\SetCompanyBasicInfoRequest;
use App\Http\Requests\SetCompanySetupCounter;
use App\Http\Requests\setOvertimePoliciesRequest;
use App\Http\Requests\TransferEmployeeToBranchRequest;
use App\Http\Requests\V1\GetCentralRequest;
use App\Http\Resources\AreaSetupCollection;
use App\Http\Resources\AttendanceGroupsCollection;
use App\Http\Resources\BranchSetupCollection;
use App\Http\Resources\CompanyAttendancePoliciesCollection;
use App\Http\Resources\CompanyLeaveSetupCollection;
use App\Http\Resources\CompanyResource;
use App\Http\Resources\DepartmentSetupCollection;
use App\Http\Resources\EmployeeSetupCollection;
use App\Http\Resources\GetSingleEmployeeResource;
use App\Http\Resources\IndustryCollection;
use App\Http\Resources\OvertimeGroupCollection;
use App\Http\Resources\PenaltyGroupsCollection;
use App\Http\Resources\PermissionGroupCollection;
use App\Http\Resources\RoleCollection;
use App\Http\Resources\TitleSetupCollection;
use App\Models\Area;
use App\Models\Branch;
use App\Models\Department;
use App\Models\Industry;
use App\Models\Title;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\Repository;
use App\Services\CompanySetup\AreasService;
use App\Services\CompanySetup\AttendanceGroupsService;
use App\Services\CompanySetup\AttendanceSettingsService;
use App\Services\CompanySetup\BranchesService;
use App\Services\CompanySetup\BusinessServices\GetEmployeesFiltersService;
use App\Services\CompanySetup\CompaniesService;
use App\Services\CompanySetup\CompanyLeaveTypesService;
use App\Services\CompanySetup\DepartmentsService;
use App\Services\CompanySetup\EmployeesService;
use App\Services\CompanySetup\OvertimeGroupsService;
use App\Services\CompanySetup\PermissionsService;
use App\Services\CompanySetup\RequestCyclesService;
use App\Services\CompanySetup\RolesService;
use App\Services\CompanySetup\TitlesService;
use App\Services\EmployeeChange\EmployeeChangesService;
use App\Services\PayrollSetup\SystemSettingsService;
use App\Services\V1\EmployeeProfile\EmployeeProfileService;
use App\Services\V1\Penalties\PenaltyGroupsService;
use App\Traits\DataPreparation;
use App\Traits\GetIdsTrait;
use App\Util\BasicInfoUtil;
use App\Util\HttpStatusCodeUtil;
use Carbon\Carbon;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use stdClass;

class CompanySetupController extends NewController
{
    use DataPreparation, GetIdsTrait;

    private $titlesRepository;

    private $departmentRepository;

    private $employeeRepository;

    private $areaRepository;

    private $branchRepository;

    private $systemSettingRepository;

    public function __construct(CompaniesService $companiesService,
        protected GetEmployeesFiltersService $getEmployeesFiltersService,
        protected TitlesService $titlesService,
        protected DepartmentsService $departmentsService,
        protected BranchesService $branchesService,
        protected AreasService $areasService,
        protected EmployeesService $employeesService,
        protected CompanyLeaveTypesService $companyLeaveTypesService,
        protected AttendanceSettingsService $attendanceSettingsService,
        protected AttendanceGroupsService $attendanceGroupsService,
        protected OvertimeGroupsService $overtimeGroupsService,
        protected RolesService $rolesService,
        protected PermissionsService $permissionsService,
        protected RequestCyclesService $requestCyclesService,
        protected EmployeeChangesService $employeeChangesService,
        protected PenaltyGroupsService $penaltyGroupsService,
        protected SystemSettingsService $systemSettingsService,
        private EmployeeProfileService $employeeProfileService)
    {
        parent::__construct($companiesService);
        $this->titlesRepository = Repository::getRepository('Title');
        $this->employeeRepository = Repository::getRepository('Employee');
        $this->departmentRepository = Repository::getRepository('Department');
        $this->branchRepository = Repository::getRepository('Branch');
        $this->areaRepository = Repository::getRepository('Area');
        $this->systemSettingRepository = new SystemSettingRepository;
    }

    public function setCompanyBasicInfo(SetCompanyBasicInfoRequest $request)
    {
        $companyId = auth()->user()->company_id;
        $data = $request->except(['employee_code_type']);
        $data = $request->validated();

        DB::beginTransaction();
        try {
            $this->service->update($companyId, $data);

            $this->addEmployeeCodeTypeSetting($request);

            $defaultScopes = DefaultScopesData::DEFUALT_SCOPES;
            $scopeData = [];

            foreach ($defaultScopes as $defaultScopeData) {
                $scopeData = $defaultScopeData;
                $scopeData['company_id'] = $companyId;
                if (auth()->user()->company->scopes()->where('key', $scopeData['key'])->doesntExist()) {
                    auth()->user()->company->scopes()->create($scopeData);
                }
            }

            DB::commit();

            return getResponseStructure(['data' => []],
                HttpStatusCodeUtil::OK, 'Basic info has been set successfully');

        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

    }

    public function setCompanySetupCounter(SetCompanySetupCounter $request)
    {
        $companyId = auth()->user()->company_id;
        $data = $request->validated();
        $this->service->update($companyId, $data);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Setup counter was added successfully');
    }

    public function getCompanySetupCounter()
    {
        return getResponseStructure(['data' => auth()->user()->company->setup_counter],
            HttpStatusCodeUtil::OK, 'Company setup counter');
    }

    public function getCompanyBasicInfo()
    {
        $company = auth()->user()->company->load('industry');
        $employeeCodeType = $this->systemSettingRepository->findFirstByKey('key', 'employee_code_type');
        $company->employee_code_type = $employeeCodeType?->value ?? null;

        return getResponseStructure(['data' => new CompanyResource($company)],
            HttpStatusCodeUtil::OK, 'Company basic info');
    }

    public function getIndustries()
    {
        return getResponseStructure(['data' => new IndustryCollection(Industry::all())],
            HttpStatusCodeUtil::OK, 'Available industries');
    }

    public function getCompanyTitles()
    {
        return getResponseStructure(['data' => new TitleSetupCollection($this->titlesRepository->getCompanyTitlesWithEmployeesCount())],
            HttpStatusCodeUtil::OK, 'Available titles');
    }

    public function getCompanyAreas()
    {
        return getResponseStructure(['data' => new AreaSetupCollection($this->areaRepository->getCompanyAreasWithBranchesCount())],
            HttpStatusCodeUtil::OK, 'Available titles');
    }

    public function getCompanyBranches()
    {
        return getResponseStructure(['data' => new BranchSetupCollection($this->branchRepository->getAll(['area'])->get())],
            HttpStatusCodeUtil::OK, 'Available titles');
    }

    public function getCompanyDepartments(GetCentralRequest $request)
    {
        $data = $request->validated();

        return getResponseStructure(['data' => new DepartmentSetupCollection($this->departmentRepository->getCompanyDepartmetnsWithTitlesCount())],
            HttpStatusCodeUtil::OK, 'Available departments');
    }

    public function getCompanyLeavesSetup()
    {
        return getResponseStructure(['data' => new CompanyLeaveSetupCollection(
            $this->service->getCompanyLeaveTypesWithFirstPolicy())],
            HttpStatusCodeUtil::OK, 'Available titles');
    }

    public function getCompanyAttendancePolicies()
    {
        return getResponseStructure(['data' => new CompanyAttendancePoliciesCollection(
            $this->attendanceSettingsService->getAttendanceSettings())],
            HttpStatusCodeUtil::OK, 'Available attendance policies');
    }

    public function setCompanyAttendancePolicies(SetCompanyAttendancePoliciesRequest $request)
    {
        $data = $request->validated();
        $this->attendanceSettingsService->setCompanyAttendancePolicies($data);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Attendance policies has been set successfully');
    }

    public function setAttendanceToggles(SetAttendanceTogglesRequest $request)
    {
        $data = $request->validated();
        $this->attendanceSettingsService->setAttendanceToggles($data);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Deduction and overtime toggles has been set successfully');
    }

    public function getAttendanceDeductionPolicies()
    {
        $attendanceGroups = $this->attendanceGroupsService->getAllWithPoliciesAndTitles();

        return getResponseStructure(['data' => new AttendanceGroupsCollection($attendanceGroups)],
            HttpStatusCodeUtil::OK, 'Available Attendance Deduction Policies');
    }

    public function setAttendanceDeductionPolicies(setAttendanceDeductionPoliciesRequest $request)
    {
        $data = $request->validated();
        $unleash = app(Unleash::class);
        DB::beginTransaction();
        try {
            $this->attendanceGroupsService->addWithDailyPolicies($data);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Attendance Deduction Polices Has Been Edited Successfully');
    }

    public function editAttendanceDeductionPolicies($id, EditAttendanceDeductionPoliciesRequest $request)
    {
        $data = $request->validated();

        DB::beginTransaction();
        try {
            $this->attendanceGroupsService->editWithDailyPolicies($id, $data);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Attendance Deduction Polices Has Been Added Successfully');
    }

    public function deleteAttendanceDeductionPolicy($id)
    {
        $this->attendanceGroupsService->delete($id);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item deleted successfully');
    }

    public function setAttendanceOvertimePolicies(setOvertimePoliciesRequest $request)
    {
        $data = $request->validated();
        if (! isset($data['calculation_method'])) {
            $data['calculation_method'] = 'rate';
        }

        $prevTo = -1;
        foreach ($data['overtime_policies'] as $policy) {
            if ($policy['from'] != $prevTo + 1 && $prevTo != -1) {
                throw new UnprocessableException('Overtime policies should be continuous');
            }
            $prevTo = $policy['to'];
        }
        DB::beginTransaction();
        try {
            $this->overtimeGroupsService->addWithOvertimePolicies($data);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Attendance Overtime Polices Has Been Added Successfully');
    }

    public function editAttendanceOvertimePolicies($id, EditOvertimePoliciesRequest $request)
    {
        $data = $request->validated();

        if (isset($data['overtime_policies'])) {
            $prevTo = -1;
            foreach ($data['overtime_policies'] as $policy) {
                if ($policy['from'] != $prevTo + 1 && $prevTo != -1) {
                    throw new UnprocessableException('Overtime policies should be continuous');
                }
                $prevTo = $policy['to'];
            }
        }
        DB::beginTransaction();
        try {
            $this->overtimeGroupsService->editWithOvertimePolicies($id, $data);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Attendance Overtime Polices Has Been Edited Successfully');
    }

    public function deleteAttendanceOvertimePolicy($id)
    {
        $this->overtimeGroupsService->delete($id);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item deleted successfully');
    }

    public function getAttendanceOvertimePolicies()
    {
        $overtimeGroups = $this->overtimeGroupsService->getAllWithPoliciesAndTitles();

        return getResponseStructure(['data' => new OvertimeGroupCollection($overtimeGroups)],
            HttpStatusCodeUtil::OK, 'Available Attendance Overtime Policies');
    }

    public function getEmployeesByFilter(GetEmployeesByFilterRequest $request)
    {
        $data = $request->validated();

        if (! isset($data['page_size'])) {
            $data['page_size'] = config('globals.MAX_PAGE_SIZE');
        }

        $employees = $this->employeeRepository->getEmployeesByFilter($data);

        $paginatedData = (new EmployeeSetupCollection($employees))->response()->getData();

        return getResponseStructure(['data' => $paginatedData->data, 'pagination' => $paginatedData->meta],
            HttpStatusCodeUtil::OK, 'Available employees');
    }

    public function getEmployee(int $id)
    {
        $employee = $this->employeesService->findOrFail($id)->load('title', 'branches', 'managedDepartments', 'managedSubDepartments', 'employeeInfo');
        $roles = $employee->user->customRoles;
        $scopes = [];
        foreach ($roles as $role) {
            foreach ($role->scopes as $scope) {
                $scopes[] = $scope->key;
            }
        }
        $employee->scopes = $scopes;
        $companyDialCode = auth()->user()->company->country->dial_code ?? '';
        $employee->countryCode = $companyDialCode;
        if (! empty($companyDialCode) && strpos($employee->phone, $companyDialCode) === 0) {
            $employee->phone = substr($employee->phone, strlen($companyDialCode));
        }

        return getResponseStructure(['data' => new GetSingleEmployeeResource($employee)],
            HttpStatusCodeUtil::OK, 'Available employees');
    }

    public function deleteDepartment($id)
    {
        $department = Department::find($id);

        if (! isset($department)) {
            throw new UnprocessableException(trans('messages.invalid_id'));
        }

        if (count($department->titles)) {
            throw new UnprocessableException(trans('messages.cannot_delete_department'));
        }

        $department->delete();

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item deleted successfully');
    }

    public function deleteTitle($id)
    {
        $title = Title::find($id);

        if (! isset($title)) {
            throw new UnprocessableException(trans('messages.invalid_id'));
        }

        if (count($title->employees)) {
            throw new UnprocessableException(trans('messages.cannot_delete_title'));
        }

        $title->delete();

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item deleted successfully');
    }

    public function deleteArea($id)
    {
        $area = Area::find($id);

        if (! isset($area)) {
            throw new UnprocessableException(trans('messages.invalid_id'));
        }

        if (count($area->branches)) {
            throw new UnprocessableException(trans('messages.cannot_delete_area'));
        }

        $area->delete();

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item deleted successfully');
    }

    public function deleteBranch($id)
    {
        $branch = Branch::find($id);

        if (! isset($branch)) {
            throw new UnprocessableException(trans('messages.invalid_id'));
        }

        if (count($branch->employees)) {
            throw new UnprocessableException(trans('messages.cannot_delete_branch'));
        }

        $branch->delete();

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item deleted successfully');
    }

    public function addDepartment(AddDepartmentRequest $request)
    {
        $data = $request->validated();
        $data['company_id'] = auth()->user()->company_id;
        if (! isset($data['name_en'])) {
            $data['name_en'] = $data['name_ar'];
        }

        $data['name'] = $data['name_ar']; // TODO this line to be removed after we drop name column

        $this->departmentsService->add($data);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item added successfully');
    }

    public function editDepartment(int $id, EditDepartmentRequest $request)
    {
        $data = $request->validated();
        $data['name'] = $data['name_ar'] ?? null; // TODO to be removed after we drop name column

        $this->departmentsService->update($id, $data);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item has been edited successfully');
    }

    public function addTitle(AddTitleRequest $request)
    {
        $data = $request->validated();
        $data['company_id'] = auth()->user()->company_id;
        if (! isset($data['name_en'])) {
            $data['name_en'] = $data['name_ar'];
        }

        $data['name'] = $data['name_ar']; // TODO this line to be removed after we drop name column

        $data['absence_deduction_policy_id'] = $data['absence_policy_id'] ?? null;
        $data['late_deduction_group_id'] = $data['late_policy_id'] ?? null;
        DB::beginTransaction();
        try {
            $this->titlesService->add($data);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item added successfully');
    }

    public function editTitle(int $id, EditTitleRequest $request)
    {

        $data = $request->validated();

        $data['absence_deduction_policy_id'] = $data['absence_policy_id'] ?? null;
        $data['late_deduction_group_id'] = $data['late_policy_id'] ?? null;
        $data['sub_department_id'] = $data['sub_department_id'] ?? null;

        DB::beginTransaction();
        try {
            $this->titlesService->update($id, $data);
            $this->employeesService->syncAllBranchesToCompanyScopeEmployees();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item has been edited successfully');
    }

    public function addBranch(AddBranchRequest $request)
    {
        $data = $request->validated();
        $data['company_id'] = auth()->user()->company_id;
        if (! isset($data['name_en'])) {
            $data['name_en'] = $data['name_ar'];
        }

        $data['name'] = $data['name_ar']; // TODO this line to be removed after we drop name column
        $data['start_time'] = $data['end_time'] = '00:00:00';

        if (isset($data['code']) && $data['code'] != null) {
            $data['branch_code'] = $data['code'];
            unset($data['code']);
        }

        DB::beginTransaction();
        try {
            $branch = $this->branchesService->add($data);
            $this->employeesService->syncAllBranchesToCompanyScopeEmployees();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item added successfully');
    }

    public function editBranch($id, EditBranchRequest $request)
    {
        $data = $request->validated();
        $data['name'] = $data['name_ar'] ?? null; // TODO to be removed after we drop name column

        if (isset($data['code']) && $data['code'] != null) {
            $data['branch_code'] = $data['code'];
            unset($data['code']);
        }

        $this->branchesService->update($id, $data);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item has been edited successfully');
    }

    public function addArea(AddAreaRequest $request)
    {
        $data = $request->validated();
        $data['company_id'] = auth()->user()->company_id;
        if (! isset($data['name_en'])) {
            $data['name_en'] = $data['name_ar'];
        }

        $data['name'] = $data['name_ar']; // TODO this line to be removed after we drop name column

        $this->areasService->add($data);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item added successfully');
    }

    public function editArea(int $id, EditAreaRequest $request)
    {
        $data = $request->validated();
        $data['name'] = $data['name_ar'] ?? null; // TODO this line to be removed after we drop name column

        $this->areasService->update($id, $data);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item has been edited successfully');
    }

    public function addEmployee(AddEmployeeRequest $request)
    {
        $data = $request->validated();
        $data['company_id'] = auth()->user()->company_id;

        $data['first_name_en'] = $data['first_name_en'] ?? $data['first_name_ar'];

        $data['second_name_en'] = $data['second_name_en'] ?? $data['second_name_ar'];

        $data['third_name_en'] = $data['third_name_en'] ?? $data['third_name_ar'];

        $data['fourth_name_en'] = $data['fourth_name_en'] ?? ($data['fourth_name_ar'] ?? null);

        $data['fifth_name_en'] = $data['fifth_name_en'] ?? ($data['fifth_name_ar'] ?? null);

        $data['name_ar'] = $data['first_name_ar'].' '.$data['second_name_ar'].' '.$data['third_name_ar'];
        $data['name_en'] = $data['first_name_en'].' '.$data['second_name_en'].' '.$data['third_name_en'];

        $data['first_name'] = $data['first_name_ar']; // TODO these line to be removed after we drop old name columns
        $data['second_name'] = $data['second_name_ar']; // TODO these line to be removed after we drop old name columns
        $data['third_name'] = $data['third_name_ar']; // TODO these line to be removed after we drop old name columns
        $data['name'] = $data['name_ar']; // TODO these line to be removed after we drop old name columns

        $data['branch_id'] = $data['main_branch_id'];

        $data['is_trackable'] = $data['is_trackable'] ?? 1;
        //        $countryCode = $data['country_code'] ?? '+20';
        $countryCode = auth()->user()->company->country->dial_code;

        $data['phone'] = $countryCode.(str_starts_with($data['phone'], '0') && $countryCode == '+20' ? substr($data['phone'], 1) : $data['phone']);
        $data['secondary_phone'] = $data['secondary_phone'] ?? null;
        $data['email'] = $data['email'] ?? null;
        $data['passport_number'] = $data['passport_number'] ?? null;
        $data['nationality'] = $data['nationality'] ?? 'egyptian';
        $data['military_status'] = $data['military_status'] ?? null;
        if (isset($data['country_code'])) {
            unset($data['country_code']);
        }

        DB::beginTransaction();
        try {
            $employee = $this->employeesService->add($data);

            // probation
            $joinDate = $employee->employeeInfo->join_date ?? null;
            $endProbationDate = Carbon::parse($joinDate)->addMonths($employee->title->probation_period)->startOfDay();
            if ($employee->title->probation_period > 0 && isset($endProbationDate) && $endProbationDate < date('Y-m-d') && $employee->on_probation) {
                $employee->on_probation = false;
                $employee->save();
            }

            if (isset($data['profile_picture'])) {
                $this->employeeProfileService->uploadProfilePicture([
                    'employee_id' => $employee->id,
                    'profile_picture' => $data['profile_picture'],
                ]);
            }
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
            throw new UnprocessableException($e->getMessage());
        }

        return getResponseStructure(['data' => ['employee' => $employee]],
            HttpStatusCodeUtil::OK, 'Item added successfully');
    }

    public function editEmployee(int $id, EditEmployeeRequest $request)
    {
        $data = $request->validated();

        DB::beginTransaction();
        try {
            $this->employeesService->update($id, $data);
            $this->employeesService->syncAllBranchesToCompanyScopeEmployees();

            if (isset($data['profile_picture'])) {
                $this->employeeProfileService->uploadProfilePicture([
                    'employee_id' => $id,
                    'profile_picture' => $data['profile_picture'],
                ]);
            }

            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item has been edited successfully');
    }

    public function addCompanyLeaveSetup(CreateCompanyLeaveSetupRequest $request)
    {
        $data = $request->validated();
        $data['name'] = $data['name_ar']; // TODO these line to be removed after we drop old name columns

        if (! isset($data['name_en'])) {
            $data['name_en'] = $data['name_ar'];
        }

        if (isset($data['leave_deduction_percentage'])) {
            $data['leave_deduction_percentage'] = $data['leave_deduction_percentage'] / 100;
        }
        DB::beginTransaction();
        try {
            $this->companyLeaveTypesService->addWithPolicy($data);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item has been set successfully');
    }

    public function editCompanyLeaveSetup($id, EditCompanyLeaveSetupRequest $request)
    {
        $data = $request->validated();

        if (isset($data['name_ar'])) {
            $data['name'] = $data['name_ar'];
        } // TODO these line to be removed after we drop old name columns

        if (isset($data['leave_deduction_percentage'])) {
            $data['leave_deduction_percentage'] = $data['leave_deduction_percentage'] / 100;
        }

        DB::beginTransaction();
        try {
            $this->companyLeaveTypesService->updateWithPolicy($id, $data);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item has been set successfully');
    }

    public function deleteCompanyLeaveTypeWithPolicy($id)
    {
        $this->companyLeaveTypesService->delete($id);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item deleted successfully');
    }

    public function getCompanyRoles()
    {
        $roles = $this->rolesService->getCompanyRolesWithPermissionsAndTitles();

        return getResponseStructure(['data' => new RoleCollection($roles)],
            HttpStatusCodeUtil::OK, 'Available Roles');
    }

    public function getEmployeesAssignedToRole($roleId, GetEmployeesByFilterRequest $request)
    {
        $data = $request->validated();

        $titleIds = $this->rolesService->getTitleIdsAssignedToRole($roleId);
        $data['title_ids'] = $titleIds;
        $data['page_size'] = 0;
        $this->setFilterData($data);

        $employees = $this->employeesService->getImportantDataByFilters($data);

        return getResponseStructure(['data' => $employees],
            HttpStatusCodeUtil::OK, 'Available Employees Assigned To This Role');
    }

    public function getAllPermissions()
    {
        $permissions = $this->permissionsService->getAllGroupedByPermissionGroup();

        return getResponseStructure(['data' => new PermissionGroupCollection($permissions)],
            HttpStatusCodeUtil::OK, 'Available Permissions');
    }

    public function addRole(AddRoleRequest $request)
    {

        $data = $request->validated();
        $data['name'] = $data['name_ar']; // TODO these line to be removed after we drop old name columns

        if (! isset($data['name_en'])) {
            $data['name_en'] = $data['name_ar'];
        }

        $data['is_system_role'] = 0;
        $data['guard_name'] = 'user-api';

        DB::beginTransaction();
        try {

            $role = $this->rolesService->addWithPermissions($data);
            DB::commit();

            return getResponseStructure(['data' => [
                'id' => $role->id,
                'name' => [
                    'en' => $role->name_en,
                    'ar' => $role->name_ar,
                ],
                'existed' => $role->existed,
            ]], HttpStatusCodeUtil::OK);

        } catch (Exception $e) {

            \Sentry\captureException($e);
            Log::info('failed to add role');
            DB::rollBack();
            throw $e;
        }
    }

    public function editRole($id, EditRoleRequest $request)
    {
        $data = $request->validated();

        DB::beginTransaction();
        try {
            $role = $this->rolesService->editWithPermissions($id, $data);
            $this->employeesService->syncAllBranchesToCompanyScopeEmployees();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => [
            'id' => $role->id,
            'name' => [
                'en' => $role->name_en,
                'ar' => $role->name_ar,
            ],
            'existed' => $role?->existed,
        ]],
            HttpStatusCodeUtil::OK, 'Item has been updated successfully');
    }

    public function deleteRole($id)
    {
        $this->rolesService->delete($id);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item deleted successfully');
    }

    public function assignRoleToTitle(AssignRoleToTitleRequest $request)
    {
        $data = $request->validated();

        DB::beginTransaction();
        try {
            $this->titlesService->assignRoleToTitle($data['title_id'], $data['role_id']);
            $this->employeesService->syncAllBranchesToCompanyScopeEmployees();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Role assigned successfully');
    }

    public function getRequestCycles() // deprecated #TODO remove this
    {
        $requestCycles = $this->requestCyclesService->getRequestCycles();

        return getResponseStructure(['data' => $requestCycles],
            HttpStatusCodeUtil::OK, 'Available Request Cycles');
    }

    public function addRequestCycle(AddCycleApproversRequest $request) // deprecated #TODO remove this
    {
        $data = $request->validated();

        DB::beginTransaction();
        try {
            $this->requestCyclesService->addRequestCycle($data);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Request Cycles Has Been Added');
    }

    public function transferEmployee(TransferEmployeeToBranchRequest $request)
    {
        $data = $request->validated();

        DB::beginTransaction();
        try {
            $data['change_type'] = 'branch';
            $output = new stdClass;
            $this->employeeChangesService->perfrom($data, $output);

            if (isset($output->Error)) {
                if (isset($output->message)) {
                    unset($output->message);
                }

                return response()->json($output, HttpStatusCodeUtil::UNPROCESSABLE_ENTITY);
            }

            DB::commit();
            $message = $output->message ?? '';

            return response()->json(['data' => ['message' => $message]], HttpStatusCodeUtil::OK);
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }
    }

    public function addPenaltyGroupWithPolicies(AddPenaltyGroupWithPoliciesRequest $request)
    {
        $data = $request->validated();

        if (! isset($request['name_en'])) {
            $request['name_en'] = $request['name_ar'];
        }

        $penaltyGroup = [];

        DB::beginTransaction();
        try {
            $penaltyGroup = $this->penaltyGroupsService->addPenaltyGroupWithPolicies($data);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => $penaltyGroup],
            HttpStatusCodeUtil::OK, 'Penalty Group with Policy Has Been set Successfully');
    }

    public function editPenaltyGroupWithPolicies($id, EditPenaltyGroupWithPoliciesRequest $request)
    {
        $data = $request->validated();

        $penaltyGroup = [];

        DB::beginTransaction();
        try {
            $penaltyGroup = $this->penaltyGroupsService->editPenaltyGroupWithPolicies($id, $data);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => $penaltyGroup],
            HttpStatusCodeUtil::OK, 'Penalty Group with Policy Has Been set Successfully');
    }

    public function getPenaltyGroups(FormRequest $request)
    {
        $searchValue = Arr::get($request->all(), 'search_value', null);
        $penaltyGroups = $this->penaltyGroupsService->getAllWithPoliciesAndTitles($searchValue);

        return getResponseStructure(['data' => new PenaltyGroupsCollection($penaltyGroups)],
            HttpStatusCodeUtil::OK, 'Available Penalty Groups');
    }

    public function deletePenaltyGroup($id)
    {
        $this->penaltyGroupsService->delete($id);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Item deleted successfully');
    }

    public function getEmployeeCodeTypeSetting($employeeCodeType)
    {
        return [
            'unique_keys' => [
                'key' => 'employee_code_type',
                'company_id' => auth()->user()->company_id,
            ],
            'setting' => [
                'value' => $employeeCodeType,
                'as_of_date' => Carbon::today()->format('Y-m-d'),
            ],

        ];
    }

    public function addEmployeeCodeTypeSetting($request)
    {
        $data = $request->only(['employee_code_type']);
        $employeeCodeType = $data['employee_code_type'];
        $companyEmployeeCodeType = $this->systemSettingsService->getCompanyEmployeeCodeType();

        if ($companyEmployeeCodeType == BasicInfoUtil::EMPLOYEE_CODE_TYPES['MANUAL'] &&
            $employeeCodeType == BasicInfoUtil::EMPLOYEE_CODE_TYPES['AUTO']) {
            throw new UnprocessableException(trans('messages.can_not_change_employee_code_type'));
        }

        if (is_null($companyEmployeeCodeType) || $companyEmployeeCodeType == BasicInfoUtil::EMPLOYEE_CODE_TYPES['AUTO']) {
            $employeeCodeTypeSetting = $this->getEmployeeCodeTypeSetting($employeeCodeType);
            $this->systemSettingRepository->upsert($employeeCodeTypeSetting['setting'], $employeeCodeTypeSetting['unique_keys']);
        }
    }

    public function getCompanyCountryCodes()
    {
        $country = auth()->user()->company->country;
        $formattedCountryCode = CountryEnum::mapCountryName($country->name_en);

        return getResponseStructure(['data' => [
            [$formattedCountryCode],
        ]], HttpStatusCodeUtil::OK, 'Company country code');
    }
}
