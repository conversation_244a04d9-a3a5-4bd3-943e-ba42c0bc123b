<?php

namespace App\Http\Controllers\V1\Attendance;

use App\Handlers\MonthlyAttendanceTags\AttendanceTagHandler;
use App\Handlers\MonthlyAttendanceTags\TagHandlerFactory;
use App\Handlers\WeeklyAttendances\AttendanceEntriesHandler;
use App\Http\Controllers\Controller;
use App\Models\Timecard;
use App\Repositories\Repository;
use App\Rules\ScopeBranchIdsRule;
use App\Rules\ScopeDepartmentIdsRule;
use App\Rules\ScopeSubDepartmentIdsRule;
use App\Services\LeaveManagement\BusinessServices\GetLeavesFiltersService;
use App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService;
use App\Services\TimeTracking\BusinessServices\GetCicosFilterService;
use App\Services\TimeTracking\BusinessServices\GetTimecardsFilterService;
use App\Services\TimeTracking\CrudServices\AttendanceCrudService;
use App\Services\V1\Attendance\NewAttendanceService;
use App\Traits\DataPreparation;
use App\Util\AttendanceUtil;
use App\Util\PayrollUtil;
use DateTime;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use stdClass;

class AttendanceController extends Controller
{
    private Repository $entityTagRepository;

    use DataPreparation;

    public function __construct(
        private GetTimecardsFilterService       $timecardsFilterService,
        private GetCicosFilterService           $cicosFilterService,
        private GetLeavesFiltersService         $leavesFiltersService,
        private AttendanceCrudService           $attendanceCrudService,
        private EmployeeLeaveRequestCrudService $employeeLeaveRequestCrudService,

    )
    {
        $this->entityTagRepository = Repository::getRepository('EntityTag');

    }

    public function monthly(array $data, stdClass &$output): void
    {
        $rules = $this->getFilterRules();
        $data = $this->getData($data);
        $validator = Validator::make($data, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $attendanceHandler = new AttendanceTagHandler(new TagHandlerFactory);
        $attendances = $this->getAttendances($validator->validate());
        $tags = $attendanceHandler->handleTags($attendances, $data);
        $output = $tags;
    }

    public function weekly(array $data, stdClass &$output): void
    {
        $rules = $this->getFilterRules();
        $data = $this->getData($data);
        $validator = Validator::make($data, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $data = $validator->validate();
        $attendanceHandler = new AttendanceEntriesHandler;
        $attendances = $this->getWeeklyAttendances($data);
        $tags = $attendanceHandler->handleEntries($attendances, $data);
        $output = $tags;
    }

    public function getFilterRules(): array
    {
        $rules = [
            'start_date' => ['date', 'date_format:Y-m-d'],
            'end_date' => ['required_with:start_date', 'date', 'date_format:Y-m-d', 'after_or_equal:start_date'],
            'from_date' => ['date', 'date_format:Y-m-d'],
            'to_date' => ['required_with:from_date', 'date', 'date_format:Y-m-d', 'after_or_equal:from_date'],
            'employee_id' => ['integer'],
            'branch_ids' => ['array'],
            'branch_ids.*' => ['integer', new ScopeBranchIdsRule],
            'department_ids' => ['array'],
            'department_ids.*' => ['integer', new ScopeDepartmentIdsRule], // Assumes the most restrictive rule
            'title_ids' => ['array'],
            'title_ids.*' => ['integer'],
            'search_value' => ['min:1', 'max:30'],
            'order_by' => ['string', 'in:from,created_at'], // Merged the different 'order_by' possibilities
            'order_by_type' => ['string', 'in:asc,desc'],
            'page' => ['integer', 'min:1'],
            'page_size' => ['integer', 'min:0'],
            'statuses' => ['array'],
            'statuses.*' => ['string', 'in:pending,approved,rejected,cancelled,in_process'],
            'company_leave_type_policy_id' => ['integer'],
            'with_restdays' => ['boolean'],
            'only_net_quantity_in_range' => ['boolean'],
            'sub_department_ids' => 'array',
            'sub_department_ids.*' => 'integer', new ScopeSubDepartmentIdsRule,
            'status' => 'string',
        ];

        // Additional rule from the second function
        $tags = implode(',', array_values(config('globals.ATTENDANCE_TAGS')));
        $rules['filter_key'] = ['string', 'in:' . $tags];

        return $rules;
    }

    public function getData(array $data): array
    {

        $data['page_size'] = 0;
        $data['order_by'] = 'from';
        $data['order_by_type'] = 'asc';
        $data['only_net_quantity_in_range'] = true;
        if (isset($data['start_date'])) {
            $data['from_date'] = $data['start_date'];
        }
        if (isset($data['end_date'])) {
            $data['to_date'] = $data['end_date'];
        }

        return $data;
    }

    /**
     * @return mixed
     */
    public function getAttendances(array $data)
    {
        $data['approved_only'] = true;
        $data['monthly_view_request'] = true;
        $verifiedAttendance = $this->timecardsFilterService->getVerifiedAttendance($data);
        $cicos = $this->cicosFilterService->getCicosFilterService($data);
        $output = new \stdClass;
        $data['statuses'] = [config('globals.REQUEST_STATUSES.APPROVED')];
        $this->leavesFiltersService->perform($data, $output);
        $leaves = $output->leavesCollection;
        unset($output->leavs);
        $attendances = $verifiedAttendance->merge($cicos)->merge($leaves);

        return $attendances->groupBy('employee_id');
    }

    /**
     * @return mixed
     */
    public function getWeeklyAttendances(array $data)
    {
        $verifiedAttendance = $this->timecardsFilterService->getVerifiedAttendance($data);
        $verifiedAttendance = $verifiedAttendance->map(function ($attendance) {
            $attendance->dateOnly = (new DateTime($attendance->from))->format('Y-m-d');

            return $attendance;
        });

        $cicos = $this->cicosFilterService->getCicosFilterService($data);
        $cicos = $cicos->map(function ($cicos) {
            $cicos->dateOnly = (new DateTime($cicos->date))->format('Y-m-d');

            return $cicos;
        });

        $output = new \stdClass;
        $data['only_restdays'] = true;
        $data['statuses'] = [config('globals.REQUEST_STATUSES.APPROVED')];

        $this->leavesFiltersService->perform($data, $output);
        $leaves = $output->leavesCollection;
        $leaves = $leaves->map(function ($leave) {
            $leave->dateOnly = (new DateTime($leave->from))->format('Y-m-d');

            return $leave;
        });
        unset($output->leavs);

        $attendances = $verifiedAttendance->merge($cicos)->merge($leaves);

        return $attendances->groupBy(['employee_id', 'dateOnly']);
    }

    public function convertAbsentDayToRestDay(array $data, stdClass &$output)
    {
        $rules = [
            'employee_id' => 'required|integer',
            'time_card_id' => 'required|integer',
        ];
        $validator = \Validator::make($data, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $this->attendanceCrudService->isValidConvert($data, $output);
        if (isset($output->Error)) {
            return;
        }
        $data['date'] = $output->timecard->from;
        $this->entityTagRepository->deleteTag(
            $data['time_card_id'],
            AttendanceUtil::SLOTABLE_TYPE_TIME_CARD,
            PayrollUtil::ABSENCE_TYPES['ABSENT']);

        $output->timecard->delete();

        $this->employeeLeaveRequestCrudService->addRestDay($data, $output);

    }

    public function convertAbsentDayToHoliday(array $data, stdClass &$output)
    {
        $rules = [
            'employee_id' => 'required|integer',
            'time_card_id' => 'required|integer',
        ];
        $validator = \Validator::make($data, $rules);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $this->attendanceCrudService->isValidConvert($data, $output);
        if (isset($output->Error)) {
            return;
        }
        $data['date'] = $output->timecard->from;

        $this->entityTagRepository->deleteTag(
            $data['time_card_id'],
            AttendanceUtil::SLOTABLE_TYPE_TIME_CARD,
            PayrollUtil::ABSENCE_TYPES['ABSENT']);

        $output->timecard->delete();

    }

    public function clockIn(array $data, stdClass &$output)
    {
        if (!$this->validateLatAndLong($data, $output)) {
            return;
        }
        DB::beginTransaction();
        try {
            $deviceType = request()->header('Device-Type', 'unknown');
            $data['by_tablet'] = $deviceType === 'tablet';
            $attendanceService = new NewAttendanceService;
            $attendanceService->clockIn($data, $output);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $output->Error = $e->getMessage();
            Log::info($e);

            return;
        }
    }

    public function clockOut(array $data, stdClass &$output)
    {
        if (!$this->validateLatAndLong($data, $output)) {
            return;
        }
        $deviceType = request()->header('Device-Type', 'unknown');
        $data['by_tablet'] = $deviceType === 'tablet';
        $attendanceService = new NewAttendanceService;
        $attendanceService->clockOut($data, $output);
    }

    public function validateLatAndLong(array $data, stdClass &$output): bool
    {
        $rules = [
            'lat' => 'required|numeric',
            'long' => 'required|numeric',
        ];
        $validator = \Validator::make($data, $rules);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return false;
        }

        return true;
    }

    public function deleteAttendanceForTesting($id) //For testing purposes only
    {

        DB::beginTransaction();
        try {
            $timecard = Timecard::find($id);

            $attendance = $timecard->attendance;
            $ci = $attendance->clockIn;
            $attendance->delete();
            $ci->delete();
            $timecard->delete();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);
            throw $e;
        }

        return response()->json(['message' => 'Attendance deleted successfully']);
    }
}
