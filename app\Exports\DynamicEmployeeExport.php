<?php

namespace App\Exports;

use App\Enums\Employee\EmployeeBulkOperationTypeEnum;
use App\Enums\Employee\EmployeeExportCategoryEnum;
use App\Enums\Employee\EmployeeExportFieldEnum;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwe<PERSON>ite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class DynamicEmployeeExport implements ShouldAutoSize, WithColumnFormatting, WithEvents, WithHeadings, WithStyles
{
    private $counters;
    private $code_type;
    private $selectedFields;
    private $operationType;
    private $companyCountryDialCode;
    private $employees;

    public function __construct($counters = null, $code_type = null, $selectedFields = [], $operationType = 'add', $companyCountryDialCode = '', $employees = null)
    {
        if ($counters == null) {
            $counters = [
                'branches_count' => 0,
                'titles_count' => 0,
                'nationality_count' => 0,
                'military_status_count' => 0,
                'gender_count' => 0,
                'religion_count' => 0,
                'marital_status_count' => 0,
                'employment_type_count' => 0,
                'degree_type_count' => 0,
                'contract_duration_count' => 0,
            ];
        }
        $this->counters = $counters;
        $this->code_type = $code_type;
        $this->selectedFields = $selectedFields;
        $this->operationType = $operationType;
        $this->companyCountryDialCode = $companyCountryDialCode;
        $this->employees = $employees;
    }



    public function headings(): array
    {
        $headings = [];

        $essentialFields = null;

        if($this->operationType == EmployeeBulkOperationTypeEnum::ADD->value) {
            $essentialFields = EmployeeExportFieldEnum::getFieldsForCategoryForBulkAdd(EmployeeExportCategoryEnum::ESSENTIAL);
        } else if($this->operationType == EmployeeBulkOperationTypeEnum::EDIT->value) {
            $essentialFields = EmployeeExportFieldEnum::getFieldsForCategoryForBulkEdit(EmployeeExportCategoryEnum::ESSENTIAL);
        }

        // we include employee code if the company disabled automatic code generation or for edit operations
        foreach ($essentialFields as $field) {
            if ($field !== EmployeeExportFieldEnum::EMPLOYEE_CODE->value) {
                $fieldEnum = EmployeeExportFieldEnum::from($field);

                // If it's a phone field, check if it's bulk edit and primary phone
                if (EmployeeExportFieldEnum::isPhoneField($fieldEnum)) {
                    // For bulk edit with primary phone, use single column format
                    if ($this->operationType === EmployeeBulkOperationTypeEnum::EDIT->value && $fieldEnum === EmployeeExportFieldEnum::PRIMARY_PHONE) {
                        $headings[] = EmployeeExportFieldEnum::getLabel($fieldEnum);
                    } else {
                        // For other phone fields or bulk add, use country code + phone format
                        $countryCodeField = EmployeeExportFieldEnum::getPhoneCountryCodeField($fieldEnum);
                        $headings[] = trans('employeeexport.' . $countryCodeField);
                        $headings[] = EmployeeExportFieldEnum::getLabel($fieldEnum);
                    }
                } else {
                    $headings[] = EmployeeExportFieldEnum::getLabel($fieldEnum);
                }
            } else if ($this->code_type === 'manual' || $this->operationType === EmployeeBulkOperationTypeEnum::EDIT->value) {
                $headings[] = EmployeeExportFieldEnum::getLabel(EmployeeExportFieldEnum::EMPLOYEE_CODE);
            }
        }

        foreach (EmployeeExportCategoryEnum::cases() as $category) {
            if ($category === EmployeeExportCategoryEnum::ESSENTIAL) {
                continue;
            }

            $categoryValue = $category->value;
            $categoryFields = null;
            if($this->operationType == EmployeeBulkOperationTypeEnum::ADD->value) {
                $categoryFields = EmployeeExportFieldEnum::getFieldsForCategoryForBulkAdd($category);
            } else if($this->operationType == EmployeeBulkOperationTypeEnum::EDIT->value) {
                $categoryFields = EmployeeExportFieldEnum::getFieldsForCategoryForBulkEdit($category);
            }

            if($categoryFields && is_array($categoryFields) && !empty($categoryFields)) {
                foreach ($categoryFields as $field) {
                    if (isset($this->selectedFields[$categoryValue][$field]) && $this->selectedFields[$categoryValue][$field]) {
                        $fieldEnum = EmployeeExportFieldEnum::from($field);

                        // If it's a phone field, check if it's bulk edit and primary phone
                        if (EmployeeExportFieldEnum::isPhoneField($fieldEnum)) {
                            // For bulk edit with primary phone, use single column format
                            if ($this->operationType === EmployeeBulkOperationTypeEnum::EDIT->value && $fieldEnum === EmployeeExportFieldEnum::PRIMARY_PHONE) {
                                $headings[] = EmployeeExportFieldEnum::getLabel($fieldEnum);
                            } else {
                                // For other phone fields or bulk add, use country code + phone format
                                $countryCodeField = EmployeeExportFieldEnum::getPhoneCountryCodeField($fieldEnum);
                                $headings[] = trans('employeeexport.' . $countryCodeField);
                                $headings[] = EmployeeExportFieldEnum::getLabel($fieldEnum);
                            }
                        } else {
                            $headings[] = EmployeeExportFieldEnum::getLabel($fieldEnum);
                        }
                    }
                }
            }
        }



        return [$headings];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Calculate highest row based on operation type
                $highestRow = config('globals.MAX_EXCEL_ROWS');
                if ($this->operationType === EmployeeBulkOperationTypeEnum::EDIT->value && $this->employees) {
                    $employeeCount = count($this->employees);
                    $highestRow = $employeeCount + 1;
                }

                $headings = $this->headings()[0];

                // Create a mapping of heading names to their column letters
                $columns = [];
                foreach ($headings as $index => $heading) {
                    $columns[$heading] = Coordinate::stringFromColumnIndex($index + 1);
                }

                // Apply validation to entire column ranges instead of individual cells
                $this->applyColumnValidation($event, $columns, $highestRow);

                $this->setCountryCodeDefaults($event, $columns, $highestRow);

                $this->protectHeaders($event, $columns);

                // For bulk edit, populate employee data
                if ($this->operationType === EmployeeBulkOperationTypeEnum::EDIT->value && $this->employees) {
                    $this->setEmployeeDataDefaults($event, $columns);
                }
            },
        ];
    }

    private function applyColumnValidation($event, $columns, $highestRow): void
    {
        $validationRules = [
            EmployeeExportFieldEnum::LOCATION->value => ['column' => 'A', 'counter' => 'branches_count'],
            EmployeeExportFieldEnum::JOB_TITLE->value => ['column' => 'B', 'counter' => 'titles_count'],
            EmployeeExportFieldEnum::NATIONALITY->value => ['column' => 'C', 'counter' => 'nationality_count'],
            EmployeeExportFieldEnum::MILITARY_STATUS->value => ['column' => 'D', 'counter' => 'military_status_count'],
            EmployeeExportFieldEnum::GENDER->value => ['column' => 'E', 'counter' => 'gender_count'],
            EmployeeExportFieldEnum::RELIGION->value => ['column' => 'F', 'counter' => 'religion_count'],
            EmployeeExportFieldEnum::MARITAL_STATUS->value => ['column' => 'G', 'counter' => 'marital_status_count'],
            EmployeeExportFieldEnum::EMPLOYMENT_TYPE->value => ['column' => 'H', 'counter' => 'employment_type_count'],
            EmployeeExportFieldEnum::DEGREE_TYPE->value => ['column' => 'I', 'counter' => 'degree_type_count'],
            EmployeeExportFieldEnum::LATEST_CONTRACT_DURATION->value => ['column' => 'J', 'counter' => 'contract_duration_count'],
        ];

        foreach ($validationRules as $fieldEnumValue => $rule) {
            $fieldLabel = EmployeeExportFieldEnum::getLabel(EmployeeExportFieldEnum::from($fieldEnumValue));
            if (isset($columns[$fieldLabel])) {
                $columnLetter = $columns[$fieldLabel];
                $range = "{$columnLetter}2:{$columnLetter}{$highestRow}";

                // Apply validation to the entire range at once
                $validation = $event->sheet->getDelegate()->getDataValidation($range);
                $this->setValidation($validation);
                $validation->setFormula1('\'Dropdown Values\'!$' . $rule['column'] . '$2:$' . $rule['column'] . '$' . ($this->counters[$rule['counter']] + 1));
            }
        }
    }

    private function setValidation(&$validation): void
    {
        $validation->setType(DataValidation::TYPE_LIST);
        $validation->setErrorStyle(DataValidation::STYLE_INFORMATION);
        $validation->setAllowBlank(false);
        $validation->setShowInputMessage(true);
        $validation->setShowErrorMessage(true);
        $validation->setShowDropDown(true);
    }

    private function setCountryCodeDefaults($event, $columns, $highestRow): void
    {
        if (empty($this->companyCountryDialCode)) {
            return;
        }

        $countryCodeFields = [
            trans('employeeexport.primary_phone_country_code'),
            trans('employeeexport.secondary_phone_country_code'),
            trans('employeeexport.emergency_contact_phone_country_code'),
        ];
        foreach ($countryCodeFields as $fieldLabel) {
            if (isset($columns[$fieldLabel])) {
                $columnLetter = $columns[$fieldLabel];
                for ($row = 2; $row <= $highestRow; $row++) {
                    $event->sheet->getDelegate()->getCell("{$columnLetter}{$row}")
                        ->setValueExplicit($this->companyCountryDialCode, DataType::TYPE_STRING);
                }
            }
        }
    }

    private function protectHeaders($event, $columns): void
    {
        $sheet = $event->sheet->getDelegate();
        $sheet->getStyle($sheet->calculateWorksheetDimension())->getProtection()->setLocked(false);
        foreach ($columns as $columnLetter) {
            $sheet->getStyle($columnLetter . '1')->getProtection()->setLocked(true);
        }
        $sheet->getProtection()->setSheet(true);
        $sheet->getProtection()->setSelectLockedCells(false);
    }

    private function setEmployeeDataDefaults($event, $columns): void
    {
        $sheet = $event->sheet->getDelegate();

        $essentialFields = EmployeeExportFieldEnum::getFieldsForCategoryForBulkEdit(EmployeeExportCategoryEnum::ESSENTIAL);

        foreach ($this->employees as $rowIndex => $employee) {
            $excelRowNumber = $rowIndex + 2;

            foreach ($essentialFields as $fieldKey) {
                $fieldEnum = EmployeeExportFieldEnum::tryFrom($fieldKey);
                if (!$fieldEnum) {
                    continue;
                }

                $fieldValue = $this->getEmployeeFieldValue($employee, $fieldEnum);

                $fieldLabel = EmployeeExportFieldEnum::getLabel($fieldEnum);
                if (isset($columns[$fieldLabel])) {
                    $columnLetter = $columns[$fieldLabel];
                    $sheet->getCell("{$columnLetter}{$excelRowNumber}")
                        ->setValueExplicit($fieldValue, DataType::TYPE_STRING);
                    //$sheet->setCellValue($columnLetter . $excelRowNumber, $fieldValue);

                    $shouldProtectCell = true;
                    if ($fieldEnum === EmployeeExportFieldEnum::EMPLOYEE_CODE && $this->code_type === 'manual') {
                        $shouldProtectCell = false;
                    }

                    if ($shouldProtectCell) {
                        $sheet->getStyle($columnLetter . $excelRowNumber)->getProtection()->setLocked(true);
                    }
                }
            }
        }

        $stopRowNumber = count($this->employees) + 2;
        foreach ($columns as $columnLetter) {
            $sheet->getStyle($columnLetter . $stopRowNumber)->getProtection()->setLocked(true);
        }

        $sheet->getProtection()->setSheet(true);
        $sheet->getProtection()->setSelectLockedCells(false);
    }

    private function getEmployeeFieldValue($employee, EmployeeExportFieldEnum $fieldEnum): string
    {
        return match ($fieldEnum) {
            EmployeeExportFieldEnum::EMPLOYEE_CODE => $employee->employee_number ?? '',
            EmployeeExportFieldEnum::EMPLOYEE_NAME_AR => $employee->name_ar ?? '',
            EmployeeExportFieldEnum::EMPLOYEE_NAME_EN => $employee->name_en ?? '',
            EmployeeExportFieldEnum::PRIMARY_PHONE => $employee->phone ?? '',
            default => '',
        };
    }

    public function columnFormats(): array
    {
        // Calculate highest row based on operation type
        $highestRow = config('globals.MAX_EXCEL_ROWS');
        if ($this->operationType === EmployeeBulkOperationTypeEnum::EDIT->value && $this->employees) {          
            $employeeCount = count($this->employees);
            $highestRow = $employeeCount + 1;
        }

        $headings = $this->headings()[0];
        $formats = [];
        for ($i = 1; $i <= count($headings); $i++) {
            $colLetter = Coordinate::stringFromColumnIndex($i);
            $formats[$colLetter . '1:' . $colLetter . $highestRow] = NumberFormat::FORMAT_TEXT;
        }
        return $formats;
    }

    public function styles(Worksheet $sheet)
    {
        $headings = $this->headings()[0];

        foreach ($headings as $index => $heading) {
            $colLetter = Coordinate::stringFromColumnIndex($index + 1);
            $sheet->getStyle($colLetter . '1')->getFont()->setBold(true);
        }
    }


}
