<?php

return [
    'unauthorized' => 'Unauthorized',
    'invalid_id' => 'Invalid identifier',
    'user_already_registered' => 'User Already Registered please login.',
    'invalid_token' => 'Invalid token.',
    'wrong_email_or_password' => 'Sorry, wrong email address or password. Please try again.',
    'phone_does_not_exist' => 'Sorry, this phone number does not correspond to a verified user',
    'incorrect_otp' => 'Wrong code, please make sure you entered the correct code',
    'code_sent_to_phone' => 'A 4 digits code has been sent to your phone',
    'phone_number_exists' => 'There is already an employee with this phone number',
    'employee_name_exists' => 'There is already an employee with this name',
    'employee_not_found' => 'Employee not found',
    'employee_info_not_found' => 'Employee info not found',
    'national_id_exists' => 'There is already an employee with this national id',
    'unprocessable' => 'Unprocessable',
    'error_occured_send_sms' => 'An error occured when trying to send sms to this phone number',
    'company_has_key' => 'Your company has already added this setting',
    'setup_is_finished_already' => 'The setup is finished already',
    'bank_not_linked_to_company' => 'This bank is not linked to you company',
    'company_en_name_exists' => 'There is another company with the same english name',
    'company_ar_name_exists' => 'There is another company with the same arabic name',
    'cannot_delete' => 'You cannot delete this item',
    'terminated_employee_has_attendance' => 'Cannot terminate this employee as they have attendance after the termination date',
    'title_belongs_to_attendance_group' => 'This title already belongs to another attendance group',
    'attendance_group_with_same_name' => 'There is another deduction policy with the same name, please choose another name',
    'overtime_group_with_same_name' => 'There is another overtime policy with the same name, please choose another name',
    'delete_titles_first' => 'You can not delete this role, there are titles assigned to this role',
    'titles_must_be_unique' => 'Titles in the approval cycle must be unique ',
    'title_should_has_one_employee' => 'Any title in approval cycle should has one employee ',
    'employee_number_exists' => 'There is already an employee with this code',
    'cannot_delete_department' => 'You can not delete this department as it has assigned titles',
    'cannot_delete_area' => 'You can not delete this area as it has assigned branches',
    'cannot_delete_title' => 'You can not delete this area as it has assigned employees',
    'cannot_delete_branch' => 'You can not delete this area as it has assigned employees',
    'penalty_group_with_same_name' => 'There is another penalty group with the same name, please choose another name',
    'penalty_group_not_applicable_to_employee' => 'This penalty group does not apply to this employee',
    'penalties_has_no_cycle' => 'There is no approved cycle for penalties. Please contact the administrator',
    'no_approving_employees_found' => 'There are no approving employees found',
    'cant_edit_approver_took_action' => 'You can not edit this penalty as an approver has already taken an action on it',
    'cant_delete_approver_took_action' => 'You can not delete this penalty as an approver has already taken an action on it',
    'penalty_amount_not_in_range' => 'The entered number does not apply to the specified range for this type of penalties  : ',
    'penalty_after_payroll_period' => 'You can not add a penalty in a previous month',
    'penalty_not_approved' => 'You can not cancel a penalty that has not been approved',
    'penalty_group_has_penalties' => 'You can not delete the penalty type because there are penalties associated with it',
    'penalty_after_termination_date' => 'You can not add a penalty after the termination date',
    'internal_server_error' => 'Internal server error',
    'title_belongs_to_another_request_group' => 'This title belongs to another request group: ',
    'can_not_take_this_action' => 'You can not take this action',
    'workflow_is_completed' => 'This workflow is completed',
    'title_belongs_to_another_sub_department' => 'This title belongs to another sub department',
    'role_with_me_scope_cant_be_in_workflow' => 'Role with me scope can not be in approval cycles',
    'no_calculation_details' => 'There is no payroll calculation details to be shown',
    'no_payroll_to_be_calculated' => 'There is no payroll to be calculated',
    'can_not_update_tag_in_finalized_payroll' => 'You can not update this entity in finalized payroll',
    'holiday_balance_not_found' => 'Holiday balance not found',
    'amount_exceeds_remaining_balance' => 'The amount exceeds the remaining balance',
    'holiday_balance_has_expired' => 'Holiday balance has expired',
    'can_not_delete_role_with_request_workflows' => 'You can not delete this role because it has request workflows',
    'can_not_change_employee_code_type' => 'You can not change the employee code type after it has been set to manual',
    'can_not_edit_auto_employee_number' => 'You can not edit the employee number as it is auto generated',
    'employee_number_required' => 'Employee number is required',
    'payroll_before_holiday' => 'You can not payout this holiday now ',
    'absence_policy_with_same_name' => 'There is another absence policy with the same name, please choose another name',
    'title_belongs_to_absence_policy' => 'This title already belongs to another absence policy',
    'late_policy_with_same_name' => 'There is another late policy with the same name, please choose another name',
    'title_belongs_to_late_policy' => 'This title already belongs to another late policy',
    'same_payroll_rehire' => 'Cannot rehire an employee before the current payroll ends.',
    'termination_not_approved' => 'The employee is already terminated',
    'termination_request_done' => 'There is no pending termination requests for this employee.',
    'rehire_date_not_valid' => 'Rehiring Date should be after : ',
    'can_not_cancel_termination' => 'you can not cancel termination',
    'termination_request_exist' => 'There is already a termination request for this employee.',
    'loan_request_on_probation' => 'You cannot request loan unless you have passed your probation period',
    'loan_amount_exceeds_allowed_amount' => 'The loan amount exceeds the allowed amount',
    'loan_duration_exceeds_allowed_duration' => 'Loan duration exceeds the maximum allowed duration',
    'ongoing_loan' => 'You can not request a new loan because you have an ongoing loan',
    'pending_loan_exists' => 'You cannot request a new loan because you have a pending loan',
    'pending_salary_advance_exists' => 'You cannot request a new loan because you have a pending salary advance',
    'employee_salary_not_found' => 'Employee salary information not found',
    'loan_salary_advance_policies_disabled' => 'Loan and salary advance policies are not enabled',
    'no_loan_salary_advance_policy_for_title' => 'No loan or salary advance policy is assigned to your job title',
    'edit_disbursed_loans_only' => 'You can only edit loans that have been disbursed',
    'extra_workday_policy_with_same_name' => 'There is another extra workday policy with the same name, please choose another name',
    'title_belongs_to_extra_workday_policy' => 'This title already belongs to another Extra Workday policy',
    'can_not_take_action_on_request_with_finalized_payroll' => 'You can not take an action on this request because the payroll is finalized',

    'not_enough_excuse_hours' => 'You do not have enough excuse hours',
    'block_excuse_policy_update' => 'You can not update this policy, There are employees who consumed from the excuse balance',
    'mission_on_finalized_payroll' => 'Can not add mission on a finalized Payroll',
    'title_doesnt_apply_missions' => 'This title does not apply to missions',
    'mission_overlaps_attendance' => 'There is already an overlapping attendance with the clock-in and clock-out time.',
    'mission_overlaps_restday' => 'There is already a rest day on mission date.',
    'mission_overlaps_leave' => 'There is already a leave on mission date.',
    'no_enough_annual_leave_balance' => 'The employee does not have enough annual leave balance',
    'mission_exists_on_day' => 'There is already a mission on this day',
    'cant_edit_mission_approver_took_action' => 'You can not edit this mission as an approver has already taken an action on it',
    'you_can_not_request_more_than_one_per_day' => 'You can not request more than one request per day',
    'already_have_pending_request' => 'You already have a pending request.',
    'partial_leave_exists' => 'There is a partial leave around the date',
    'can_not_remove_central_department_already_used' => 'can not remove central department already used',
    'pending_mission_on_date' => 'You can not request a mission on this date as there is a pending mission on it',
    'failed_retrieve_templates' => 'Failed to retrieve templates',
    'failed_create_template' => 'Failed to create template',
    'template_not_found' => 'Template not found or cannot be deleted',
    'failed_delete_template' => 'Failed to delete template',
    'templates_retrieved_success' => 'Templates retrieved successfully',
    'template_created_success' => 'Template created successfully',
    'template_deleted_success' => 'Template deleted successfully',
    'employee_profile_created_success' => 'Employee profile created successfully',
    'profile_updated_success' => 'Profile updated successfully',
    'failed_generate_download' => 'Failed to generate download file',
    'employees_bulk_added_success' => 'Employees added successfully via bulk upload',
    'employees_bulk_updated_success' => 'Employees updated successfully via bulk upload',
    'failed_process_bulk_upload' => 'Failed to process bulk upload',
    'bulk_upload_failed' => 'Bulk upload failed',
    'bulk_upload_failed_with_errors' => 'Bulk upload failed due to processing errors. All changes have been rolled back.',
    'bulk_upload_partial_success' => 'Processed :successful employees successfully, :errors rows had errors.',
];
