<?php

namespace App\Console\Commands;

use App\Jobs\NotClockOutYetPushNotification;
use App\Models\User;
use App\Services\TimeTracking\CrudServices\AttendanceCrudService;
use Illuminate\Console\Command;

class clockOutPushNotification extends Command
{
    public function __construct(private AttendanceCrudService $attendanceCrudService)
    {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clock:out:push:notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'send notification In the time of shift end and 10 mins after shift end time in case the user did not CICO';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (env('APP_ENV') != 'local') {
            $users = User::query()->whereIn('id', $this->attendanceCrudService->getUsersIdsWithoutClockOut())->get();
            dispatch(new NotClockOutYetPushNotification($users));
        }
    }
}
