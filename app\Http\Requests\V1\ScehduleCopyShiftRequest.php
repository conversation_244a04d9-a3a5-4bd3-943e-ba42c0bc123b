<?php

namespace App\Http\Requests\V1;

use App\Rules\EmployeeIdRule;
use Illuminate\Foundation\Http\FormRequest;

class ScehduleCopyShiftRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'shifts' => [
                'required',
                'array',
            ],
            'shifts.*.employeeId' => [
                'required',
                'integer',
                new EmployeeIdRule,
            ],
            'shifts.*.date' => [
                'required',
                'date',
                'after_or_equal:today',
            ],
            'shifts.*.shiftId' => [
                'required',
                'integer',
                'exists:shifts,id',
            ],
        ];
    }
}
