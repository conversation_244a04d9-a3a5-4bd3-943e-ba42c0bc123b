<?php

namespace App\Enums\EmployeeInfo;

enum GenderEnum: string
{
    case MALE = 'male';
    case FEMALE = 'female';

    public static function all(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    /**
     * Get translated gender values for dropdowns
     */
    public static function getTranslatedValues(): array
    {
        return array_map(fn ($case) => trans('employeeexport.' . $case->value), self::cases());
    }

    /**
     * Get the translated label for a gender
     */
    public static function getLabel(self $value): string
    {
        return trans('employeeexport.' . $value->value);
    }
}
