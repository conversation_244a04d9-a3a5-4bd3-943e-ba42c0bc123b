<?php

namespace App\Http\Controllers\V1\CompanySetup;

use App\Exceptions\UnprocessableException;
use App\Exports\V1\ApprovalCycleExport;
use App\FeatureToggles\Unleash;
use App\Http\Controllers\NewCompanySetup\CompanySetupController as OldCompanySetupController;
use App\Http\Requests\V1\AddCompanyFileCategoryRequest;
use App\Http\Requests\V1\AddEmployeeFileCategoryRequest;
use App\Http\Requests\V1\AddSubDepartmentRequest;
use App\Http\Requests\V1\Attendance\AddAbsenceDeductionRequest;
use App\Http\Requests\V1\Attendance\AddExtraWorkdayPolicyRequest;
use App\Http\Requests\V1\Attendance\AddLateDeductionGroupRequest;
use App\Http\Requests\V1\Attendance\SetAttendanceTogglesRequest;
use App\Http\Requests\V1\Attendance\ToggleDeductionMethodRequest;
use App\Http\Requests\V1\EditCompanyFileCategoryRequest;
use App\Http\Requests\V1\EditEmployeeFileCategoryRequest;
use App\Http\Requests\V1\EditSubDepartmentRequest;
use App\Http\Requests\V1\GetCentralRequest;
use App\Http\Requests\V1\Holidays\AddPublicHolidaysPolicyRequest;
use App\Http\Requests\V1\Holidays\EditPublicHolidaysPolicyRequest;
use App\Http\Requests\V1\Leaves\EditCompanyLeaveTypeRequest;
use App\Http\Requests\V1\RequestGroups\AddRequestGroupRequest;
use App\Http\Requests\V1\RequestGroups\AddWorkFlowsToRequestGroupRequest;
use App\Http\Requests\V1\RequestGroups\EditRequestGroupRequest;
use App\Http\Requests\V1\RequestGroups\GetRequestGroupTitlesRequest;
use App\Http\Requests\WorkTypePolicyRequest;
use App\Http\Resources\CompanyAttendancePoliciesCollection;
use App\Http\Resources\V1\Holidays\PublicHolidaysPolicyCollection;
use App\Http\Resources\WorkTypePolicyResource;
use App\Models\CompanyLeaveType;
use App\Repositories\NewRoleRepository;
use App\Repositories\Repository;
use App\Repositories\V1\EmployeeRepository;
use App\Services\PayrollSetup\SystemSettingsService;
use App\Services\V1\Attendance\AbsenceDeductionPoliciesService;
use App\Services\V1\Attendance\AttendanceSettingsService;
use App\Services\V1\Attendance\ExtraWorkdayPoliciesService;
use App\Services\V1\Attendance\LateDeductionGroupService;
use App\Services\V1\Files\CompanyFileCategoryService;
use App\Services\V1\Files\EmployeeFileCategoryService;
use App\Services\V1\Holidays\PublicHolidaysPolicyService;
use App\Services\V1\InternalDashboard\CustomerSuccess\CustomerSuccessCompanyService;
use App\Services\V1\LeaveManagement\CompanyLeaveTypesService;
use App\Services\V1\SubDepartment\SubDepartmentService;
use App\Services\V1\WorkFlows\RequestGroupsService;
use App\Services\V1\WorkFlows\RequestWorkFlowService;
use App\Services\V1\WorkFlows\ScopeService;
use App\Traits\DataPreparation;
use App\Traits\GetIdsTrait;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class CompanySetupController extends OldCompanySetupController
{
    use DataPreparation, GetIdsTrait;

    private $titlesRepository;

    private $departmentRepository;

    private $employeeRepository;

    private $areaRepository;

    private $branchRepository;

    public function __construct(
        private CompanyFileCategoryService      $companyFileCategoryService,
        private EmployeeFileCategoryService     $employeeFileCategoryService,
        private RequestGroupsService            $requestGroupsService,
        private RequestWorkFlowService          $requestWorkFlowService,
        private ScopeService                    $scopeService,
        private NewRoleRepository               $roleRepository,
        private SubDepartmentService            $subDepartmentService,
        private PublicHolidaysPolicyService     $publicHolidaysPolicyService,
        private AbsenceDeductionPoliciesService $AbsenceDeductionPoliciesService,
        private LateDeductionGroupService       $lateDeductionGroupService,
        private AttendanceSettingsService       $attendanceSettingsServiceV1,
        private CustomerSuccessCompanyService   $customerSuccessCompanyService,
        private EmployeeRepository              $newEmployeeRepository,
        private ExtraWorkdayPoliciesService     $extraWorkdayPoliciesService,
        private CompanyLeaveTypesService        $companyLeaveTypesServiceV1,
        protected SystemSettingsService         $systemSettingsService,
    )
    {
        $this->titlesRepository = Repository::getRepository('Title');
        $this->employeeRepository = Repository::getRepository('Employee');
        $this->departmentRepository = Repository::getRepository('Department');
        $this->branchRepository = Repository::getRepository('Branch');
        $this->areaRepository = Repository::getRepository('Area');

    }

    public function getAllCompanyFileCategories(Request $request)
    {

        $data = $request->validate([
            'search_value' => 'string',
        ]);

        $categories = $this->companyFileCategoryService->getAllCompanyFileCategories($data);

        return getResponseStructure(['data' => $categories],
            HttpStatusCodeUtil::OK, 'Available Company File Categories');
    }

    public function addCompanyFileCategory(AddCompanyFileCategoryRequest $request)
    {
        $data = $request->validated();
        $category = $this->companyFileCategoryService->add($data);

        return getResponseStructure(['data' => $category],
            HttpStatusCodeUtil::OK, 'Category Added Successfully');
    }

    public function updateCompanyFileCategory($id, EditCompanyFileCategoryRequest $request)
    {

        $data = $request->validated();
        $category = $this->companyFileCategoryService->update($id, $data);

        return getResponseStructure(['data' => $category],
            HttpStatusCodeUtil::OK, 'Category Updated Successfully');
    }

    public function deleteCompanyFileCategory(int $id)
    {
        $this->companyFileCategoryService->delete($id);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Category Deleted Successfully');
    }

    public function getAllEmployeeFileCategories(Request $request)
    {
        $data = $request->validate([
            'search_value' => 'string',
        ]);

        $categories = $this->employeeFileCategoryService->getAllEmployeeFileCategories($data);

        return getResponseStructure(['data' => $categories],
            HttpStatusCodeUtil::OK, 'Available Employee File Categories');
    }

    public function addEmployeeFileCategory(AddEmployeeFileCategoryRequest $request)
    {
        $data = $request->validated();
        $data['company_id'] = auth()->user()->company_id;
        $category = $this->employeeFileCategoryService->add($data);

        return getResponseStructure(['data' => $category],
            HttpStatusCodeUtil::OK, 'Category Added Successfully');
    }

    public function updateEmployeeFileCategory($id, EditEmployeeFileCategoryRequest $request)
    {
        $data = $request->validated();
        $category = $this->employeeFileCategoryService->update($id, $data);

        return getResponseStructure(['data' => $category],
            HttpStatusCodeUtil::OK, 'Category Updated Successfully');
    }

    public function deleteEmployeeFileCategory($id)
    {
        $this->employeeFileCategoryService->delete($id);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Category Deleted Successfully');
    }

    public function getAllRequestGroups(Request $request)
    {

        $groups = $this->requestGroupsService->getAllRequestGroups();

        return getResponseStructure(['data' => $groups],
            HttpStatusCodeUtil::OK, 'Available Request Groups');
    }

    public function addRequestGroup(AddRequestGroupRequest $request)
    {
        $data = $request->validated();

        $group = $this->requestGroupsService->add($data);

        return getResponseStructure(['data' => $group],
            HttpStatusCodeUtil::OK, 'Request Group Added Successfully');
    }

    public function updateRequestGroup(int $id, EditRequestGroupRequest $request)
    {
        $data = $request->validated();

        $group = $this->requestGroupsService->update($id, $data);

        return getResponseStructure(['data' => $group],
            HttpStatusCodeUtil::OK, 'Request Group Updated Successfully');
    }

    public function deleteRequestGroup(int $id)
    {
        $this->requestGroupsService->delete($id);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Request Group Deleted Successfully');
    }

    public function isValidLeaveType($leaveTypeUuid)
    {
        $companyLeaveTypeExists = CompanyLeaveType::where('uuid', $leaveTypeUuid)->exists();
        if (!$companyLeaveTypeExists) {
            throw new UnprocessableException('Invalid Workflow Leave Type');
        }
    }

    public function addWorkFlowsToRequestGroup(int $id, AddWorkFlowsToRequestGroupRequest $request)
    {

        DB::beginTransaction();
        try {

            $data = $request->validated();
            $this->requestWorkFlowService->addWorkFlowsToRequestGroup($id, $data);
            DB::commit();

            return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK, 'WorkFlows Added Successfully to Request Group');
        } catch (\Exception $e) {

            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function getRequestGroupTitles(GetRequestGroupTitlesRequest $request)
    {
        $data = $request->validated();

        $titles = $this->requestGroupsService->getRequestGroupTitles($data);

        return getResponseStructure(['data' => $titles],
            HttpStatusCodeUtil::OK, 'Available Titles');

    }

    public function getCompanyScopes()
    {
        $scopes = $this->scopeService->getCompanyScopes();

        return getResponseStructure(['data' => $scopes],
            HttpStatusCodeUtil::OK, 'Available Scopes');
    }

    // public function exportAllEmployeesBranches(){

    //     $usersWithMoreThanOnePermission = User::withoutGlobalScopes()->limit(8)->get()->filter(function($user) {
    //         $permissions = $user->getAllPermissions();
    //         return count($permissions) > 1;
    //     });

    //    $data = $this->exportEmployeeBranchesService->prepareData($usersWithMoreThanOnePermission);

    //    return  Excel::download(new EmployeeBranchesExport($data), 'EmployeeBranches.xlsx');
    // }

    public function getCompanySubDepartments(GetCentralRequest $request)
    {
        $companySubDepartments = $this->subDepartmentService->getCompanySubDepartments($request->validated());

        return getResponseStructure(['data' => $companySubDepartments],
            HttpStatusCodeUtil::OK, 'Available Sub Departments');
    }

    public function addSubDepartment(int $department, AddSubDepartmentRequest $request)
    {
        $data = $request->validated();
        if (!isset($data['name_en'])) {
            $data['name_en'] = $data['name_ar'];
        }
        $subDepartment = $this->subDepartmentService->addSubDepartment($department, $data);

        return getResponseStructure(['data' => $subDepartment],
            HttpStatusCodeUtil::OK, 'Sub Department Added Successfully');

    }

    public function updateSubDepartment(int $id, EditSubDepartmentRequest $request)
    {
        $data = $request->validated();

        $subDepartment = $this->subDepartmentService->updateSubDepartment($id, $data);

        return getResponseStructure(['data' => $subDepartment],
            HttpStatusCodeUtil::OK, 'Sub Department Updated Successfully');
    }

    public function deleteSubDepartment(int $id)
    {
        $this->subDepartmentService->deleteSubDepartment($id);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Sub Department Deleted Successfully');

    }

    public function togglePublicHolidaysPolicy(Request $request)
    {

        DB::beginTransaction();
        try {
            $this->systemSettingsService->togglePublicHolidaysPolicy();
            DB::commit();

            return getResponseStructure(
                [
                    'data' => [],
                ],
                HttpStatusCodeUtil::OK,
                'Public Holidays Policy Toggled Successfully'
            );
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }

    }

    public function getPublicHolidaysStatus(Request $request)
    {
        $status = $this->systemSettingsService->getPublicHolidaysStatus();

        return getResponseStructure(['data' => [
            'apply_public_holiday' => $status,
        ]],
            HttpStatusCodeUtil::OK, 'Public Holidays Status');
    }

    public function hasCustomPublicHolidaysPolicy()
    {
        $hasCustomPolicy = $this->publicHolidaysPolicyService->hasCustomPublicHolidaysPolicy();

        return getResponseStructure(['data' => $hasCustomPolicy],
            HttpStatusCodeUtil::OK, 'Public Holidays Policy');
    }

    public function getPublicHolidaysPolicies()
    {
        $policies = $this->publicHolidaysPolicyService->getPublicHolidaysPolicies();

        return getResponseStructure(['data' => new PublicHolidaysPolicyCollection($policies)],
            HttpStatusCodeUtil::OK, 'Public Holidays Policies');

    }

    public function addPublicHolidaysPolicy(
        AddPublicHolidaysPolicyRequest $request
    )
    {
        DB::beginTransaction();
        try {
            $data = $request->validated();
            $policy = $this->publicHolidaysPolicyService->addPublicHolidaysPolicy($data);
            DB::commit();

            return getResponseStructure(['data' => $policy],
                HttpStatusCodeUtil::OK, 'Public Holidays Policy Added Successfully');

        } catch (\Exception $e) {

            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function updatePublicHolidaysPolicy(
        int                             $id,
        EditPublicHolidaysPolicyRequest $request
    )
    {
        $data = $request->validated();
        $policy = $this->publicHolidaysPolicyService->updatePublicHolidaysPolicy($id, $data);

        return getResponseStructure(['data' => $policy],
            HttpStatusCodeUtil::OK, 'Public Holidays Policy Updated Successfully');

    }

    public function deletePublicHolidaysPolicy(int $id)
    {
        $this->publicHolidaysPolicyService->deletePublicHolidaysPolicy($id);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Public Holidays Policy Deleted Successfully');
    }

    public function addAbsenseDeductionPolicy(AddAbsenceDeductionRequest $addAbsenceDeductionRequest)
    {
        $data = $addAbsenceDeductionRequest->validated();
        DB::beginTransaction();
        try {
            $policy = $this->AbsenceDeductionPoliciesService->addPolicyWithTitles($data);
            DB::commit();

            return getResponseStructure(['data' => $policy],
                HttpStatusCodeUtil::OK, 'Absense Deduction Policy Added Successfully');

        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }

    }

    public function editAbsenseDeductionPolicy(int $id, AddAbsenceDeductionRequest $addAbsenceDeductionRequest)
    {
        $data = $addAbsenceDeductionRequest->validated();
        DB::beginTransaction();
        try {
            $policy = $this->AbsenceDeductionPoliciesService->editPolicyWithTitles($id, $data);
            DB::commit();

            return getResponseStructure(['data' => $policy],
                HttpStatusCodeUtil::OK, 'Absense Deduction Policy Updated Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }

    }

    public function getAbsenseDeductionPolicies()
    {
        $groups = $this->AbsenceDeductionPoliciesService->list();

        return getResponseStructure(['data' => $groups],
            HttpStatusCodeUtil::OK, 'Available Absense Deduction Groups');
    }

    public function deleteAbsenseDeductionPolicy($id)
    {
        DB::beginTransaction();
        try {
            $this->AbsenceDeductionPoliciesService->delete($id);
            DB::commit();

            return getResponseStructure(['data' => []],
                HttpStatusCodeUtil::OK, 'Absense Deduction Policy Deleted Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function getLateDeductionGroups()
    {

        $groups = $this->lateDeductionGroupService->list()->toArray();
        $deductionMethod = $this->systemSettingsService->getLateDeductionMethod();
        foreach ($groups as &$group) {
            $group['deduction_method'] = $deductionMethod;
        }

        return getResponseStructure(
            ['data' => $groups],
            HttpStatusCodeUtil::OK,
            'Available Late Deduction Groups');
    }

    public function getDeductionMethod()
    {
        $deductionMethod = $this->systemSettingsService->getLateDeductionMethod();

        return getResponseStructure([
            'data' => ['deduction_method' => $deductionMethod]],
            HttpStatusCodeUtil::OK,
            'Late Deduction Method');
    }

    public function setDeductionMethod(ToggleDeductionMethodRequest $request)
    {
        $data = $request->validated();

        $this->systemSettingsService->setLateDeductionMethod($data['deduction_method']);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK,
            'Late Deduction Method Updated Successfully');
    }

    public function deleteLateDeductionGroup($id)
    {
        DB::beginTransaction();
        try {
            $this->lateDeductionGroupService->delete($id);
            DB::commit();

            return getResponseStructure(['data' => []],
                HttpStatusCodeUtil::OK, 'Late Deduction Group Deleted Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function addLateDeductionGroup(AddLateDeductionGroupRequest $request)
    {
        $data = $request->validated();
        DB::beginTransaction();
        try {
            $prevTo = -1;
            foreach ($data['late_policies'] as $key => $policy) {
                if ($policy['from'] != $prevTo + 1 && $prevTo != -1) { // each from should be one more than the previous from except the first one
                    throw new UnprocessableException('Each from should be one more than the previous to except the first one'); // validation error no need to translate it
                }
                $prevTo = $policy['to'];
            }

            $group = $this->lateDeductionGroupService->addWithPolicies($data);

            $unleash = app(Unleash::class);
            if ($unleash->isPartialLeaveEnabled() && isset($data['deduction_method'])) {
                $this->systemSettingsService->setLateDeductionMethod($data['deduction_method']);
            }
            DB::commit();

            return getResponseStructure(['data' => $group],
                HttpStatusCodeUtil::OK, 'Late Deduction Group Added Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function editLateDeductionGroup($id, AddLateDeductionGroupRequest $request)
    {
        $data = $request->validated();
        DB::beginTransaction();
        try {
            $prevTo = -1;
            foreach ($data['late_policies'] as $key => $policy) {
                if ($policy['from'] != $prevTo + 1 && $prevTo != -1) { // each from should be one more than the previous from except the first one
                    throw new UnprocessableException('Each from should be one more than the previous to except the first one'); // validation error no need to translate it
                }
                $prevTo = $policy['to'];
            }

            $group = $this->lateDeductionGroupService->editWithPolicies($id, $data);

            $unleash = app(Unleash::class);
            if ($unleash->isPartialLeaveEnabled()) {
                $this->systemSettingsService->setLateDeductionMethod($data['deduction_method']);
            }
            DB::commit();

            return getResponseStructure(['data' => $group],
                HttpStatusCodeUtil::OK, 'Late Deduction Group Edited Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            throw $e;
        }
    }

    public function setAttendancePoliciesToggles(SetAttendanceTogglesRequest $request)
    {
        $data = $request->validated();
        $this->attendanceSettingsServiceV1->setAttendanceToggles($data);

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Deduction and overtime toggles has been set successfully');
    }

    public function getCompanyAttendancePolicies()
    {
        return getResponseStructure(['data' => new CompanyAttendancePoliciesCollection(
            $this->attendanceSettingsServiceV1->getAttendanceSettings())],
            HttpStatusCodeUtil::OK, 'Available attendance policies');
    }

    public function getWorkTypePolicies()
    {
        $policies = $this->attendanceSettingsServiceV1->getWorkTypePolicies();

        return getResponseStructure(['data' => WorkTypePolicyResource::collection($policies)],
            HttpStatusCodeUtil::OK, 'Work Type Policies');
    }

    public function addWorkTypePolicy(WorkTypePolicyRequest $request)
    {
        $data = $request->validated();
        try {
            DB::beginTransaction();
            $policy = $this->attendanceSettingsServiceV1->addWorkTypePolicy($data);
            DB::commit();

            return getResponseStructure(['data' => $policy],
                HttpStatusCodeUtil::OK, 'Work Type Policy Added Successfully');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);
            \Sentry\captureException($e);
        }

    }

    public function updateWorkTypePolicy($id, WorkTypePolicyRequest $request)
    {
        $data = $request->validated();
        try {
            DB::beginTransaction();
            $policy = $this->attendanceSettingsServiceV1->editWorkTypePolicy($id, $data);
            DB::commit();

            return getResponseStructure(['data' => $policy],
                HttpStatusCodeUtil::OK, 'Work Type Policy Added Successfully');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);
            \Sentry\captureException($e);
        }

    }

    public function exportRequestGroups($companyId)
    {
        config(['globals.user' => null]); // disable company global scope
        $data = $this->newEmployeeRepository->getEmployeesWithTheirApprovers($companyId, 'penalty');

        //        return getResponseStructure(['data' => $data[0]],
        //            HttpStatusCodeUtil::OK, "Work Type Policy Added Successfully");
        return Excel::download(new ApprovalCycleExport($data, $companyId), 'ApprovalCycle.xlsx');

    }

    public function editCompanyLeaveSetupV1($id, EditCompanyLeaveTypeRequest $request)
    {
        $data = $request->getOnlyEditRequestData();

        if (isset($data['name_ar'])) {
            $data['name'] = $data['name_ar'];
        } //TODO these line to be removed after we drop old name columns

        DB::beginTransaction();
        try {
            $allowCashCompensation = $request->getAllowCashCompensation();
            $this->companyLeaveTypesServiceV1->updateWithPolicy($id, $data);
            if (isset($allowCashCompensation)) {
                if ($id != config('globals.annual_leave_id')) {
                    throw new UnprocessableException("You can't set cash compensation for this leave type");
                }
                $this->systemSettingsService->toggleLeaveBalanceCompensationPolicy($allowCashCompensation);
            }
            DB::commit();

            return getResponseStructure(['data' => []],
                HttpStatusCodeUtil::OK, 'Item has been set successfully');
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

    }

    public function getExtraWorkdayPolicies()
    {
        $policies = $this->extraWorkdayPoliciesService->list();

        return getResponseStructure(['data' => $policies], HttpStatusCodeUtil::OK);
    }

    public function addExtraWorkdayPolicy(AddExtraWorkdayPolicyRequest $request)
    {
        $data = $request->validated();
        DB::beginTransaction();
        try {
            $policy = $this->extraWorkdayPoliciesService->addPolicyWithTitles($data);
            DB::commit();

            return getResponseStructure(['data' => $policy],
                HttpStatusCodeUtil::OK, 'Extra Workday Policy Added Successfully');

        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            Log::error($e);

            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }
    }

    public function editExtraWorkdayPolicy($id, AddExtraWorkdayPolicyRequest $request)
    {
        $data = $request->validated();

        DB::beginTransaction();
        try {
            $this->extraWorkdayPoliciesService->editPolicyWithTitles($id, $data);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);
            \Sentry\captureException($e);

            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, 'Extra Workday Policy Has Been Edited Successfully');
    }

    public function deleteExtraWorkdayPolicy($id)
    {
        DB::beginTransaction();
        try {
            $this->extraWorkdayPoliciesService->delete($id);
            DB::commit();

            return getResponseStructure(['data' => []],
                HttpStatusCodeUtil::OK, 'Extra Workday Policy Deleted Successfully');
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
            Log::error($e);

            return getErrorResponseStructure($e->getCode(), $e->getMessage());
        }
    }
}
