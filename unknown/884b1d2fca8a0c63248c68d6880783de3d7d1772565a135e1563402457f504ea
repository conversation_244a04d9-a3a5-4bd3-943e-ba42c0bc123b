<?php

namespace App\DomainData;

use App\Rules\ScopeSubDepartmentIdsRule;
use Illuminate\Validation\Rules\Exists;

trait EmployeeLeaveRequestDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'company_leave_type_id' => 'required|integer',
            'company_leave_type_policy_id' => 'required|integer',
            'employee_id' => 'required|integer',
            'status' => 'string',
            'from' => 'required|date_format:Y-m-d H:i:s',
            'to' => 'required|date_format:Y-m-d H:i:s|after_or_equal:from',
            'net_quantity' => 'numeric',
            'note' => 'string',
            'type' => 'string|in:regular,carryover,grant,prorated',
            'branch_id' => 'integer',
            'uploaded_by_id' => 'integer|exists:employees,id',
            'workflow_id' => 'integer',
            'parent_timecard_id' => 'integer',
            // 'attachments' => 'array|max:5',
            // 'attachments.*' => 'mimes:pdf,doc,docx,png,jpg,jpeg',
            // 'existing_attachment_ids' => 'array',
            // 'existing_attachment_ids.*' =>  [
            //     'integer',
            //     (new Exists('attachments', 'id'))->where(function ($query) {
            //         $query
            //         ->whereNull('deleted_at');
            //     }),
            // ],
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function getLeavesFilterRules(): array
    {
        $rules['from_date'] = ['date', 'required_with:only_net_quantity_in_range'];
        $rules['to_date'] = ['date', 'required_with:only_net_quantity_in_range'];
        $rules['employee_id'] = ['integer'];
        $rules['order_by'] = ['string', 'in:from,created_at']; // from is the leave start date ,, created_at is when the request was requested
        $rules['order_by_type'] = ['string', 'in:asc,desc'];
        $rules['statuses'] = ['array'];
        $rules['statuses.*'] = ['string', 'in:pending,approved,rejected,cancelled,in_process'];
        $rules['branch_ids'] = ['array'];
        $rules['branch_ids.*'] = ['integer'];
        $rules['title_ids'] = ['array'];
        $rules['title_ids.*'] = ['integer'];
        $rules['department_ids'] = ['array'];
        $rules['department_ids.*'] = ['integer', 'exists:departments,id'];
        $rules['page'] = ['integer', 'min:1'];
        $rules['page_size'] = ['integer', 'min:0']; // 0 means no pagination
        $rules['company_leave_type_id'] = ['integer'];
        $rules['search_value'] = ['min:1', 'max:30'];
        $rules['with_restdays'] = ['boolean'];
        $rules['only_net_quantity_in_range'] = ['boolean'];
        $rules['sub_department_ids'] = ['array'];
        $rules['sub_department_ids.*'] = ['integer', new ScopeSubDepartmentIdsRule];

        return $rules;
    }

    public function initializeEmployeeLeaveRequestDto(): void
    {
        //        $this->fillable = array_keys($this->getRules()); // we use $guarded instead of $fillable
    }
}
