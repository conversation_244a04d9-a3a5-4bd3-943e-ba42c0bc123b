<?php

namespace App\Exports;

use App\Models\Employee;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class CompanyEmployeesExport implements FromArray, WithHeadings
{
    public function headings(): array
    {
        $columnNames = [
            'Full Name',
            'Code',
            'Phone Number',
            'National ID',
            'Title',
            'Department',
            'Main Branch',
            'Other Branches',
            'Status',
            'Termination Date',
            'Employment Date',
            'Birth Date',
            'Experience Start Date',
            'Years of Experience',
            'Address',
            '# of Dependents',
            'Gender',
        ];

        return [
            array_values($columnNames),
        ];
    }

    public function array(): array
    {
        $companyId = config('globals.company')->id;

        // $payrollRepository = new PayrollsRepository();
        // $payroll = $payrollRepository->getLastDraftedPayroll();

        $employees = Employee::with('branch', 'title.department', 'employeeInfo', 'branches')->where('company_id', $companyId)->get();
        // ->whereHas('employeeInfo', function($q) use ($payroll){
        //     $q->whereNull('termination_date')->orWhere('termination_date', '>=', $payroll->start);
        // })->get();

        $data = [];
        foreach ($employees as $employee) {
            $data[] = ['Full Name' => $employee->name,
                'Code' => $employee->employee_number,
                'Phone Number' => $employee->phone,
                'National ID' => $employee->national_id,
                'Title' => $employee->title?->name,
                'Department' => $employee->title?->department?->name,
                'Main Branch' => $employee->branch?->name,
                'Other Branches' => $employee->branches?->pluck('name')->join(', '),
                'Status' => $employee->status,
                'Termination Date' => $employee->employeeInfo?->termination_date,
                'Employment Date' => $employee->employeeInfo?->join_date,
                'Birth Date' => $employee->employeeInfo?->birth_date ?? null,
                'Experience Start Date' => $employee->employeeInfo?->career_start_date,
                'Years of Experience' => $employee->employeeInfo?->number_of_years_of_experience ?? 0,
                'Address' => $employee->employeeInfo?->address,
                '# of Dependents' => $employee->employeeInfo?->number_kids,
                'Gender' => $employee->employeeInfo?->gender,

            ];
        }

        return [
            array_values($data),
        ];
    }
}
