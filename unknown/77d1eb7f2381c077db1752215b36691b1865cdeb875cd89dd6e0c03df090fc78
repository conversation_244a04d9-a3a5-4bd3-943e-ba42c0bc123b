<?php

namespace App\DomainData;

trait CustomRoleDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'employee_id' => 'required|numeric',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'permissions' => 'required|json',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeCustomRoleDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
