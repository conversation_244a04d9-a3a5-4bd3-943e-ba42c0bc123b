<?php

namespace App\DomainData;

trait DeductionDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'name' => 'required|string',
            'type' => 'required|string|in:fixed_amount,percentage_of_salary,working_days',
            'value' => 'numeric',
            'added_to_taxes' => 'boolean',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeDeductionDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }

    public function getAdditionalRules(array $rules = []): array
    {
        $rules['static_value'] = ['required', 'boolean'];
        $rules['title_ids'] = ['required', 'array'];
        $rules['title_ids.*'] = ['required', 'numeric'];
        $rules['branch_ids'] = ['required', 'array'];
        $rules['branch_ids.*'] = ['required', 'numeric'];
        $rules['related_objects'] = ['array'];
        $rules['related_objects.*'] = ['in:titles,branches'];

        return $rules;
    }
}
