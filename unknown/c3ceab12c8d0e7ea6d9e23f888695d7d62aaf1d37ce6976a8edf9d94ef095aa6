<?php

namespace App\DomainData;

trait DailyOvertimePolicyDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'from' => 'required|integer|min:0',
            'to' => 'required|integer|min:0',
            'percentage' => 'required|numeric|min:0',
            'overtime_group_id' => 'required|numeric',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeDailyOvertimePolicyDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
