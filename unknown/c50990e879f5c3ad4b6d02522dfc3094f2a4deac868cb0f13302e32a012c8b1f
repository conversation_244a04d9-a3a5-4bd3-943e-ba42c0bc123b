<?php

namespace App\DomainData;

trait EmployeeImageDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'path' => 'required|string',
            'file_name' => 'required|string',
            'type' => 'required|string|in:LEFT,FRONT,RIGHT',
            'employee_id' => 'required|numeric',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeEmployeeImageDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
