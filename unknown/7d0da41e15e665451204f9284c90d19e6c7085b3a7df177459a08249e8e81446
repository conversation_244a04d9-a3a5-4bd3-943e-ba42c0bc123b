<?php

namespace App\DomainData;

trait DailyAttendancePolicyDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'from' => 'required|integer|min:0',
            'to' => 'required|integer|min:0',
            'deduct_days' => 'required|numeric|min:0',
            'sequence' => 'required|integer|min:0',
            'attendance_group_id' => 'required|numeric',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeDailyAttendancePolicyDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
