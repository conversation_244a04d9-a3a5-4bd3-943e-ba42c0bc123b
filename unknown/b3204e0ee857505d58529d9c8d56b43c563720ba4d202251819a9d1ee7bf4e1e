<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class AttendanceExport implements FromCollection, ShouldAutoSize, WithChunkReading, WithHeadings, WithTitle
{
    public function __construct(private $data) {}

    public function collection()
    {
        return collect($this->data);
    }

    public function headings(): array
    {
        return [
            'Employee Name',
            'Code',
            'Branch',
            'Area',
            'Title',
            'Date',
            'Shift Start Time',
            'Clock In Time',
            'Shift End Time',
            'Clock Out Time',
            'Late Duration',
            'Worked Hours',
            'Location',
            'Deductions (Days)',
            'Overtime',
            'Status',
        ];
    }

    public function title(): string
    {
        return 'Attendance Sheet';
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}
