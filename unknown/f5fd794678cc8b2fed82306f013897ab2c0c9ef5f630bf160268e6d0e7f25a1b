<?php

namespace App\Factories\Attendance;

use App\Services\Attendance\UsersTypes\NonTimeCardUserService;
use App\Services\Attendance\UsersTypes\TimeCardUserService;

class AttendanceUserTypeFactory
{
    public static function createAttendanceUser($isTimeCardAble = true)
    {
        if ($isTimeCardAble) {
            return new TimeCardUserService;
        } else {
            return new NonTimeCardUserService;
        }
    }
}
