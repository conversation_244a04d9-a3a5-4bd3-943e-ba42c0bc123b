<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Employee;
use App\Models\EmployeeLeaveRequest;
use App\Traits\GenerateUuid;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixLeavesBranch extends Command
{
    use GenerateUuid;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:leaves:original_branch';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $leaves = EmployeeLeaveRequest::with(['employee.employeeChanges' => function ($q) {
                $q->select('id', 'employee_id', 'from_value', 'to_value', 'as_of_date')
                    ->where('change_type', 'branch')
                    ->where('status', 'completed')
                    ->orderBy('as_of_date', 'asc');
            }])->whereDate('from', '>', '2024-03-01')->get();

            foreach ($leaves as $leave) {
                $employeeBranchOnDate = $this->employeeBranchOnDate($leave->employee, $leave->from);
                if ($employeeBranchOnDate != $leave->branch_id) {
                    echo 'leave with id '.$leave->id.' for employee . '.$leave->employee_id.' changed branch from '.$leave->branch_id.' to '.$employeeBranchOnDate.PHP_EOL;
                    $leave->branch_id = $employeeBranchOnDate;
                    $leave->save();
                }
            }
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
            // dd($e);
        }
    }

    private function employeeBranchOnDate(Employee $employee, $date)
    {
        $date = Carbon::parse($date)->format('Y-m-d');

        if ((! isset($employee->employeeChanges) || count($employee->employeeChanges) == 0)) {
            return $employee->branch_id;
        }

        $lastBranchBeforeDate = -1;
        $firstBranchAfterDate = -1;
        foreach ($employee->employeeChanges as $employeeChange) {
            if ($employeeChange->as_of_date <= $date) {
                $lastBranchBeforeDate = $employeeChange->to_value;
            }
            if ($employeeChange->as_of_date >= $date) { // if != -1 then we have stored first branch before this date
                $firstBranchAfterDate = $employeeChange->from_value;
                break;
            }
        }
        if ($lastBranchBeforeDate != -1) {
            return $lastBranchBeforeDate;
        }

        return $firstBranchAfterDate;

    }
}
