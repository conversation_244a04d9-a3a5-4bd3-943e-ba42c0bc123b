<?php

namespace App\Services\V1\ShiftView;

use App\DataTransferObject\CreateTimeCardObject;
use App\Enums\V2\WorkTypes\RestDaysTypesEnum;
use App\Exceptions\UnprocessableException;
use App\Models\EntityTag;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\V1\Attendance\NewShiftRepository;
use App\Repositories\V1\Attendance\TimecardRepository;
use App\Repositories\V1\Holidays\PublicHolidaysRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveBalancesRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use App\Repositories\V1\SavedLocationRepository;
use App\Repositories\V1\Schedule\TimecardTypeRepository;
use App\Services\BaseService;
use App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService;
use App\Services\NewSchedule\BusinessServices\LinkTitlesBranchesWithShiftService;
use App\Traits\V1\ShiftsValidationTrait;
use App\Traits\V2\WorkTypesTrait;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use stdClass;

class ShiftViewService extends BaseService
{
    use ShiftsValidationTrait, WorkTypesTrait;

    public function __construct(
        protected TimecardRepository                 $timecardRepository,
        protected NewShiftRepository                 $newShiftRepository,
        protected EmployeeLeaveRequestRepository     $employeeLeaveRequestRepository,
        protected NewEmployeeRepository              $employeeRepository,
        protected SavedLocationRepository            $savedLocationRepository,
        protected TimecardTypeRepository             $timecardTypeRepository,
        protected EmployeeLeaveRequestCrudService    $employeeLeaveRequestCrudService,
        protected EmployeeLeaveBalancesRepository    $employeeLeaveBalancesRepository,
        protected LinkTitlesBranchesWithShiftService $linkTitlesBranchesWithShiftService,
        protected PublicHolidaysRepository           $publicHolidaysRepository

    )
    {
    }

    public function getShiftView($request)
    {
        $from = $request['from'];
        $to = $request['to'];
        $branchId = $request['branch_id'];
        $departmentId = $request->get('department_id');
        $shiftId = $request->get('shift_id');

        $shifts = $this->newShiftRepository->getScheduleShiftView($from, $to, $branchId, 0, $departmentId, $shiftId);
        $restDays = $this->employeeLeaveRequestRepository->getEmployeesRestDaysInDateRange(config('globals.rest_day_leave_id'), $from, $to, $branchId, $departmentId);
        $customShifts = $this->newShiftRepository->getScheduleShiftView($from, $to, $branchId, 1, $departmentId, $shiftId);
        $leaves = $this->employeeLeaveRequestRepository->getEmployeesApprovedLeavesInDateRange($from, $to, $branchId, $departmentId);
        $missions = $this->timecardRepository->getMissionsOnDateRange($branchId, $from, $to, $departmentId);
        $publicHolidays = $this->publicHolidaysRepository->getPublicHolidaysInDateRange($from, $to);

        $dates = $this->generateDaysFromTwoDates($from, $to);

        $rawData = collect($dates)->mapWithKeys(function ($_, $date) use ($shifts, $restDays, $customShifts, $leaves, $missions, $publicHolidays) {
            $dailyLeaves = $leaves->filter(function ($leave) use ($date) {
                $leaveFrom = new \DateTime($leave->from);
                $leaveTo = new \DateTime($leave->to);
                $currentDate = new \DateTime($date);

                return $leaveFrom <= $currentDate && $leaveTo >= $currentDate;
            });

            $publicHoliday = $publicHolidays->first(function ($holiday) use ($date) {
                $holidayStart = new \DateTime($holiday->start);
                $holidayEnd = new \DateTime($holiday->end);
                $currentDate = new \DateTime($date);

                return $holidayStart <= $currentDate && $holidayEnd >= $currentDate;
            });

            return [
                $date => [
                    'shifts' => $shifts,
                    'rest_days' => $restDays,
                    'custom_shifts' => $customShifts ?? [],
                    'leaves' => $dailyLeaves,
                    'missions' => $missions->get($date) ?? [],
                    'public_holiday' => $publicHoliday ? [
                        'id' => $publicHoliday->id,
                        'name_en' => $publicHoliday->name_en,
                        'name_ar' => $publicHoliday->name_ar,
                        'start' => $publicHoliday->start,
                        'end' => $publicHoliday->end,
                    ] : null,
                ],
            ];
        });

        return $rawData;
    }

    private function generateDaysFromTwoDates($from, $to)
    {
        $start = new \DateTime($from);
        $end = new \DateTime($to);
        $end->modify('+1 day');

        $interval = new \DateInterval('P1D');
        $dateRange = new \DatePeriod($start, $interval, $end);

        $dates = [];
        foreach ($dateRange as $date) {
            $dates[$date->format('Y-m-d')] = false;
        }

        return $dates;
    }

    public function getEmployeesWithValidations($request)
    {
        $data = [
            'branch_id' => $request['branch_id'],
            'department_ids' => $request['department_ids'],
            'title_ids' => $request['title_ids'],
            'search_value' => $request['search_value'],
            'date' => $request['date'],
            'shift_id' => $request['shift_id'],
            'type' => $request['type'],
        ];

        $employees = $this->employeeRepository->getShiftViewEmployeesByFilters($data);

        foreach ($employees as $employee) {
            $validations = $this->getEmployeeValidations($employee, $request['date'], $request['type']);
            $blockers = $this->getBlockers($validations, 0);
            $employee->blocker_reasons = $blockers;
            $employee->timecard = $validations['timecard'];
            $employee->overlap = $validations['has_timecard_overlap'];

            $employee->selectable = $this->isEmployeeSelectable($request['type'], $validations);
        }

        return $employees;
    }

    private function getEmployeeValidations($employee, $date, $type): array
    {
        $hasTimecard = $this->hasTimecardOverlap($employee->id, $date);
        $timecardAssignableTypes = $this->getTimecardAssignableTypes($employee->company_id);

        return [
            'has_rest_day' => $this->hasRestDayOnDate($employee->id, $date, config('globals.rest_day_leave_id')),
            'has_leave' => $this->hasApprovedLeaveOnDate($employee->id, $date),
            'has_attendance' => $this->hasVerifiedAttendanceOnDate($employee->id, $date),
            'has_timecard' => (bool)$hasTimecard['timecard'],
            'has_timecard_overlap' => $hasTimecard['overlap'] ?? false,
            'has_termination' => $this->hasTerminationDate($employee->id, $date),
            'has_scheduled_transfer' => $this->hasScheduledTransfer($employee->id, $date),
            'timecard' => $hasTimecard['timecard'] ?? null,
            'fixed_timecards' => $type == 'shift' && !in_array($employee->title->workTypePolicy->work_days_type, $timecardAssignableTypes),
            'is_fixed_rest_days' => $type == 'rest_day' && $employee->title->workTypePolicy->rest_days_type == RestDaysTypesEnum::FIXED->value,
        ];
    }

    public function getBlockers(array $validations, $copy): array
    {
        $blockers = [];

        if ($validations['has_rest_day']) {
            $blockers[] = 'rest_day';
        }
        if ($validations['has_leave']) {
            $blockers[] = 'leave';
        }
        if ($validations['has_attendance']) {
            $blockers[] = 'attendance';
        }
        if ($validations['has_termination']) {
            $blockers[] = 'termination';
        }
        if ($validations['has_scheduled_transfer']) {
            $blockers[] = 'scheduled_transfer';
        }
        if ($validations['has_timecard'] && $copy) {
            $blockers[] = 'timecard';
        }
        if ($validations['fixed_timecards']) {
            $blockers[] = 'fixed_timecards';
        }
        if ($validations['is_fixed_rest_days']) {
            $blockers[] = 'fixed_rest_days';
        }

        return $blockers;
    }

    private function isEmployeeSelectable(string $type, array $validations): bool
    {
        $exclusions = [
            $validations['has_leave'],
            $validations['has_attendance'],
            $validations['has_termination'],
            $validations['has_scheduled_transfer'],
        ];

        if ($type == 'shift') {
            $exclusions[] = $validations['fixed_timecards'];
        } elseif ($type === 'rest_day') {
            $exclusions[] = $validations['has_rest_day'];
            $exclusions[] = $validations['has_timecard'];
            $exclusions[] = $validations['is_fixed_rest_days'];
        }

        return !in_array(true, $exclusions, true);
    }

    public function addEmployees($request)
    {
        $shiftStart = Carbon::parse($request['date'] . ' ' . $request['from_time']);
        $shiftEnd = Carbon::parse($request['date'] . ' ' . $request['to_time']);
        if ($request['to_time'] < $request['from_time']) {
            $shiftEnd = $shiftEnd->addDay();
        }
        $currentDateTime = now();
        
        if ($shiftStart < $currentDateTime || $shiftEnd < $currentDateTime) {
            throw new UnprocessableException('Cannot add shift to past date or time.');
        }
        $output = new \stdClass;
        $output->deleted_count = 0;
        $output->Error = [];

        $employees = $this->employeeRepository->getEmployeesWithWorkTypePolicySettings($request['employee_ids']);

        foreach ($employees as $employee) {
            $this->deleteRestDayForEmployees($employee->id, $request['date'], $output);
            $validations = $this->getEmployeeValidations($employee, $request['date'], $request['type']);
            $blockers = $this->getBlockers($validations, 0);
            $this->deleteOverlappingTimeCards($validations);
        }

        return $this->addEmployeesToShift($request, 0);
    }

    public function addEmployeesToShift($request, $copy)
    {
        $isCustomShift = isset($request['is_custom_shift']) && $request['is_custom_shift'];
        $shift = !$isCustomShift || ($isCustomShift && $copy) ? $this->newShiftRepository->find($request['shift_id']) : null;

        if ($isCustomShift && !$copy) {
            $shift = $this->createCustomShift($request);
        }

        $extraAttributes = $this->setExtraAttributes($request, $isCustomShift, $copy);

        $createdTimecards = $this->createTimecardsForEmployees(
            $shift,
            $request['employee_ids'],
            $shift->id ?? $request['shift_id'] ?? null,
            $extraAttributes['ci_branch_id'] ?? (isset($extraAttributes['co_branch_id']) ? $extraAttributes['co_branch_id'] : ($request['branch_id'] ?? null)),
            $request['date'],
            $request['timecard_type_id'],
            $extraAttributes
        );

        return $createdTimecards;
    }

    private function createCustomShift($request)
    {
        $duration = $this->calculateDuration($request['from_time'], $request['to_time']);

        $shiftData = [
            'name' => $request['name'],
            'company_id' => auth()->user()->company_id,
            'from_date' => Carbon::parse($request['date'])->toDateString(),
            'to_date' => Carbon::parse($request['date'])->addDay()->toDateString(),
            'colorhex' => $request['color'] ?? null,
            'timecard_type_id' => $request['timecard_type_id'],
            'is_custom' => true,
        ];

        $days = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
        foreach ($days as $day) {
            $shiftData["{$day}_from_time"] = $request['from_time'];
            $shiftData["{$day}_duration"] = $duration;
        }

        $shift = $this->newShiftRepository->add($shiftData);

        $request['shift_id'] = $shift->id;
        if (!empty($request['ci_branch_id'])) {
            $request['branches'] = [['id' => $request['ci_branch_id']]];
        } elseif (!empty($request['co_branch_id'])) {
            $request['branches'] = [['id' => $request['co_branch_id']]];
        } else {
            $request['branches'] = [];
        }

        $output = new stdClass;

        if (!empty($request['branches'])) {
            $this->linkTitlesBranchesWithShiftService->perform($request->toArray(), $output);
        }

        return $shift;
    }

    private function calculateDuration(string $fromTime, string $toTime): float
    {
        $from = Carbon::createFromFormat('H:i', $fromTime);
        $to = Carbon::createFromFormat('H:i', $toTime);

        // return $to->diffInMinutes($from) / 60; // Returns duration in hours

        if ($to->lessThan($from)) {
            $to->addDay();
        }

        $timeDiff = $to->diff($from);
        $mins = $timeDiff->i < 10 ? '0' . $timeDiff->i : $timeDiff->i;

        return $timeDiff->h . '.' . $mins;
    }

    public function createTimecardsForEmployees($shift, $employeeIds, $shiftId, $branchId, $date,
                                                $timecardTypeId, $extraAttributes = []): array
    {
        $timeCardType = $shift ? $this->timecardTypeRepository->getDefaultTimecardType()->id : $timecardTypeId;
        $timecards = $this->prepareTimecardData($shift, $employeeIds, $shiftId, $branchId, $date, $timeCardType, $extraAttributes);

        $createdTimecards = $this->saveTimecards($timecards);

        $this->tagTimecards($createdTimecards, $shift);

        return $createdTimecards;
    }

    private function prepareTimecardData($shift, $employeeIds, $shiftId,
                                         $branchId, $date, $timeCardTypeId, $extraAttributes = []): array
    {
        $timecards = [];
        $ci_saved_location = $this->savedLocationRepository->find($extraAttributes['ci_saved_location_id']) ?? null;
        $co_saved_location = $this->savedLocationRepository->find($extraAttributes['co_saved_location_id']) ?? null;
        foreach ($employeeIds as $employeeId) {
            $ci_saved_location = $ci_saved_location ?? null;
            $co_saved_location = $co_saved_location ?? null;
            $fromDateTime = isset($date, $extraAttributes['from_time'])
                ? Carbon::createFromFormat('Y-m-d H:i', "{$date} {$extraAttributes['from_time']}")
                : null;

            $toDateTime = isset($date, $extraAttributes['to_time'])
                ? Carbon::createFromFormat('Y-m-d H:i', "{$date} {$extraAttributes['to_time']}")
                : null;
            $data = [
                'employee_id' => $employeeId,
                'shift_id' => $shiftId,
                'timeCardDefaultTypeId' => $timeCardTypeId,
                'name' => $shift->name ?? $extraAttributes['name'] ?? null,
                'color' => $shift->colorhex ?? $extraAttributes['color'] ?? null,
                'required_ci_branch' => isset($extraAttributes['any_location']) && $extraAttributes['any_location']
                    ? null : ($extraAttributes['ci_branch_id'] ?? $branchId),
                'required_co_branch' => isset($extraAttributes['any_location']) && $extraAttributes['any_location']
                    ? null : ($extraAttributes['co_branch_id'] ?? $branchId),
                'ci_saved_location_id' => $extraAttributes['ci_saved_location_id'] ?? null,
                'co_saved_location_id' => $extraAttributes['co_saved_location_id'] ?? null,
                'required_ci_lat' => $extraAttributes['ci_latitude'] ?? null,
                'required_ci_long' => $extraAttributes['ci_longitude'] ?? null,
                'required_co_lat' => $extraAttributes['co_latitude'] ?? null,
                'required_co_long' => $extraAttributes['co_longitude'] ?? null,
                'ci_saved_location_lat' => $ci_saved_location->latitude ?? null,
                'ci_saved_location_long' => $ci_saved_location->longitude ?? null,
                'co_saved_location_lat' => $co_saved_location->latitude ?? null,
                'co_saved_location_long' => $co_saved_location->longitude ?? null,
                'date' => $date,
                'from' => $fromDateTime ? $fromDateTime->format('Y-m-d H:i:s') : null,
                'to' => $toDateTime ? $toDateTime->format('Y-m-d H:i:s') : null,
                'branch_id' => $branchId,
                'shift' => $shift,
                'is_custom' => isset($shift) ? 0 : 1,
                'any_location' => $extraAttributes['any_location'] ?? 0,
            ];
            $timecards[] = new CreateTimeCardObject($data);
        }

        return array_map(fn($timecard) => $timecard->toArray(), $timecards);
    }

    private function saveTimecards($timecardsArray)
    {
        return $this->timecardRepository->createMany($timecardsArray);
    }

    private function tagTimecards($createdTimecards, $shift)
    {
        $timecardIds = array_column($createdTimecards, 'id');
        $entityTags = array_map(function ($timecardId) use ($shift) {
            return [
                'entity_id' => $timecardId,
                'entity_type' => 'time_card',
                'company' => $shift->company_id ?? auth()->user()->company_id,
                'tag' => 'scheduled',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }, $timecardIds);

        EntityTag::insert($entityTags);
    }

    public function removeEmployeesFromShift($request)
    {
        $this->timecardRepository->removeEmployeeFromShift($request['shift_id'], $request['employee_id'], $request['date'], $request['branch_id']);
    }

    public function removeShift($request)
    {
        if ($request['date'] < now()->format('Y-m-d')) {
            throw new UnprocessableException('Cannot remove shift to past date');
        }
        $this->timecardRepository->removeShiftOnDate($request['shift_id'], $request['branch_id'], $request['date']);
    }

    public function copyShift($request)
    {
        if (!isset($request['employee_ids']) || empty($request['employee_ids'])) {
            throw new UnprocessableException('No employees provided for shift copy');
        }

        if ($request['date'] < now()->format('Y-m-d')) {
            throw new UnprocessableException('Cannot copy shift to past date');
        }

        $uncoppiedEmployees = [];

        $employees = $this->employeeRepository->getEmployeesWithWorkTypePolicySettings($request['employee_ids']);
        $employeesMap = $employees->keyBy('id');

        $employeeIds = $request['employee_ids'];

        foreach ($employeeIds as $employeeId) {
            $validations = $this->getEmployeeValidations($employeesMap[$employeeId], $request['date'], $request['type']);
            $blockers = $this->getBlockers($validations, 1);
            $this->excludeEmployees($blockers, $employeeId, $uncoppiedEmployees, $employeeIds, $validations);
        }
        $request['employee_ids'] = $employeeIds;
        $this->addEmployeesToShift($request, 1);

        return $uncoppiedEmployees;
    }

    public function excludeEmployees(array $blockers, $employeeId, &$uncoppiedEmployees, &$employeeIds, $validations): array
    {
        if (empty($blockers)) {
            return $uncoppiedEmployees;
        }

        $employee = $this->employeeRepository->find($employeeId);
        $lang = config('globals.lang') ?? 'ar';

        if (!$employee) {
            return $uncoppiedEmployees;
        }

        $getEmployeeData = function ($additionalData = []) use ($employee, $lang) {
            return array_merge([
                'id' => $employee->id,
                'employee_number' => $employee->employee_number,
                'first_name' => $lang === 'ar' ? $employee->first_name_ar : $employee->first_name_en,
                'second_name' => $lang === 'ar' ? $employee->second_name_ar : $employee->second_name_en,
                'title' => [
                    'id' => $employee->title->id,
                    'name' => $lang === 'ar' ? $employee->title->name_ar : $employee->title->name_en,
                    'color' => $employee->title->color,
                ],
                'branch' => [
                    'id' => $employee->branch->id,
                    'name' => $lang === 'ar' ? $employee->branch->name_ar : $employee->branch->name_en,
                ],
                'profile_image' => $employee->profilePicture ? $employee->profilePicture->attachment_url : null,
            ], $additionalData);
        };

        foreach ($blockers as $blocker) {
            switch ($blocker) {
                case 'termination':
                    if (isset($employee->employeeInfo->termination_date)) {
                        $uncoppiedEmployees[] = $getEmployeeData([
                            'termination_date' => $employee->employeeInfo->termination_date,
                            'blocker' => 'termination',
                        ]);
                        break 2;
                    }
                    break;

                case 'scheduled_transfer':
                    $transfer = $employee->employeeChange
                        ->where('change_type', 'branch')
                        ->where('status', 'scheduled')
                        ->where('as_of_date', '>=', now())
                        ->first();

                    if ($transfer) {
                        $uncoppiedEmployees[] = $getEmployeeData([
                            'transfer_date' => $transfer->as_of_date,
                            'blocker' => 'scheduled_transfer',
                        ]);
                        break 2;
                    }
                    break;

                case 'timecard':
                    $uncoppiedEmployees[] = $getEmployeeData([
                        'blocker' => 'timecard',
                        'timecard' => $validations['timecard'],
                    ]);
                    break 2;

                case 'fixed_rest_days':
                    $uncoppiedEmployees[] = $getEmployeeData([
                        'blocker' => 'fixed_rest_days',
                    ]);
                    break 2;

                case 'fixed_timecards':
                    $uncoppiedEmployees[] = $getEmployeeData([
                        'blocker' => 'fixed_timecards',
                    ]);
                    break 2;

                default:
                    $uncoppiedEmployees[] = $getEmployeeData([
                        'blocker' => $blocker,
                    ]);
                    break 2;
            }
        }

        // Remove employee ID from the list
        unset($employeeIds[array_search($employeeId, $employeeIds)]);

        return $uncoppiedEmployees;
    }

    private function deleteOverlappingTimeCards($validations)
    {
        if ($validations['has_timecard_overlap']) {
            $timecard = $validations['timecard'];
            $this->timecardRepository->delete([$timecard->id]);
        }
    }

    public function addEmployeesToRestday($request)
    {
        $result = new \stdClass;
        $result->deleted_count = 0;
        $result->Error = [];
        $employeeIds = $request['employee_ids'];
        $restdays = [];
        $restDayLeaveId = config('globals.rest_day_leave_id');
        $blockedEmployees = $this->employeeRepository->restDayBlockerValidation($employeeIds, $request['date'], $restDayLeaveId);
        $blockedIds = $blockedEmployees->pluck('id')->toArray();
        $employees = $this->employeeRepository->findByIds($employeeIds);
        $this->timecardRepository->removeTimecardsOnDateWithBranch($request['employee_ids'], $request['date'], $request['branch_id']);
        foreach ($employees as $employee) {
            if (in_array($employee->id, $blockedIds)) {
                continue;
            }
            $data = [
                'employee_id' => $employee->id,
                'date' => $request['date'],
            ];
            $restdays[] = $data;

        }
        $bulkAddRequest['entity_array'] = $restdays;
        $this->employeeLeaveRequestCrudService->addManyRestDays($bulkAddRequest, $result);

        return $restdays;
    }

    public function addSavedLocation($request)
    {
        $data = $request;
        $data['company_id'] = auth()->user()->employee->company_id;

        return $this->savedLocationRepository->add($request);
    }

    public function getSavedLocationsByStatus($request)
    {
        return $this->savedLocationRepository->getSavedLocationsByFilter($request['status']);
    }

    public function changeSavedLocationStatus($id)
    {
        $savedLocation = $this->savedLocationRepository->find($id);
        $savedLocation->is_active = !$savedLocation->is_active;
        $savedLocation->save();
    }

    public function setExtraAttributes($request, bool $isCustomShift, $copy): array
    {
        $extraAttributes = [];

        if ($copy && $isCustomShift) {
            $timecard = $this->timecardRepository->findByKey('shift_id', $request['shift_id'])->first();
            if ($timecard) {
                $extraAttributes = [
                    'ci_saved_location_id' => $timecard->ci_saved_location_id,
                    'co_saved_location_id' => $timecard->co_saved_location_id,
                    'ci_branch_id' => $timecard->ci_branch_id,
                    'co_branch_id' => $timecard->co_branch_id,
                    'ci_latitude' => $timecard->ci_latitude,
                    'ci_longitude' => $timecard->ci_longitude,
                    'co_latitude' => $timecard->co_latitude,
                    'co_longitude' => $timecard->co_longitude,
                    'name' => $timecard->name,
                    'from_time' => Carbon::parse($timecard->from)->format('H:i'),
                    'to_time' => Carbon::parse($timecard->to)->format('H:i'),
                    'color' => $timecard->color,
                    'any_location' => $timecard->any_location,
                ];
            }
        } else {
            $extraAttributes = [
                'ci_saved_location_id' => $request['ci_saved_location_id'] ?? null,
                'co_saved_location_id' => $request['co_saved_location_id'] ?? null,
                'ci_branch_id' => $request['ci_branch_id'] ?? null,
                'co_branch_id' => $request['co_branch_id'] ?? null,
                'ci_latitude' => $request['ci_latitude'] ?? null,
                'ci_longitude' => $request['ci_longitude'] ?? null,
                'co_latitude' => $request['co_latitude'] ?? null,
                'co_longitude' => $request['co_longitude'] ?? null,
                'name' => $isCustomShift ? ($request['name'] ?? null) : null,
                'from_time' => $isCustomShift ? ($request['from_time'] ?? null) : null,
                'to_time' => $isCustomShift ? ($request['to_time'] ?? null) : null,
                'color' => $isCustomShift ? ($request['color'] ?? null) : null,
                'any_location' => $isCustomShift ? ($request['is_any_location'] ?? false) : null,
            ];
        }

        return $extraAttributes;
    }

    public function removeRestDayOnEmployee($request)
    {

        $result = new \stdClass;
        $result->deleted_count = 0;
        $result->Error = [];

        $this->deleteRestDayForEmployees($request['employee_id'], $request['date'], $result);

        return $result;
    }

    public function deleteRestDayForEmployees($employeeId, $date, stdClass &$output)
    {
        $restDay = $this->employeeLeaveRequestRepository->getEmployeeRestDayByDate($employeeId, config('globals.rest_day_leave_id'), $date);
        if ($restDay) {
            $data = [
                'ids' => [
                    $restDay->id,
                ],
            ];
            $this->employeeLeaveRequestCrudService->deleteRestDays($data, $output);
        } else {
            $output->Error[] = [
                'message' => 'No rest day found for the provided employee and date.',
                'details' => ['employee_id' => $employeeId, 'date' => $date],
            ];
        }
    }

    public function removeRestDayForEmployees($request)
    {
        $restDays = $this->employeeLeaveRequestRepository->getRestDaysOnDate($request['employee_ids'], $request['date'], config('globals.rest_day_leave_id'));
        $restDaysIds = $restDays->pluck('id')->toArray();
        $result = new \stdClass;
        $result->deleted_count = 0;
        $result->Error = [];

        $this->employeeLeaveRequestCrudService->deleteRestDays(['ids' => $restDaysIds], $result);
    }

    public function removeEmployeeFromCustomShift($request)
    {
        return $this->timecardRepository->removeEmployeeFromCustomShift($request['employee_id'], $request['shift_name'], $request['from_date'], $request['to_date']);
    }

    public function removeCustomShiftOnDate($request)
    {
        return $this->timecardRepository->removeCustomShiftOnDate($request['shift_name'], $request['from_date'], $request['to_date'], $request['branch_id']);
    }
}
