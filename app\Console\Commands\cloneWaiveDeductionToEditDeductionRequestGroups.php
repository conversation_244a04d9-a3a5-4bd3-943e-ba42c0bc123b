<?php

namespace App\Console\Commands;

use App\Models\RequestWorkflow;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class cloneWaiveDeductionToEditDeductionRequestGroups extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:clone-waive-deduction-to-edit-deduction-request-groups';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            // $waiveDeductionWorkflows = DB::table('request_workflows')->where('type', 'waive_late_deduction')->get();
            // echo "Total Waive Deduction Workflows: " . count($waiveDeductionWorkflows) . "\n";
            // foreach ($waiveDeductionWorkflows as $waiveDeductionWorkflow) {
            //   $editDeductionWorkflow = RequestWorkflow::create([
            //         'request_group_id' => $waiveDeductionWorkflow->request_group_id,
            //         'company_id' => $waiveDeductionWorkflow->company_id,
            //         'order' => $waiveDeductionWorkflow->order,
            //         'operator' => $waiveDeductionWorkflow->operator,
            //         'type' => 'edit_attendance_deduction',
            //         'role_id' => $waiveDeductionWorkflow->role_id,
            //         'created_at' => now(),
            //         'updated_at' => now(),
            //     ]);

            //     echo "Waive Deduction Workflow ID: " . $waiveDeductionWorkflow->id . " cloned to Edit Deduction Workflow ID: " . $editDeductionWorkflow->id . "\n";
            // }

            // echo "All Waive Deduction Workflows cloned to Edit Deduction Workflows\n";

            // $editDeductionWorkflows = DB::table('request_workflows')->where('type', 'edit_attendance_deduction')->get();

            // echo "Total Edit Deduction Workflows: " . count($editDeductionWorkflows) . "\n";

            $waiveDeductionWorkflows = DB::table('request_workflows')->where('type', 'waive_late_deduction')->get();
            echo 'Total Waive Deduction Workflows: '.count($waiveDeductionWorkflows)."\n";
            foreach ($waiveDeductionWorkflows as $waiveDeductionWorkflow) {
                DB::table('request_workflows')->where('id', $waiveDeductionWorkflow->id)->update([
                    'type' => 'edit_attendance_deduction',
                    'updated_at' => now(),
                ]);
                echo 'Waive Deduction Workflow ID: '.$waiveDeductionWorkflow->id." renamed to Edit Deduction Workflow\n";

            }

            echo "All Waive Deduction Workflows Renamed to Edit Deduction Workflows\n";

            $editDeductionWorkflows = DB::table('request_workflows')->where('type', 'edit_attendance_deduction')->get();

            echo 'Total Edit Deduction Workflows: '.count($editDeductionWorkflows)."\n";

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            //// dd($e);
        }
    }
}
