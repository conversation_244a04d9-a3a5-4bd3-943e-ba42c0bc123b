<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Company;
use App\Models\Role;
use App\Models\RoleScope;
use App\Models\Scope;
use App\Util\ScopeUtil;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddSuperAdminToAllCompanies extends Command
{
    private $permissions;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:super-admin';

    /**
     * this command requires fill:default:scopes to run first.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $companies = Company::all();
            $this->permissions = \App\Models\Permission::all()->pluck('id');
            foreach ($companies as $company) {
                $this->addSuperAdminToAllCompanies($company->id);
            }
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }

    public function addSuperAdminToAllCompanies($companyId)
    {
        $this->createSuperAdminRole($companyId);
        $superAdminRole = Role::where('company_id', $companyId)->where('name', 'Super Admin')->first();
        $superAdminRoleId = $superAdminRole?->id;
        $companyScope = Scope::where('company_id', $companyId)->where('key', ScopeUtil::COMPANY_SCOPE)->first();
        if (isset($superAdminRoleId) && isset($companyScope)) {
            RoleScope::insert([
                'role_id' => $superAdminRoleId,
                'scope_id' => $companyScope->id,
            ]);
        }

        $superAdminRole->syncPermissions($this->permissions);
    }

    private function createSuperAdminRole($companyRecordId)
    {
        return Role::updateOrCreate(['name' => 'Super Admin', 'company_id' => $companyRecordId],
            [
                'name' => 'Super Admin',
                'company_id' => $companyRecordId,
                'guard_name' => 'user-api',
                'name_en' => 'Super Admin',
                'name_ar' => 'سوبر ادمن',
                'is_system_role' => 1,
                'is_super_admin' => 1,
            ]);
    }
}
