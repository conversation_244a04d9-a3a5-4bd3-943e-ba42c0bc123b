<?php

namespace App\Repositories;

use App\Models\EmployeeInfo;
use App\Traits\QueriesHelper;
use App\Util\EmployeeUtil;

class NewEmployeeInfoRepository extends BaseRepository
{
    use QueriesHelper;
    public function model(): string
    {
        return EmployeeInfo::class;
    }

    public function countTerminatedEmployees($filters){
        $query = $this->model
            ->whereNotNull('termination_date')
            ->whereDate('termination_date', '>=', $filters['from_date'])
            ->whereDate('termination_date', '<=', $filters['to_date']);
        $this->appendScopeQuery($query, $filters);
        return $query->count();
    }

    public function getExistingPassportNumbersForBulkValidation(): array
    {
        return $this->model
            ->select('passport_number')
            ->whereNotNull('passport_number')
            ->where('passport_number', '!=', '')
            ->pluck('passport_number')
            ->flip()
            ->toArray();
    }

    public function getExistingWorkEmailsForBulkValidation(): array
    {
        return $this->model
            ->select('email')
            ->whereNotNull('email')
            ->where('email', '!=', '')
            ->pluck('email')
            ->flip()
            ->toArray();
    }

    public function getExistingPersonalEmailsForBulkValidation(): array
    {
        return $this->model
            ->select('personal_email')
            ->whereNotNull('personal_email')
            ->where('personal_email', '!=', '')
            ->pluck('personal_email')
            ->flip()
            ->toArray();
    }
}
