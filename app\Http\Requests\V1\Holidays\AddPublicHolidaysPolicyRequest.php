<?php

namespace App\Http\Requests\V1\Holidays;

use Illuminate\Foundation\Http\FormRequest;

class AddPublicHolidaysPolicyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'policy_name' => ['required', 'string'],
            'compensation_method' => ['required', 'in:holidays_leave_balance,extra_work_day,custom'],
            'compensation_pay_rate' => ['required_if:compensation_method,extra_work_day', 'numeric'],
            'compensation_holidays_rate' => ['required_if:compensation_method,holidays_leave_balance', 'numeric', 'in:0.5,1,1.5,2,2.5,3'],
            'expiration_months' => ['integer', 'in:2,3,12'],
            'title_ids' => ['required', 'array'],
            'title_ids.*' => ['required', 'integer', 'exists:titles,id'],
        ];
    }
}
