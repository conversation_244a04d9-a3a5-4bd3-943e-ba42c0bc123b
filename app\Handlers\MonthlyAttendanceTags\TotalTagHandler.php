<?php

namespace App\Handlers\MonthlyAttendanceTags;

class TotalTagHandler implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): ?array
    {
        return null;
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        $tagKeys = ['present', 'absent', 'absent_without_permission', 'rest_day', 'leaves', 'public_holidays'];
        $totalCount = 0;

        foreach ($tagKeys as $key) {
            $totalCount += $tags[$employeeId]['tags'][$key]['count'] ?? 0;
        }

        return $totalCount;
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return true;
    }

    public function handleTagUnit(): string
    {
        return 'days';
    }
}
