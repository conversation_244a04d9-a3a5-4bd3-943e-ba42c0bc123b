name: Production Deployment

on:
  push:
    branches: [ "master" ]

jobs:
  tests:
    name: Run tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Setup PHP with PECL extension
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.2"
      - uses: actions/checkout@v2
      - name: Copy .env
        run: php -r "file_exists('.env') || copy('.env.example', '.env');"
      - name: Install Dependencies
        run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress
      - name: Directory Permissions
        run: chmod -R 777 storage bootstrap/cache
      - name: Create Database
        run: |
          mkdir -p database
          touch database/database.sqlite
      - name: Create JWT Secrey key
        env:
          DB_CONNECTION: sqlite
          DB_DATABASE: database/database.sqlite
          CACHE_DRIVER: array
          SESSION_DRIVER: array
          QUEUE_DRIVER: sync
        run: |
          php artisan jwt:secret
      - name: Create Application key
        env:
          DB_CONNECTION: sqlite
          DB_DATABASE: database/database.sqlite
          CACHE_DRIVER: array
          SESSION_DRIVER: array
          QUEUE_DRIVER: sync
        run: |
          php artisan key:generate
      # - name: Execute tests (Unit and Feature tests) via PHPUnit
      #   env:
      #     DB_CONNECTION: sqlite
      #     DB_DATABASE: database/database.sqlite
      #     CACHE_DRIVER: array
      #     SESSION_DRIVER: array
      #     QUEUE_DRIVER: sync
      #   run: vendor/bin/phpunit
      
  production-deployment:
    needs: tests

    runs-on: ubuntu-latest

    steps:
      - name: Check test status
        id: test-status
        run: echo "::set-output name=status::${{ needs.tests.status }}"
      
      - name: Production deployment
        # if: steps.test-status.outputs.status == 'success'
        run: |
            install -m 600 -D /dev/null ~/.ssh/id_rsa
            echo "${{ secrets.API_PRIVATE_KEY }}" > ~/.ssh/id_rsa
            ssh-keyscan -H ${{ secrets.API_SSH_HOST }} > ~/.ssh/known_hosts

      - name: ssh
        # if: steps.test-status.outputs.status == 'success'
        run: ssh bluworks@${{ secrets.API_SSH_HOST }} "cd ${{secrets.PROD_DIR}} && ${{vars.PRODUCTION_DEPLOYMENT_COMMANDS}} && exit"
