<?php

namespace App\Http\Requests\V1\Attendance;

use App\Rules\ScopeSubDepartmentIdsRule;
use Illuminate\Foundation\Http\FormRequest;

class AttendanceFiltersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules['start_date'] = ['date', 'date_format:Y-m-d'];
        $rules['end_date'] = ['required_with:start_date', 'date', 'date_format:Y-m-d', 'after_or_equal:start_date'];
        $rules['employee_id'] = ['integer'];
        $rules['branch_ids'] = ['array'];
        $rules['branch_ids.*'] = ['integer'];
        $rules['department_ids'] = ['array'];
        $rules['department_ids.*'] = ['integer'];
        $rules['title_ids'] = ['array'];
        $rules['title_ids.*'] = ['integer'];
        $rules['search_value'] = ['min:1', 'max:30'];
        $rules['sub_department_ids'] = ['array'];
        $rules['sub_department_ids.*'] = ['integer', new ScopeSubDepartmentIdsRule];
        $rules['page'] = ['integer', 'min:1'];
        $rules['page_size'] = ['integer', 'min:0']; // 0 means no pagination
        $rules['id'] = ['integer'];

        return $rules;
    }
}
