<?php

namespace App\DomainData;

use App\Rules\ScopeSubDepartmentIdsRule;
use App\Rules\ValidEmployeeTitleForShift;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\RequiredIf;

trait TimecardDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'from' => 'required|date_format:Y-m-d H:i:s',
            'to' => 'required|date_format:Y-m-d H:i:s|after:from',
            'shift_id' => 'integer',
            'branch_id' => 'integer',
            'employee_id' => 'integer',
            'created_by_id' => 'integer',
            'timecard_type_id' => 'integer',
            'parent_timecard_id' => 'integer',
            'any_location' => 'boolean',
            'required_ci_lat' => ['required_if:required_ci_branch_id,null', 'regex:/^[-]?(([0-8]?[0-9])\.(\d+))|(90(\.0+)?)$/'],
            'required_ci_long' => ['required_if:required_ci_branch_id,null', 'regex:/^[-]?((((1[0-7][0-9])|([0-9]?[0-9]))\.(\d+))|180(\.0+)?)$/'],
            'required_co_lat' => ['required_if:required_co_branch_id,null', 'regex:/^[-]?(([0-8]?[0-9])\.(\d+))|(90(\.0+)?)$/'],
            'required_co_long' => ['required_if:required_co_branch_id,null', 'regex:/^[-]?((((1[0-7][0-9])|([0-9]?[0-9]))\.(\d+))|180(\.0+)?)$/'],
            'required_ci_branch_id' => 'integer',
            'required_co_branch_id' => 'integer',
            'name' => 'nullable|string',
            'color' => 'nullable|string',
            'ci_saved_location_id' => 'nullable|integer|exists:saved_locations,id',
            'co_saved_location_id' => 'nullable|integer|exists:saved_locations,id',
            'out_of_company_in_location' => 'string',
            'out_of_company_out_location' => 'string',
            'by_admin' => 'integer|nullable',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeTimecardDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }

    public function getUpdateRules(array $data): array
    {
        $rules = [
            'branch_id' => ['required', 'exists:branches,id'],
            //     , Rule::exists('branch_employee', 'branch_id')
            //         ->where(function ($query) use ($data) {
            //             $query
            //                 ->whereIn('employee_id', [auth()->user()->employee_id]);
            //         })
            // ],
            'time_card_id' => ['required', 'exists:timecards,id'],
            'ci_saved_location_id' => 'nullable|integer|exists:saved_locations,id',
            'co_saved_location_id' => 'nullable|integer|exists:saved_locations,id',
        ];

        return array_merge($rules, $this->generalTimeCardsRules($data));
    }

    public function getAssignEmployeeRules(array $data): array
    {
        $rules = [
            'branch_id' => ['integer', 'exists:branches,id'],
            //     , Rule::exists('branch_employee', 'branch_id')
            //         ->where(function ($query) use ($data) {
            //             $query
            //                 ->whereIn('employee_id', [auth()->user()->employee_id, $data['employee_id']]);
            //         })
            // ],
            'employee_id' => ['required', 'exists:employees,id'],
            'ci_saved_location_id' => 'nullable|integer|exists:saved_locations,id',
            'co_saved_location_id' => 'nullable|integer|exists:saved_locations,id',
        ];

        if (isset($data['shift_id'])) {
            $rules['employee_id'] = ['required', new ValidEmployeeTitleForShift($data['shift_id'])];
        }

        return array_merge($rules, $this->generalTimeCardsRules($data));
    }

    /**
     * @return array[]
     */
    public function generalTimeCardsRules($data): array
    {
        return [
            'date' => [new RequiredIf(isset($data['shift_id'])), new RequiredIf(!isset($data['from'])), new RequiredIf(!isset($data['to'])), 'date_format:Y-m-d'],
            'shift_id' => ['exists:new_shifts,id'],
            'name' => ['string', new RequiredIf(!isset($data['shift_id']))],
            'timecard_type_id' => ['exists:timecard_types,id'],
            'from' => [
                new RequiredIf(!isset($data['shift_id'])),
                'date_format:Y-m-d H:i:s',
                'after_or_equal:now',
                'before:to',
            ],
            'to' => [
                new RequiredIf(!isset($data['shift_id'])),
                'date_format:Y-m-d H:i:s',
                'after:from',
            ],
            'required_co_branch_id' => ['exists:branches,id'],
            'required_ci_branch_id' => ['exists:branches,id'],
            'clock_in_lat' => ['numeric', 'min:-90', 'max:90'],
            'clock_out_lat' => ['numeric', 'min:-90', 'max:90'],
            'clock_in_long' => ['numeric', 'min:-180', 'max:180'],
            'clock_out_long' => ['numeric', 'min:-180', 'max:180'],
            'by_create_manual_card' => ['nullable', 'integer'],
            'color' => ['string', new RequiredIf(!isset($data['shift_id']))],
            'ci_saved_location_id' => 'nullable|integer|exists:saved_locations,id',
            'co_saved_location_id' => 'nullable|integer|exists:saved_locations,id',
            'by_admin' => 'integer|nullable',
        ];
    }

    public function getDeleteRules(): array
    {
        return [
            'time_card_id' => [
                'required',
                Rule::exists('timecards', 'id')->whereNull('deleted_at'),
            ],
        ];
    }

    public function getFilterRules(): array
    {
        $rules['start_date'] = ['date', 'date_format:Y-m-d'];
        $rules['end_date'] = ['required_with:start_date', 'date', 'date_format:Y-m-d', 'after_or_equal:start_date'];
        $rules['order_by'] = ['string', 'in:from'];
        $rules['order_by_type'] = ['string', 'in:asc,desc'];
        $rules['page'] = ['integer', 'min:1'];
        $rules['page_size'] = ['integer', 'min:0'];
        $rules['employee_id'] = ['integer'];
        $rules['branch_ids'] = ['array'];
        $rules['branch_ids.*'] = ['integer'];
        $rules['department_ids'] = ['array'];
        $rules['department_ids.*'] = ['integer', 'exists:departments,id'];
        $rules['title_ids'] = ['array'];
        $rules['title_ids.*'] = ['integer'];
        $tags = implode(',', array_values(config('globals.ATTENDANCE_TAGS')));
        $rules['filter_keys'] = ['array'];
        $rules['filter_keys.*'] = ['string', 'in:' . $tags];
        $rules['search_value'] = ['min:1', 'max:30'];
        $rules['with_pending_overtime_only'] = ['boolean'];
        $rules['with_pending_waive_deduction_only'] = ['boolean'];
        $rules['sub_department_ids'] = ['array'];
        $rules['sub_department_ids.*'] = ['integer', new ScopeSubDepartmentIdsRule];
        $rules['by_admin'] = ['integer|nullable'];

        return $rules;
    }

    public function getAddAbsentTimecardRule(): array
    {
        return [
            'employee_id' => ['required', 'exists:employees,id'],
            'date' => ['required', 'date_format:Y-m-d', 'before_or_equal:now'],
        ];
    }
}
