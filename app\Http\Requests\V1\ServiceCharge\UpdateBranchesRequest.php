<?php

namespace App\Http\Requests\V1\ServiceCharge;

use App\Rules\BranchIdRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateBranchesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'branch_ids' => [
                'required',
                'array',
            ],
            'branch_ids.*' => [
                new BranchIdRule,
            ],
        ];

    }
}
