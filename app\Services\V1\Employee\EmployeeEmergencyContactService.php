<?php

namespace App\Services\V1\Employee;

use App\Exceptions\UnprocessableException;
use App\Repositories\V1\Employee\EmployeeEmergencyContactRepository;
use App\Services\BaseService;

class EmployeeEmergencyContactService extends BaseService
{

    public function __construct(EmployeeEmergencyContactRepository $repository)
    {
        $this->repository = $repository;
    }

    public function getByEmployeeId(int $employeeId)
    {
        return $this->repository->getByEmployeeId($employeeId);
    }
}