<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Models\Timecard;
use Illuminate\Support\Carbon;

class TagOverTimeHandler implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        return [
            'name' => $employeeAttendance->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($employeeAttendance->from)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        $overtimeValue = ! is_null($employeeAttendance->attendance->attendanceOvertimes[0]->updated_value)
        ? $employeeAttendance->attendance->attendanceOvertimes[0]->updated_value
        : $employeeAttendance->attendance->attendanceOvertimes[0]->overtime_minutes;

        $count = isset($tags[$employeeId]['tags']['overtime']) ?
            $tags[$employeeId]['tags']['overtime']['count'] +
            $overtimeValue / 60
            : $overtimeValue / 60;

        return round($count, 2);
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return $employeeAttendance instanceof Timecard &&
            $employeeAttendance->attendance &&
            count($employeeAttendance->attendance->attendanceOvertimes) > 0;
    }

    public function handleTagUnit(): string
    {
        return 'hours';
    }
}
