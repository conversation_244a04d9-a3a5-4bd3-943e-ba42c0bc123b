<?php

namespace App\Traits\V1;

use App\Jobs\ApplyOnLeaveRequestPushNotificationJob;
use App\Jobs\ApprovedLeaveRequestPushNotificationJob;
use App\Jobs\ApprovedLoanPushNotificationJob;
use App\Jobs\ApprovedMissionPushNotificationJob;
use App\Jobs\ApprovedOverTimePushNotificationJob;
use App\Jobs\ApprovedOvertimeRequestPushNotificationJob;
use App\Jobs\ApprovedSalaryAdvancePushNotificationJob;
use App\Jobs\CancelledMissionPushNotificationJob;
use App\Jobs\OverTimeRequestPushNotificationJob;
use App\Jobs\RejectedLoanPushNotificationJob;
use App\Jobs\RejectedMissionPushNotificationJob;
use App\Jobs\RejectedOvertimeRequestPushNotificationJob;
use App\Jobs\RejectedSalaryAdvancePushNotificationJob;
use App\Jobs\RejectLeaveRequestPushNotificationJob;
use App\Jobs\V1\FilePenaltyPushNotificationJob;
use App\Jobs\V1\PenaltyApprovedPushNotificationJob;
use App\Jobs\WaiveDeductionRequestPushNotificationJob;
use App\Traits\QueriesHelper;
use Exception;
use Illuminate\Support\Facades\Log;

trait NotificationRedirection
{
    use QueriesHelper;

    public function redirectNotificationsAfterRequestCreated($request, $employee)
    {
        Log::info('inside in redirectNotificationsAfterRequestCreated ');

        $requestType = class_basename($request);
        try {
            $approvals = $this->getApprovalCycle($request, $employee);
            $approvingEmployees = [];
            foreach ($approvals as $approval) {

                $approvingEmployees = array_merge($approvingEmployees, $approval->employees);

            }

            switch ($requestType) {
                case 'Penalty':
                    dispatch(new FilePenaltyPushNotificationJob($request, $approvingEmployees))->afterCommit();
                    break;
                case 'EmployeeLeaveRequest':
                    dispatch(new ApplyOnLeaveRequestPushNotificationJob($request, $approvingEmployees))->afterCommit();
                    break;
                case 'AttendanceOvertime':
                    dispatch(new OverTimeRequestPushNotificationJob($approvingEmployees, $employee, $request))->afterCommit();
                    break;
                case 'AttendanceDeduction':
                    dispatch(new WaiveDeductionRequestPushNotificationJob($approvingEmployees, $request?->employeeRequests?->first()?->requestedBy, $employee, $request))->afterCommit();
                    break;

            }
        } catch (Exception $e) {
            Log::info('Error in redirectNotificationsAfterRequestCreated ');
            // \Sentry\captureException($e);
        }
    }

    public function redirectNotificationsAfterRequestFinalized($request, $status)
    {
        Log::info('inside in redirectNotificationsAfterRequestApproved');

        $requestType = class_basename($request);
        // try {
        //     if ($status == 'approved') {
        //         switch ($requestType) {
        //             case 'Penalty':
        //                 dispatch(new PenaltyApprovedPushNotificationJob($request))->afterCommit();
        //                 break;
        //             case 'EmployeeLeaveRequest':
        //                 dispatch(new ApprovedLeaveRequestPushNotificationJob($request))->afterCommit();
        //                 break;
        //             case 'AttendanceOvertime':
        //                 dispatch(new ApprovedOverTimePushNotificationJob($request?->attendance))->afterCommit();
        //                 break;
        //             case 'SalaryAdvance':
        //                 dispatch(new ApprovedSalaryAdvancePushNotificationJob($request))->afterCommit();
        //                 break;
        //             case 'Loan':
        //                 dispatch(new ApprovedLoanPushNotificationJob($request))->afterCommit();
        //                 break;
        //             case 'OvertimeRequest':
        //                 dispatch(new ApprovedOvertimeRequestPushNotificationJob($request))->afterCommit();
        //                 break;

        //             case 'MissionRequest':
        //                 dispatch(new ApprovedMissionPushNotificationJob($request))->afterCommit();
        //                 break;
        //         }
        //     } elseif ($status == 'rejected') {
        //         switch ($requestType) {
        //             case 'EmployeeLeaveRequest':
        //                 dispatch(new RejectLeaveRequestPushNotificationJob($request))->afterCommit();
        //                 break;
        //             case 'SalaryAdvance':
        //                 dispatch(new RejectedSalaryAdvancePushNotificationJob($request))->afterCommit();
        //                 break;
        //             case 'Loan':
        //                 dispatch(new RejectedLoanPushNotificationJob($request))->afterCommit();
        //                 break;
        //             case 'OvertimeRequest':
        //                 dispatch(new RejectedOvertimeRequestPushNotificationJob($request))->afterCommit();
        //                 break;
        //             case 'MissionRequest':
        //                 dispatch(new RejectedMissionPushNotificationJob($request))->afterCommit();
        //                 break;
        //         }
        //     } elseif ($status == 'cancelled') {
        //         switch ($requestType) {
        //             case 'MissionRequest':
        //                 dispatch(new CancelledMissionPushNotificationJob($request))->afterCommit();
        //                 break;
        //         }
        //     }
        // } catch (Exception $e) {
        //     Log::Info('Error in redirectNotificationsAfterRequestApproved');
        //     // \Sentry\captureException($e);
        // }
    }
}
