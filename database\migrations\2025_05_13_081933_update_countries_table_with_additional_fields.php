<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Add the new columns
        // Schema::table('countries', function (Blueprint $table) {
        //     $table->string('name_en')->nullable();
        //     $table->string('name_ar')->nullable();
        //     $table->string('currency')->nullable();
        //     $table->string('timezone')->nullable();
        //     $table->string('dial_code')->nullable();
        // });
        
        // Step 2: Copy data from name to name_en
        // DB::statement('UPDATE countries SET name_en = name');
        
        // // Step 3: Drop the original name column
        // Schema::table('countries', function (Blueprint $table) {
        //     $table->dropColumn('name');
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Step 1: Add the name column back
        Schema::table('countries', function (Blueprint $table) {
            $table->string('name')->nullable();
        });
        
        // Step 2: Copy data back from name_en to name
        DB::statement('UPDATE countries SET name = name_en');
        
        // Step 3: Drop the added columns
        Schema::table('countries', function (Blueprint $table) {
            $table->dropColumn([
                'name_en',
                'name_ar',
                'currency',
                'timezone',
                'dial_code'
            ]);
        });
    }
};
