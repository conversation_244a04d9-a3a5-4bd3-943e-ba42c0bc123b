<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Department;
use App\Models\Employee;
use App\Models\SubDepartment;
use App\Models\Title;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class MoveCompanyData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:move-company-data {from_company_id} {to_company_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Move employees, titles, departments and sub-departments from one company to another';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $fromCompanyId = $this->argument('from_company_id');
        $toCompanyId = $this->argument('to_company_id');

        if (! $this->confirm('This will move all data from company '.$fromCompanyId.' to company '.$toCompanyId.'. Are you sure?')) {
            return;
        }

        DB::transaction(function () use ($fromCompanyId, $toCompanyId) {
            // Move departments
            $this->info('Moving departments...');
            $departments = Department::where('company_id', $fromCompanyId)->get();
            foreach ($departments as $department) {
                $oldId = $department->id;
                $department->company_id = $toCompanyId;
                $department->save();
                $this->info("Moved department: {$department->name} (ID: {$oldId} -> {$department->id})");
            }

            // Move sub-departments
            $this->info('Moving sub-departments...');
            $subDepartments = SubDepartment::where('company_id', $fromCompanyId)->get();
            foreach ($subDepartments as $subDepartment) {
                $oldId = $subDepartment->id;
                $subDepartment->company_id = $toCompanyId;
                $subDepartment->save();
                $this->info("Moved sub-department: {$subDepartment->name} (ID: {$oldId} -> {$subDepartment->id})");
            }

            // Move Spatie roles
            $this->info('Moving Spatie roles...');
            $roles = Role::where('company_id', $fromCompanyId)->get();
            foreach ($roles as $role) {
                try {
                    $oldId = $role->id;
                    $role->name = $role->name.' new';
                    $role->name_en = $role->name_en.' new';
                    $role->name_ar = $role->name_ar.' new';
                    $role->company_id = $toCompanyId;
                    $role->save();
                    $this->info("Moved role: {$role->name} (ID: {$oldId} -> {$role->id})");
                } catch (\Exception $e) {
                    $this->info('Error moving role: '.$e->getMessage());
                }
            }

            // Move titles
            $this->info('Moving titles...');
            $titles = Title::where('company_id', $fromCompanyId)->get();
            foreach ($titles as $title) {
                $oldId = $title->id;
                $title->request_group_id = null;
                $title->public_holidays_policy_id = null;
                $title->absence_deduction_policy_id = null;
                $title->late_deduction_group_id = null;
                $title->work_type_policy_id = null;
                $title->loan_policy_id = null;
                $title->salary_advance_policy_id = null;
                $title->extra_workday_policy_id = null;
                $title->excuse_policy_id = null;
                $title->apply_missions = false;
                $title->company_id = $toCompanyId;
                $title->save();
                $this->info("Moved title: {$title->name} (ID: {$oldId} -> {$title->id})");
            }

            // Move employees
            $this->info('Moving employees...');
            $employees = Employee::where('company_id', $fromCompanyId)->get();
            foreach ($employees as $employee) {
                $oldId = $employee->id;
                $employee->company_id = $toCompanyId;
                $employee->save();
                $this->info("Moved employee: {$employee->name} (ID: {$oldId} -> {$employee->id})");
            }
        });

        // Move users
        $this->info('Moving users...');
        $users = User::where('company_id', $fromCompanyId)->get();
        foreach ($users as $user) {
            try {
                $oldId = $user->id;
                $user->company_id = $toCompanyId;
                $user->save();
                $this->info("Moved user: {$user->name} (ID: {$oldId} -> {$user->id})");
            } catch (\Exception $e) {
                $this->info('Error moving user: '.$e->getMessage());
            }
        }
        $this->info('Data migration completed successfully!');
    }
}
