<?php

namespace App\Http\Resources\V1\LoansAndSalaryAdvances;

use App\Http\Resources\BaseResource;
use App\Http\Resources\EmployeeRequestEmployeeInfo;
use App\Http\Resources\V1\EmployeesInApprovalCycleResource;
use App\Http\Resources\V1\WorkFlows\ApprovalCycleCollection;
use App\Traits\QueriesHelper;
use App\Traits\RequestsHelper;
use App\Util\LoansUtil;
use Carbon\Carbon;

class SalaryAdvanceRequestResource extends BaseResource
{
    use QueriesHelper, RequestsHelper;

    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'employee' => new EmployeeRequestEmployeeInfo($this->employee),
            'requested_at' => Carbon::parse($this->created_at)->format('Y-m-d H:i:s'),
            'date' => $this->date,
            'status' => $this->status,
            'salary_advance_request' => [
                'id' => $this->requestable?->id,
                'amount' => $this->requestable?->amount,
                'comment' => $this->requestable?->comment,
                'status' => $this->requestable?->status,
                'disbursement_date' => $this->requestable?->disbursement_date,
                'approval_cycle' => new ApprovalCycleCollection($this->getApprovalCycle($this->requestable, $this->employee)),
                'decider_admin' => isset($this->deciderAdmin) ? new EmployeesInApprovalCycleResource($this->deciderAdmin) : null,
                'status' => $this->requestable?->status,
            ],
            'actionable' => $this?->requestable?->status == config('globals.REQUEST_STATUSES.PENDING') && $this->isActionable($this->requestable->employeeApproves, $this->status),
            'can_cancel' => $this->requestable?->status == LoansUtil::READY_TO_DISBURSE && auth()->user()->hasPermissionTo('cancel_loans'),
        ];
    }
}
