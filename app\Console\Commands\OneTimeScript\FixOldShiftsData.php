<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\NewShift;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixOldShiftsData extends Command
{
    public function __construct(
    ) {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:old:shift_templates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $shifts = NewShift::all();
            foreach ($shifts as $shift) {
                $sun_from_time = $shift->sun_from_time;
                $sun_duration = $shift->sun_duration;
                $shift->mon_from_time = $sun_from_time;
                $shift->mon_duration = $sun_duration;
                $shift->tue_from_time = $sun_from_time;
                $shift->tue_duration = $sun_duration;
                $shift->wed_from_time = $sun_from_time;
                $shift->wed_duration = $sun_duration;
                $shift->thu_from_time = $sun_from_time;
                $shift->thu_duration = $sun_duration;
                $shift->fri_from_time = $sun_from_time;
                $shift->fri_duration = $sun_duration;
                $shift->sat_from_time = $sun_from_time;
                $shift->sat_duration = $sun_duration;
                $shift->save();
            }
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
