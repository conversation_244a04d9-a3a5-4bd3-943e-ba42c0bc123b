<?php

namespace App\Http\Controllers\Schedule;

use App\DomainData\FilterDto;
use App\DomainData\TitleShiftEmployeeDto;
use App\Http\Controllers\Controller;
use App\Services\Schedule\CrudServices\TitleShiftEmployeeCrudService;

class TitleShiftEmployeeController extends Controller
{
    use FilterDto, TitleShiftEmployeeDto;

    public function __construct(
        private TitleShiftEmployeeCrudService $service
    ) {}

    public function createMany(array $request, \stdClass &$output): void
    {
        $rules['related_objects'] = ['array'];
        //$rules['related_objects.*'] = ['in:titleShift,employee'];
        $rules['shift_id'] = 'required|numeric';
        $rules['employees'] = 'required|array';
        $rules['employees.*'] = 'required|numeric';
        $validator = \Validator::make($request, $rules);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        $this->service->createMany($request, $output);
    }

    public function create(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['employee_id']);
        $rules['shift_id'] = 'required|numeric';
        $rules['related_objects'] = ['array'];
        $rules['related_objects.*'] = ['in:branch,titleShift,employee'];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->create($request, $output);

    }

    public function delete(array $request, \stdClass &$output): void
    {
        $rules['ids'] = ['required', 'array'];
        $rules['ids.*'] = ['required', 'numeric'];
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            // $output->Error = $this->failMessages($validator->messages());  // commented this because failMessages implementation doens't exist
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->delete($request, $output);
    }
}
