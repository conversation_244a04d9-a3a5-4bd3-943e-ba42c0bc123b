<?php

namespace App\Traits;

use App\Enums\V2\WorkTypes\RestDaysTypesEnum;
use App\FeatureToggles\Unleash;
use App\Models\Attendance;
use App\Repositories\NewAttendanceSettingRepository;
use App\Repositories\NewTitleRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\Repository;
use App\Repositories\V1\Attendance\AttendanceDeductionRepository;
use App\Repositories\V1\Attendance\AttendanceRepository;
use App\Repositories\V1\Attendance\ExtraWorkdayPolicyRepository;
use App\Repositories\V1\CompanySettings\WorkTypePolicyRepository;
use App\Repositories\V1\EmployeeRequestRepository;
use App\Repositories\V1\EntityTagRepository;
use App\Repositories\V1\ExtraWorkdayRequestRepository;
use App\Services\LeaveManagement\LeavesSplitterService;
use App\Traits\V1\AttendancePoliciesTrait;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Util\AttendanceUtil;
use App\Util\PayrollUtil;
use App\Util\UserWorkTypesUtil;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use stdClass;
use App\Jobs\AssignApprovalsJob;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;

trait CICOHelper
{
    use AttendancePoliciesTrait, PayrollHelper, WorkflowTrait, PrepareAssignRequestCycleDataTrait;

    /**
     * @throws DdException
     */
    public function getManagerBranches(): array
    {
        // $employee = config('globals.user')->employee;
        // return $employee->branches->pluck('id')->toArray();
        return config('globals.scope_branch_ids');
    }

    public function getManagerMainBranch(): int
    {
        $employee = config('globals.user')->employee;

        return $employee->branch_id;
    }

    public function validateManagerBranches($pairedClock): bool
    {
        return $this->getManagerMainBranch() === $pairedClock->branch_id || (in_array($pairedClock->branch_id, $this->getManagerBranches()));
    }

    public function validateManagerAtBranches($pairedClock): bool
    {
        return $this->getManagerMainBranch() !== $pairedClock->branch_id && (!in_array($pairedClock->branch_id, $this->getManagerBranches()));
    }

    public function getCoBranchSource($pairedClock): ?string
    {
        if ($pairedClock && $this->employee && $pairedClock->branch) {
            if ($this->validateManagerBranches($pairedClock) && $this->employee->branch_id !== $pairedClock->branch_id) {
                return 'from';
            }
            if ($this->validateManagerAtBranches($pairedClock) && $this->employee->branch_id !== $pairedClock->branch_id) {
                return 'at';
            }
        }

        return null;
    }

    public function fixLeavesSpanningClockIn($checkWarning, $employeeId, $date, stdClass &$output): bool
    {
        $leavesSplitterService = app(LeavesSplitterService::class);
        $date = Carbon::parse($date)->toDateString();
        $intersectingLeaves = $this->employeeLeaveRequestRepository->intersectingDailyLeaves($employeeId, $date);

        if (count($intersectingLeaves) == 0) {
            return true;
        }

        if ($checkWarning) {
            $output->Warning[] = ['This Employee has an approved leave on this date ' . $date . ' would you exclude this day from the approved leave '];

            return false;
        }

        foreach ($intersectingLeaves as $leave) {
            $leavesSplitterService->addVerifiedAttendanceOnLeave($leave, $date, $output);
        }

        return true;
    }

    public function calculateAttendanceLateDeduction($attendanceGroup, $employeeLateCount, $clockInDifferenceTime)
    {
        $featureFlag = $this->getAttendancePoliciesFlag();
        if ($featureFlag == 0) { // deprecated
            return $this->oldCalculateLateDeductionAmount($attendanceGroup, $employeeLateCount, $clockInDifferenceTime);
        }

        return $this->newCalculateLateDeductionAmount($attendanceGroup, $employeeLateCount, $clockInDifferenceTime);

    }

    private function oldCalculateLateDeductionAmount($attendanceGroup, $employeeLateDict, $clockInDifferenceTime)
    {
        $this->firstLatePolicy = $attendanceGroup->dailyAttendancePolicies->first();
        $this->lastLatePolicy = $attendanceGroup->dailyAttendancePolicies->last();
        $policyObj = null;
        $deductDays = null;
        foreach ($attendanceGroup->dailyAttendancePolicies as $policy) {
            $key = $policy->to;
            if (!Arr::get($employeeLateDict, $key, false)) {
                $employeeLateDict[$key] = ['late_count' => 0];
            }

            if ($clockInDifferenceTime < $this->firstLatePolicy->from) {
                break;
            }

            if (($clockInDifferenceTime >= $policy->from && $clockInDifferenceTime <= $policy->to) ||
                ($clockInDifferenceTime >= $this->lastLatePolicy->to && $policy->to == $this->lastLatePolicy->to)) {
                $policyObj = $policy;
                $deductDays = $policy->deduct_days;
                if ($employeeLateDict[$key]['late_count'] + 1 == $policy->sequence) {
                    break;
                }
            }
        }

        return [$policyObj, $deductDays];
    }

    private function newCalculateLateDeductionAmount($attendanceGroup, $employeeLateDict, $clockInDifferenceTime)
    {
        $this->firstLatePolicy = $attendanceGroup->latePolicies->first();
        $this->lastLatePolicy = $attendanceGroup->latePolicies->last();
        $policyObj = null;
        $deductDays = null;

        foreach ($attendanceGroup->latePolicies as $policy) {
            $key = $policy->to;
            if (!Arr::get($employeeLateDict, $key, false)) {
                $employeeLateDict[$key] = ['late_count' => 0];
            }

            if ($clockInDifferenceTime < $this->firstLatePolicy->from) {
                break;
            }

            if (($clockInDifferenceTime >= $policy->from && $clockInDifferenceTime <= $policy->to) ||
                ($clockInDifferenceTime >= $this->lastLatePolicy->to && $policy->to == $this->lastLatePolicy->to)) {
                $policyObj = $policy;
                $sequenceSize = count($policy?->sequence);

                if ($sequenceSize == 0) { // this case shouldnt happen /// just for safety
                    $this->deductDays = 0;
                } else {
                    $nextDeductDays = $employeeLateDict[$key]['late_count'] + 1;
                    if ($nextDeductDays - 1 < $sequenceSize) {
                        $deductDays = $policy->sequence[$nextDeductDays - 1]; // get the corrosponding deduct days

                    } else {
                        $deductDays = $policy->sequence[$sequenceSize - 1];
                    } // get the maximum deduct days
                }
                break;

            }
        }

        return [$policyObj, $deductDays];
    }

    public function createAttendanceDeductionIfExist($employee, $clockInDifferenceTime, $attendance)
    {
        $titleId = $employee->title_id;

        $attendanceGroup = $this->getAttendanceGroupThatRelatedToTitleNotInBranches($titleId, [$attendance?->clockIn->branch_id]);

        $systemSettingRepository = new SystemSettingRepository;

        $attendanceDeductionRepository = new AttendanceDeductionRepository;

        $companyId = config('globals.company')->id ?? $attendance->company_id;

        if (!is_null($attendanceGroup)) {
            $monthlyClosingDay = $systemSettingRepository->payrollMonthlyClosingDay($companyId);
            $employeeLateCounts = $this->getEmployeeLateCount($employee->id, $monthlyClosingDay, $attendance);
            $employeeLateDict = $employeeLateCounts->keyBy('policy_to')->toArray();

            [$policyObj, $deductDays] = $this->calculateAttendanceLateDeduction($attendanceGroup, $employeeLateDict, $clockInDifferenceTime);
            if (isset($deductDays) && $deductDays >= 0 && isset($policyObj)) {
                $featureFlag = $this->getAttendancePoliciesFlag();
                if ($featureFlag == 0) {
                    $attendanceDeductionData = $this->prepareAttendanceDeductionData($deductDays, $clockInDifferenceTime, $attendance, $policyObj?->id, null);
                } else {
                    $attendanceDeductionData = $this->prepareAttendanceDeductionData($deductDays, $clockInDifferenceTime, $attendance, null, $policyObj?->id);
                }

                return $attendanceDeductionRepository->add($attendanceDeductionData);
            }
        }

        return null;

    }

    public function prepareAttendanceDeductionData($deductDays, $deductionMinutes, $attendance, $dailyAttendancePolicyId = null, $lateDeductionGroupPolicyId = null)
    {
        return [
            'date' => date($attendance->date),
            'deduction_value' => $deductDays,
            'deduction_minutes' => $deductionMinutes,
            'status' => 'applied',
            'employee_id' => $attendance->employee_id,
            'attendance_id' => $attendance->id,
            'daily_attendance_policy_id' => $dailyAttendancePolicyId,
            'late_deduction_group_policy_id' => $lateDeductionGroupPolicyId,
            'company_id' => config('globals.company')?->id ?? $attendance->company_id,
            'branch_id' => $attendance?->branch_id ?? null,
        ];
    }

    private function getEmployeeLateCount($employeeId, $monthlyClosingDay, $attendance)
    {
        $featureFlag = $this->getAttendancePoliciesFlag();

        $attendanceRepository = Repository::getRepository('Attendance');

        if ($featureFlag == 0) {
            return $attendanceRepository->getEmployeeLateCount($employeeId, $monthlyClosingDay, $attendance);
        } else {
            return $attendanceRepository->newGetEmployeeLateCount($employeeId, $monthlyClosingDay, $attendance);
        }

    }

    public function getMaximumAllowedLate($titleId)
    {
        if (env('APP_ENV') == 'testing') {
            return 60;
        }

        $attendanceGroup = $this->getAttendanceGroupThatRelatedToTitleNotInBranches($titleId, []);
        if (!isset($attendanceGroup)) {
            return PHP_INT_MAX;
        }
        $lastLatePolicy = $this->getAttendancePoliciesFlag() ? $attendanceGroup->latePolicies->last() : $attendanceGroup->dailyAttendancePolicies->last();

        return $lastLatePolicy?->to ?? PHP_INT_MAX;

    }

    public function validateAllowedLastCicosDuration($request, $cicoRepository, stdClass &$output): bool
    {
        if (isset($request['action']) || isset($request['is_manual_action'])) {
            return true;
        }

        $cicoInTimeRange = $cicoRepository->cicoForEmployeeInTimeRange($request['employee']->id, AttendanceUtil::MINIMUM_CICO_TIME_DIFFERENCE);

        if (!isset($cicoInTimeRange)) {
            return true;
        }

        $lastClockOutDate = \Carbon\Carbon::parse($cicoInTimeRange->date);

        $now = \Illuminate\Support\Carbon::now();
        $diff = $now->diffInMinutes($lastClockOutDate);
        $remainingMinutes = AttendanceUtil::MINIMUM_CICO_TIME_DIFFERENCE - $diff;

        if ($diff <= AttendanceUtil::MINIMUM_CICO_TIME_DIFFERENCE) {
            $output->Error = [
                'You can not clock in now. Please try again after ' . $remainingMinutes . ' minutes.',
                'لا يمكنك الدخول الآن. يرجى المحاولة مرة أخرى بعد ' . $remainingMinutes . ' دقيقة.',
            ];

            return false;
        }

        return true;
    }

    public function applyAttendanceLateDeduction($employee, $attendance, $clockInDifferenceTime)
    {
        $lateDeductionPolicy = $this->getLateDeductionPolicy();

        if (isset($lateDeductionPolicy) && $lateDeductionPolicy->is_used && $lateDeductionPolicy->value < $clockInDifferenceTime) {
            $this->createAttendanceDeductionIfExist($employee, $clockInDifferenceTime, $attendance);
        }
    }

    public function handleExtraWorkday(Attendance $attendance)
    {
        $titleRepository = new NewTitleRepository;
        $newAttendanceRepository = new AttendanceRepository;
        $newAttendanceSettingRepository = new NewAttendanceSettingRepository;
        $extraWorkdayPolicyRepository = new ExtraWorkdayPolicyRepository;
        $workTypePolicyRepository = new WorkTypePolicyRepository;
        $attendanceRepository = Repository::getRepository('Attendance');

        $employee = $attendance->employee;
        $title = $titleRepository->find($employee->title_id);
        $extraWorkdayPolicyId = $title->extra_workday_policy_id ?? null;
        $extraWorkdayPolicy = $extraWorkdayPolicyRepository->find($extraWorkdayPolicyId);

        $attendanceDate = $attendance->date;
        $attendanceDay = strtolower(date('D', strtotime($attendanceDate)));
        if (!isset($extraWorkdayPolicy)) {
            return;
        }

        $extraWorkdaySettingPolicy = $newAttendanceSettingRepository->findFirstByKeys(['key' => 'extra_workday', 'company_id' => $employee->company_id]);
        if (isset($extraWorkdaySettingPolicy) && !$extraWorkdaySettingPolicy['is_used']) {
            return;
        }

        $extraWorkDayAttendanceWithSameDay = $newAttendanceRepository->getExistedExtraWorkDayAttendance($employee->id, $attendance->id, $attendanceDate);
        if (!empty($extraWorkDayAttendanceWithSameDay)) {
            return;
        }
        $attendanceUpdateData = ['extra_work_day_status' => 'pending'];
        $newAttendanceRepository->update($attendance->id, $attendanceUpdateData);
        $workTypePolicyId = $title->work_type_policy_id;
        $workTypePolicy = $workTypePolicyRepository->find($workTypePolicyId);
        if (!$this->doesEmployeeHaveFixedRestdays($employee)) {
            if ($this->applyDynamicMonthlyClosingDay()) {
                $attendanceDate = Carbon::parse($attendance->date);

                $payrollStartDate = $attendanceDate->copy()->startOfMonth()->toDateString();
                $payrollEndDate = $attendanceDate->copy()->endOfMonth()->toDateString();
            } else {
                $payrollMonthlyClosingDate = $this->getMonthlyClosingDay();
                $attendanceDate = Carbon::parse($attendance->date)->copy();

                if ($attendanceDate->day <= $payrollMonthlyClosingDate) {
                    $payrollStartDate = $attendanceDate->copy()->subMonth()->day($payrollMonthlyClosingDate)->addDay()->toDateString();
                    $payrollEndDate = $attendanceDate->copy()->day($payrollMonthlyClosingDate)->toDateString();
                } else {
                    $payrollStartDate = $attendanceDate->copy()->day($payrollMonthlyClosingDate)->addDay()->toDateString();
                    $payrollEndDate = $attendanceDate->copy()->addMonth()->day($payrollMonthlyClosingDate)->toDateString();
                }
            }
            $workingDaysCount = $attendanceRepository->getEmployeeDistinctAttendanceCountInDateRange($employee->id, $payrollStartDate, $payrollEndDate);
            $restDaysCount = $workTypePolicy->rest_days_count;
            Log::info('payroll start ' . $payrollStartDate . ' payroll end ' . $payrollEndDate);
            if ($workingDaysCount > (PayrollUtil::PAYROLL_MONTH_DAYS - $restDaysCount)) {
                $this->addExtraWorkDayRequest($attendance, $employee, $extraWorkdayPolicy['compensation_rate']);
            }
        } else {
            $restDays = $workTypePolicy->rest_days;
            $restDaysArray = explode(',', $restDays);
            if (in_array($attendanceDay, $restDaysArray)) {
                $this->addExtraWorkDayRequest($attendance, $employee, $extraWorkdayPolicy['compensation_rate']);
            }
        }
    }

    private function addExtraWorkDayRequest($attendance, $employee, $compensationRate)
    {
        $employeeRequestRepository = new EmployeeRequestRepository;
        $extraWorkdayRequestRepository = new ExtraWorkdayRequestRepository;
        $entityTagRepository = new EntityTagRepository;

        $extraWorkDayRequest = $extraWorkdayRequestRepository->add([
            'employee_id' => $employee->id,
            'extra_work_day_date' => $attendance?->date,
            'status' => 'pending',
            'compensation_rate' => $compensationRate,
            'attendance_id' => $attendance?->id,
        ]);
        $employeeRequestRepository->add([
            'company_id' => $attendance->company_id,
            'requested_by' => $attendance->employee_id,
            'requestable_id' => $extraWorkDayRequest->id,
            'requestable_type' => 'extra_work_day_request',
            'request_name' => 'extra_work_day_request',
            'comment' => null,
            'status' => 'pending',
            'employee_id' => $employee->id,
            'date' => $attendance?->date,
        ]);
        $entityTagRepository->add([
            'tag' => config('globals.ATTENDANCE_TAGS.EXTRA_WORKDAY'),
            'entity_id' => $attendance->id,
            'entity_type' => 'attendance',
            'company' => $attendance->company_id,
        ]);
        $requesterRoleIds = $attendance->employee->user->roles->pluck('id')->toArray();
        AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('extra_workday', $extraWorkDayRequest, $requesterRoleIds));
    }

    public function doesEmployeeHaveFixedRestdays($employee)
    {
        $workTypePolicy = $employee?->title?->workTypePolicy;

        $unleash = app(Unleash::class);
        $isNewWorkTypes = $unleash->getNewWorkTypesFeatureFlag($employee->company_id);

        return $isNewWorkTypes ? $workTypePolicy->rest_days_type == RestDaysTypesEnum::FIXED->value :
            $workTypePolicy->work_days_type != UserWorkTypesUtil::DYNAMIC_ON_SITE;

    }
}
