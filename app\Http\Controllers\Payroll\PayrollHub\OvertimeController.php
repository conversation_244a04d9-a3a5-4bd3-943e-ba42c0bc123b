<?php

namespace App\Http\Controllers\Payroll\PayrollHub;

use App\Exceptions\UnprocessableException;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\BulkActionRequest;
use App\Http\Requests\V1\PayrollHub\CompanyPayrollEntriesRequest;
use App\Http\Requests\V1\PayrollHub\EmployeePayrollEntriesRequest;
use App\Http\Resources\V1\PayrollHub\GetCompanyAnalyticsResource;
use App\Http\Resources\V1\PayrollHub\GetCompanyOvertimesResource;
use App\Http\Resources\V1\PayrollHub\GetEmployeeOvertimesResource;
use App\Services\V1\PayrollHub\OvertimesService;
use App\Services\V1\PayrollHub\RequestsBulkActionService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Util\ScopeUtil;
use App\Traits\QueriesHelper;

class OvertimeController extends NewController
{

    use QueriesHelper;

    public function __construct(
        protected OvertimesService           $overtimesService,
        protected RequestsBulkActionService $requestsBulkActionService,
    )
    {
    }
    public function getOvertimesForEmployee(EmployeePayrollEntriesRequest $request)
    {
        $data = $this->overtimesService->getEmployeeOvertimes($request->validated()); 
        return getResponseStructure(
            ['data' => new GetEmployeeOvertimesResource($data)],
            HttpStatusCodeUtil::OK,
            'Overtimes fetched successfully'
        );
    }

    public function getAllOvertimes(CompanyPayrollEntriesRequest $request)
    {
        $overtimes = $this->overtimesService->getAllOvertimesForCompany($request->validated());
        $data = GetCompanyOvertimesResource::collection($overtimes);
        $paginatedData = $data->response()->getData();
        return getResponseStructure(
            [
                'data' => $data,
                'pagination' => $paginatedData->meta,
            ],
            HttpStatusCodeUtil::OK,
            'Overtimes fetched successfully'
        );
    }

    public function getMonthlyAnalytics(CompanyPayrollEntriesRequest $request){
        $data = null;
        if($this->getUserHighestScopeKey(auth()->user()) == ScopeUtil::COMPANY_SCOPE)
        {
            $data = $this->overtimesService->getMonthlyAnalytics($request->validated());
        }
        return getResponseStructure(
            ['data' => $data ? GetCompanyAnalyticsResource::collection($data) : null],
            HttpStatusCodeUtil::OK,
            'Analytics fetched successfully'
        );
    }

    public function requestsBulkAction(BulkActionRequest $request)
    {
        try {
            DB::transaction(function () use ($request) {
                $this->requestsBulkActionService->requestsBulkAction($request->validated());
                return getResponseStructure(
                    ['data' => []],
                    HttpStatusCodeUtil::OK,
                    'Bulk action performed successfully'
                );
            });
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function bulkWaiveDeductions(BulkActionRequest $request)
    {
        try {
            DB::transaction(function () use ($request) {
                $this->requestsBulkActionService->bulkWaiveDeductions($request->validated());
            });
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }
}