<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\EmployeeRequest;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateOldEmployeeRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:old:employee_requests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $employeeRequests = EmployeeRequest::whereIn('requestable_type', ['attendance_deduction', 'attendance_overtime'])
                ->with('requestable.workflow')->get();

            foreach ($employeeRequests as $employeeRequest) {
                $employeeRequest->status = $employeeRequest?->requestable?->workflow?->status == 'completed' ? 'completed' : 'pending';
                $employeeRequest->save();
            }

            DB::commit();
        } catch (Exception $e) {
            //// dd($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
