<?php

namespace App\Factories\V1;

use App\Models\StateMachines\LoanRequest\RequestReadyToDisburse;
use App\Models\StateMachines\RequestApproved;
use App\Models\StateMachines\WaiveRequest\RequestWaived;
use App\Util\EmployeeUtil;

class RequestUpdateFactory
{
    /**
     * Get the appropriate update data for a request type
     *
     * @param  mixed  $request
     */
    public static function getUpdateData(string $requestType, $request): ?array
    {
        $factory = self::getFactory($requestType);

        return $factory ? $factory($request) : null;
    }

    /**
     * Get the factory method for a request type
     */
    protected static function getFactory(string $requestType): ?callable
    {
        $factories = [
            'AttendanceDeduction' => self::getAttendanceDeductionFactory(),
            'EmployeeLeaveRequest' => self::getLeaveRequestFactory(),
            'TerminationRequest' => self::getTerminationRequestFactory(),
            'MissionRequest' => self::getMissionRequestFactory(),
        ];

        return $factories[$requestType] ?? null;
    }

    /**
     * Get the factory for Attendance Deduction requests
     */
    protected static function getAttendanceDeductionFactory(): callable
    {
        return function ($request) {
            return [
                'entity_data' => [
                    'status' => $request->updated_value != null ? ($request->updated_value == 0 ? 'waived' : 'applied') : "applied",
                    'updated_value' => $request->updated_value ?? $request->deduction_value,
                    'updated_by' => config('globals.user')?->employee_id,
                ],
            ];
        };
    }

    

    /**
     * Get the factory for Leave requests
     */
    protected static function getLeaveRequestFactory(): callable
    {
        return function ($request) {
            return [
                'entity_data' => ['status' => RequestApproved::class],
            ];
        };
    }

    /**
     * Get the factory for Termination requests
     */
    protected static function getTerminationRequestFactory(): callable
    {
        return function ($request) {
            return [
                'entity_data' => [
                    'status' => RequestApproved::class,
                    'employee_status' => $request->terminate_date <= now() ? EmployeeUtil::STATUSES['TERMINATED'] : EmployeeUtil::STATUSES['TERMINATION_PENDING'],
                ],
            ];
        };
    }

    /**
     * Get the factory for Mission requests
     */
    protected static function getMissionRequestFactory(): callable
    {
        return function ($request) {
            return [
                'entity_data' => ['status' => RequestApproved::class],
            ];
        };
    }
}
