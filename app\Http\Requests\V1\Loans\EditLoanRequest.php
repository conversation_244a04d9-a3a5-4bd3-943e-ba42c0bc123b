<?php

namespace App\Http\Requests\V1\Loans;

use Illuminate\Foundation\Http\FormRequest;

class EditLoanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'loan_amount' => 'required|numeric|min:1',
            'duration_in_months' => 'required|integer|min:1|max:12',
            'installments' => ['required', 'array', function ($attribute, $value, $fail) {
                $installmentsAmount = array_sum(array_column($value, 'amount'));
                if ($installmentsAmount != $this->input('loan_amount')) {
                    $fail('The sum of installments amount must be equal to loan amount');
                }
                if (count($value) != $this->input('duration_in_months')) {
                    $fail('The number of installments must be equal to duration in months');
                }
            }],
            //validate date is in format YYYY-MM
            'installments.*.date' => 'required|date_format:Y-m',
            'installments.*.amount' => 'required|numeric|min:1',

        ];

    }
}
