<?php

namespace App\Console\Commands;

use App\Models\AttendanceDeduction;
use App\Models\EmployeeRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CloneDeductionStatusToEmployeeRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:clone-deduction-status-to-employee-requests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            echo 'Cloning deduction status to employee requests';

            $attendanceDeductions = AttendanceDeduction::select('id', 'status', 'company_id')
                ->whereDoesntHave('employeeRequests')
                ->orWhereHas('employeeRequests', function ($query) {
                    $query->whereNull('status');
                })
                ->get();

            echo 'Deductions count: '.$attendanceDeductions->count()."\n";

            foreach ($attendanceDeductions as $deduction) {
                $employeeRequest = EmployeeRequest::where('requestable_id', $deduction->id)
                    ->where('requestable_type', 'attendance_deduction')
                    ->whereNull('status')
                    ->whereNull('deleted_at')
                    ->first();

                if (! $employeeRequest) {
                    echo 'Employee request not found for deduction: '.$deduction->id."\n";
                    $employeeRequest = EmployeeRequest::create([
                        'requestable_id' => $deduction->id,
                        'requestable_type' => 'attendance_deduction',
                        'request_name' => 'waive_deduction',
                        'status' => $deduction->status,
                        'company_id' => $deduction->company_id,
                    ]);

                    echo 'Employee request created: '.$employeeRequest->status.' Request id : '.$employeeRequest->id."\n";
                } else {
                    echo 'Employee request: '.$employeeRequest->id.' Status: '.$deduction->status."\n";

                    $employeeRequest->update([
                        'status' => $deduction->status,
                    ]);

                    echo 'Employee request: '.$employeeRequest->id.' Status updated to: '.$deduction->status."\n";
                }

            }
            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
        }
    }
}
