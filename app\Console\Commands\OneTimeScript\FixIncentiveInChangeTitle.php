<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Employee;
use App\Services\V1\KPIs\IncentiveService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixIncentiveInChangeTitle extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:incentive_in_change_title';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $titles = DB::table('employee_title_changes')
                // select all
                ->select('to_title_id')
                ->whereIn('to_title_id', function ($query) {
                    $query->select('title_id')
                        ->from('incentive_per_titles');
                })
                ->where('status', 'completed')
                ->get();

            $employees = Employee::whereIn('title_id', $titles->pluck('to_title_id')->toArray())->get();
            Log::info('Total employees: '.$employees->count());

            $incentiveService = app(IncentiveService::class);
            foreach ($employees as $employee) {
                $incentiveService->maintainIncentivePerEmployeeTable($employee->id, $employee->branch_id, $employee->title_id, $employee->company_id);
            }

            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
