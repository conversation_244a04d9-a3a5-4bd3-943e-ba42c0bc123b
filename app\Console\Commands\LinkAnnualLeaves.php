<?php

namespace App\Console\Commands;

use App\Services\CompanySetup\LinkAnnualLeavesService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class LinkAnnualLeaves extends Command
{
    public function __construct(private LinkAnnualLeavesService $linkAnnualLeavesService)
    {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'link:annual_leaves';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $this->linkAnnualLeavesService->run();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
