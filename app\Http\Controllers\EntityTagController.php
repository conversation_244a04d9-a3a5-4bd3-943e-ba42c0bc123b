<?php

namespace App\Http\Controllers;

use App\Services\TimeTracking\CrudServices\EntityTagCrudService;
use Illuminate\Validation\Rule;
use stdClass;
use Symfony\Component\HttpFoundation\Response;

class EntityTagController extends Controller
{
    public function __construct(
        private EntityTagCrudService $entityTagCrudService
    ) {}

    public function updateAbsentTag(array $request, stdClass &$output): void
    {
        try {
            $validator = \Validator::make($request,
                [
                    'id' => ['required', 'exists:entity_tags,id'],
                    'tag_name' => [
                        'required',
                        Rule::in(['absent', 'absent_without_permission']),
                    ],
                ]
            );
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }
            $request = $validator->validate();
            $request['related_objects'] = [];
            $request['related_objects_count'] = [];

            $this->entityTagCrudService->getById($request, $output);
            if (! in_array($output->entity_tag->tag, ['absent', 'absent_without_permission'])) {
                $output->Error = ['Invalid tag name', 'غير مسموح بتعديل هذا النوع من الغياب'];

                return;
            }

            $this->entityTagCrudService->updateTagName($request['id'], $request['tag_name']);
            $output->entity_tag = $output->entity_tag->refresh();
            $output->code = Response::HTTP_OK;
        } catch (\Exception $e) {
            // Handle exception
            if ($e->getMessage() != '') {
                $output->Error = [$e->getMessage(), $e->getMessage()];
            } else {
                $output->Error = ['Something went wrong', 'حدث خطأ ما'];
            }
        }
    }
}
