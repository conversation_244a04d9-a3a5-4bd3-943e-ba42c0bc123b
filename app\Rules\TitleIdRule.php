<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TitleIdRule implements Rule
{
    public function passes($attribute, $value)
    {
        if ($value === 'all') {
            return true;
        } elseif (is_numeric($value)) {
            $exists = DB::table('titles')
                ->where('id', $value)
                ->where('company_id', Auth::user()->company_id)
                ->whereNull('deleted_at')
                ->exists();

            return $exists;
        }

        return false;
    }

    public function message()
    {
        return __('validation.title_id');
    }
}
