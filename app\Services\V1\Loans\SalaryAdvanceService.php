<?php

namespace App\Services\V1\Loans;

use App\Exceptions\UnprocessableException;
use App\Models\StateMachines\LoanRequest\LoanRequestCancelled;
use App\Models\StateMachines\LoanRequest\RequestDisbursed;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\NewTitleRepository;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\V1\Loans\LoanRepository;
use App\Repositories\V1\Loans\SalaryAdvancePolicyRepository;
use App\Repositories\V1\Loans\SalaryAdvanceRepository;
use App\Services\BaseService;
use App\Services\PayrollSetup\SalaryComponentsCategoriesService;
use App\Traits\GetLastDraftedPayRollOrCreate;
use App\Traits\QueriesHelper;
use App\Traits\RequestsHelper;
use App\Traits\UploadFile\UploadFile;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Traits\V1\EmployeeRequestsTrait;
use App\Traits\WorkflowTrait;
use App\Util\LoansUtil;
use App\Util\PayrollUtil;
use Illuminate\Support\Facades\Log;
use Workflow\WorkflowStub;
use App\Jobs\AssignApprovalsJob;
use App\Models\StateMachines\LoanRequest\RequestReadyToDisburse;
use App\Traits\V1\NotificationRedirection;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;

class SalaryAdvanceService extends BaseService
{
    use GetLastDraftedPayRollOrCreate, PayrollHelper,QueriesHelper, RequestsHelper, UploadFile, WorkflowTrait, EmployeeRequestsTrait, NotificationRedirection, PrepareAssignRequestCycleDataTrait;

    private SalaryAdvanceRepository $salaryAdvanceRepository;

    private SalaryAdvancePolicyRepository $salaryAdvancePolicyRepository;

    private NewEmployeeRepository $newEmployeeRepository;

    private NewTitleRepository $newTitleRepository;

    private SystemSettingRepository $systemSettingRepository;

    private PayrollsRepository $payrollsRepository;

    private NewTitleRepository $titleRepository;

    private $salaryAdvancePolicy;

    private LoanRepository $loansRepository;

    private $payroll;

    public function __construct(SalaryAdvanceRepository $salaryAdvanceRepository)
    {
        parent::__construct($salaryAdvanceRepository);
        $this->salaryAdvancePolicyRepository = new SalaryAdvancePolicyRepository;
        $this->newEmployeeRepository = new NewEmployeeRepository;
        $this->newTitleRepository = new NewTitleRepository;
        $this->titleRepository = new NewTitleRepository;
        $this->systemSettingRepository = new SystemSettingRepository;
        $this->payrollsRepository = new PayrollsRepository;
        $this->loansRepository = new LoanRepository;
    }

    public function getMaxSalaryAdvancePercentage($employeeId = null): ?array
    {
        $employee = $employeeId 
        ? $this->newEmployeeRepository->findOrFail($employeeId) :
         auth()->user()->employee;
        $employeeNetSalary = $employee->employeeSalary->net_salary ?? 0;
        $title = $this->newTitleRepository->findOrFail($employee->title_id);
        $salaryAdvancePolicy = $title->salaryAdvancePolicy ?? [];
        $maxPercentage = $salaryAdvancePolicy->max_percentage_of_salary ?? 0;
        $maxSalaryAdvanceAmount = $employeeNetSalary * ($maxPercentage / 100);

        return [
            'max_salary_advance_percentage' => $maxPercentage,
            'max_salary_advance_amount' => $maxSalaryAdvanceAmount,
        ];
    }

    public function requestSalaryAdvance($request, $employeeId = null)
    {
        $employee = $employeeId ?
         $this->newEmployeeRepository->findOrFail($employeeId) :
         auth()->user()->employee;
        $this->payroll = $this->getCurrentPayroll();
        if (isset($this->payroll) && $this->payroll->status == PayrollUtil::PAYROLL_STATUS['FINALIZED']) {
            throw new UnprocessableException('you cannot request a salary advance on a finalized payroll');
        }

        $this->checkISValidSalaryAdvanceRequest($employee, $request);

        $this->salaryAdvancePolicy = $employee->title?->salaryAdvancePolicy;

        $salaryAdvance = $this->repository->add([
            'employee_id' => $employee->id,
            'amount' => $request['amount'],
            'status' => 'pending',
            'comment' => $request['comment'] ?? null,
            'policy_id' => $this->salaryAdvancePolicy->id,
            'payroll_id' => $this->payroll->id
        ]);

        $attachments = $request['attachments'] ?? [];
        if (! empty($attachments) && isset($salaryAdvance)) {
            $companyUuid = $employee->company->uuid;
            $employeeUuid = $employee->uuid;

            $filePaths = [];

            foreach ($attachments as $attachment) {
                $filePath = $this->uploadFile($attachment, 's3', "{$companyUuid}/employee-files/{$employeeUuid}/loans");
                $filePaths[] = $filePath;
            }
            $this->saveAttachmets($filePaths, $salaryAdvance);

            $salaryAdvance->save();

        }

        $requesterRoleIds = auth()->user()->roles->pluck('id')->toArray();

        AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('salary_advance', $salaryAdvance, $requesterRoleIds));

        return $salaryAdvance;
    }

    public function checkISValidSalaryAdvanceRequest($employee, $request)
    {
        $policyEnabled = $this->systemSettingRepository->findFirstByKey('key', 'salary_advance_policy')?->value;
        if (! $policyEnabled) {
            throw new UnprocessableException('Salary advance policy is not enabled');
        }

        if ($employee->status == config('globals.EMPLOYEE_STATUSES.TERMINATED')) {
            throw new UnprocessableException('You are not an active employee');
        }

        $this->salaryAdvancePolicy = $employee->title?->salaryAdvancePolicy;
        if (! $this->salaryAdvancePolicy) {
            throw new UnprocessableException('Salary advance policy not found for your title');
        }
        $onProbationAllowed = $this->salaryAdvancePolicy?->apply_on_probation ?? false;
        $employeeOnProbation = $employee?->status ? $employee?->status == 'on_probation' : false;
        if (! $onProbationAllowed && $employeeOnProbation) {
            throw new UnprocessableException(trans('messages.loan_request_on_probation'));
        }

        $employeeHasAPendingSalaryAdvance = $this->repository->getNotPaidSalaryAdvancesForEmployee($employee->id);
        $pendingLoan = $this->loansRepository->getNotPaidLoansForEmployee($employee->id);
        if ($employeeHasAPendingSalaryAdvance || $pendingLoan) {
            throw new UnprocessableException(trans('messages.ongoing_loan'));
        }
        $employeeNetSalary = $employee->employeeSalary?->net_salary;
        $allowedAmount = $employeeNetSalary * ($this->salaryAdvancePolicy->max_percentage_of_salary / 100);

        if ($request['amount'] > $allowedAmount) {
            throw new UnprocessableException(trans('messages.loan_amount_exceeds_allowed_amount'));
        }

        $minDayToRequest = $this->salaryAdvancePolicy->allowed_request_day;
        $isValidDay = $this->checkIfValidDayToRequest($minDayToRequest);
        if (! $isValidDay) {
            throw new UnprocessableException('You can only request for salary advance on the '.$minDayToRequest.'th day of the month');
        }
    }

    public function checkIfValidDayToRequest($minDayToRequest)
    {
        $dayDiff = now()->diffInDays($this->payroll->start) + 1;

        return $minDayToRequest <= $dayDiff;
    }

    public function disburseSalaryAdvance($id, $request)
    {
        $salaryAdvance = $this->repository->findOrFail($id);

        if ($salaryAdvance->status != LoansUtil::READY_TO_DISBURSE) {

            throw new UnprocessableException('Salary advance with id: '.$id.' is not approved yet');
        }
        $salaryAdvance->status->transitionTo(RequestDisbursed::class);  // employee request for loan will get the status approved after the loan is disbursed // "boot method in Loan model will handle this"

        $payroll = $this->payrollsRepository->payrollCoversDate($request['disbursement_date']);
        if (! $payroll || $payroll->status != 'draft') {
            throw new UnprocessableException('There is no drafted payroll for this month');
        }

        $salaryAdvance->disbursement_date = $request['disbursement_date'];
        $salaryAdvance->payroll_id = $payroll->id;
        $salaryAdvance->save();

        return $salaryAdvance;
    }

    public function enableSalaryAdvancePolicyToggle(): void
    {
        $salaryAdvancePolicyExists = $this->systemSettingRepository->companyHasKey('salary_advance_policy');
        $companyId = auth()->user()->company->id;
        if ($salaryAdvancePolicyExists) {
            $salaryAdvancePolicySetting = $this->systemSettingRepository->getSettingByKey('salary_advance_policy', $companyId);
            $salaryAdvancePolicySetting->value = ! $salaryAdvancePolicySetting->value;
            $salaryAdvancePolicySetting->save();
        } else {
            $this->systemSettingRepository->add([
                'as_of_date' => date('Y-m-d'),
                'key' => 'salary_advance_policy',
                'value' => 1,
                'company_id' => $companyId,
            ]);
        }
        $salaryComponentCategoryService = app(SalaryComponentsCategoriesService::class);
        $salaryComponentCategoryService->setDefaultLoansAndSalaryAdvanceCategory();
    }

    public function salaryAdvancepolicyWarning(array $titleIds)
    {
        $assignedTitles = [];
        $lang = (config('globals.lang') ?? 'ar');

        foreach ($titleIds as $titleId) {
            $title = $this->titleRepository->find($titleId);
            if ($title && isset($title->salary_advance_policy_id)) {
                $salaryAdvancePolicy = $title->SalaryAdvancePolicy;
                $assignedTitles[] = [
                    'id' => $title->id,
                    'name' => $lang == 'ar' ? $title->name_ar : $title->name_en,
                    'salary_advance_policy_name' => $lang == 'ar' ? $salaryAdvancePolicy->name_ar : $salaryAdvancePolicy->name_en,
                ];
            }
        }

        return $assignedTitles;
    }

    public function createSalaryAdvancePolicy($request)
    {
        $companyId = auth()->user()->company->id;
        $salaryAdvancePolicy = $this->salaryAdvancePolicyRepository->add([
            'company_id' => $companyId,
            'name_en' => $request->name_en,
            'name_ar' => $request->name_ar,
            'max_percentage_of_salary' => $request->max_percentage_of_salary,
            'apply_on_probation' => $request->apply_on_probation,
            'allowed_request_day' => $request->day_to_request,
        ]);
        $titleIds = $request->title_ids ?? [];
        if (is_array($titleIds) && count($titleIds) > 0) {
            foreach ($titleIds as $titleId) {
                $title = $this->titleRepository->find($titleId);
                if ($title) {
                    $title->salary_advance_policy_id = $salaryAdvancePolicy->id;
                    $title->save();
                }
            }
        }

        return $salaryAdvancePolicy;
    }

    public function editSalaryAdvancePolicy($request, $salaryAdvancePolicyId)
    {
        $companyId = auth()->user()->company->id;

        // Find and deactivate the existing salary advance policy
        $salaryAdvancePolicy = $this->salaryAdvancePolicyRepository->find($salaryAdvancePolicyId);
        $salaryAdvancePolicy->is_active = false;
        $salaryAdvancePolicy->save();

        // Create the new salary advance policy
        $updatedSalaryAdvancePolicy = $this->salaryAdvancePolicyRepository->add([
            'company_id' => $companyId,
            'name_en' => $request->name_en,
            'name_ar' => $request->name_ar,
            'max_percentage_of_salary' => $request->max_percentage_of_salary,
            'apply_on_probation' => $request->apply_on_probation,
            'allowed_request_day' => $request->day_to_request,
        ]);

        $titleIds = $request->title_ids ?? [];
        $existingTitlesQuery = $this->titleRepository->findFirstByKey('salary_advance_policy_id', $salaryAdvancePolicyId);
        $existingTitles = $existingTitlesQuery ? $existingTitlesQuery->get() : collect(); // Use collect() to return an empty collection if no results

        $titleIdSet = collect($titleIds)->flip();

        foreach ($existingTitles as $title) {
            if ($titleIdSet->has($title->id)) {
                $title->salary_advance_policy_id = $updatedSalaryAdvancePolicy->id;
            } else {
                $title->salary_advance_policy_id = null;
            }
            $title->save();
        }

        foreach ($titleIds as $titleId) {
            if (! $existingTitles->contains('id', $titleId)) {
                $title = $this->titleRepository->find($titleId);
                if ($title) {
                    $title->salary_advance_policy_id = $updatedSalaryAdvancePolicy->id;
                    $title->save();
                }
            }
        }

        return $updatedSalaryAdvancePolicy;
    }

    public function getSalaryAdvanceByFilter($data)
    {
        $salaryAdvances = $this->repository->getSalaryAdvanceByFilters($data);

        foreach ($salaryAdvances as $salaryAdvance) {
            $payroll = $this->payrollsRepository->find($salaryAdvance->payroll_id);
            $approvalCycle = $this->getApprovalCycle($salaryAdvance, $salaryAdvance->employee);
            $salaryAdvance->actionable = $salaryAdvance != config('globals.REQUEST_STATUSES.PENDING') && $this->isActionable($approvalCycle, $salaryAdvance->employeeRequest->status);
            $salaryAdvance->type = 'salary_advance';
            $salaryAdvance->final_payment_date = $payroll?->end;
            $salaryAdvance->disbursable = ($salaryAdvance->status == LoansUtil::READY_TO_DISBURSE);
            $salaryAdvance->editable = ($salaryAdvance->status == LoansUtil::DISBURSED);
        }

        return $salaryAdvances;
    }

    public function approveSalaryAdvance($id)
    {
        Log::info('approveSalaryAdvance:service');
        $salaryAdvance = $this->repository->findOrFail($id);

        $employeeHasPendingSalaryAdvance = $this->repository->getNotPaidSalaryAdvancesForEmployee($salaryAdvance->employee_id, [LoansUtil::DISBURSED, LoansUtil::READY_TO_DISBURSE]);
        if ($employeeHasPendingSalaryAdvance) {
            throw new UnprocessableException(trans('messages.ongoing_loan'));
        }

        $this->actionOnSalaryAdvance($salaryAdvance, 'approve');

        return $salaryAdvance;
    }

    public function rejectSalaryAdvance($id)
    {
        $salaryAdvance = $this->repository->findOrFail($id);

        $this->actionOnSalaryAdvance($salaryAdvance, 'reject');

        return $salaryAdvance;
    }

    public function actionOnSalaryAdvance($salaryAdvance, $actionType)
    {
        Log::info('actionOnSalaryAdvance:type: '.$actionType);

        try {
            $roleIds = config('globals.user')->roles->pluck('id')->toArray();
            $userCanTakeAnAction = $this->canTakeAnAction($roleIds, $salaryAdvance->id, config('globals.REQUEST_WORKFLOW_TYPES.SALARY_ADVANCE'), $actionType);
            if (! $userCanTakeAnAction) {
                throw new UnprocessableException(trans('messages.can_not_take_this_action'));
            }

            if ($actionType != 'cancel' && $salaryAdvance->status != config('globals.REQUEST_STATUSES.PENDING')) {
                throw new UnprocessableException(trans('messages.workflow_is_completed'));
            }

            $this->doAnAction($actionType);
            if($this->checkRequestIsCompleted($salaryAdvance->employeeRequest)) {
                $finalStatus = $this->getFinalStatus($salaryAdvance->employeeRequest, $actionType);
                $this->updateRequest($salaryAdvance->employeeRequest, $finalStatus);
                $this->updateEntity($salaryAdvance, $finalStatus == "approved" ? "ready_to_disburse" : $finalStatus);
                $this->redirectNotificationsAfterRequestFinalized($salaryAdvance, $finalStatus);
            }
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException('Something went wrong while taking an action on the salary advance');
        }
    }

    public function forceCancelSalaryAdvance($id)
    {
        $salaryAdvance = $this->repository->findOrFail($id);

        if ($salaryAdvance->status != LoansUtil::READY_TO_DISBURSE) {
            throw new UnprocessableException('You can not cancel this salary advance');
        }

        $this->actionOnSalaryAdvance($salaryAdvance, 'cancel');

        return $salaryAdvance;
    }

    public function getEmployeeSalaryAdvance($request)
    {
        $employeeId = $request['employeeId'] ?? auth()->user()->employee->id;
        $salaryAdvances = $this->repository->getSalaryAdvanceByEmployeeId($employeeId);
        if ($salaryAdvances->isEmpty()) {
            return collect([]);
        }
        $currentYear = date('Y');
        $currentMonth = date('m');
        $monthlyClosingDay = $this->getMonthlyClosingDay()
            ?? throw new UnprocessableException('Monthly closing day not set');
        foreach ($salaryAdvances as $salaryAdvance) {
            $salaryAdvance->pay_out_date = "{$currentYear}-{$currentMonth}-{$monthlyClosingDay}";
            $salaryAdvance->type = 'salary_advance';
        }

        return $salaryAdvances;
    }

    public function getReadyToDisburseSalaryAdvancesCount()
    {
        return $this->repository->getReadyToDisburseSalaryAdvancesCount();
    }


}
