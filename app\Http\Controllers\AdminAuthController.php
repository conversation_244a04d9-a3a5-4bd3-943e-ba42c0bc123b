<?php

namespace App\Http\Controllers;

use App\Services\AdminAuth\AdminAuthService;
use stdClass;

class AdminAuthController extends Controller
{
    private AdminAuthService $adminAuthService;

    public function __construct(AdminAuthService $adminAuthService)
    {
        $this->adminAuthService = $adminAuthService;
    }

    public function login(array $request, stdClass &$output): void
    {
        $this->adminAuthService->login($request, $output);
    }

    public function impersonate(array $request, stdClass &$output): void
    {
        try {
            $this->adminAuthService->impersonate($request, $output);
        } catch (\Exception $e) {
            $output->Error = trans('impersonate.impersonate_error');
        }
    }
}
