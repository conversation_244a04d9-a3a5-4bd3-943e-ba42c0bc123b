<?php

namespace App\Console\Commands;

use App\Services\CompanySetup\GroupPermissionsService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GroupPermissions extends Command
{
    public function __construct(private GroupPermissionsService $groupPermissionsService)
    {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'group:permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $this->groupPermissionsService->run();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
