<?php

namespace App\Http\Controllers\Integrations;

use App\Http\Requests\Integrations\VerifyFoodicsUserRequest;
use App\Services\Integrations\FoodicsIntegrationService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Http\JsonResponse;

class FoodicsIntegrationController
{
    public function __construct(
        protected FoodicsIntegrationService $foodicsIntegrationService
    ) {}

    public function verify(VerifyFoodicsUserRequest $request): JsonResponse
    {
        try {
            $this->foodicsIntegrationService->verify($request->get('code'), $request->get('state'));

            return getResponseStructure(null, HttpStatusCodeUtil::OK, 'Foodics Integration Verified Successfully');
        } catch (\Exception $exception) {
            return getErrorResponseStructure(HttpStatusCodeUtil::BAD_REQUEST, $exception->getMessage());
        }
    }

    public function syncEmployees(): JsonResponse
    {
        $this->foodicsIntegrationService->syncCompanyEmployees(config('globals.company'));

        return getResponseStructure(null, HttpStatusCodeUtil::OK, 'Foodics Integration Employees Synced Successfully');
    }

    public function syncShifts(): JsonResponse
    {
        $this->foodicsIntegrationService->syncCompanyShifts(config('globals.company'));

        return getResponseStructure(null, HttpStatusCodeUtil::OK, 'Foodics Integration Shifts Synced Successfully');
    }
}
