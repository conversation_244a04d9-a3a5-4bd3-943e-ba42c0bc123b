<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Models\Cico;
use Illuminate\Support\Carbon;

class UnverifiedTagHandler implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        return [
            'name' => $employeeAttendance->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($employeeAttendance->date)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        return isset($tags[$employeeId]['tags']['unverified']) ? $tags[$employeeId]['tags']['unverified']['count'] + 1 : 1;
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return $employeeAttendance instanceof Cico && $employeeAttendance->status == 'unverified';
    }

    public function handleTagUnit(): string
    {
        return 'days';
    }
}
