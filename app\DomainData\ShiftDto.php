<?php

namespace App\DomainData;

trait ShiftDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'date' => 'required|date',
            'shift_day' => 'nullable|string',
            'name' => 'required|string',
            'type' => 'required|string|in:NORMAL,MISSION,TRAINING',
            'start_time' => 'required|date_format:H:i:s',
            'end_time' => 'required|date_format:H:i:s',
            'shift_branch_id' => 'nullable|numeric',
            'schedule_id' => 'nullable|numeric',
            'branch_id' => 'required|numeric',
            'company_id' => 'numeric',
            'color' => 'string',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeShiftDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
