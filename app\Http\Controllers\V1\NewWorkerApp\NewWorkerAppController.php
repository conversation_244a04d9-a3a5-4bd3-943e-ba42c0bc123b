<?php

namespace App\Http\Controllers\V1\NewWorkerApp;

use App\Exceptions\UnprocessableException;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\WorkerApp\AddBalanceToOvertimeRequest;
use App\Http\Requests\V1\WorkerApp\GetPayslipRequest;
use App\Http\Requests\V1\WorkerApp\GetRequestsToWorkerRequest;
use App\Http\Requests\V1\WorkerApp\GetWorkerAttendanceMetricsRequest;
use App\Http\Requests\V1\WorkerApp\GetWorkerAttendanceRequest;
use App\Http\Requests\V1\WorkerApp\RequestOvertimeRequest;
use App\Http\Resources\EmployeeRequestResource;
use App\Http\Resources\V1\WorkerApp\EmployeeLeaveRequestResource;
use App\Http\Resources\V1\WorkerApp\ExcuseHistoryResource;
use App\Http\Resources\V1\WorkerApp\GetAllBalancesCollection;
use App\Http\Resources\V1\WorkerApp\GetWorkerAttendanceResource;
use App\Http\Resources\V1\WorkerApp\GetWorkerMetricsResource;
use App\Http\Resources\V1\WorkerApp\GetWorkerMissionTimecards;
use App\Http\Resources\V1\WorkerApp\PayslipDetailsResource;
use App\Http\Resources\V1\WorkerApp\WorkerAbsentResource;
use App\Http\Resources\V1\WorkerApp\WorkerAttendanceDeductionResource;
use App\Http\Resources\V1\WorkerApp\WorkerAttendanceDetailsResource;
use App\Http\Resources\V1\WorkerApp\WorkerMissionRequestsResource;
use App\Http\Resources\V1\WorkerApp\WorkerNoClockOutResource;
use App\Http\Resources\V1\WorkerApp\WorkerOverTimeResource;
use App\Http\Resources\V1\WorkerApp\WorkerPublicHolidaysAbsenceResource;
use App\Http\Resources\V1\WorkerApp\WorkerPublicHolidaysWorkedResource;
use App\Http\Resources\V1\WorkerApp\WorkerRestDaysResource;
use App\Http\Resources\V1\WorkerApp\WorkerTimecardDetailsResource;
use App\Http\Resources\V1\WorkerApp\WorkerUnaccountedDaysResource;
use App\Services\V1\LeaveManagement\EmployeeLeavesService;
use App\Services\V1\NewWorkerApp\NewWorkerAppService;
use App\Services\V1\RequestOvertime\RequestOvertimeService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

class NewWorkerAppController extends NewController
{
    private Logger $logger;

    public function __construct(
        protected NewWorkerAppService    $newWorkerAppService,
        protected RequestOvertimeService $requestOvertimeService,
        private EmployeeLeavesService    $leavesService,

    )
    {
        $this->logger = new Logger('NewWorkerAppController');
        $this->logger->pushHandler(new StreamHandler(storage_path('logs/datadog.log')));
    }

    public function getAttendanceMetrics(GetWorkerAttendanceMetricsRequest $request)
    {
        $employeeId = auth()->user()->employee->id;
        $validatedData = $request->validated();
        $data = Cache::remember('employee_metrics_' . $validatedData['from_date'] . '_' . $employeeId, 43200, function () use ($validatedData, $employeeId) {
            $this->logger->info('Cache miss for employee_metrics_' . $employeeId);
            $attendanceMetrics = $this->newWorkerAppService->getAttendanceMetrics($validatedData, $employeeId);
            $data = new GetWorkerMetricsResource($attendanceMetrics);

            return $data->response()->getData(true)['data'];
        });

        return getResponseStructure(
            ['data' => $data],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerPenalties(GetWorkerAttendanceRequest $request)
    {

        $penaltyData = $this->newWorkerAppService->getPenaltyData($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => $penaltyData],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerExcuseHistory(GetWorkerAttendanceRequest $request)
    {
        $excuseHistory = $this->newWorkerAppService->getExcusedData($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => ExcuseHistoryResource::collection($excuseHistory)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerRestDays(GetWorkerAttendanceRequest $request)
    {
        $restDays = $this->newWorkerAppService->getRestDayData($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => WorkerRestDaysResource::collection($restDays)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerPublicHolidaysAbsences(GetWorkerAttendanceRequest $request)
    {
        $publicHolidaysAbsences = $this->newWorkerAppService->getAbsentPublicHolidayData($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => WorkerPublicHolidaysAbsenceResource::collection($publicHolidaysAbsences)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerPublicHolidaysWorkDays(GetWorkerAttendanceRequest $request)
    {
        $publicHolidaysWorkDays = $this->newWorkerAppService->getWorkPublicHolidayData($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => WorkerPublicHolidaysWorkedResource::collection($publicHolidaysWorkDays)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerAbsentDays(GetWorkerAttendanceRequest $request)
    {
        $absentDays = $this->newWorkerAppService->getAbsentData($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => WorkerAbsentResource::collection($absentDays)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerNoClockOutDays(GetWorkerAttendanceRequest $request)
    {
        $noClockOutDays = $this->newWorkerAppService->getNoClockOutData($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => WorkerNoClockOutResource::collection($noClockOutDays)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerOvertime(GetWorkerAttendanceRequest $request)
    {
        $overtime = $this->newWorkerAppService->getOvertimeData($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => WorkerOverTimeResource::collection($overtime)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerAttendanceDeduction(GetWorkerAttendanceRequest $request)
    {
        $attendanceDeduction = $this->newWorkerAppService->getDeductionData($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => WorkerAttendanceDeductionResource::collection($attendanceDeduction)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerUnaccountedDays(GetWorkerAttendanceRequest $request)
    {

        $unaccountedDays = $this->newWorkerAppService->getUnaccountedData($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => new WorkerUnaccountedDaysResource($unaccountedDays)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerExtraWorkDays(GetWorkerAttendanceRequest $request)
    {
        $extraWorkDays = $this->newWorkerAppService->getExtraWorkDayData($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => GetWorkerAttendanceResource::collection($extraWorkDays)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerAttendanceDetails(GetWorkerAttendanceRequest $request)
    {
        $attendanceDetails = $this->newWorkerAppService->getWorkerAttendanceDetails($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => new WorkerAttendanceDetailsResource($attendanceDetails)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getWorkerTimecardDetailsById($id)
    {
        $timecardDetails = $this->newWorkerAppService->getTimeCardById($id);

        return getResponseStructure(
            ['data' => new WorkerTimecardDetailsResource($timecardDetails)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getLeavesBalances()
    {
        $employeeWithBalances = $this->leavesService->getLeavesBalances(auth()->user()->employee_id);

        return getResponseStructure(
            ['data' => new GetAllBalancesCollection($employeeWithBalances)],
            HttpStatusCodeUtil::OK,
            'balances retrieved successfully'
        );
    }

    public function getLeaves(GetWorkerAttendanceRequest $request)
    {
        $leaves = $this->newWorkerAppService->getLeavesForWorker($request->validated(), auth()->user()->employee->id);

        return getResponseStructure(
            ['data' => EmployeeLeaveRequestResource::collection($leaves)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getRequests(GetRequestsToWorkerRequest $request)
    {
        // Todo: Refactor this service and repository
        $data = [
            'employee_id' => auth()->user()->employee->id,
            'for_employee' => true,
            'from_date' => $request->validated('from_date'),
            'to_date' => $request->validated('to_date'),
        ];
        if ($request->has('filter')) {
            $data['filter'] = $request->validated('filter');
        }
        $requests = $this->newWorkerAppService->getRequests($data);

        return getResponseStructure(
            ['data' => new EmployeeRequestResource($requests['data']->groupBy('requestable_type'))],
            HttpStatusCodeUtil::OK
        );
    }

    public function getPayslips(GetPayslipRequest $request)
    {
        try {
            $employeeId = empty($request->validated('employee_id')) ? auth()->user()->employee_id : $request->validated('employee_id');
            $payslip = $this->newWorkerAppService->getPayslips($employeeId, $request->validated());
            if (empty($payslip)) {
                return getResponseStructure(['data' => []], HttpStatusCodeUtil::UNPROCESSABLE_ENTITY);
            }

            return getResponseStructure(
                ['data' => new PayslipDetailsResource($payslip)],
                HttpStatusCodeUtil::OK
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function getMissionTimecards(GetWorkerAttendanceRequest $request)
    {
        $missions = $this->newWorkerAppService->getMissionTimecards(auth()->user()->employee_id, $request->validated());

        return getResponseStructure(
            ['data' => GetWorkerMissionTimecards::collection($missions)],
            HttpStatusCodeUtil::OK
        );
    }

    public function requestOvertime(RequestOvertimeRequest $request)
    {
        try {
            return DB::transaction(function () use ($request) {
                $this->requestOvertimeService->requestOvertime($request->validated());

                return getResponseStructure(
                    [],
                    HttpStatusCodeUtil::OK,
                    'overtime requested successfully');
            });
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }

    }

    public function getOvertimeRequests()
    {
        $data = $this->requestOvertimeService->getOvertimeRequest(auth()->user()->employee_id);

        return getResponseStructure(
            ['data' => $data],
            HttpStatusCodeUtil::OK
        );
    }

    public function approveOvertime($requestId)
    {
        try {
            DB::transaction(function () use ($requestId) {
                $this->requestOvertimeService->actionOnRequest($requestId, 'approve');

                return getResponseStructure(
                    ['data' => []],
                    HttpStatusCodeUtil::OK,
                    'Request approved successfully');
            });
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function rejectOvertime($requestId)
    {
        try {
            DB::transaction(function () use ($requestId) {
                $this->requestOvertimeService->actionOnRequest($requestId, 'reject');

                return getResponseStructure(
                    ['data' => []],
                    HttpStatusCodeUtil::OK,
                    'Request rejected successfully');
            });
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function addOvertimeLeaveBalance(AddBalanceToOvertimeRequest $request)
    {
        try {
            $this->requestOvertimeService->addBalanceToOvertimeLeave($request->validated());

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'balance added successfully');
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function getWorkerMissionRequests()
    {
        $missions = $this->newWorkerAppService->getMissionRequests(auth()->user()->employee_id);

        return getResponseStructure(
            ['data' => WorkerMissionRequestsResource::collection($missions)],
            HttpStatusCodeUtil::OK
        );
    }

    public function getAttendance(GetWorkerAttendanceRequest $request)
    {
        $attendance = $this->newWorkerAppService->getAttendance($request->validated(), auth()->user()->employee->id);
        $sortedAttendance = $attendance->sortBy('date');

        return getResponseStructure(
            ['data' => GetWorkerAttendanceResource::collection($sortedAttendance)],
            HttpStatusCodeUtil::OK
        );
    }

}
