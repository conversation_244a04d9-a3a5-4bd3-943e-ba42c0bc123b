<?php

namespace App\Http\Controllers\TimeCard;

use App\DomainData\TimecardDto;
use App\Http\Controllers\Controller;
use App\Http\Resources\V1\GetVerifiedAttendanceForWorkerAppResource;
use App\Http\Resources\VerifiedAttendanceCollection;
use App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService;
use App\Services\Schedule\CrudServices\RestEmployeeCrudService;
use App\Services\TimeTracking\BusinessServices\CopyTimecardsAndRestsService;
use App\Services\TimeTracking\BusinessServices\GetTimecardsFilterService;
use App\Services\TimeTracking\CrudServices\CicoCrudService;
use App\Services\TimeTracking\CrudServices\TimecardCrudService;
use Illuminate\Support\Facades\Validator;
use stdClass;

class TimeCardController extends Controller
{
    use TimecardDto;

    public function __construct(
        private TimecardCrudService             $timeCardCrudService,
        private RestEmployeeCrudService         $restEmployeeCrudService,
        private CicoCrudService                 $cicoCrudService,
        private GetTimecardsFilterService       $getTimecardsFilterService,
        private EmployeeLeaveRequestCrudService $employeeLeaveRequestCrudService,
        private CopyTimecardsAndRestsService    $copyTimecardsAndRestsService
    )
    {
    }

    public function assignEmployee(array $data, stdClass &$output): void
    {
        $validator = Validator::make($data, $this->getAssignEmployeeRules($data));
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $this->timeCardCrudService->assignEmployee($validator->validate(), $output);

    }

    public function addAbsentTimecard(array $data, stdClass &$output): void
    {
        $validator = Validator::make($data, $this->getAddAbsentTimecardRule($data));
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $this->timeCardCrudService->addAbsentTimecard($validator->validate(), $output);
    }

    public function update(array $data, stdClass &$output): void
    {
        $validator = Validator::make($data, $this->getUpdateRules($data));
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $this->timeCardCrudService->updateTimeCard($validator->validate(), $output);
    }

    public function getTypes(array $data, stdClass &$output): void
    {
        $rules = [
            'without_mission_timecard_type' => 'bool',
        ];
        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $this->timeCardCrudService->getTimeCardTypesByCompanyId($data, $output);
    }

    public function delete(array $data, stdClass &$output): void
    {
        $validator = Validator::make($data, $this->getDeleteRules());
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $this->timeCardCrudService->deleteTimeCard($validator->validate(), $output);
    }

    public function getTimeCardsForWorker(array $request, stdClass &$output): void
    {
        $rules = [
            'start_date' => 'required|date',
            'end_date' => 'required|date',
            'branch_id' => 'required|numeric',
        ];

        if (!isset($request['branch_id']) && !is_null(config('globals.branchId'))) {
            $request['branch_id'] = config('globals.branchId');
        }
        $validator = Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();
        $request['employee_id'] = auth()->user()->employee_id;

        $this->timeCardCrudService->getTimeCardsForWorker($request, $output);
        $this->cicoCrudService->getLastCico($request, $output);
        $this->getRestDays($request, $output);
    }

    private function isValidFilters(array &$request, stdClass &$output): bool
    {
        $rules = $this->getFilterRules();

        if (!isset($request['page_size'])) {
            $request['page_size'] = config('globals.MAX_PAGE_SIZE');
        }

        if (!isset($request['order_by'])) {
            $request['order_by'] = 'from';
        }

        if (!isset($request['order_by_type'])) {
            $request['order_by_type'] = 'asc';
        }

        $validator = Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return false;
        }

        $request = $validator->validate();

        return true;
    }

    public function getByFilters(array $request, stdClass &$output): void
    {
        if (!$this->isValidFilters($request, $output)) {
            return;
        }
        $this->getTimecardsFilterService->perform($request, $output);
        $timecards = $output->verified_attendance;
        $output->verified_attendance = (new VerifiedAttendanceCollection($timecards))->response()->getData();

    }

    private function getRestDays($request, stdClass &$output): void
    {
        $restDaysRequest = [
            'employee_id' => $request['employee_id'],
            'from' => $request['start_date'],
            'to' => $request['end_date'],
        ];
        $this->employeeLeaveRequestCrudService->getRestDaysForWorker($restDaysRequest, $output);
    }

    public function copy($request, stdClass &$output): void
    {
        $rules = [
            'copy_from' => 'required|date_format:Y-m-d',
            'copy_to' => 'required|date_format:Y-m-d',
            'paste_from' => 'required|date_format:Y-m-d',
            'paste_to' => 'required|date_format:Y-m-d',
            'branch_id' => 'required|numeric',
            'copy_timecards' => 'required|boolean',
        ];

        $validator = Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $this->copyTimecardsAndRestsService->perform($request, $output);
    }

    public function workerGetByFilters(array $request, stdClass &$output): void
    {
        if (!$this->isValidFilters($request, $output)) {
            return;
        }

        $request['employee_id'] = auth()->user()->employee_id;

        $this->getTimecardsFilterService->perform($request, $output);
    }

    public function getById(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|numeric';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->timeCardCrudService->getById($request, $output);
    }


    #TODO this is the second version of the normal get attendance by filters
    public function getAttendances(array $request, stdClass &$output): void
    {
        if (!$this->isValidFilters($request, $output)) {
            return;
        }
        $this->getTimecardsFilterService->perform($request, $output);
        $timecards = $output->verified_attendance;
        $output->verified_attendance = (GetVerifiedAttendanceForWorkerAppResource::collection($timecards))->response()->getData();

    }
}
