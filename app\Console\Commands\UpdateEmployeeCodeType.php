<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\SystemSetting;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateEmployeeCodeType extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-employee-code-type';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();

        try {
            $companies = Company::all();
            $currentDate = Carbon::now()->toDateString();

            foreach ($companies as $company) {
                $setting = SystemSetting::where('company_id', $company->id)
                    ->where('key', 'employee_code_type')
                    ->first();

                if ($setting) {
                    $setting->value = 'manual';
                    $setting->as_of_date = $currentDate;

                    if (! $setting->save()) {
                        throw new \Exception("Failed to update employee code type for company ID: {$company->id}");
                    }
                } else {
                    $newSetting = new SystemSetting([
                        'company_id' => $company->id,
                        'key' => 'employee_code_type',
                        'value' => 'manual',
                        'as_of_date' => $currentDate,
                    ]);

                    if (! $newSetting->save()) {
                        throw new \Exception("Failed to create employee code type for company ID: {$company->id}");
                    }
                }
            }

            DB::commit();
            $this->info('Employee code type has been successfully changed to manual for all companies.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to change employee code type to manual: '.$e->getMessage());
            $this->error('An error occurred while changing employee code type to manual.');
        }
    }
}
