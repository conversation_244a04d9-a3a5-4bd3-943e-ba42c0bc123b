<?php

namespace App\Http\Controllers\Payroll;

use App\Exceptions\UnprocessableException;
use App\Exports\PayrollExport;
use App\Exports\UnaccountedDaysMultisheetExport;
use App\Exports\VariableSalaryComponentExport;
use App\FeatureToggles\Unleash;
use App\Http\Controllers\NewController;
use App\Http\Requests\CalculateGrossSalaryRequest;
use App\Http\Requests\CreateServiceChargeRequest;
use App\Http\Requests\ExportPayrollRequest;
use App\Http\Requests\GetPayslipDetailsRequest;
use App\Http\Requests\PayrollRequest;
use App\Http\Requests\salaryCalculatorRequest;
use App\Http\Requests\V1\EmployeePayrollDetailsRequest;
use App\Http\Requests\V1\GetUnaccountedDaysRequest;
use App\Http\Resources\Payroll\UnaccountedDaysCollection;
use App\Http\Resources\PayrollCalculationCollection;
use App\Http\Resources\PayrollSummaryResource;
use App\Http\Resources\V1\Payroll\EmployeePayrollCalculationDetailsResource;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Services\CompanySetup\EmployeesService;
use App\Services\PayrollSetup\EmployeePayrollService;
use App\Services\PayrollSetup\MonthlySummariesService;
use App\Services\PayrollSetup\SystemSettingsService;
use App\Services\V1\LeaveManagement\EmployeeLeavesService;
use App\Services\V1\Loans\LoanService;
use App\Services\V1\Loans\SalaryAdvanceService;
use App\Services\V1\Payroll\ConvertGrossToNetService;
use App\Services\V1\Payroll\ConvertNetToGrossService;
use App\Services\V1\Probation\ProbationService;
use App\Traits\GetLastDraftedPayRollOrCreate;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Util\HttpStatusCodeUtil;
use App\Util\ScopeUtil;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class PayrollController extends NewController
{
    use GetLastDraftedPayRollOrCreate, PayrollHelper;

    private $unleash;

    public function __construct(private EmployeePayrollService $employeePayrollService,
        private MonthlySummariesService $monthlySummariesService,
        private EmployeesService $employeesService,
        private SystemSettingsService $systemSettingsService,
        private PayrollsRepository $payrollsRepository,
        private SystemSettingRepository $systemSettingRepository,
        private LoanService $loansService,
        private SalaryAdvanceService $salaryAdvanceService,
        private EmployeeLeavesService $employeeLeavesService,
        protected ProbationService $probationService
    ) {
        parent::__construct($employeePayrollService);
    }

    public function calculatePayroll(PayrollRequest $request)
    {
        $data = $request->all();

        DB::beginTransaction();
        try {
            [$employeesPayroll, $payrollStatus, $isPublished] = $this->service->calculatePayroll($data);
            DB::commit();
        } catch (Exception $e) {

            Log::info($e);
            DB::rollBack();

            throw $e;
        }

        return getResponseStructure(['data' => ['payroll_calculation' => new PayrollCalculationCollection($employeesPayroll), 'payroll_status' => $payrollStatus, 'is_published' => $isPublished]], HttpStatusCodeUtil::OK);
    }

    public function getEmployeePayrollCalculation(EmployeePayrollDetailsRequest $request)
    {
        $data = $request->all();
        $employeePayrollCalculationDetails = $this->service->getEmployeePayrollCalculation($data);

        return getResponseStructure(['data' => new EmployeePayrollCalculationDetailsResource($employeePayrollCalculationDetails)], HttpStatusCodeUtil::OK);
    }

    public function getPayroll(PayrollRequest $request)
    {
        try {
            $data = $request->validated();
            [$employeesPayroll, $payrollStatus, $isPublished] = $this->service->getPayroll($data);

            return getResponseStructure(['data' => ['payroll_calculation' => new PayrollCalculationCollection($employeesPayroll), 'payroll_status' => $payrollStatus, 'is_published' => $isPublished]], HttpStatusCodeUtil::OK);
        } catch (Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function getPendingRequests(PayrollRequest $request)
    {
        $data = $request->all();
        $allPendingRequests = $this->monthlySummariesService->getPendingRequests($data);

        return getResponseStructure(['data' => $allPendingRequests], HttpStatusCodeUtil::OK);
    }

    public function getUnAccountedDaysInBranch(GetUnaccountedDaysRequest $request)
    {
        $data = $request->validated();

        $monthlyClosingDay = $this->getMonthlyClosingDay()
            ?? throw new UnprocessableException('Monthly closing day not set');
        $response['monthly_closing_day'] = (int) $monthlyClosingDay;

        $currentPayroll = $this->getCurrentPayroll();

        if (! isset($data['month'])) {
            $data['month'] = isset($data['payroll_to_date']) ? Carbon::parse($data['payroll_to_date'])->month
                : $currentPayroll['month'];
            $data['year'] = isset($data['payroll_to_date']) ? Carbon::parse($data['payroll_to_date'])->year
                : $currentPayroll['year'];
        }

        if (! isset($data['payroll_from_date'])) {
            $data['payroll_to_date'] = date('Y-m-d', strtotime(date($data['year'].'-'.$data['month'].'-'.$monthlyClosingDay)));
            $data['payroll_from_date'] = $this->getPayrollMonthStartBasedOnMonthEnd($data['payroll_to_date']);
        }

        $data['payroll_from_date'] = min(now()->toDateString(), $data['payroll_from_date']);
        $data['payroll_to_date'] = min(now()->toDateString(), $data['payroll_to_date']);

        $unaccountedDays = new UnaccountedDaysCollection($this->monthlySummariesService->allUnaccountedDays($data));
        $response['unaccounted_days'] = $unaccountedDays;
        $response['month'] = $data['month'];
        $response['date'] = $data['payroll_to_date'];

        return getResponseStructure(['data' => $response], HttpStatusCodeUtil::OK);
    }

    public function exportUnAccountedDays(GetUnaccountedDaysRequest $request)
    {
        $data = $request->validated();
        $unaccountedDays = $this->monthlySummariesService->allUnaccountedDays($data);
        $groupedUnaccountedDays = [];
        // Iterate through the unaccounted days
        foreach ($unaccountedDays as $unaccountedDay) {
            $branchName = $unaccountedDay->branch?->name;
            // Check if the branch name exists as a key in the grouped array
            if (! isset($groupedUnaccountedDays[$branchName])) {
                // If not, initialize it as an empty array
                $groupedUnaccountedDays[$branchName] = [];
            }

            // Push the unaccounted day into the array corresponding to its branch name
            $groupedUnaccountedDays[$branchName][] = $unaccountedDay;
        }

        return Excel::download(new UnaccountedDaysMultisheetExport($groupedUnaccountedDays), 'unaccounted_days.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function getPendingRequestsAndUnaccountedDaysCounter(GetUnaccountedDaysRequest $request)
    {
        $data = $request->validated();

        $allPendingRequestsCounter = $this->monthlySummariesService->getPendingRequestsCounter($data);
        $allUnaccountedDaysCounter = $this->monthlySummariesService->getUnaccountedDaysCounter($data);

        // //////////////////////////
        if (env('APP_ENV') == 'local') { // TODO remove this .. just for testing // pending and unaccounted days no dev  & staging will always be 0
            $allPendingRequestsCounter = 0;
            $allUnaccountedDaysCounter = 0;
        }
        // //////////////////////////

        return getResponseStructure(['data' => ['pending_requests_counter' => $allPendingRequestsCounter,
            'unaccounted_days_counter' => $allUnaccountedDaysCounter]], HttpStatusCodeUtil::OK);
    }

    public function getAllPendingDataCounters(GetUnaccountedDaysRequest $request)
    {
        $data = $request->validated();
        $this->unleash = app(Unleash::class);
        $allPendingRequestsCounter = $this->monthlySummariesService->getPendingRequestsCounter($data);
        $allPendingLoansAndSalaryAdvancesCounter = $this->monthlySummariesService->getPendingLoansAndSalaryAdvancesCounter($data);
        $allPendingTerminationRequestsCounter = $this->monthlySummariesService->getPendingTerminationRequestsCounter($data);
        $allUnaccountedDaysCounter = $this->monthlySummariesService->getUnaccountedDaysCounter($data);
        $missingSalaryInfoCount = $this->employeesService->getEmployeesMissingSalaryInfoCount($data);
        $readyToDisburseLoans = $this->loansService->getReadyToDisburseLoansCount();
        $readyToDisburseSalaryAdvances = $this->salaryAdvanceService->getReadyToDisburseSalaryAdvancesCount();
        $terminatedEmployeesWithOutstandingLoans = $this->employeesService->getCountOfTerminatedEmployeesWithOutstandingLoans();

        $terminatedEmployeesWithRemainingLeaveBalance = $this->employeeLeavesService->countTerminatedEmployeesWithRemainingLeaveBalanceForTheLastDraftedPayroll($data['payroll_from_date'], $data['payroll_to_date']);
        $isProbationEnabled = $this->unleash->isProbationEnabled();
        $pendingProbationRequests = $isProbationEnabled
            ? $this->probationService->getPendingProbationRequestsCount($data)
            : null;

        // Prepare the response data
        $responseData = [
            'pending_requests_count' => $allPendingRequestsCounter,
            'unaccounted_days_count' => $allUnaccountedDaysCounter,
            'missing_salary_info_count' => $missingSalaryInfoCount,
            'ready_to_disburse_loans_or_salary_advances_count' => $readyToDisburseLoans + $readyToDisburseSalaryAdvances,
            'terminated_employees_with_outstanding_loans_or_salary_advances_count' => $terminatedEmployeesWithOutstandingLoans,
            'terminated_employees_with_remaining_leave_balance_count' => $terminatedEmployeesWithRemainingLeaveBalance,
            'loan_or_salary_advance_requests_count' => $allPendingLoansAndSalaryAdvancesCounter,
            'terminated_employees_requests_count' => $allPendingTerminationRequestsCounter,
        ];

        // Conditionally include the probation count if enabled
        if ($isProbationEnabled) {
            $responseData['pending_probation_requests_count'] = $pendingProbationRequests;
        }

        return getResponseStructure(['data' => $responseData], HttpStatusCodeUtil::OK);
    }

    public function finalizePayroll(PayrollRequest $request)
    {
        $data = $request->all();

        $data['is_final'] = true;

        DB::beginTransaction();
        try {
            $userScope = config('globals.scope_key');
            if ($userScope != ScopeUtil::COMPANY_SCOPE) {
                throw new UnprocessableException('You are not authorized to finalize payroll');
            }
            $payrollSummary = $this->service->calculatePayroll($data);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();

            return getErrorResponseStructure(HttpStatusCodeUtil::INTERNAL_SERVER_ERROR, $e->getMessage());
        }

        return getResponseStructure(['data' => new PayrollSummaryResource($payrollSummary)], HttpStatusCodeUtil::OK);
    }

    public function approvePayroll()
    {

        DB::beginTransaction();
        try {
            $userScope = config('globals.scope_key');
            if ($userScope != ScopeUtil::COMPANY_SCOPE) {
                throw new UnprocessableException('You are not authorized to finalize payroll');
            }
            $this->service->approvePayroll();
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();

            return getErrorResponseStructure(HttpStatusCodeUtil::INTERNAL_SERVER_ERROR, $e->getMessage());
        }

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);
    }

    public function getPendingDataCounters()
    {
        $missingPayrollDataCount = $this->employeesService->getEmployeesMissingSalaryInfoCount();
        $missingEmployeeDataCount = $this->employeesService->getEmployeesMissingHiringDateCount();

        return getResponseStructure(['data' => ['missing_payroll_data_count' => $missingPayrollDataCount, 'missing_employees_data_count' => $missingEmployeeDataCount]],
            HttpStatusCodeUtil::OK);
    }

    public function exportVariableSalaryComponentTemplate()
    {
        return Excel::download(new VariableSalaryComponentExport, 'salary_components_template.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function getTotalBasicSalaries(PayrollRequest $request)
    {
        $data = $request->all();
        $totalBasicSalaries = $this->service->getTotalBasicSalaries($data);

        return getResponseStructure(['data' => ['total_basic_salaries' => $totalBasicSalaries]], HttpStatusCodeUtil::OK);
    }

    public function saveServiceCharge(CreateServiceChargeRequest $request)
    {
        $data = $request->Validated();
        DB::beginTransaction();
        try {
            $this->service->saveServiceCharge($data);
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            DB::rollBack();

            return getErrorResponseStructure(HttpStatusCodeUtil::INTERNAL_SERVER_ERROR, $e->getMessage());
        }

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);

    }

    public function sendPayslip(PayrollRequest $request)
    {
        $data = $request->validated();
        try {
            $this->service->sendPayslip($data);
        } catch (Exception $e) {
            Log::info($e);

            return getErrorResponseStructure(HttpStatusCodeUtil::INTERNAL_SERVER_ERROR, $e->getMessage());
        }

        return getResponseStructure(['data' => []], HttpStatusCodeUtil::OK);

    }

    public function unaccountedDayCounterInBranches(GetUnaccountedDaysRequest $request)
    {
        $data = $request->validated();
        $allUnaccountedDays = $this->monthlySummariesService->unaccountedDayCounterInBranches($data);

        return getResponseStructure(['data' => $allUnaccountedDays], HttpStatusCodeUtil::OK);
    }

    public function getEmployeePayslips()
    {
        $employeeId = auth()->user()->employee_id;

        if (! $employeeId) {
            throw new UnprocessableException('Employee not found');
        }

        $payslips = $this->service->getEmployeePayslips($employeeId);

        return getResponseStructure(['data' => $payslips], HttpStatusCodeUtil::OK);
    }

    public function getPayslipDetails(GetPayslipDetailsRequest $request)
    {
        $data = $request->validated();

        $payslipDetails = $this->service->getPayslipDetails($data);

        return getResponseStructure(['data' => $payslipDetails], HttpStatusCodeUtil::OK);
    }

    public function exportPayroll(ExportPayrollRequest $request)
    {
        $data = $request->validated();

        return Excel::download(new PayrollExport($data), 'payroll.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function calculateGrossSalary(CalculateGrossSalaryRequest $request)
    {
        $data = $request->validated();
        $netSalary = Arr::get($data, 'net_salary');
        $payrollPolicy = Arr::get($data, 'tax_policy');
        if (is_null($payrollPolicy)) {
            $payrollPolicy = $this->getTaxInsurancePolicy(Arr::get($data, 'employee_id'), config('globals.company')->id);
        }

        $socialInsuranceSalary = Arr::get($data, 'social_insurance_salary');

        $convertNetToGrossService = new ConvertNetToGrossService;
        $result = $convertNetToGrossService->calculate($netSalary, $socialInsuranceSalary, $payrollPolicy);

        return getResponseStructure(['data' => ['gross_salary' => $result['grossSalary']]], HttpStatusCodeUtil::OK);

    }

    public function calculateSalaryThroughCalculator(salaryCalculatorRequest $request)
    {
        $data = $request->validated();
        $salary = Arr::get($data, 'salary');
        $type = Arr::get($data, 'type');
        $socialInsuranceSalary = Arr::get($data, 'social_insurance_salary');
        $result = [];
        if ($type == 'net') {
            $convertNetToGrossService = new ConvertNetToGrossService;
            $result = $convertNetToGrossService->calculate($salary, $socialInsuranceSalary);
        } elseif ($type == 'gross') {
            $convertGrossToNetService = new ConvertGrossToNetService;
            $result = $convertGrossToNetService->calculate($salary, $socialInsuranceSalary);
        }

        return getResponseStructure(['data' => $result], HttpStatusCodeUtil::OK);
    }
}
