<?php

namespace App\Http\Resources\V1\EmployeeProfile;

use App\FeatureToggles\Unleash;
use App\Http\Resources\BaseResource;
use App\Helpers\PhoneParserHelper;

class EmployeeInfoResource extends BaseResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $lang = (config('globals.lang') ?? 'ar');
        $titleName = ($this->checkRelationIfExists('title') and $lang == 'en') ? $this->title?->name_en : null;
        $titleName = is_null($titleName) ? $this->title?->name_ar : $titleName;

        $branchName = ($this->checkRelationIfExists('branch') and $lang == 'en') ? $this->branch?->name_en : null;
        $branchName = is_null($branchName) ? $this->branch?->name_ar : $branchName;

        $departmentName = ($this->checkRelationIfExists('title.department') and $lang == 'en') ? $this->title?->department?->name_en : null;
        $departmentName = is_null($departmentName) ? $this->title?->department?->name_ar : $departmentName;

        $joinDate = $this->checkRelationIfExists('employeeInfo') ? $this->employeeInfo->join_date : null;

        //        $terminationDate = $this->checkRelationIfExists('terminationRequest') ? $this->terminationRequest->terminate_date : null;

        $data = [
            'id' => $this->id,
            'is_trackable' => $this->is_trackable,
            'name' => [
                'en' => $this->name_en,
                'ar' => $this->name_ar,
            ],
            'employee_number' => $this->whenAttributeExists('employee_number'),
            'status' => $this->whenAttributeExists('status'),
            'profile_image' => $this->whenLoaded('profilePicture', function () {
                return $this->profilePicture->attachment_url;
            }),
            'title' => $this->checkRelationIfExists('title') ? [
                'id' => $this->title->id,
                'name' => $titleName,
                'name_localized' => [
                    'en' => $this->title->name_en,
                    'ar' => $this->title->name_ar,
                ],
                'color' => $this->title->color,
            ] : null,
            'department' => [
                'id' => $this->title?->department?->id,
                'name' => $departmentName,
                'name_localized' => [
                    'en' => $this->title?->department?->name_en,
                    'ar' => $this->title?->department?->name_ar,
                ],
            ],
            'branch' => $this->checkRelationIfExists('branch') ? [
                'id' => $this->branch->id,
                'name' => $branchName,
                'name_localized' => [
                    'en' => $this->branch->name_en,
                    'ar' => $this->branch->name_ar,
                ],
                'from' => $this->getCurrentBranchFromDate(),

            ] : null,
            'scope' => [
                'name' => $this->scope,
                'branches' => $this->checkRelationIfExists('branches') ? $this->branches->map(function ($branch) use ($lang) {
                    return [
                        'id' => $branch->id,
                        'name' => $lang == 'en' ? $branch->name_en : $branch->name_ar,
                        'name_localized' => [
                            'en' => $branch->name_en,
                            'ar' => $branch->name_ar,
                        ],
                    ];
                })->toArray() : [],
                'departments' => $this->checkRelationIfExists('managedDepartments') ? $this->managedDepartments->map(function ($department) use ($lang) {
                    return [
                        'id' => $department->id,
                        'name' => $lang == 'en' ? $department->name_en : $department->name_ar,
                        'name_localized' => [
                            'en' => $department->name_en,
                            'ar' => $department->name_ar,
                        ],
                    ];
                })->toArray() : [],
                'sub_departments' => $this->checkRelationIfExists('managedSubDepartments') ? $this->managedSubDepartments->map(function ($subDepartment) use ($lang) {
                    return [
                        'id' => $subDepartment->id,
                        'name' => $lang == 'en' ? $subDepartment->name_en : $subDepartment->name_ar,
                        'name_localized' => [
                            'en' => $subDepartment->name_en,
                            'ar' => $subDepartment->name_ar,
                        ],
                    ];
                })->toArray() : [],
            ],
            'title_history' => $this->title_history,
            'info' => $this->checkRelationIfExists('employeeInfo') ? [
                'national_id' => $this->whenAttributeExists('national_id'),
                'birth_date' => $this->employeeInfo->birth_date,
                'years_of_experience' => $this->employeeInfo->number_of_years_of_experience ?? null,
                'phone' => $this->whenAttributeExists('phone'),
                'parsed_phone' => PhoneParserHelper::parsePhone($this->phone),
                'employee_number' => $this->whenAttributeExists('employee_number'),
                'revoke_date' => $this->whenAttributeExists('revoke_date'),
                'gender' => $this->employeeInfo->gender,
                'number_kids' => $this->employeeInfo->number_kids,
                'address' => $this->employeeInfo->address,
                'termination_date' => $this->employeeInfo->termination_date,
                'termination_reason' => $this->getLatestTerminateReason() ?? null,
                'termination_type' => $this->getLatestTerminateType() ?? null,
                'join_date' => $joinDate,
                'status' => $this->whenAttributeExists('status'),
                'email' => $this->employeeInfo->email,
                'passport_number' => $this->employeeInfo->passport_number,
                'secondary_phone' => $this->employeeInfo->secondary_phone,
                'parsed_secondary_phone' => PhoneParserHelper::parsePhone($this->employeeInfo->secondary_phone),
                'nationality' => $this->employeeInfo->nationality,
                'age' => $this->age,
                'military_status' => $this->employeeInfo->military_status,
                'notes' => $this->employeeInfo->notes,
                'training_certification_status' => $this->employeeInfo->training_certification_status ?? null,
            ] : null,
            'previous_branches' => $this->checkRelationIfExists('employeeChanges') ? $this->getPreviousBranches() : null,
            'termination_web_button' => $this->termination_web_button ?? false,
            'rehiring_web_button' => $this->rehiring_web_button ?? false,
            'cancel_web_button' => $this->cancel_web_button ?? false,
            'is_egyptian' => $this->is_egyptian ?? true,
        ];

        $unleash = app(Unleash::class);
        if ($unleash->isEnhancedEmployeeProfileEnabled()) {
            $data['place_of_birth'] = $this->employeeInfo->place_of_birth ?? null;
            $data['religion'] = $this->employeeInfo->religion ?? null;
            $data['other_religion'] = $this->employeeInfo->other_religion ?? null;
            $data['marital_status'] = $this->employeeInfo->marital_status ?? null;
            $data['personal_email'] = $this->employeeInfo->personal_email ?? null;
            $data['can_reset_device'] = $this->can_reset_device ?? false;
            if ($this->relationLoaded('emergencyContacts') && $this->emergencyContacts && $this->emergencyContacts->count() > 0) {
                $data['emergency_contacts'] = EmployeeEmergencyContactResource::collection($this->emergencyContacts);
            } else {
                $data['emergency_contacts'] = [];

            }
            $data['employment_type'] = $this->employeeInfo->employment_type ?? null;
            $data['length_of_service'] = $this->employeeInfo->getLengthOfServiceAttribute() ?? null;
            if ($this->relationLoaded('contracts') && $this->contracts && $this->contracts->count() > 0) {
                $data['contracts'] = EmployeeContractResource::collection($this->contracts);
            } else {
                $data['contracts'] = [];
            }
            if ($this->relationLoaded('education') && $this->education && $this->education->count() > 0) {
                $data['education'] = EmployeeEducationResource::collection($this->education);
            } else {
                $data['education'] = [];
            }
            $data['scope']['employees'] = new EmployeeProfileCollection($this->managedEmployees);
            $data['scope']['count'] = $this->managedEmployees->count();
        }
        return $data;
    }

    public function getCurrentBranchFromDate()
    {
        $employeeChanges = $this->employeeChanges->toArray();
        $mostRecentDate = $this->employeeInfo?->join_date;

        foreach ($employeeChanges as $change) {

            $from = $change['as_of_date'];

            if ($from > $mostRecentDate) {
                $mostRecentDate = $from;
            }
        }

        return $mostRecentDate;
    }

    public function getPreviousBranches()
    {
        $lang = (config('globals.lang') ?? 'ar');
        $employeeChanges = $this->employeeChanges->toArray();
        $branches = [];
        $previousTo = null;
        for ($i = 0; $i < count($employeeChanges); $i++) {

            $from = $i == 0 ? $this->employeeInfo?->join_date : $previousTo;
            $to = $employeeChanges[$i]['as_of_date'];
            $previousTo = $to;

            $branchName = (array_key_exists('from_branch', $employeeChanges[$i]) && $lang == 'en') ? $employeeChanges[$i]['from_branch']['name_en'] : null;
            $branchName = is_null($branchName) ? $employeeChanges[$i]['from_branch']['name_ar'] : $branchName;

            $branches[] = [
                'name_localized' => [
                    'en' => array_key_exists('from_branch', $employeeChanges[$i]) ? $employeeChanges[$i]['from_branch']['name_en'] : null,
                    'ar' => array_key_exists('from_branch', $employeeChanges[$i]) ? $employeeChanges[$i]['from_branch']['name_ar'] : null,
                ],
                'name' => $branchName,
                'from' => $from,
                'to' => $to,
            ];

        }

        return $branches;
    }
}
