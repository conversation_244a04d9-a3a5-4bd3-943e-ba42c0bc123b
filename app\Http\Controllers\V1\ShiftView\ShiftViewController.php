<?php

namespace App\Http\Controllers\V1\ShiftView;

use App\Http\Controllers\NewController;
use App\Http\Requests\GetShiftEmployeesRequest;
use App\Http\Requests\V1\AddCustomShiftRequest;
use App\Http\Requests\V1\AddEmployeesToRestDayRequest;
use App\Http\Requests\V1\AddEmployeesToShiftRequest;
use App\Http\Requests\V1\AddSavedLocationRequest;
use App\Http\Requests\V1\GetSavedLocationRequest;
use App\Http\Requests\V1\GetShiftViewRequest;
use App\Http\Requests\V1\RemoveCustomShiftOnDateRequest;
use App\Http\Requests\V1\RemoveEmployeeFromCustomShiftRequest;
use App\Http\Requests\V1\RemoveEmployeeFromShiftRequest;
use App\Http\Requests\V1\RemoveRestDayOnDateRequest;
use App\Http\Requests\V1\RemoveRestDayOnEmployeeRequest;
use App\Http\Requests\V1\RemoveShiftOnDateRequest;
use App\Http\Resources\ShiftViewScheduleResource;
use App\Http\Resources\V1\GetEmployeeShiftResource;
use App\Http\Resources\V1\GetSavedLocationsResource;
use App\Services\V1\ShiftView\ShiftViewService;
use App\Traits\RolesAndPermissionsTrait;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class ShiftViewController extends NewController
{
    use RolesAndPermissionsTrait;

    public function __construct(
        protected ShiftViewService $shiftViewService,
    ) {}

    public function getShiftView(GetShiftViewRequest $request)
    {
        $shifts = $this->shiftViewService->getShiftView($request);

        return getResponseStructure([
            'data' => new ShiftViewScheduleResource($shifts), 'show_public_holidays' => $this->getIsEgyptianFlag()],
            HttpStatusCodeUtil::OK);
    }

    public function getShiftEmployees(GetShiftEmployeesRequest $request)
    {
        $employees = $this->shiftViewService->getEmployeesWithValidations($request);

        return getResponseStructure([
            'data' => GetEmployeeShiftResource::collection($employees),
        ], HttpStatusCodeUtil::OK);
    }

    public function addEmployeesToShift(AddEmployeesToShiftRequest $request)
    {
        DB::beginTransaction();
        try {
            $timecards = $this->shiftViewService->addEmployees($request);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);

            throw $e;
        }

        return getResponseStructure([
            'data' => $timecards],
            HttpStatusCodeUtil::OK,
            'Employees added to shift successfully');
    }

    public function removeEmployeesFromShift(RemoveEmployeeFromShiftRequest $request)
    {
        DB::beginTransaction();
        try {
            $this->shiftViewService->removeEmployeesFromShift($request);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);

            throw $e;
        }

        return getResponseStructure([
            'data' => 'Employees removed from shift successfully'],
            HttpStatusCodeUtil::OK,
        );
    }

    public function removeShiftOnDate(RemoveShiftOnDateRequest $request)
    {
        DB::beginTransaction();
        try {
            $this->shiftViewService->removeShift($request);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);

            throw $e;
        }

        return getResponseStructure([
            'data' => 'Shift removed successfully'],
            HttpStatusCodeUtil::OK,
        );
    }

    public function copyShift(AddEmployeesToShiftRequest $request)
    {
        DB::beginTransaction();
        try {
            $uncoppiedEmployees = $this->shiftViewService->copyShift($request);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);

            throw $e;
        }

        return getResponseStructure([
            'data' => [
                'uncoppied_employees' => $uncoppiedEmployees,
            ]],
            HttpStatusCodeUtil::OK,
        );
    }

    public function addEmployeesToRestday(AddEmployeesToRestDayRequest $request)
    {
        DB::beginTransaction();
        try {
            $restdays = $this->shiftViewService->addEmployeesToRestday($request);
            if (isset($restdays->Error)) {
                throw new UnprocessableEntityHttpException($restdays->Error[0]);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);

            throw $e;
        }

        return getResponseStructure([
            'data' => $restdays,
        ],
            HttpStatusCodeUtil::OK);
    }

    public function addSavedLocation(AddSavedLocationRequest $request)
    {
        DB::beginTransaction();
        try {
            $location = $this->shiftViewService->addSavedLocation($request->validated());
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);

            throw $e;
        }

        return getResponseStructure([
            'data' => $location],
            HttpStatusCodeUtil::OK,
            'Location added successfully'
        );
    }

    public function getSavedLocationsByStatus(GetSavedLocationRequest $request)
    {
        $locations = $this->shiftViewService->getSavedLocationsByStatus($request);

        return getResponseStructure([
            'data' => GetSavedLocationsResource::collection($locations)],
            HttpStatusCodeUtil::OK,
        );
    }

    public function changeSavedLocationStatus(int $id)
    {
        $this->shiftViewService->changeSavedLocationStatus($id);

        return getResponseStructure([
            'data' => 'Location status changed successfully'],
            HttpStatusCodeUtil::OK,
        );
    }

    public function createCustomShift(AddCustomShiftRequest $request)
    {
        DB::beginTransaction();
        try {
            $timecards = $this->shiftViewService->addEmployees($request);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);

            throw $e;
        }

        return getResponseStructure([
            'data' => $timecards],
            HttpStatusCodeUtil::OK,
            'Employees added to shift successfully');
    }

    public function removeRestDayOnEmployee(RemoveRestDayOnEmployeeRequest $request)
    {
        DB::beginTransaction();
        try {
            $this->shiftViewService->removeRestDayOnEmployee($request);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);

            throw $e;
        }

        return getResponseStructure([
            'data' => 'Employees removed from rest day successfully',
        ],
            HttpStatusCodeUtil::OK);
    }

    public function removeRestDayOnDate(RemoveRestDayOnDateRequest $request)
    {
        DB::beginTransaction();
        try {
            $this->shiftViewService->removeRestDayForEmployees($request);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);

            throw $e;
        }

        return getResponseStructure([
            'data' => 'Employees removed from rest day successfully',
        ],
            HttpStatusCodeUtil::OK);
    }

    public function removeEmployeeFromCustomShift(RemoveEmployeeFromCustomShiftRequest $request)
    {
        DB::beginTransaction();
        try {
            $this->shiftViewService->removeEmployeeFromCustomShift($request);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);
            throw $e;
        }

        return getResponseStructure([
            'data' => 'Employee removed from custom shift successfully',
        ],
            HttpStatusCodeUtil::OK);
    }

    public function removeCustomShiftOnDate(RemoveCustomShiftOnDateRequest $request)
    {
        DB::beginTransaction();
        try {
            $this->shiftViewService->removeCustomShiftOnDate($request);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e);
            throw $e;
        }

        return getResponseStructure([
            'data' => 'Employees removed from custom shift successfully',
        ],
            HttpStatusCodeUtil::OK);
    }
}
