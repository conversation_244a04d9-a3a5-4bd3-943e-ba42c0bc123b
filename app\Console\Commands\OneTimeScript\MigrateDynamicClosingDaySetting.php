<?php

namespace App\Console\Commands\OneTimeScript;

use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveBalancesRepository;
use App\Util\BasicInfoUtil;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateDynamicClosingDaySetting extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    public function __construct(
        private SystemSettingRepository $systemSettingRepository,
        private EmployeeLeaveBalancesRepository $employeeLeaveBalancesRepository
    ) {
        parent::__construct();
    }

    protected $signature = 'migrate:dynamic-closing-day {arrayParam}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $arrayParam = $this->argument('arrayParam');
            $systemSettingRepository =
            $array = $arrayParam ? json_decode($arrayParam, true) : [];
            foreach ($array as $num) {
                $this->addDynamicClosingDaySetting($num);
                $this->fixBalancePeriods($num);
            }
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }

    private function addDynamicClosingDaySetting($companyId)
    {
        $dynamicClosingDaySettingRequest =
            [
                'as_of_date' => date('Y-m-d'),
                'key' => BasicInfoUtil::APPLY_DYNAMIC_CLOSING_DAY,
                'value' => 1,
                'company_id' => $companyId,
            ];
        $this->systemSettingRepository->add($dynamicClosingDaySettingRequest);
    }

    private function fixBalancePeriods($companyId)
    {
        $balances = $this->employeeLeaveBalancesRepository->getBalanceCalculatedMonthlyForLeaveType($companyId);
        foreach ($balances as $balance) {
            $end = Carbon::parse($balance->end)->endOfMonth()->endOfDay()->toDateTimeString();
            $start = Carbon::parse($end)->startOfMonth()->startOfDay()->toDateTimeString();
            $balance->update(['start' => $start, 'end' => $end]);
        }
    }
}
