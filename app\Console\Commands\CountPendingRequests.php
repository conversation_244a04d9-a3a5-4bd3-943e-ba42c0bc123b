<?php

namespace App\Console\Commands;

use App\Models\AttendanceOvertime;
use App\Models\EmployeeLeaveRequest;
use App\Models\Loan;
use App\Models\MissionRequest;
use App\Models\Penalty;
use App\Models\SalaryAdvance;
use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use App\Traits\V1\NotificationRedirection;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CountPendingRequests extends Command
{
    use NotificationRedirection;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:count-pending-requests 
        {type : The request type to update (attendance_overtime, employee_leave_request, penalty, mission)} 
        {date : The starting date to filter requests (format: Y-m-d)}
        {company_id? : Optional company ID to filter requests (except for leaves)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update status of pending requests based on their approval cycles';

    public $is_completed = false;

    public $finalStatus = 'pending';

    public $requestWorkflowApprovals;

    /**
     * Map request types to their corresponding models
     */
    protected function getModelClass(): string
    {
        return match ($this->argument('type')) {
            'attendance_overtime' => AttendanceOvertime::class,
            'employee_leave_request' => EmployeeLeaveRequest::class,
            'penalty' => Penalty::class,
            'mission' => MissionRequest::class,
            'loan' => Loan::class,
            'salary_advance' => SalaryAdvance::class,
            default => throw new \InvalidArgumentException('Invalid request type'),
        };
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting pending requests fix process...');
        $this->line('----------------------------------------');

        try {
            $type = $this->argument('type');
            $date = $this->argument('date');
            $companyId = $this->argument('company_id');
            $modelClass = $this->getModelClass();

            if ($type === 'employee_leave_request' && $companyId) {
                throw new \InvalidArgumentException('Company ID cannot be used with employee_leave_request');
            }

            $modelClass::where('created_at', '>=', $date)->where('status', 'pending')->get();

            $query = $modelClass::where('created_at', '>=', $date)->where('status', 'pending');

            if ($companyId && $type !== 'employee_leave_request') {
                $query->where('company_id', $companyId);
            }

            $requests = $query->get();

            DB::beginTransaction();
            $pendingCounter = 0;
            $missingCounter = 0;

            foreach ($requests as $model) {
                $this->is_completed = false;

                if (! isset($model->employeeRequest)) {
                    $this->info('👤 related employee request not found');
                    Log::info('model with missing employee request model_id:'.$model->id);
                    $missingCounter++;

                    continue;
                }

                $request = [
                    'requestable_id' => $model->id,
                    'requestable_type' => $type,
                ];

                //  $isSuperAdmin = ! empty($model->employeeRequest->decider_admin_id);
                $isSuperAdmin = false;
                $this->checkApprovalCycleStatus($request, $isSuperAdmin);
                if (! $this->is_completed) {
                    $this->info('still pending');
                    $pendingCounter++;

                    continue;
                }
                $this->getFinalStatus($model);
                $this->info('finalStatus: '.$this->finalStatus);
                $this->info('request model id: '.$model->id);

                if ($type == 'loan' || $type == 'salary_advance') {
                    // $this->updateLoansRequestStatus($model);

                } else {
                    // $this->updateRequestStatus($model);
                }
                //  $this->updateEmployeeRequestStatus($model->employeeRequest);

            }

            DB::commit();
            $this->info("✅ total {$requests->count()} requests");
            $total = $requests->count() - $pendingCounter - $missingCounter;
            $this->info("✅ need to fix {$total} requests");
            $this->info("✅ pending {$pendingCounter} requests");
            $this->info("✅ missing {$missingCounter} requests");

        } catch (\Exception $e) {
            $this->error("\n❌ Error encountered: ".$e->getMessage());
            $this->warn('⏪ Rolling back database changes...');
            DB::rollBack();

            Log::error('FixPendingRequests failed: '.$e->getMessage());

            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    private function checkApprovalCycleStatus($data, $isSuperAdmin = false)
    {

        if ($isSuperAdmin) {
            $this->is_completed = true;
            //  $this->info('superAdmin');

        } else {
            //   $this->info('not superAdmin');

            $workflowApprovalCycleRepository = new WorkflowApprovalCycleRepository;
            $approvalCycle = $workflowApprovalCycleRepository->getWorkflowApprovalCycle($data);
            // $this->info('cycle'.json_encode($approvalCycle));

            if (empty($approvalCycle)) {
                $this->info('No approval cycle found for request');
                $this->info('No approval cycle for requestable_id: '.$data['requestable_id'].', type: '.$data['requestable_type']);
                $this->is_completed = false;

                return;
            }

            $this->requestWorkflowApprovals = $this->getApprovals($approvalCycle);
            $this->checkIsCompleted();
        }
    }

    private function getApprovals($approvalCycle)
    {
        $approvals = [
            'then' => [],
            'or' => [],
        ];

        foreach ($approvalCycle as $approval) {
            if ($approval->operator == 'then') {
                $approvals['then'][] = $approval->status;
            } elseif ($approval->operator == 'or') {
                $approvals['or'][] = $approval->status;
            }
        }

        return $approvals;
    }

    private function checkIsCompleted()
    {
        $orExists = count($this->requestWorkflowApprovals['or']) > 0;
        $thenExists = count($this->requestWorkflowApprovals['then']) > 0;

        if (($orExists && ! in_array(config('globals.REQUEST_STATUSES.PENDING'), $this->requestWorkflowApprovals['or'])) ||
        ($thenExists && in_array(config('globals.REQUEST_STATUSES.REJECTED'), $this->requestWorkflowApprovals['then'])) ||
        ($thenExists && in_array(config('globals.REQUEST_STATUSES.CANCELLED'), $this->requestWorkflowApprovals['then'])) ||
        ($thenExists && ! in_array(config('globals.REQUEST_STATUSES.PENDING'), $this->requestWorkflowApprovals['then']))) { // ||
            //    ($thenExists && in_array(config('globals.REQUEST_STATUSES.APPROVED'), $this->requestWorkflowApprovals['then']))) {

            $this->info('checkIsCompleted true');

            $this->is_completed = true;
        }
    }

    private function getFinalStatus($data)
    {
        $data?->refresh();

        if (empty($this->requestWorkflowApprovals['then']) && empty($this->requestWorkflowApprovals['or'])) {
            $this->info('getFinalStatus: No approval cycle data, keeping pending');
            $this->finalStatus = 'pending';

            return;
        }

        if (in_array(config('globals.REQUEST_STATUSES.CANCELLED'), $this->requestWorkflowApprovals['then'])
        || in_array(config('globals.REQUEST_STATUSES.CANCELLED'), $this->requestWorkflowApprovals['or'])) {
            $this->finalStatus = config('globals.REQUEST_STATUSES.CANCELLED');
        } elseif (count($this->requestWorkflowApprovals['or']) > 0 && ! in_array('pending', $this->requestWorkflowApprovals['or'])) {
            $this->finalStatus = $this->requestWorkflowApprovals['or'][0];
        } elseif (count($this->requestWorkflowApprovals['then']) > 0
        && in_array('rejected', $this->requestWorkflowApprovals['then'])) {
            $this->finalStatus = 'rejected';
        } else {
            $this->info('getFinalStatus: approved');

            $this->finalStatus = 'approved';
        }
    }

    private function updateRequestStatus($requestObj)
    {
        if ($this->finalStatus == 'approved') {
            $requestObj->update(['status' => 'approved']);
            // $this->redirectNotificationsAfterRequestFinalized($requestObj, 'approved');
        } elseif ($this->finalStatus == config('globals.REQUEST_STATUSES.CANCELLED')) {
            $requestObj->update(['status' => 'cancelled']);
            //  $this->redirectNotificationsAfterRequestFinalized($requestObj, 'cancelled');
        } else {
            $requestObj->update(['status' => 'rejected']);
            //   $this->redirectNotificationsAfterRequestFinalized($requestObj, 'rejected');
        }
    }

    private function updateEmployeeRequestStatus($employeeRequestObj)
    {
        if ($this->finalStatus == 'approved') {
            $employeeRequestObj->update(['status' => 'approved']);
        } elseif ($this->finalStatus == config('globals.REQUEST_STATUSES.CANCELLED')) {
            $employeeRequestObj->update(['status' => 'cancelled']);
        } else {
            $employeeRequestObj->update(['status' => 'rejected']);
        }
    }

    public function updateLoansRequestStatus($requestObj)
    {
        if ($this->finalStatus == 'ready_to_disburse') {
            $requestObj->update(['status' => 'ready_to_disburse']);
            // $this->redirectNotificationsAfterRequestFinalized($requestObj, 'approved');
            // Log::info('Loan request is approved and ready to disburse');
        } elseif ($this->finalStatus == config('globals.REQUEST_STATUSES.CANCELLED')) {
            $requestObj->update(['status' => 'cancelled']);
            $requestObj->installments()?->delete(); // delete installments if the request is loan and has installments
            //            $this->redirectNotificationsAfterRequestFinalized($requestObj, 'cancelled'); // TODO fix this notifications redirection
            //  Log::info('Loan request is cancelled');
        } else {
            $requestObj->update(['status' => 'rejected']);
            if (isset($requestObj->installments)) {
                $requestObj->installments()->each(function ($installment) {
                    $installment->delete();
                });
            }
            //  $this->redirectNotificationsAfterRequestFinalized($requestObj, 'rejected');
            Log::info('loan request is rejected');
        }
    }
}
