<?php

namespace App\DomainData;

trait TitleShiftDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'shift_id' => 'required|numeric',
            'title_id' => 'required|numeric',
            'employee_count' => 'required|integer|min:1',
            'branch_id' => 'required|numeric',
            'company_id' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeTitleShiftDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
