<?php

namespace App\Console\Commands;

use App\Jobs\BranchWeekStartDayPushNotificationJob;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class BranchWeekStartDayPushNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'branch:week:start:day';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reminder to create a shift schedule for next week The day before branch week start day';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tomorrowDayName = strtoupper(Carbon::tomorrow()->format('l')); // Get tomorrow's day name in capital letters
        $branches = Branch::where('start_day', $tomorrowDayName)->get();
        $employeesIds = $branches->flatMap(function ($branch) {
            return $branch->employees->pluck('id');
        })->toArray();
        $users = User::query()->whereIn('employee_id', $employeesIds)->get();
        $usersWithManageSchedulePermission = $users->filter(function ($user) {
            return $user->hasPermissionTo('manage_schedule', 'user-api');
        });

        dispatch(new BranchWeekStartDayPushNotificationJob($usersWithManageSchedulePermission));
    }
}
