<?php

namespace App\Console\Commands;

use App\Models\AttendanceDeduction;
use App\Models\AttendanceOvertime;
use App\Models\EmployeeLeaveRequest;
use App\Models\Penalty;
use App\Models\Workflow;
use App\Models\WorkflowApprovalCycle;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RemoveDuplication extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:approval-duplication';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $duplicateApprovals = DB::table('workflow_approval_cycle as ac')
                ->select('ac.requestable_id', 'ac.requestable_type', 'ac.order', DB::raw('count(*) as count'))
                ->groupBy('ac.requestable_id', 'ac.requestable_type', 'ac.order')
                ->having('count', '>', 1)
                ->get();

            foreach ($duplicateApprovals as $approval) {
                $workflowApprovals = WorkflowApprovalCycle::where('requestable_id', $approval->requestable_id)
                    ->where('requestable_type', $approval->requestable_type)
                    ->where('order', $approval->order)
                    ->get();
                if ($approval->requestable_type == 'attendance_deduction') {
                    $attDeduction = AttendanceDeduction::find($approval->requestable_id);
                    if ($attDeduction->status == 'waived') {
                        Workflow::where('id', $attDeduction->workflow_id)->update(['status' => 'completed']);
                    }
                    for ($i = 1; $i < count($workflowApprovals); $i++) {
                        $workflowApprovals[$i - 1]->delete();
                    }
                } elseif ($approval->requestable_type == 'employee_leave_request') {
                    $leaveRequest = EmployeeLeaveRequest::find($approval->requestable_id);
                    if ($leaveRequest->status != 'pending') {
                        Workflow::where('id', $leaveRequest->workflow_id)->update(['status' => 'completed']);
                    }
                    for ($i = 1; $i < count($workflowApprovals); $i++) {
                        $workflowApprovals[$i - 1]->delete();
                    }
                } elseif ($approval->requestable_type == 'attendance_overtime') {
                    $attOvertime = AttendanceOvertime::find($approval->requestable_id);
                    if ($attOvertime->status != 'pending') {
                        Workflow::where('id', $attOvertime->workflow_id)->update(['status' => 'completed']);
                    }
                    for ($i = 1; $i < count($workflowApprovals); $i++) {
                        $workflowApprovals[$i - 1]->delete();
                    }
                } elseif ($approval->requestable_type == 'penalty') {
                    $penalty = Penalty::find($approval->requestable_id);
                    if ($penalty->status != 'pending') {
                        Workflow::where('id', $penalty->workflow_id)->update(['status' => 'completed']);
                    }
                    for ($i = 1; $i < count($workflowApprovals); $i++) {
                        $workflowApprovals[$i - 1]->delete();
                    }
                }

            }

            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::error($e);
            DB::rollBack();
        }
    }
}
