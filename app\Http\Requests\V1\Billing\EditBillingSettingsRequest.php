<?php

namespace App\Http\Requests\V1\Billing;

use Illuminate\Foundation\Http\FormRequest;

class EditBillingSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'billing_frequency' => [
                'required',
                'integer',
                'in:3,6,12',
            ],
            'price_per_employee' => [
                'sometimes',
                'integer',
            ],
            'commercial_register_no' => [
                'min:1',
                'max:30',
            ],
            'tax_id' => [
                'min:1',
                'max:30',
            ],
        ];
    }
}
