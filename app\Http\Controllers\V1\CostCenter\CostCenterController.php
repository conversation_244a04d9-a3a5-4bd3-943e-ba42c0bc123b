<?php

namespace App\Http\Controllers\V1\CostCenter;

use App\Exceptions\UnprocessableException;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\CostCenter\AddCostCenterRequest;
use App\Http\Resources\V1\CostCenter\CostCenterResource;
use App\Services\V1\CostCenter\CostCenterService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CostCenterController extends NewController
{
    public function __construct(private CostCenterService $costCenterService) {}

    public function getCostCenters()
    {
        try {
            $data = $this->costCenterService->getCostCenters();

            return getResponseStructure(
                ['data' => CostCenterResource::collection($data)],
                HttpStatusCodeUtil::OK,
                'Cost Centers retrieved successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function getCostCenterDetails($id)
    {
        try {
            $data = $this->costCenterService->getCostCenterDetails($id);

            return getResponseStructure(
                ['data' => new CostCenterResource($data)],
                HttpStatusCodeUtil::OK,
                'Cost Centers retrieved successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function addCostCenter(AddCostCenterRequest $request)
    {
        try {
            $this->costCenterService->addCostCenter($request->validated());

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'cost center added successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function updateCostCenter(AddCostCenterRequest $request, int $id)
    {
        try {
            $this->costCenterService->updateCostCenter($id, $request->validated());

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'cost center added successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function deleteCostCenter(int $id)
    {
        try {
            $this->costCenterService->deleteCostCenter($id);

            return getResponseStructure(
                ['data' => []],
                HttpStatusCodeUtil::OK,
                'cost center deleted successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function prepare($id)// api for testing
    {
        try {
            DB::transaction(function () use ($id) {
                $this->costCenterService->prepare($id);
            });
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }
}
