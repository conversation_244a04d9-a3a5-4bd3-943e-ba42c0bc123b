<?php

namespace App\Http\Resources\V1\Payroll\Payslip;

use Illuminate\Http\Resources\Json\ResourceCollection;

class EmployeesCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'employees' => $this->collection->map(function ($employee) {
                return [
                    'id' => $employee->emp_id,
                    'name' => $employee->emp_name,
                    'first_name' => $employee->first_name,
                    'second_name' => $employee->second_name,
                    'title' => [
                        'id' => $employee->title_id,
                        'name' => $employee->title_name,
                        'color' => $employee->title_color
                    ],
                    'emp_code' => $employee->emp_code,
                    'payslip_status' => $employee->payslip_status,
                    'cash_only' => $employee->pay_method == 'cash' ? true : false,
                    'branch' => [
                        'id' => $employee->branch_id,
                        'name' => $employee->branch_name
                    ]
                ];
            }),
            'meta' => [
                'total_count' => $this->collection->count(),
            ],
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function with($request)
    {
        return [
            'success' => true,
            'message' => "Retrieved {$this->collection->count()} employees",
        ];
    }
}