<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\CompanyLeaveType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CompanyLeaveTypePolicy>
 */
class CompanyLeaveTypePolicyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
            'company_leave_type_id' => CompanyLeaveType::factory(),
            'base_balance' => $this->faker->numberBetween(1, 30),
            'unit' => 'days',
            'allow_balance_transfer' => false,
            'max_transfer_balance' => 0,
            'transferred_balance_usable_until' => 0,
            'prorated_monthly' => false,
        ];
    }

    public function withTransfer(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'allow_balance_transfer' => true,
                'max_transfer_balance' => 7,
                'transferred_balance_usable_until' => 3,
            ];
        });
    }

    public function prorated(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'prorated_monthly' => true,
            ];
        });
    }
}
