<?php

namespace App\Console\Commands;

use App\Jobs\OvertimeCalculationService;
use App\Models\AttendanceOvertime;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Log;

class FixOvertimeCalculation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:overtime-calculation {company_id} {from_date : The start date (YYYY-MM-DD)} {to_date : The end date (YYYY-MM-DD)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate overtime records from a specific date';

    public function handle()
    {
        $companyId = $this->argument('company_id');
        $fromDate = $this->argument('from_date');
        $toDate = $this->argument('to_date');

        if (! Date::hasFormat($fromDate, 'Y-m-d') || ! Date::hasFormat($toDate, 'Y-m-d')) {
            $this->error('Invalid date format. Please use YYYY-MM-DD.');

            return 1;
        }

        $query = AttendanceOvertime::query()
            ->where('company_id', $companyId)
           //  ->where('status', 'pending')
            ->where('date', '>=', $fromDate)
            ->where('date', '<=', $toDate)
            ->select('id', 'date', 'employee_id');

        $count = $query->count();

        if ($count === 0) {
            $this->info('No pending overtime records found.');

            return 0;
        }

        if (! $this->confirm("This will dispatch {$count} jobs. Continue?", true)) {
            $this->info('Operation canceled.');

            return 0;
        }

        $progressBar = $this->output->createProgressBar($count);
        $progressBar->setFormat(" %current%/%max% [%bar%] %percent:3s%% %estimated:-6s% remaining\n");

        $this->info("Dispatching {$count} overtime calculation jobs...");
        $progressBar->start();

        $query->chunkById(200, function ($overtimes) use ($progressBar) {
            foreach ($overtimes as $overtime) {
                try {
                    dispatch(new OvertimeCalculationService($overtime->employee_id, $overtime->date))
                        ->onConnection(config('globals.OVERTIME_JOB.CONNECTION'))
                        ->onQueue(config('globals.OVERTIME_JOB.QUEUE'))->afterCommit();

                    Log::info('Processing overtime record', ['id' => $overtime->id]);
                } catch (\Exception $e) {
                    Log::error('Failed to dispatch job for overtime record', [
                        'id' => $overtime->id,
                        'error' => $e->getMessage(),
                    ]);
                }

                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine();
        $this->info('All jobs have been dispatched successfully.');

        return 0;
    }
}
