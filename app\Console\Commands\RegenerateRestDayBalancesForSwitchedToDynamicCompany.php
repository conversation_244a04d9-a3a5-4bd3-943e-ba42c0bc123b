<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Models\EmployeeLeaveBalance;
use App\Models\EmployeeLeaveRequest;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RegenerateRestDayBalancesForSwitchedToDynamicCompany extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:regenerate-rest-day-balances-for-switched-to-dynamic-company 
                            {company_id : The ID of the company}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerates rest-day leave balances for dynamic-company employees, consolidating multiple records into one per April.';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $companyId = (int)$this->argument('company_id');
        $this->info("Starting rest-day balance consolidation for company #{$companyId}…");

        try {
            DB::transaction(function () use ($companyId) {
                // Define the check date and month boundaries for April
                $checkDate = Carbon::parse('2025-04-04');
                $startOfMonth = $checkDate->copy()->startOfMonth()->format('Y-m-d'); // 2025-04-01
                $endOfMonth = $checkDate->copy()->endOfMonth()->format('Y-m-d');   // 2025-04-30

                $employees = Employee::where('company_id', $companyId)->get();

                foreach ($employees as $employee) {
                    $restDayType = $employee->company->restDayLeaveType;

                    if (!$restDayType) {
                        $this->warn("  • Skipped employee {$employee->id}: no rest-day leave type defined.");

                        continue;
                    }

                    if ($employee->title->workTypePolicy->work_days_type != 'dynamic_onsite') {
                        $this->warn("  • Skipped employee {$employee->id}: not a dynamic-onsite employee.");

                        continue;
                    }

                    // Fetch all balances that cover the check date
                    $balances = EmployeeLeaveBalance::where('employee_id', $employee->id)
                        ->where('company_leave_type_id', $restDayType->id)
                        ->whereDate('start', '<=', $checkDate)
                        ->whereDate('end', '>=', $checkDate)
                        ->get();

                    if ($balances->count() > 1) {
                        // Use first balance as template
                        $template = $balances->first();

                        // Delete all old balances
                        EmployeeLeaveBalance::whereIn('id', $balances->pluck('id'))->delete();

                        // Sum net_quantity of leave requests in April
                        $totalRequests = EmployeeLeaveRequest::where('employee_id', $employee->id)
                            ->where('company_leave_type_id', $restDayType->id)
                            ->whereDate('from', '>=', $startOfMonth)
                            ->whereDate('from', '<=', $endOfMonth)
                            ->sum('net_quantity');

                        // Calculate the new balance
                        $newBalance = ($employee->title->workTypePolicy->rest_days_count * 8) - $totalRequests;

                        // Prepare data for new consolidated record
                        $data = $template->replicate()->toArray();
                        unset($data['id'], $data['created_at'], $data['updated_at']);
                        $data['start'] = $startOfMonth;
                        $data['end'] = $endOfMonth;
                        $data['balance'] = $newBalance;

                        EmployeeLeaveBalance::create($data);

                        $this->info("  ✓ Consolidated {$balances->count()} records for employee {$employee->id} into one (balance={$newBalance}).");
                    } else {
                        $this->info("  • Employee {$employee->id} has {$balances->count()} record(s); no consolidation needed.");
                    }
                }
            });
            DB::commit();
            $this->info('Restday balance consolidation complete.');

            return 0;

        } catch (\Throwable $e) {
            DB::rollBack();
            $this->error('Error: ' . $e->getMessage());

            return 1;
        }
    }
}
