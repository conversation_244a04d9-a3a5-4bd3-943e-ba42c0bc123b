<?php

namespace App\Traits\V1;

use App\Factories\V1\RequestUpdateFactory;
use App\Services\V1\Missions\MissionService;
use App\Models\StateMachines\RequestApproved;
use Carbon\Carbon;
use App\Jobs\HandleActionOnLeaveJob;
use App\Models\Timecard;
use Illuminate\Support\Facades\Log;

trait AutoApproveTrait
{
    use EmployeeRequestsTrait;

    /**
     * Handle auto-approval for a request
     *
     * @param  mixed  $request  The request object
     * @return void
     */
    protected function handleAutoApproveIfExist($request)
    {
        try {
            if (! $this->shouldAutoApprove($request)) {
                return;
            }

            $requestType = class_basename($request);
            $updateData = RequestUpdateFactory::getUpdateData($requestType, $request);

            if ($updateData) {
                $this->updateRequestAndEntity($request, $updateData);
            }
        } catch (\Exception $e) {
            Log::error('Error in handleAutoApprove: '.$e->getMessage());
        }
    }

    /**
     * Check if the request should be auto-approved
     *
     * @param  mixed  $request  The request object
     */
    protected function shouldAutoApprove($request): bool
    {
        if (! $request || ! $request->employeeRequest) {
            return false;
        }

        return $this->checkRequestIsCompleted($request->employeeRequest, $request->employeeRequest->requestedBy);
    }

    /**
     * Update both the request and its entity
     *
     * @param  mixed  $request  The request object
     * @param  array  $updateData  The data to update
     */
    protected function updateRequestAndEntity($request, array $updateData): void
    {
        $this->updateRequest($request->employeeRequest, RequestApproved::class);
        if(class_basename($request) != 'TerminationRequest'){
            $request->update($updateData['entity_data']);
        }

        // Handle special cases
        $this->handleSpecialCases($request, $updateData);
    }

    /**
     * Handle special cases for specific request types
     *
     * @param  mixed  $request  The request object
     * @param  array  $updateData  The update data
     */
    protected function handleSpecialCases($request, array $updateData): void
    {
        $requestType = class_basename($request);
        Log::info('requestType: '.$requestType);

        switch ($requestType) {
            case 'TerminationRequest':
                $this->updateEntity($request, $updateData['entity_data']['status']);
                if (isset($updateData['entity_data']['employee_status'])) {
                    $request->employee->status = $updateData['entity_data']['employee_status'];
                    $request->employee->save();
                }
                break;


            case 'EmployeeLeaveRequest':
                $fromDate = Carbon::parse($request->from)->toDateString();
                $toDate = Carbon::parse($request->to)->toDateString();
                $timecards = Timecard::where('employee_id', $request->employee_id)
                                ->select('id')
                                ->whereDate('from', '>=', $fromDate)
                                ->whereDate('from', '<=', $toDate)
                                ->get();
                $jobData = [
                    'request' => $request,
                    'final_status' => 'approved',
                    // TODO: remove time cards from here
                    'timecards' => $timecards ?? [],
                ];
                HandleActionOnLeaveJob::dispatch($jobData);
                break;

            case 'MissionRequest':
                $missionService = app(MissionService::class);
                $missionService->resolveForApprovedMission($request);
                break;
            case 'AttendanceDeduction':
                $dataToBeUpdated = [
                    'comment' => $request->employeeRequest->comment,
                    'updated_by' => $request->employeeRequest->requestedBy->id,
                    'request_name' => 'waive_deduction',
                ];
                $this->updateEntity($request, $request->updated_value == 0 ? 'waived' : 'applied');
                $this->updateRequest($request->employeeRequest, 'approved', $dataToBeUpdated);
                break;
        }
    }
}
