<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\EmployeeRequest;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncEmployeeRequestStatusWithLeaves extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:leave_status:with_requests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        DB::beginTransaction();
        try {
            $this->syncLeaveStatusWithEmployeeRequests();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }

    public function syncLeaveStatusWithEmployeeRequests()
    {
        $employeeRequests = EmployeeRequest::where('requestable_type', 'employee_leave_request')->with('requestable')->get();

        $numSynced = 0;
        foreach ($employeeRequests as $employeeRequest) {
            $requestable = $employeeRequest->requestable;
            if ($requestable) {
                $employeeRequest->status = $requestable->status;
                $employeeRequest->save();
                $numSynced++;
            }
        }
        echo 'Total synced: '.$numSynced."\n";

    }
}
