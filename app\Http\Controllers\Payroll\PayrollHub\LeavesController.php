<?php

namespace App\Http\Controllers\Payroll\PayrollHub;

use App\Http\Controllers\NewController;
use App\Http\Requests\V1\PayrollHub\BulkLeaveManagementRequest;
use App\Services\V1\PayrollHub\RequestsBulkActionService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\Log;
use App\Exceptions\UnprocessableException;

class LeavesController extends NewController
{
    public function __construct(
        protected RequestsBulkActionService $requestsBulkActionService,
    )
    {
    }

    public function bulkApprove(BulkLeaveManagementRequest $request)
    {
        try {
            $outputs = $this->requestsBulkActionService->bulkApproveLeaveRequests($request->validated());
            $valid = collect($outputs)->contains(function ($output) {
                return !isset($output->Error);
            });
    
            if(!$valid){
                return getErrorResponseStructure(
                    HttpStatusCodeUtil::UNPROCESSABLE_ENTITY,
                    'Bulk action failed'
                );
            }
            return getResponseStructure(
                ['data' => $outputs],
              HttpStatusCodeUtil::OK,
                'Bulk action performed successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function bulkReject(BulkLeaveManagementRequest $request)
    {
        try {
            return getResponseStructure(
                ['data' => $this->requestsBulkActionService->bulkRejectLeaveRequests($request->validated())],
                HttpStatusCodeUtil::OK,
                'Bulk action performed successfully'
            );
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

}