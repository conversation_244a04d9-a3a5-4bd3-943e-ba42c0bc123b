<?php

namespace App\Http\Resources\V1\LoansAndSalaryAdvances;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ScopeEmployeeForLoansResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $lang = (config('globals.lang') ?? 'ar');

        return [
            'id' => $this->id,
            'name' => $lang == 'en' ? $this->name_en : $this->name_ar,
            'employee_number' => $this->employee_number,
            'net_salary' => $this->employeeSalary?->net_salary ?? 0,
            'on_probation' => $this->status == 'on_probation' ?? false,
            'is_pending_loan' => $this->isPendingLoan ?? false,
            'is_pending_salary_advance' => $this->isPendingSalaryAdvance ?? false,
            'title' => [
                'id' => $this->title?->id,
                'name' => $lang == 'en' ? $this->title?->name_en : $this->title?->name_ar,
                'loan_policy_id' => $this->title?->loan_policy_id,
                'salary_advance_policy_id' => $this->title?->salary_advance_policy_id,
                'allow_salary_advance_beginning_from' => $this->title?->salaryAdvancePolicy?->allowed_request_day,
            ],
        ];
    }
}
