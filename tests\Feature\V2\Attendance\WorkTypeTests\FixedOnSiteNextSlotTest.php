<?php

namespace Tests\Feature\V1\Running\Attendance\WorkTypeTests;

use App\Enums\Missions\MissionsEnum;
use App\FeatureToggles\Unleash;
use App\Models\Attendance;
use App\Models\AttendanceSetting;
use App\Models\Branch;
use App\Models\Cico;
use App\Models\CompanyLeaveTypePolicy;
use App\Models\EmployeeLeaveRequest;
use App\Models\Permission;
use App\Models\PublicHoliday;
use App\Models\Role;
use App\Models\Timecard;
use App\Models\User;
use App\Models\WorkTypePolicy;
use App\Traits\FeatureTestingTrait;
use App\Traits\RefreshOnlyBluworksDatabase;
use App\Util\AttendanceUtil;
use App\Util\UserWorkTypesUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class FixedOnSiteNextSlotTest extends TestCase
{
    use FeatureTestingTrait;
    //        , RefreshOnlyBluworksDatabase

    private $user;

    private $employee;

    private $anyLocationRole;

    private $anyBranchRole;

    private $workerRole;

    private $secondBranch;

    private $mock;

    public function setupRolesAndPermissions()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $manageWorkerPermission = Permission::where('name', 'manage_worker')->first() ?? null;
        if (! isset($manageWorkerPermission)) {
            Permission::factory()->state(['name' => 'manage_worker', 'company_id' => $this->user->company_id])->create();
        }

        $permission = Permission::where('name', 'any_location')->first() ?? null;
        if (! isset($permission)) {
            Permission::factory()->state(['name' => 'any_location', 'company_id' => $this->user->company_id])->create();
        }

        $this->workerRole = Role::factory()->state(['name' => 'Worker Role', 'company_id' => $this->user->company_id])->create();
        $this->workerRole->givePermissionTo('manage_worker', 'manage_worker');

        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    public function setWorkerUser(string $workType = UserWorkTypesUtil::FIXED_ON_SITE)
    {
        $this->user = User::factory()->state(['company_id' => $this->user->company_id])->create();
        $workType = WorkTypePolicy::factory()->state(['company_id' => $this->user->company_id, 'work_days_type' => $workType])->create();
        $this->user->assignRole($this->workerRole);
        $this->employee = $this->user->employee;
        $this->employee->title->work_type_policy_id = $workType->id;
    }

    public function setAnyBranchWorkTypeUser(string $workType = UserWorkTypesUtil::FIXED_ON_SITE)
    {
        $this->user = User::factory()->state(['company_id' => $this->user->company_id])->create();
        $workType = WorkTypePolicy::factory()->state(['company_id' => $this->user->company_id, 'work_days_type' => $workType, 'apply_any_branch' => true])->create();
        $this->user->assignRole($this->workerRole);
        $this->employee = $this->user->employee;
        $this->employee->title->work_type_policy_id = $workType->id;
    }

    private function prepareData()
    {
        $this->user = User::factory()->create();
        $this->setupRolesAndPermissions();

        $this->secondBranch = Branch::factory()->create(['company_id' => $this->user->company_id]);

        $this->mock = $this
            ->getMockBuilder(Unleash::class)
            ->onlyMethods(['isEnabled', 'getUnscheduledShiftsFeatureFlag', 'getAbsenceLateEditsFeatureFlag'])
            ->getMock();

        $this->mock
            ->expects($this->any())
            ->method('isEnabled')
            ->willReturn(true);

        $this->mock
            ->expects($this->any())
            ->method('getUnscheduledShiftsFeatureFlag')
            ->willReturn(true);

        $this->mock
            ->expects($this->any())
            ->method('getAbsenceLateEditsFeatureFlag')
            ->willReturn(true);

        $this->app->instance(Unleash::class, $this->mock);

    }

    protected function assertStatusAndJson($response, $status, $clockInable)
    {
        $response->assertStatus($status);

        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'clockInable' => $clockInable,
                ],
            ],
        ]);
    }

    public function AttendanceSettingFactory(string $key, int $value, bool $isUsed = true): void
    {
        AttendanceSetting::factory()->create(['company_id' => $this->user->company_id, 'key' => $key, 'value' => $value, 'is_used' => $isUsed]);
    }

    protected function createTimecardAndAttendance()
    {
        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now(), 'to' => now()->addHours(8),
            'required_ci_branch_id' => $this->secondBranch->id,
            'required_co_branch_id' => $this->secondBranch->id,
        ]);

        $cico = Cico::factory()->state([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->secondBranch->id,
            'status' => AttendanceUtil::CICO_VERIFIED_STATUS,
            'date' => now()->subHour()->format('Y-m-d H:i:s'),
        ])->create();

        $attendance = Attendance::factory()->state([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'slotable_id' => $timecard->id,
            'slotable_type' => 'time_card',
            'company_id' => $this->employee->company_id,
            'ci_id' => $cico->id,
        ])->create();
    }

    protected function updateCicoStatus($status)
    {
        $cico = Cico::where('employee_id', $this->employee->id)->first();
        $cico->status = $status;
        $cico->save();
    }

    public function testClockinableWorkerWithAnyBranchLocationOutOfCompany()
    {
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => 21321.23, 'long' => 213213.2321]);

        $this->assertStatusAndJson($response, 200, false);

        $this->destroyData(); // Clean up
    }

    public function testClockinableWorkerWithAnyBranchLocationInCompanyBranches()
    {
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $branchLat = explode(',', $this->secondBranch->location)[0];
        $branchLong = explode(',', $this->secondBranch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $this->assertStatusAndJson($response, 200, true);

        $this->destroyData(); // Clean up
    }

    public function testClockinableWorkerWithNoAnyBranchNextSlotMainBranch()
    {
        $this->prepareData();
        $this->setWorkerUser();

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $this->assertStatusAndJson($response, 200, true);

        $this->destroyData(); // Clean up
    }

    public function testClockinableWorkerWithNoAnyBranchNextSlotOtherBranch()
    {
        $this->prepareData();
        $this->setWorkerUser();

        $branchLat = explode(',', $this->secondBranch->location)[0];
        $branchLong = explode(',', $this->secondBranch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $this->assertStatusAndJson($response, 200, true);

        $this->destroyData(); // Clean up
    }

    public function testClockinableWorkerNextSlotLocationOutOfCompany()
    {
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => 20.0321, 'long' => 20.0321]);

        $this->assertStatusAndJson($response, 200, false);

        $this->destroyData(); // Clean up
    }

    public function testClockinableWorkeHasFullDayMissionLocationOutOfCompany()
    {
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $this->createMissionTimecard(MissionsEnum::FULL_DAY->value);

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => 213621.23, 'long' => 2138213.2321]);

        $this->assertStatusAndJson($response, 200, true);

        $this->destroyData(); // Clean up
    }

    public function testClockinableWorkeHasHalfDayMissionLocationOutOfCompany()
    {
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $this->createMissionTimecard(MissionsEnum::HALF_DAY_BEFORE_WORK->value);

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => 213621.23, 'long' => 2138213.2321]);

        $this->assertStatusAndJson($response, 200, false);

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $this->assertStatusAndJson($response, 200, false);

        $this->destroyData(); // Clean up
    }

    public function testClockinableWorkerNextSlotWithClockInAnotherBranch()
    {
        $this->prepareData();
        $this->setWorkerUser();

        $this->createTimecardAndAttendance();

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $this->assertStatusAndJson($response, 200, true);

        $this->updateCicoStatus(AttendanceUtil::CICO_UNVERIFIED_STATUS);

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $this->assertStatusAndJson($response, 200, true);

        $this->destroyData(); // Clean up
    }

    public function testStatusEmployeeWithAnyBranchLocationInCompanyBranch()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $branchLat = explode(',', $this->secondBranch->location)[0];
        $branchLong = explode(',', $this->secondBranch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'status' => AttendanceUtil::CICO_VERIFIED_STATUS,
                ],
            ],
        ]);
    }

    //////////////////////////////////////////  status testing //////////////////////////////////////

    public function testStatusWorkerNoAnyBranchLocationOnMainBranch()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setWorkerUser();

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'status' => AttendanceUtil::CICO_VERIFIED_STATUS,
                ],
            ],
        ]);

        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now(), 'to' => now()->addHours(8),
            'required_ci_branch_id' => $this->employee->branch_id,
            'required_co_branch_id' => $this->employee->branch_id,
        ]);

        $unverifiedCico = Cico::factory()->state([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->secondBranch->id,
            'status' => AttendanceUtil::CICO_UNVERIFIED_STATUS,
            'date' => now()->subHour()->format('Y-m-d H:i:s'),
        ])->create();

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'status' => AttendanceUtil::CICO_OFF_BRANCH_STATUS,
                ],
            ],
        ]);
    }

    public function testStatusWorkerNoAnyBranchLocationOtherBranch()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setWorkerUser();

        $branchLat = explode(',', $this->secondBranch->location)[0];
        $branchLong = explode(',', $this->secondBranch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'status' => AttendanceUtil::CICO_OFF_BRANCH_STATUS,
                ],
            ],
        ]);
    }

    public function testStatusAnyBranchWorkerLocationOnMainBranch()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'status' => AttendanceUtil::CICO_VERIFIED_STATUS,
                ],
            ],
        ]);
    }

    public function testStatusAnyBranchWorkerLocationOnOtherBranch()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $branchLat = explode(',', $this->secondBranch->location)[0];
        $branchLong = explode(',', $this->secondBranch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'status' => AttendanceUtil::CICO_VERIFIED_STATUS,
                ],
            ],
        ]);
    }

    public function testStatusWorkerHasFulDayMission()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $this->createMissionTimecard(MissionsEnum::FULL_DAY->value);
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => 213621.23, 'long' => 2138213.2321]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'status' => AttendanceUtil::CICO_VERIFIED_STATUS,
                ],
            ],
        ]);
    }

    //////////////////////////////////////////  slot data testing //////////////////////////////////////

    public function testSlotDataWorkerHasTimeCardGetNextSlotInAllowedClockinTime()
    {
        // fixed on site workers can clock on the timecard whatever the time of day as long as he has a timecard on that day

        $this->destroyData();
        $this->prepareData();
        $this->setWorkerUser();

        $this->AttendanceSettingFactory(config('globals.ATTENDANCE_SETTINGS.ALLOWED_MINUTES_TO_CHECK_IN_BEFORE'), 15); // 59 min

        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now()->addminutes(10),
            'to' => now()->addHours(8),
            'required_ci_branch_id' => $this->employee->branch_id,
            'required_co_branch_id' => $this->employee->branch_id,
            'required_ci_lat' => null, 'required_ci_long' => null,
        ]);

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'id' => $timecard->id,
                    'name' => $timecard->shift->name,
                    'from' => $timecard->from->format('Y-m-d H:i:s'),
                    'to' => $timecard->to->format('Y-m-d H:i:s'),
                    'type' => 'timecard',
                ],
            ],
        ]);

        $timecard->update(['from' => now()->endOfDay(),
            'to' => now()->endOfDay()->addHours(8)]);
        $mockedTime = now()->endOfDay();
        Carbon::setTestNow($mockedTime);
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'id' => $timecard->id,
                    'name' => $timecard->shift->name,
                    'from' => $timecard->from->format('Y-m-d H:i:s'),
                    'to' => $timecard->to->format('Y-m-d H:i:s'),
                    'type' => 'timecard',
                ],
            ],
        ]);

        $timecard->update(['from' => now()->startOfDay(),
            'to' => now()->startOfDay()->addHours(8)]);
        $mockedTime = now()->startOfDay();
        Carbon::setTestNow($mockedTime);
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'id' => $timecard->id,
                    'name' => $timecard->shift->name,
                    'from' => $timecard->from->format('Y-m-d H:i:s'),
                    'to' => $timecard->to->format('Y-m-d H:i:s'),
                    'type' => 'timecard',
                ],
            ],
        ]);
    }

    public function testSlotDataWorkerGetTimecardOnTheTimeOfTimecard()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setWorkerUser();

        $this->AttendanceSettingFactory(config('globals.ATTENDANCE_SETTINGS.ALLOWED_MINUTES_TO_CHECK_IN_BEFORE'), 15); // 15 min

        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now()->addMinutes(10),
            'to' => now()->addHours(8),
            'required_ci_branch_id' => $this->employee->branch_id,
            'required_co_branch_id' => $this->employee->branch_id,
            'required_ci_lat' => null, 'required_ci_long' => null,
        ]);

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'id' => $timecard->id,
                    'name' => $timecard->shift->name,
                    'from' => $timecard->from->format('Y-m-d H:i:s'),
                    'to' => $timecard->to->format('Y-m-d H:i:s'),
                    'type' => 'timecard',
                ],
            ],
        ]);
    }

    public function testSlotDataWorkerGetTimecardBeforeTheTimeOfTimecard()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setWorkerUser();

        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now()->endOfDay(),
            'to' => now()->endOfDay()->addHours(8),
            'required_ci_branch_id' => $this->employee->branch_id,
            'required_co_branch_id' => $this->employee->branch_id,
            'required_ci_lat' => null, 'required_ci_long' => null,
        ]);

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];

        $mockedTime = now()->startOfDay();
        Carbon::setTestNow($mockedTime);

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'id' => $timecard->id,
                    'name' => $timecard->shift->name,
                    'from' => $timecard->from->format('Y-m-d H:i:s'),
                    'to' => $timecard->to->format('Y-m-d H:i:s'),
                    'type' => 'timecard',
                ],
            ],
        ]);

        Carbon::setTestNow(); // Reset the time mocking
    }

    public function testSlotDataWorkerGetTimecardAfterTheTimeOfTimecard()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setWorkerUser();

        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now()->startOfDay(),
            'to' => now()->startOfDay()->addHours(8),
            'required_ci_branch_id' => $this->employee->branch_id,
            'required_co_branch_id' => $this->employee->branch_id,
            'required_ci_lat' => null, 'required_ci_long' => null,
        ]);

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];

        $mockedTime = now()->endOfDay();
        Carbon::setTestNow($mockedTime);

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'id' => $timecard->id,
                    'name' => $timecard->shift->name,
                    'from' => $timecard->from->format('Y-m-d H:i:s'),
                    'to' => $timecard->to->format('Y-m-d H:i:s'),
                    'type' => 'timecard',
                ],
            ],
        ]);

        Carbon::setTestNow(); // Reset the time mocking
    }

    public function testSlotDataWorkerHasLeave()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setWorkerUser();

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => '0', 'long' => '0']);

        $companyLeaveTypePolicy = CompanyLeaveTypePolicy::factory()->state(['company_id' => $this->employee->company_id])->create();
        $employeeLeaveRequest = EmployeeLeaveRequest::factory()->state([
            'employee_id' => $this->user->employee_id,
            'from' => now()->startOfDay()->toDateTimeString(),
            'to' => now()->endOfDay()->toDateTimeString(),
            'status' => 'approved',
            'branch_id' => $this->employee->branch_id,
            'company_leave_type_id' => $companyLeaveTypePolicy->company_leave_type_id,
            'company_leave_type_policy_id' => $companyLeaveTypePolicy->id,
        ])->create();

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'id' => $employeeLeaveRequest->id,
                    'name' => $employeeLeaveRequest->companyLeaveType->name,
                    'from' => $employeeLeaveRequest->from,
                    'to' => $employeeLeaveRequest->to,
                    'type' => 'leave',
                    'slot_branch' => null,
                    'clockInable' => true,
                ],
            ],
        ]);
    }

    public function testSlotDataPublicHoliday()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setWorkerUser();

        $publicHoliday = PublicHoliday::factory()->create();

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'id' => $publicHoliday->id,
                    'name' => $publicHoliday->name,
                    'from' => $publicHoliday->start,
                    'to' => $publicHoliday->end,
                    'type' => 'public_holiday',
                    'clockInable' => true,
                ],
            ],
        ]);
    }

    public function testSlotDataWorkerHasPartialLeaveMission() #TODO fix this
    {
        $this->destroyData();
        $this->prepareData();
        $this->setWorkerUser();

        // create timecard on employees main branch make required_ci_branch_id = $this->employee->branch_id
        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now()->startOfDay()->toDateTimeString(),
            'to' => now()->endOfDay()->toDateTimeString(),
            'required_ci_branch_id' => $this->employee->branch_id,
            'required_co_branch_id' => $this->employee->branch_id,
            'required_ci_lat' => null, 'required_ci_long' => null,
        ]);

        
        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => 20.123546, 'long' => 30.123546]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'id' => $timecard->id,
                    'name' => MissionsEnum::FULL_DAY->value,
                    'from' => $timecard->from,
                    'to' => $timecard->to,
                ],
            ],
        ]);
    }

    //////////////////////////////////////////  //////////////////////////////////////

    ///////////////////////////// test slot and location branch ////////////////////////////
    public function testSlotBranchMainBranch()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now(),
            'to' => now()->addHours(8),
            'required_ci_branch_id' => $this->employee->branch_id,
            'required_co_branch_id' => $this->employee->branch_id,
            'required_ci_lat' => null, 'required_ci_long' => null,
        ]);

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'slot_branch' => [
                        'id' => $this->employee->branch_id,
                        'name' => $this->employee->branch->name,
                    ],
                ],
            ],
        ]);
    }

    public function testSlotBranchOtherBranch()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now(),
            'to' => now()->addHours(8),
        ]);

        $timecard->required_ci_branch_id = $this->secondBranch->id;
        $timecard->required_co_branch_id = $this->secondBranch->id;
        $timecard->save();

        $branchLat = explode(',', $this->secondBranch->location)[0];
        $branchLong = explode(',', $this->secondBranch->location)[1];

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'slot_branch' => [
                        'id' => $this->secondBranch->id,
                        'name' => $this->secondBranch->name,
                    ],
                ],
            ],
        ]);
    }

    public function testLocationBranchOnOtherBranch()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now(),
            'to' => now()->addHours(8),
            'required_ci_branch_id' => $this->secondBranch->id,
            'required_co_branch_id' => $this->secondBranch->id,
            'required_ci_lat' => null, 'required_ci_long' => null,
        ]);

        $branchLat = explode(',', $this->secondBranch->location)[0];
        $branchLong = explode(',', $this->secondBranch->location)[1];

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'location_branch' => [
                        'id' => $this->secondBranch->id,
                        'name' => $this->secondBranch->name,
                    ],
                ],
            ],
        ]);
    }

    public function testLocationBranchCloseToBranch()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now(),
            'to' => now()->addHours(8),
            'required_ci_branch_id' => $this->employee->branch_id,
            'required_co_branch_id' => $this->employee->branch_id,
            'required_ci_lat' => null, 'required_ci_long' => null,
        ]);

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];
        $currentLat = (float) $branchLat + 0.0005;
        $currentLong = (float) $branchLong + 0.0005;

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $currentLat, 'long' => $currentLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'location_branch' => [
                        'id' => $this->employee->branch->id,
                        'name' => $this->employee->branch->name,
                    ],
                ],
            ],
        ]);
    }

    public function testLocationBranchOnSecondBranch()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $branchLat = explode(',', $this->secondBranch->location)[0];
        $branchLong = explode(',', $this->secondBranch->location)[1];
        $currentLat = (float) $branchLat + 0.0005;
        $currentLong = (float) $branchLong + 0.0005;

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $currentLat, 'long' => $currentLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'location_branch' => [
                        'id' => $this->secondBranch->id,
                        'name' => $this->secondBranch->name,
                    ],
                ],
            ],
        ]);
    }

    public function testLocationBranchOutOfCompanyBranches()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now(),
            'to' => now()->addHours(8),
            'required_ci_branch_id' => $this->employee->branch_id,
            'required_co_branch_id' => $this->employee->branch_id,
            'required_ci_lat' => null, 'required_ci_long' => null,
        ]);

        $this->secondBranch->delete();

        $mainBranchLat = explode(',', $this->employee->branch->location)[0];
        $mainBranchLong = explode(',', $this->employee->branch->location)[1];
        $currentLat = (float) $mainBranchLat + 0.0008;
        $currentLong = (float) $mainBranchLong + 0.0006;

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $currentLat, 'long' => $currentLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'location_branch' => null,
                ],
            ],
        ]);
    }

    public function testLocationBranchStillInSecondBranchRadius()
    {
        $this->destroyData();
        $this->prepareData();
        $this->setAnyBranchWorkTypeUser();

        $timecard = Timecard::factory()->create([
            'employee_id' => $this->employee->id,
            'branch_id' => $this->employee->branch_id,
            'from' => now(),
            'to' => now()->addHours(8),
            'required_ci_branch_id' => $this->secondBranch->id,
            'required_co_branch_id' => $this->secondBranch->id,
            'required_ci_lat' => null, 'required_ci_long' => null,
        ]);

        $mainBranchLat = explode(',', $this->employee->branch->location)[0];
        $mainBranchLong = explode(',', $this->employee->branch->location)[1];

        $newBranch = Branch::factory()->create([
            'company_id' => $this->user->company_id,
            'location' => (float) $mainBranchLat + 0.00005.','.(float) $mainBranchLong + 0.00005,
        ]);

        $timecard->required_ci_branch_id = $newBranch->id;
        $timecard->required_co_branch_id = $newBranch->id;
        $timecard->save();

        $branchLat = explode(',', $this->employee->branch->location)[0];
        $branchLong = explode(',', $this->employee->branch->location)[1];

        $response = $this->actingAs($this->user, 'user-api')->withHeaders(['Accept-Language' => 'en'])
            ->json('GET', 'api/v1/work-type-next-slot', ['lat' => $branchLat, 'long' => $branchLong]);

        $response->assertStatus(200);
        $response->assertJson([
            'version' => '1',
            'payload' => [
                'data' => [
                    'location_branch' => [
                        'id' => $newBranch->id,
                        'name' => $newBranch->name,
                    ],
                ],
            ],
        ]);
    }
}
