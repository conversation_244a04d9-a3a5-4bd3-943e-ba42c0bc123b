<?php

namespace App\Actions;

use Illuminate\Support\Facades\Crypt;
use TCG\Voyager\Actions\AbstractAction;

class ImpersonateAction extends AbstractAction
{
    public function getTitle(): string
    {
        return 'impersonate';
    }

    public function getIcon(): string
    {
        return 'voyager-eye';
    }

    public function getPolicy(): string
    {
        return 'read';

    }

    public function getAttributes(): array
    {
        return [
            'class' => 'btn btn-sm btn-primary pull-right',
        ];
    }

    public function shouldActionDisplayOnDataType(): bool
    {
        return $this->dataType->slug == 'employees';
    }

    public function getDefaultRoute(): string
    {
        return config('globals.APP_DOMAIN').'/auth/impersonate/'.Crypt::encryptString($this->data->id);
    }
}
