<?php

namespace App\Actions;

use TCG\Voyager\Actions\AbstractAction;

class FillRestDayAction extends AbstractAction
{
    public function getTitle(): string
    {
        return 'Add Rest Days';
    }

    public function getIcon(): string
    {
        return 'voyager-eye';
    }

    public function getPolicy(): string
    {
        return 'read';

    }

    public function getAttributes(): array
    {
        return [
            'class' => 'btn btn-sm btn-primary pull-right',
        ];
    }

    public function shouldActionDisplayOnDataType(): bool
    {
        return $this->dataType->slug == 'employees';
    }

    public function getDefaultRoute(): string
    {
        return '/api/employees/fillRestDays';
    }
}
