<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\V1\Payroll\PayslipServiceV2;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Config;

class TestPayslipServiceV2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:payslip-v2 {--lang=en} {--company_id=} {--employee_ids=*}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test PayslipServiceV2 with Spatie PDF integration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $language = $this->option('lang') ?? 'en';
            $companyId = $this->option('company_id');
            $employeeIds = $this->option('employee_ids');

            $this->info('Testing PayslipServiceV2 PDF generation...');
            $this->info("Language: {$language}");
            $this->info("Company ID: {$companyId}");
            $this->info("Employee IDs: " . ($employeeIds ? implode(',', $employeeIds) : 'all'));

            // Set company ID in config for testing
            if ($companyId) {
                Config::set('globals.company.id', $companyId);
            }

            // Create test data
            $data = [
                'end_date' => Carbon::now()->format('Y-m-d'),
                'print_lang' => $language,
                'company_id' => $companyId
            ];

            if ($employeeIds) {
                $data['employee_ids'] = $employeeIds;
            }

            // Get service instance from container
            $service = app(PayslipServiceV2::class);

            // Generate payslips
            $result = $service->generateAndStorePayslips($data);

            $this->info('✅ Payslips generated successfully!');
            $this->info("ZIP file path: {$result['path']}");
            $this->info("Total employees: {$result['total_employees']}");

        } catch (Exception $e) {
            $this->error('❌ Test failed: ' . $e->getMessage());
            $this->error('File: ' . $e->getFile() . ':' . $e->getLine());

            return Command::FAILURE;
        }
    }
}
