<?php

namespace App\Http\Requests\V1\WorkerApp;

use App\Exceptions\UnprocessableException;
use App\FeatureToggles\Unleash;
use Illuminate\Foundation\Http\FormRequest;

class AddBalanceToOvertimeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $unleash = app(Unleash::class);

        if (! $unleash->isRequestOvertimeHoursEnabled()) {
            throw new UnprocessableException('overtime requests is not enabled');
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'hours' => [
                'required',
                'integer',
            ],
            'minutes' => [
                'required',
                'integer',
            ],
            'employee_id' => [
                'required',
                'integer',

            ],
        ];

    }

    public function prepareForValidation()
    {
        $this->merge([
            'hours' => $this->input('hours', 0),
            'minutes' => $this->input('minutes', 0),
        ]);
    }
}
