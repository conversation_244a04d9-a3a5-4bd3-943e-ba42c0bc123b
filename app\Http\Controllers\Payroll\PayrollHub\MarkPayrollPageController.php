<?php

namespace App\Http\Controllers\Payroll\PayrollHub;

use App\Http\Controllers\NewController;
use App\Http\Requests\V1\PayrollHub\MarkPayrollPageRequest;
use App\Services\V1\PayrollHub\PayrollCompletingService;
use App\Util\HttpStatusCodeUtil;
use App\Traits\QueriesHelper;

class MarkPayrollPageController extends NewController
{
    use QueriesHelper;
    public function __construct(
        protected PayrollCompletingService $payrollService,
    )
    {
    }

    public function markTabInsideSection(MarkPayrollPageRequest $request)
    {
        $status = $this->payrollService->updatePageStatus($request->validated());
        return getResponseStructure(
            ['data' => [$status]],
            HttpStatusCodeUtil::OK,
            'status updated successfully'
        );
    }

    public function getCompletedPages()
    {
        $completedPages = $this->payrollService->getCompletedPages();
        return getResponseStructure(
            ['data' => $completedPages],
            HttpStatusCodeUtil::OK,
            'status updated successfully'
        );
    }

}