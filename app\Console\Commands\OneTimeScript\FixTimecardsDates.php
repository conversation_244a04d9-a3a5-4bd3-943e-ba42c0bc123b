<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Employee;
use App\Models\Timecard;
use App\Services\V1\Holidays\PublicHolidaysAttendanceService;
use App\Services\V1\Holidays\PublicHolidaysService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixTimecardsDates extends Command
{
    public function __construct(
        private PublicHolidaysService $publicHolidaysService,
        private PublicHolidaysAttendanceService $publicHolidaysAttendanceService,
    ) {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:timecards:dates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            // $timecards = Model::select(DB::raw("SELECT * FROM timecards as t
            // join employees as e on e.id = t.employee_id WHERE TIMESTAMPDIFF(DAY, t.from, t.to) > 1 order by t.from desc;"))->get();

            $timecards = Timecard::whereRaw('TIMESTAMPDIFF(DAY, `from`, `to`) > 1')->orWhereRaw('TIMESTAMPDIFF(HOUR, `from`, `to`) < 0 ')->get();

            foreach ($timecards as $timecard) {
                $oldFrom = $timecard->from;
                $oldTo = $timecard->to;
                $from = Carbon::parse($timecard->from);
                $to = Carbon::parse($timecard->to);
                if ($to->format('H:i:s') < $from->format('H:i:s')) {
                    $fromDate = $from->format('Y-m-d');
                    $toDate = Carbon::parse($fromDate)->addDay()->format('Y-m-d');
                    $to = Carbon::parse($toDate.' '.$to->format('H:i:s'));
                } else {
                    $fromDate = $from->format('Y-m-d');
                    $to = Carbon::parse($fromDate.' '.$to->format('H:i:s'));
                }
                $timecard->from = $from->toDateTimeString();
                $timecard->to = $to->toDateTimeString();
                $timecard->save();
                echo 'Timecard: '.$timecard->id.' From: '.$oldFrom.' To: '.$oldTo.' New From: '.$timecard->from.' New To: '.$timecard->to."\n";

            }
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
            // dd($e);
        }
    }
}
