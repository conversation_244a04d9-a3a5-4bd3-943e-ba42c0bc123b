<?php

namespace App\Enums\EmployeeInfo;

enum EmploymentTypeEnum: string
{
    case FULL_TIME = 'full_time';
    case PART_TIME = 'part_time';
    case CONSULTANT = 'consultant';
    case OUT_SOURCED = 'out_sourced';

    public static function all(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    public static function getTranslatedValues(): array
    {
        return array_map(fn ($case) => trans('employeeexport.' . $case->value), self::cases());
    }

    public static function getLabel(self $value): string
    {
        return trans('employeeexport.' . $value->value);
    }
}