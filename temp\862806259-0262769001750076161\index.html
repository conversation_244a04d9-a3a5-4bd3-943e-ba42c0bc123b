<!DOCTYPE html>
<html dir="rtl" lang="ar">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ﺐﺗاﺮﻟا ﻒﺸﻛ -
        دﻮﻤﺤﻣ ﻢﻴﻫاﺮﺑإ ﺪﻤﺤﻣ
    </title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Inter:wght@400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family:
                &#039;Amiri&#039;, serif
            ;
            direction:
                rtl
            ;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: white;
            padding: 20px;
        }

        .payslip-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }

        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }

        .company-info {
            flex: 1;
        }

        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .company-logo {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
            text-align:
                left
            ;
        }

        /* Employee Info Section */
        .employee-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
        }

        .payslip-title {
            font-size: 22px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }

        .employee-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .employee-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
        }

        .field-label {
            font-weight: bold;
            color: #555;
        }

        .field-value {
            color: #333;
            font-weight: 500;
        }

        /* Categories Section */
        .categories-section {
            margin-bottom: 25px;
        }

        .category-block {
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .category-title {
            font-size: 16px;
            font-weight: bold;
        }

        .category-amount-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .category-content {
            background: white;
        }

        .component-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .component-item:last-child {
            border-bottom: none;
        }

        .component-name {
            font-weight: 500;
            color: #555;
        }

        .component-amount {
            font-weight: bold;
            color: #2c3e50;
        }

        .category-total {
            background: #f8f9fa;
            padding: 12px 20px;
            border-top: 2px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
        }

        .total-label {
            color: #555;
        }

        .total-amount {
            color: #2c3e50;
            font-size: 16px;
        }

        /* Deductions Section */
        .deductions-section {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .deductions-title {
            font-size: 18px;
            font-weight: bold;
            color: #c53030;
            margin-bottom: 15px;
            text-align: center;
        }

        .deductions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .deduction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid #fed7d7;
        }

        .deduction-label {
            font-weight: 500;
            color: #744210;
        }

        .deduction-amount {
            font-weight: bold;
            color: #c53030;
        }

        /* Net Pay Section */
        .net-pay-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 8px;
            text-align: center;
        }

        .net-pay-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .net-pay-amount {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .net-pay-currency {
            font-size: 16px;
            opacity: 0.9;
        }

        /* RTL Specific Styles */
        .rtl {
            direction: rtl;
        }

        .rtl .employee-field {
            flex-direction: row-reverse;
        }

        .rtl .category-header {
            flex-direction: row-reverse;
        }

        .rtl .component-item {
            flex-direction: row-reverse;
        }

        .rtl .category-total {
            flex-direction: row-reverse;
        }

        .rtl .deduction-item {
            flex-direction: row-reverse;
        }

        /* Page Break */
        .new-page {
            page-break-before: always;
        }

        /* Print Styles */
        @media print {
            body {
                padding: 0;
            }

            .payslip-container {
                box-shadow: none;
                border: none;
            }
        }

        /* Arabic Text Shaping */
        .arabic-text {
            font-family: 'Amiri', serif;
            font-feature-settings: "liga" 1, "calt" 1, "kern" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
    </style>
</head>

<body class="rtl">
    
    <div class="payslip-container">
        <!-- Header -->
        <div class="header">
            <div class="company-info">
                <div class="company-name"></div>
            </div>
            <div class="company-logo">LOGO</div>
        </div>

        <!-- Employee Info Section -->
        <div class="employee-section">
            <div class="payslip-title">
                ﺐﺗاﺮﻟا ﻒﺸﻛ -
                                     2025
                            </div>

            <div class="employee-grid">
                <div class="employee-field">
                    <span class="field-label">ﻒﻇﻮﻤﻟا ﻢﺳا:</span>
                    <span
                        class="field-value">دﻮﻤﺤﻣ ﻢﻴﻫاﺮﺑإ ﺪﻤﺤﻣ</span>
                </div>
                <div class="employee-field">
                    <span class="field-label">ﻒﻇﻮﻤﻟا دﻮﻛ:</span>
                    <span class="field-value">14</span>
                </div>
                <div class="employee-field">
                    <span class="field-label">ﻲﻔﻴﻇﻮﻟا ﻰﻤﺴﻤﻟا:</span>
                    <span
                        class="field-value">ءﻼﻤﻌﻟا ﺔﻣﺪﺧ فﺮﺸﻣ</span>
                </div>
                <div class="employee-field">
                    <span class="field-label">salary_components.employee_id:</span>
                    <span class="field-value">4501</span>
                </div>
            </div>
        </div>

        <!-- Categories Section -->
        <div class="categories-section">
                                                <div class="category-block">
                        <div class="category-header">
                            <span class="category-title">
                                ﺔﻴﻤﺳﺮﻟا ﻞﻄﻌﻟا                            </span>
                            <span class="category-amount-label">ﻎﻠﺒﻤﻟا</span>
                        </div>

                        <div class="category-content">
                                                                                        <div class="component-item">
                                    <span class="component-name">
                                        ﺔﻴﻤﺳﺮﻟا ﻞﻄﻌﻟا                                    </span>
                                    <span class="component-amount">568.13 ﻪﻴﻨﺟ</span>
                                </div>
                                                            
                            <div class="category-total">
                                <span class="total-label">ﻲﻟﺎﻤﺟﻹا</span>
                                <span class="total-amount">568.13 ﻪﻴﻨﺟ</span>
                            </div>
                        </div>
                    </div>
                                    <div class="category-block">
                        <div class="category-header">
                            <span class="category-title">
                                رﻮﻀﺤﻟا ﻲﻓﺎﺿإ                            </span>
                            <span class="category-amount-label">ﻎﻠﺒﻤﻟا</span>
                        </div>

                        <div class="category-content">
                                                                                        <div class="component-item">
                                    <span class="component-name">
                                        ﻲﻓﺎﺿﻹا ﻞﻤﻌﻟا                                    </span>
                                    <span class="component-amount">179.32 ﻪﻴﻨﺟ</span>
                                </div>
                                                            
                            <div class="category-total">
                                <span class="total-label">ﻲﻟﺎﻤﺟﻹا</span>
                                <span class="total-amount">179.32 ﻪﻴﻨﺟ</span>
                            </div>
                        </div>
                    </div>
                                    <div class="category-block">
                        <div class="category-header">
                            <span class="category-title">
                                رﻮﻀﺤﻟا ﻢﺼﺧ                            </span>
                            <span class="category-amount-label">ﻎﻠﺒﻤﻟا</span>
                        </div>

                        <div class="category-content">
                                                                                        <div class="component-item">
                                    <span class="component-name">
                                        بﺎﻴﻐﻟا ﻢﺼﺧ                                    </span>
                                    <span class="component-amount">0.00 ﻪﻴﻨﺟ</span>
                                </div>
                                                                                            <div class="component-item">
                                    <span class="component-name">
                                        ﺮﻴﺧﺄﺘﻟا ﻢﺼﺧ                                    </span>
                                    <span class="component-amount">0.00 ﻪﻴﻨﺟ</span>
                                </div>
                                                            
                            <div class="category-total">
                                <span class="total-label">ﻲﻟﺎﻤﺟﻹا</span>
                                <span class="total-amount">0.00 ﻪﻴﻨﺟ</span>
                            </div>
                        </div>
                    </div>
                                    <div class="category-block">
                        <div class="category-header">
                            <span class="category-title">
                                تﻻﺪﺒﻟا                            </span>
                            <span class="category-amount-label">ﻎﻠﺒﻤﻟا</span>
                        </div>

                        <div class="category-content">
                                                                                        <div class="component-item">
                                    <span class="component-name">
                                        عوﺮﻔﻠﻟ ﻞﻘﻧ لﺪﺑ                                    </span>
                                    <span class="component-amount">936.00 ﻪﻴﻨﺟ</span>
                                </div>
                                                            
                            <div class="category-total">
                                <span class="total-label">ﻲﻟﺎﻤﺟﻹا</span>
                                <span class="total-amount">936.00 ﻪﻴﻨﺟ</span>
                            </div>
                        </div>
                    </div>
                                    </div>

        <!-- Deductions Section -->
        <div class="deductions-section">
            <div class="deductions-title">تﺎﻣﻮﺼﺨﻟا</div>
            <div class="deductions-grid">
                <div class="deduction-item">
                    <span class="deduction-label">salary_components.tax</span>
                    <span class="deduction-amount">666.39 ﻪﻴﻨﺟ</span>
                </div>
                <div class="deduction-item">
                    <span class="deduction-label">ﻲﻋﺎﻤﺘﺟﻻا ﻦﻴﻣﺄﺘﻟا</span>
                    <span class="deduction-amount">0.00 ﻪﻴﻨﺟ</span>
                </div>
            </div>
        </div>

        <!-- Net Pay Section -->
        <div class="net-pay-section">
            <div class="net-pay-title">ﺐﺗاﺮﻟا ﻲﻓﺎﺻ</div>
            <div class="net-pay-amount">9,534.43</div>
            <div class="net-pay-currency">ﻪﻴﻨﺟ</div>
        </div>
    </div>
</body>

</html>