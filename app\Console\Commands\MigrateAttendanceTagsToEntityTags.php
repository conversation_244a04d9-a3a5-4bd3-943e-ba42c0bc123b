<?php

namespace App\Console\Commands;

use App\Models\AttendanceTag;
use App\Models\EntityTag;
use App\Models\Timecard;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateAttendanceTagsToEntityTags extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:to:entityTags';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if ($this->confirm('Do you wish to continue?', true)) {
            //            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            DB::table('entity_tags')->truncate();
            //            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
            $attendanceTag = AttendanceTag::withWhereHas('attendance')->get();
            $scheduledTimecards = Timecard::with('branch')->where('from', '>', Carbon::now()->format('Y-m-d H:i:s'))->get();

            $bar = $this->output->createProgressBar(count($attendanceTag));
            $scheduledTimecardsBar = $this->output->createProgressBar(count($scheduledTimecards));

            $bar->start();
            Db::beginTransaction();

            try {
                foreach ($attendanceTag as $tag) {
                    if ($tag->name === 'absent') {
                        if ($tag->attendance->slotable_id) {
                            EntityTag::create([
                                'entity_id' => $tag->attendance->slotable_id,
                                'entity_type' => 'time_card',
                                'company' => $tag->company_id,
                                'tag' => $tag->name,
                                'created_at' => $tag->created_at,
                                'updated_at' => $tag->updated_at,
                            ]);
                        }
                    } else {
                        EntityTag::create([
                            'entity_id' => $tag->attendance_id,
                            'entity_type' => 'attendance',
                            'company' => $tag->company_id,
                            'tag' => $tag->name,
                            'created_at' => $tag->created_at,
                            'updated_at' => $tag->updated_at,
                        ]);
                    }
                    $bar->advance();
                }
                $bar->finish();

                $scheduledTimecardsBar->start();
                foreach ($scheduledTimecards as $timecard) {
                    EntityTag::create([
                        'entity_id' => $timecard->id,
                        'entity_type' => 'time_card',
                        'company' => $timecard->branch->company_id,
                        'tag' => 'scheduled',
                        'created_at' => $timecard->created_at,
                        'updated_at' => $timecard->updated_at,
                    ]);

                    $scheduledTimecardsBar->advance();
                }
                $scheduledTimecardsBar->finish();

                DB::commit();
            } catch (Exception $e) {
                \Sentry\captureException($e);
                DB::rollBack();
            }
        }
    }
}
