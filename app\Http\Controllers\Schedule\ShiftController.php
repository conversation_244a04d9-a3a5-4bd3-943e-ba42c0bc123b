<?php

namespace App\Http\Controllers\Schedule;

use App\DomainData\FilterDto;
use App\DomainData\ShiftDto;
use App\DomainData\TitleShiftDto;
use App\Http\Controllers\Controller;
use App\Jobs\SchedulePushNotificationJob;
use App\Services\LeaveManagement\CrudServices\EmployeeLeaveRequestCrudService;
use App\Services\Schedule\CrudServices\ShiftCrudService;

class ShiftController extends Controller
{
    use FilterDto, ShiftDto, TitleShiftDto {
        ShiftDto::getRules insteadof TitleShiftDto;
        TitleShiftDto::getRules as TitleShiftDto;
    }

    public function __construct(
        private ShiftCrudService $service,
        private EmployeeLeaveRequestCrudService $employeeLeaveRequestCrudService
    ) {}

    public function createMany(array $request, \stdClass &$output): void
    {
        $validator = \Validator::make($request, [
            'entity_array' => 'required|array',
            'related_objects' => 'array',
            'related_objects.*' => 'in:shiftBranch,titleShifts.title,titleShifts.titleShiftEmployees.employee',
        ]);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        $rules = $this->getRules(['name', 'type', 'start_time', 'end_time', 'shift_branch_id',
            'schedule_id', 'branch_id', 'color']);
        $rules['date_array'] = 'required|array';
        $rules['date_array.*'] = 'required|date';
        $rules['titles'] = 'required|array';
        $titleRules = $this->titleShiftDto(['title_id', 'employee_count']);

        foreach ($request['entity_array'] as $key => $entity) {

            if (! isset($entity['branch_id']) && ! is_null(config('globals.branchId'))) {
                $entity['branch_id'] = config('globals.branchId');
                $request['entity_array'][$key]['branch_id'] = config('globals.branchId');
            }

            $validator = \Validator::make($entity, $rules);
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }

            foreach ($entity['titles'] as $k => $title) {
                $validator = \Validator::make($title, $titleRules);
                if ($validator->fails()) {
                    $output->Error = $validator->messages();

                    return;
                }
            }
        }

        $this->service->createMany($request, $output);
    }

    public function create(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['date', 'name', 'type', 'start_time', 'end_time',
            'shift_branch_id', 'schedule_id', 'branch_id', 'color']);
        $rules['related_objects'] = ['array'];
        $rules['related_objects.*'] = ['in:shiftBranch,branch,titleShifts,titles'];
        $rules['titles'] = ['required', 'array'];

        if (! isset($request['branch_id']) && ! is_null(config('globals.branchId'))) {
            $request['branch_id'] = config('globals.branchId');
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $rules = $this->titleShiftDto(['title_id', 'employee_count']);

        foreach ($request['titles'] as $key => $entity) {
            $validator = \Validator::make($entity, $rules);
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }
        }

        $this->service->create($request, $output);

    }

    public function update(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['name', 'type', 'start_time', 'end_time',
            'shift_branch_id', 'schedule_id', 'branch_id', 'color']);
        $rules['id'] = 'required|numeric';
        $rules['titles'] = ['present', 'array'];
        $rules['related_objects'] = ['array'];
        $rules['related_objects.*'] = ['in:shiftBranch,titleShifts.title,titleShifts.titleShiftEmployees.employee'];

        if (! isset($request['branch_id']) && ! is_null(config('globals.branchId'))) {
            $request['branch_id'] = config('globals.branchId');
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $rules = $this->titleShiftDto(['title_id', 'employee_count']);
        $rules['id'] = 'nullable|numeric';
        foreach ($request['titles'] as $key => $entity) {
            $validator = \Validator::make($entity, $rules);
            if ($validator->fails()) {
                $output->Error = $validator->messages();

                return;
            }
        }

        $this->service->update($request, $output);
    }

    public function getByFilter(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules(['page', 'filters', 'related_objects.*', 'related_objects_count.*', 'page_size']);
        $rules['branch_id'] = 'required|numeric';

        if (! isset($request['branch_id'])) {
            $request['branch_id'] = config('globals.branchId');
        } else {
            config(['globals.branchId' => $request['branch_id']]);
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['related_objects'])) {
            $request['related_objects'] = [];
        }
        if (! isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $this->service->getByFilter($request, $output);
    }

    public function getById(array $request, \stdClass &$output): void
    {
        $rules = $this->getFilterRules(['related_objects.*', 'related_objects_count.*']);
        $rules['id'] = 'required|numeric';
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        if (! isset($request['related_objects'])) {
            $request['related_objects'] = [];
        }
        if (! isset($request['related_objects_count'])) {
            $request['related_objects_count'] = [];
        }

        $this->service->getById($request, $output);
    }

    public function delete(array $request, \stdClass &$output): void
    {
        $rules['ids'] = ['required', 'array'];
        $rules['ids.*'] = ['required', 'numeric'];
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $this->failMessages($validator->messages());

            return;
        }

        $request = $validator->validate();

        $this->service->delete($request, $output);
    }

    public function getShiftsForWorker(array $request, \stdClass &$output): void
    {
        $rules['start_date'] = 'required|date';
        $rules['end_date'] = 'required|date';
        $rules['branch_id'] = 'required|numeric';
        $rules['employee_id'] = 'required|numeric';
        $rules['related_objects'] = 'array';
        $rules['related_objects.*'] = 'in:shiftBranch,branch';

        if (! isset($request['branch_id']) && ! is_null(config('globals.branchId'))) {
            $request['branch_id'] = config('globals.branchId');
        }

        if (! isset($request['employee_id']) && ! is_null(config('globals.user')) && isset(config('globals.user')->employee_id)) {
            $request['employee_id'] = config('globals.user')->employee_id;
        }

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->service->getShiftsForWorker($request, $output);
        $this->getRestDays($request, $output);
    }

    private function getRestDays($request, \stdClass &$output): void
    {
        $restDaysRequest = [
            'employee_id' => $request['employee_id'],
            'from' => $request['start_date'],
            'to' => $request['end_date'],
        ];
        $this->employeeLeaveRequestCrudService->getRestDaysForWorker($restDaysRequest, $output);
    }

    public function notify($request, \stdClass &$output): void
    {
        $rules = [
            'branch_id' => 'required|numeric|exists:branches,id',
        ];

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $this->dispatch(new SchedulePushNotificationJob($request['branch_id']));
    }
}
