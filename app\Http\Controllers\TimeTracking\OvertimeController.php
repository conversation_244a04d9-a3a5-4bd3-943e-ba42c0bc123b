<?php

namespace App\Http\Controllers\TimeTracking;

use App\Http\Controllers\Controller;
use App\Services\TimeTracking\BusinessServices\OvertimeActionsService;
use stdClass;

class OvertimeController extends Controller
{
    public function __construct(
        private OvertimeActionsService $overtimeActionsService
    ) {}

    public function approve(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|integer';
        $rules['overtime_minutes'] = 'integer';
        $rules['reason'] = 'in:manager,cover,event,inventory';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request['action'] = 'approve';
        $request['related_objects'] = ['employeeApproves'];
        $request['related_objects_count'] = [];

        if (isset($request['overtime_minutes'])) {
            $output->overtime_minutes = $request['overtime_minutes'];
        }
        if (isset($request['reason'])) {
            $output->reason = $request['reason'];
        }

        $this->overtimeActionsService->perform($request, $output);
    }

    public function reject(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|integer';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request['action'] = 'reject';
        $request['related_objects'] = ['employeeApproves'];
        $request['related_objects_count'] = [];

        $this->overtimeActionsService->perform($request, $output);

    }

    public function edit(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|integer';
        $rules['overtime_minutes'] = 'required|integer';
        $rules['comment'] = 'string';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request['action'] = 'edit';
        $request['related_objects'] = ['employeeApproves'];
        $request['related_objects_count'] = [];

        $this->overtimeActionsService->perform($request, $output);
    }

    public function cancel(array $request, stdClass &$output): void
    {
        $rules['id'] = 'required|integer';

        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $request['action'] = 'cancel';
        $request['related_objects'] = ['employeeApproves'];
        $request['related_objects_count'] = [];

        $this->overtimeActionsService->perform($request, $output);

    }
}
