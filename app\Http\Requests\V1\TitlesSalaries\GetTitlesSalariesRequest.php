<?php

namespace App\Http\Requests\V1\TitlesSalaries;

use Illuminate\Foundation\Http\FormRequest;

class GetTitlesSalariesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {

        return [
            'page_size' => [
                'sometimes',
                'integer',
                'min:1',
            ],
            'page' => [
                'sometimes',
                'integer',
                'min:1',
            ],
            'department_ids' => [
                'array',
            ],
            'department_ids.*' => [
                'integer',
            ],
            'title_ids' => [
                'array',
            ],
            'title_ids.*' => [
                'integer',
            ],
            'search_value' => [
                'string',
            ],
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'page_size' => $this->input('page_size', 10),
        ]);
    }
}
