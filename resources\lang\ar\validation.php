<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => 'The :attribute must be accepted.',
    'accepted_if' => 'The :attribute must be accepted when :other is :value.',
    'active_url' => 'The :attribute is not a valid URL.',
    'after' => 'يجب أن يكون :attribute تاريخاً بعد :date.',
    'after_or_equal' => 'يجب أن يكون :attribute تاريخاً بعد أو يساوي :date.',
    'alpha' => 'The :attribute must only contain letters.',
    'alpha_dash' => 'The :attribute must only contain letters, numbers, dashes and underscores.',
    'alpha_num' => 'The :attribute must only contain letters and numbers.',
    'array' => 'يجب أن يكون :attribute مصفوفة.',
    'before' => 'يجب أن يكون :attribute تاريخاً قبل :date.',
    'before_or_equal' => 'يجب أن يكون :attribute تاريخاً قبل أو يساوي :date.',
    'between' => [
        'array' => 'The :attribute must have between :min and :max items.',
        'file' => 'The :attribute must be between :min and :max kilobytes.',
        'numeric' => 'The :attribute must be between :min and :max.',
        'string' => 'The :attribute must be between :min and :max characters.',
    ],
    'boolean' => 'يجب أن يكون حقل :attribute صحيح أو خطأ.',
    'confirmed' => 'The :attribute confirmation does not match.',
    'current_password' => 'The password is incorrect.',
    'date' => 'The :attribute is not a valid date.',
    'date_equals' => 'The :attribute must be a date equal to :date.',
    'date_format' => 'لا يتطابق :attribute مع التنسيق :format.',
    'declined' => 'The :attribute must be declined.',
    'declined_if' => 'The :attribute must be declined when :other is :value.',
    'different' => 'The :attribute and :other must be different.',
    'digits' => 'The :attribute must be :digits digits.',
    'digits_between' => 'The :attribute must be between :min and :max digits.',
    'dimensions' => 'The :attribute has invalid image dimensions.',
    'distinct' => 'The :attribute field has a duplicate value.',
    'doesnt_end_with' => 'The :attribute may not end with one of the following: :values.',
    'doesnt_start_with' => 'The :attribute may not start with one of the following: :values.',
    'email' => 'يجب أن يكون :attribute عنوان بريد إلكتروني صالح.',
    'ends_with' => 'The :attribute must end with one of the following: :values.',
    'enum' => 'القيمة المحددة لـ :attribute غير صالحة.',
    'exists' => 'The selected :attribute is invalid.',
    'file' => 'The :attribute must be a file.',
    'filled' => 'The :attribute field must have a value.',
    'gt' => [
        'array' => 'The :attribute must have more than :value items.',
        'file' => 'The :attribute must be greater than :value kilobytes.',
        'numeric' => 'The :attribute must be greater than :value.',
        'string' => 'The :attribute must be greater than :value characters.',
    ],
    'gte' => [
        'array' => 'The :attribute must have :value items or more.',
        'file' => 'The :attribute must be greater than or equal to :value kilobytes.',
        'numeric' => 'The :attribute must be greater than or equal to :value.',
        'string' => 'The :attribute must be greater than or equal to :value characters.',
    ],
    'image' => 'The :attribute must be an image.',
    'in' => 'The selected :attribute is invalid.',
    'in_array' => 'The :attribute field does not exist in :other.',
    'integer' => 'يجب أن يكون :attribute رقماً صحيحاً.',
    'ip' => 'The :attribute must be a valid IP address.',
    'ipv4' => 'The :attribute must be a valid IPv4 address.',
    'ipv6' => 'The :attribute must be a valid IPv6 address.',
    'json' => 'The :attribute must be a valid JSON string.',
    'lt' => [
        'array' => 'The :attribute must have less than :value items.',
        'file' => 'The :attribute must be less than :value kilobytes.',
        'numeric' => 'The :attribute must be less than :value.',
        'string' => 'The :attribute must be less than :value characters.',
    ],
    'lte' => [
        'array' => 'The :attribute must not have more than :value items.',
        'file' => 'The :attribute must be less than or equal to :value kilobytes.',
        'numeric' => 'The :attribute must be less than or equal to :value.',
        'string' => 'The :attribute must be less than or equal to :value characters.',
    ],
    'mac_address' => 'The :attribute must be a valid MAC address.',
    'max' => [
        'array' => 'The :attribute must not have more than :max items.',
        'file' => 'The :attribute must not be greater than :max kilobytes.',
        'numeric' => 'The :attribute must not be greater than :max.',
        'string' => 'The :attribute must not be greater than :max characters.',
    ],
    'max_digits' => 'The :attribute must not have more than :max digits.',
    'mimes' => 'The :attribute must be a file of type: :values.',
    'mimetypes' => 'The :attribute must be a file of type: :values.',
    'min' => [
        'array' => 'يجب أن يحتوي :attribute على :min عناصر على الأقل.',
        'file' => 'يجب أن يكون حجم :attribute :min كيلوبايت على الأقل.',
        'numeric' => 'يجب أن يكون :attribute :min على الأقل.',
        'string' => 'يجب أن يحتوي :attribute على :min أحرف على الأقل.',
    ],
    'min_digits' => 'The :attribute must have at least :min digits.',
    'multiple_of' => 'The :attribute must be a multiple of :value.',
    'not_in' => 'The selected :attribute is invalid.',
    'not_regex' => 'The :attribute format is invalid.',
    'numeric' => 'The :attribute must be a number.',
    'password' => [
        'letters' => 'The :attribute must contain at least one letter.',
        'mixed' => 'The :attribute must contain at least one uppercase and one lowercase letter.',
        'numbers' => 'The :attribute must contain at least one number.',
        'symbols' => 'The :attribute must contain at least one symbol.',
        'uncompromised' => 'The given :attribute has appeared in a data leak. Please choose a different :attribute.',
    ],
    'present' => 'The :attribute field must be present.',
    'prohibited' => 'The :attribute field is prohibited.',
    'prohibited_if' => 'The :attribute field is prohibited when :other is :value.',
    'prohibited_unless' => 'The :attribute field is prohibited unless :other is in :values.',
    'prohibits' => 'The :attribute field prohibits :other from being present.',
    'regex' => 'The :attribute format is invalid.',
    'required' => 'حقل :attribute مطلوب.',
    'required_array_keys' => 'يجب أن يحتوي حقل :attribute على إدخالات لـ: :values.',
    'required_if' => 'حقل :attribute مطلوب عندما يكون :other هو :value.',
    'required_unless' => 'حقل :attribute مطلوب ما لم يكن :other في :values.',
    'required_with' => 'حقل :attribute مطلوب عندما يكون :values موجوداً.',
    'required_with_all' => 'حقل :attribute مطلوب عندما تكون :values موجودة.',
    'required_without' => 'حقل :attribute مطلوب عندما لا يكون :values موجوداً.',
    'required_without_all' => 'حقل :attribute مطلوب عندما لا تكون أي من :values موجودة.',
    'same' => 'The :attribute and :other must match.',
    'size' => [
        'array' => 'The :attribute must contain :size items.',
        'file' => 'The :attribute must be :size kilobytes.',
        'numeric' => 'The :attribute must be :size.',
        'string' => 'The :attribute must be :size characters.',
    ],
    'starts_with' => 'يجب أن يبدأ :attribute بأحد القيم التالية: :values.',
    'string' => 'يجب أن يكون :attribute نصاً.',
    'timezone' => 'The :attribute must be a valid timezone.',
    'unique' => 'The :attribute has already been taken.',
    'uploaded' => 'The :attribute failed to upload.',
    'url' => 'The :attribute must be a valid URL.',
    'uuid' => 'The :attribute must be a valid UUID.',

    'unique_phone' => 'رقم الهاتف مع رمز البلد هذا موجود بالفعل في النظام.',
    'phone_number_format' => 'يجب أن يكون :attribute رقم هاتف صالح لرمز البلد \':country_code\'.',
    'duplicate_emergency_contact' => 'تم العثور على رقم هاتف مكرر لجهة اتصال في حالات الطوارئ: :phone. يجب أن يكون لكل جهة اتصال في حالات الطوارئ رقم هاتف فريد.',
    'is_arabic' => 'يجب أن يكون حقل :attribute باللغة العربية.',
    'is_english' => 'يجب أن يكون حقل :attribute باللغة الإنجليزية.',
    'egyptian_national_id' => 'للجنسية المصرية، يجب أن يكون الرقم القومي مكون من 14 رقم بالضبط.',
    'title_id' => 'يجب أن يكون :attribute "all" أو معرف وظيفة صالح لشركتك.',
    'branch_id' => 'يجب أن يكون :attribute "all" أو معرف فرع صالح لشركتك.',
    'spatie_roles_id' => 'يجب أن يكون :attribute معرف دور صالح لشركتك.',
    'unique_employee_number' => 'رقم الموظف موجود بالفعل في النظام.',
    'unique_national_id' => 'الرقم القومي موجود بالفعل في النظام.',
    'unique_passport_number' => 'رقم جواز السفر موجود بالفعل في النظام.',
    'unique_work_email' => 'البريد الإلكتروني للعمل موجود بالفعل في النظام.',
    'unique_personal_email' => 'البريد الإلكتروني الشخصي موجود بالفعل في النظام.',
    'department_id' => 'يجب أن يكون :attribute معرف قسم صالح لشركتك.',
    'sub_department_id' => 'يجب أن يكون :attribute "all" أو معرف قسم فرعي صالح لشركتك.',
    'emergency_contact_id' => 'يجب أن يكون :attribute معرف جهة اتصال طوارئ صالح لهذا الموظف.',
    'education_id' => 'يجب أن يكون :attribute معرف سجل تعليمي صالح لهذا الموظف.',
    'contract_id' => 'يجب أن يكون :attribute معرف عقد صالح لهذا الموظف.',

    'titles_id' => 'يجب أن يكون :attribute معرف وظيفة صالح لشركتك.',
    'branches_id' => 'يجب أن يكون :attribute معرف فرع صالح لشركتك.',
    'departments_id' => 'يجب أن يكون :attribute معرف قسم صالح لشركتك.',
    'sub_departments_id' => 'يجب أن يكون :attribute معرف قسم فرعي صالح لشركتك.',
    'employee_emergency_contacts_id' => 'يجب أن يكون :attribute معرف جهة اتصال طوارئ صالح لهذا الموظف.',
    'employee_contracts_id' => 'يجب أن يكون :attribute معرف عقد صالح لهذا الموظف.',
    'employee_education_id' => 'يجب أن يكون :attribute معرف سجل تعليمي صالح لهذا الموظف.',

    'template_name_required' => 'اسم القالب مطلوب',
    'template_name_exists' => 'اسم القالب موجود بالفعل. الرجاء اختيار اسم مختلف.',
    'category_fields_required' => 'حقول :category مطلوبة',
    'category_fields_array' => 'يجب أن تكون حقول :category عبارة عن مصفوفة',
    'field_required' => 'الحقل :field في :category مطلوب',
    'field_boolean' => 'يجب أن يكون الحقل :field في :category قيمة منطقية',
    'category_all_fields_required_when_any_selected' => 'عند تحديد أي حقل في :category، يجب تحديد جميع الحقول في هذه الفئة. الرجاء تحديد :field أو إلغاء تحديد جميع الحقول في :category.',
    'file_size_too_large' => 'حجم الملف كبير جداً، الحد الأقصى المسموح به هو :max',
    'file_empty' => 'الملف المرفوع فارغ',
    'no_data_rows' => 'لم يتم العثور على صفوف بيانات في الملف',
    'edit_operation_not_implemented' => 'عملية التحديث غير مطبقة بعد',
    'duplicate_national_id_in_excel' => 'تم العثور على رقم قومي مكرر في ملف الإكسل: :national_id',
    'duplicate_phone_in_excel' => 'تم العثور على رقم هاتف مكرر في ملف الإكسل: :phone',
    'duplicate_employee_code_in_excel' => 'تم العثور على رقم موظف مكرر في ملف الإكسل: :employee_code',
    'duplicate_passport_number_in_excel' => 'تم العثور على رقم جواز سفر مكرر في ملف الإكسل: :passport_number',
    'duplicate_work_email_in_excel' => 'تم العثور على بريد إلكتروني للعمل مكرر في ملف الإكسل: :work_email',
    'duplicate_personal_email_in_excel' => 'تم العثور على بريد إلكتروني شخصي مكرر في ملف الإكسل: :personal_email',
    'primary_phone_required_for_bulk_edit' => 'رقم الهاتف الأساسي مطلوب لعمليات التحديث المجمع',
    'at_row_number' => 'في الصف رقم :row',
    'employee_not_found_with_phone' => 'لم يتم العثور على موظف برقم الهاتف: :phone',
    'invalid_operation_type' => 'نوع العملية المحدد غير صحيح',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'birth_date' => [
            'before' => 'يجب أن يكون :attribute تاريخاً قبل :date (يجب أن يكون عمر الموظف 16 سنة على الأقل).',
        ],
        'years_of_experience' => [
            'min' => 'يجب أن تكون :attribute :min على الأقل.',
        ],
        'contract_start_date' => [
            'required' => 'حقل :attribute مطلوب.',
            'after_or_equal' => 'يجب أن يكون :attribute تاريخ بعد أو يساوي تاريخ الالتحاق.',
        ],
        'contract_duration' => [
            'required' => 'حقل :attribute مطلوب.',
        ],

        'contract_details.join_date' => [
            'required' => 'حقل :attribute مطلوب.',
        ],
        'contract_details.contracts.items.*.contract_start_date' => [
            'required' => 'حقل :attribute مطلوب.',
            'after_or_equal' => 'يجب أن يكون :attribute تاريخاً بعد أو يساوي تاريخ الالتحاق.',
            'after' => 'يجب أن يكون :attribute تاريخاً بعد تاريخ انتهاء العقد السابق.',
        ],
        'contract_details.contracts.items.*.contract_duration' => [
            'required' => 'حقل :attribute مطلوب.',
        ],
        'contract_details.contracts.items.*.contract_end_date' => [
            'required_if' => 'حقل :attribute مطلوب عندما تكون مدة العقد مخصصة.',
            'after' => 'يجب أن يكون :attribute تاريخاً بعد تاريخ بداية العقد.',
        ],
        'contract_details.contracts.items.*' => [
            'prohibited' => 'لا يُسمح بعقود بعد العقد المفتوح.',
        ],


        'emergency_contacts.0.phone' => [
            'required_with' => 'حقل :attribute مطلوب عندما يكون اسم جهة الاتصال في حالات الطوارئ أو علاقة جهة الاتصال موجود.',
        ],
        'education.0.degree_type' => [
            'required_with' => 'حقل :attribute مطلوب عندما يكون اسم الدرجة العلمية أو اسم المؤسسة التعليمية أو سنة التخرج موجود.',
        ],

        'include_contract_details' => [
            'required' => 'حقل :attribute مطلوب.',
        ],
        'contract_start_date_same_as_join_date' => [
            'required_if' => 'حقل :attribute مطلوب عندما يكون تضمين تفاصيل العقد مفعل.',
        ],

        'contract_end_date' => [
            'required' => 'حقل :attribute مطلوب.',
            'after' => 'يجب أن يكون :attribute تاريخاً بعد تاريخ بداية العقد.',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [
        'birth_date' => 'تاريخ الميلاد',
        'years_of_experience' => 'سنوات الخبرة',
        'phone' => 'رقم الهاتف',
        'national_id' => 'الرقم القومي',
        'passport_number' => 'رقم جواز السفر',
        'work_email' => 'البريد الإلكتروني للعمل',
        'personal_email' => 'البريد الإلكتروني الشخصي',
        'employee_number' => 'رقم الموظف',
        'join_date' => 'تاريخ الالتحاق',
        'rehire_date' => 'تاريخ إعادة التوظيف',
        'contract_start_date' => 'تاريخ بداية العقد',
        'contract_duration' => 'مدة العقد',
        'contract_end_date' => 'تاريخ نهاية العقد',

        'basic_info.first_name_ar' => 'الاسم الأول (عربي)',
        'basic_info.second_name_ar' => 'الاسم الثاني (عربي)',
        'basic_info.third_name_ar' => 'الاسم الثالث (عربي)',
        'basic_info.fourth_name_ar' => 'الاسم الرابع (عربي)',
        'basic_info.fifth_name_ar' => 'الاسم الخامس (عربي)',
        'basic_info.first_name_en' => 'الاسم الأول (إنجليزي)',
        'basic_info.second_name_en' => 'الاسم الثاني (إنجليزي)',
        'basic_info.third_name_en' => 'الاسم الثالث (إنجليزي)',
        'basic_info.fourth_name_en' => 'الاسم الرابع (إنجليزي)',
        'basic_info.fifth_name_en' => 'الاسم الخامس (إنجليزي)',
        'basic_info.employee_number' => 'رقم الموظف',
        'basic_info.rehire_date' => 'تاريخ إعادة التوظيف',
        'basic_info.is_trackable' => 'قابل للتتبع',

        'personal_details.nationality' => 'الجنسية',
        'personal_details.national_id' => 'الرقم القومي',
        'personal_details.birth_date' => 'تاريخ الميلاد',
        'personal_details.passport_number' => 'رقم جواز السفر',
        'personal_details.gender' => 'النوع',
        'personal_details.address' => 'العنوان',
        'personal_details.place_of_birth' => 'مكان الميلاد',
        'personal_details.religion' => 'الديانة',
        'personal_details.other_religion' => 'ديانة أخرى',
        'personal_details.marital_status' => 'الحالة الاجتماعية',
        'personal_details.military_status' => 'الحالة العسكرية',
        'personal_details.number_kids' => 'عدد الأطفال',
        'personal_details.years_of_experience' => 'سنوات الخبرة',
        'personal_details.notes' => 'ملاحظات',

        'contact_details.phone_country_code' => 'رمز البلد للهاتف',
        'contact_details.phone' => 'رقم الهاتف',
        'contact_details.secondary_phone_country_code' => 'رمز البلد للهاتف الثانوي',
        'contact_details.secondary_phone' => 'رقم الهاتف الثانوي',
        'contact_details.work_email' => 'البريد الإلكتروني للعمل',
        'contact_details.personal_email' => 'البريد الإلكتروني الشخصي',

        'contract_details.join_date' => 'تاريخ الالتحاق',
        'contract_details.employment_type' => 'نوع التوظيف',
        'contract_details.contracts.items.*.contract_start_date' => 'تاريخ بداية العقد',
        'contract_details.contracts.items.*.contract_duration' => 'مدة العقد',
        'contract_details.contracts.items.*.contract_end_date' => 'تاريخ نهاية العقد',

        'emergency_contacts.0.phone' => 'هاتف جهة الاتصال في حالات الطوارئ',
        'emergency_contacts.0.name' => 'اسم جهة الاتصال في حالات الطوارئ',
        'emergency_contacts.0.relation' => 'علاقة جهة الاتصال في حالات الطوارئ',
        'contact_details.emergency_contacts.items.*.phone' => 'هاتف جهة الاتصال في حالات الطوارئ',
        'contact_details.emergency_contacts.items.*.name' => 'اسم جهة الاتصال في حالات الطوارئ',
        'contact_details.emergency_contacts.items.*.relation' => 'علاقة جهة الاتصال في حالات الطوارئ',

        'education.0.degree_type' => 'نوع الدرجة العلمية',
        'education.0.degree_name' => 'اسم الدرجة العلمية',
        'education.0.institution_name' => 'اسم المؤسسة التعليمية',
        'education.0.graduation_year' => 'سنة التخرج',
        'education.items.*.degree_type' => 'نوع الدرجة العلمية',
        'education.items.*.degree_name' => 'اسم الدرجة العلمية',
        'education.items.*.institution_name' => 'اسم المؤسسة التعليمية',
        'education.items.*.graduation_year' => 'سنة التخرج',

        'first_name_ar' => 'الاسم الأول (عربي)',
        'second_name_ar' => 'الاسم الثاني (عربي)',
        'third_name_ar' => 'الاسم الثالث (عربي)',
        'fourth_name_ar' => 'الاسم الرابع (عربي)',
        'fifth_name_ar' => 'الاسم الخامس (عربي)',
        'first_name_en' => 'الاسم الأول (إنجليزي)',
        'second_name_en' => 'الاسم الثاني (إنجليزي)',
        'third_name_en' => 'الاسم الثالث (إنجليزي)',
        'fourth_name_en' => 'الاسم الرابع (إنجليزي)',
        'fifth_name_en' => 'الاسم الخامس (إنجليزي)',
        'gender' => 'النوع',
        'phone_country_code' => 'رمز البلد للهاتف',
        'title_id' => 'المسمى الوظيفي',
        'role_id' => 'الدور',
        'branch_id' => 'الفرع',
        'managed_department_ids' => 'الإدارات المُدارة',
        'managed_sub_department_ids' => 'الإدارات الفرعية المُدارة',
        'managed_branch_ids' => 'الفروع المُدارة',
        'nationality' => 'الجنسية',
        'address' => 'العنوان',
        'place_of_birth' => 'مكان الميلاد',
        'religion' => 'الديانة',
        'other_religion' => 'ديانة أخرى',
        'marital_status' => 'الحالة الاجتماعية',
        'military_status' => 'الحالة العسكرية',
        'number_kids' => 'عدد الأطفال',
        'notes' => 'ملاحظات',
        'secondary_phone_country_code' => 'رمز البلد للهاتف الثانوي',
        'secondary_phone' => 'رقم الهاتف الثانوي',

        'include_contract_details' => 'تضمين تفاصيل العقد',
        'contract_start_date_same_as_join_date' => 'تاريخ بداية العقد نفس تاريخ الالتحاق',

        'emergency_contacts.*.phone' => 'هاتف جهة الاتصال في حالات الطوارئ',
        'emergency_contacts.*.name' => 'اسم جهة الاتصال في حالات الطوارئ',
        'emergency_contacts.*.relation' => 'علاقة جهة الاتصال في حالات الطوارئ',
        'emergency_contacts.*.phone_country_code' => 'رمز البلد لهاتف جهة الاتصال في حالات الطوارئ',
        'contact_details.emergency_contacts.items.*.phone_country_code' => 'رمز البلد لهاتف جهة الاتصال في حالات الطوارئ',
        'education.*.degree_type' => 'نوع الدرجة العلمية',
        'education.*.degree_name' => 'اسم الدرجة العلمية',
        'education.*.institution_name' => 'اسم المؤسسة التعليمية',
        'education.*.graduation_year' => 'سنة التخرج',

        'management_scope.managed_department_ids' => 'الأقسام المُدارة',
        'management_scope.managed_sub_department_ids' => 'الأقسام الفرعية المُدارة',
        'management_scope.managed_branch_ids' => 'الفروع المُدارة',
        'management_scope.managed_department_ids.*' => 'الأقسام المُدارة',
        'management_scope.managed_sub_department_ids.*' => 'الأقسام الفرعية المُدارة',
        'management_scope.managed_branch_ids.*' => 'الفروع المُدارة',

        'contract_details.contracts.items' => 'عقود الموظف',
        'contract_details.contracts.delete_ids' => 'العقود المراد حذفها',
        'education.items' => 'المؤهلات التعليمية',
        'education.delete_ids' => 'المؤهلات المراد حذفها',
        'contact_details.emergency_contacts.items' => 'جهات الاتصال في حالات الطوارئ',
        'contact_details.emergency_contacts.delete_ids' => 'جهات الاتصال المراد حذفها',

        'is_trackable' => 'قابل للتتبع',
        'employment_type' => 'نوع التوظيف',
        'emergency_contact_name' => 'اسم جهة الاتصال في حالات الطوارئ',
        'emergency_contact_phone' => 'هاتف جهة الاتصال في حالات الطوارئ',
        'emergency_contact_relation' => 'علاقة جهة الاتصال في حالات الطوارئ',
    ],

];
