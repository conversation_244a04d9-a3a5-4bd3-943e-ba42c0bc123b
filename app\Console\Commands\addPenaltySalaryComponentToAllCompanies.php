<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class addPenaltySalaryComponentToAllCompanies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add-penalty-salary-component-to-all-companies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companies = \App\Models\Company::all();
        foreach ($companies as $company) {
            $companyId = $company->id;
            $penaltySalaryComponentsCategory = \App\Models\SalaryComponentsCategory::updateOrCreate([
                'company_id' => $companyId,
                'name' => \App\Util\PayrollUtil::PENALTY,
            ],
                [
                    'is_addition' => 0,
                    'order' => null,
                    'is_taxable' => 1,
                ]);

            if (isset($penaltySalaryComponentsCategory)) {
                \App\Models\SalaryComponent::updateOrCreate([
                    'company_id' => $companyId,
                    'name' => \App\Util\PayrollUtil::PENALTY,
                ],
                    [
                        'salary_components_category_id' => $penaltySalaryComponentsCategory->id,
                        'is_variable' => 0,
                        'order' => null,
                        'is_automated' => 0,
                    ]);
            }
        }

    }
}
