<?php

namespace App\Services\V1\Missions;

use App\Enums\EntityTags\AttendanceTags;
use App\Enums\Missions\MissionsEnum;
use App\Enums\V2\WorkTypes\WorkTypesEnum;
use App\Exceptions\UnprocessableException;
use App\Models\ExcusesBalance;
use App\Models\Title;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\V1\Attendance\AttendanceRepository;
use App\Repositories\V1\Attendance\TimecardRepository;
use App\Repositories\V1\CompanySettings\TitleRepository;
use App\Repositories\V1\EmployeeRepository;
use App\Repositories\V1\EntityTagRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use App\Repositories\V1\Missions\MissionRepository;
use App\Repositories\V1\Schedule\TimecardTypeRepository;
use App\Repositories\V1\WorkFlow\WorkflowApprovalCycleRepository;
use App\Services\BaseService;
use App\Services\TimeTracking\BusinessServices\ClockInService;
use App\Services\TimeTracking\BusinessServices\ClockOutService;
use App\Services\V1\LeaveManagement\LeavesSplitterService;
use App\Traits\CICOHelper;
use App\Traits\V1\AttendancePoliciesTrait;
use App\Traits\V1\EmployeeRequestsTrait;
use App\Traits\V1\Missions\MissionsTrait;
use App\Traits\WorkflowTrait;
use App\Util\UserWorkTypesUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Jobs\HandleActionOnMissionJob;
use App\Jobs\AssignApprovalsJob;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;
use Workflow\WorkflowStub;

class MissionService extends BaseService
{
    use AttendancePoliciesTrait, CICOHelper, EmployeeRequestsTrait, MissionsTrait, WorkflowTrait, PrepareAssignRequestCycleDataTrait;

    public function __construct(
        private MissionRepository $missionRepository,
        private SystemSettingRepository $systemSettingRepository,
        private TitleRepository $titleRepository,
        private TimecardTypeRepository $timecardTypeRepository,
        private PayrollsRepository $payrollsRepository,
        private EmployeeRepository $employeeRepository,
        private AttendanceRepository $attendanceRepository,
        private TimecardRepository $timecardRepository,
        private EmployeeLeaveRequestRepository $employeeLeaveRequestRepository,
        private EntityTagRepository $entityTagRepository,
        private WorkflowApprovalCycleRepository $workflowApprovalCycleRepository,
        private ClockInService $clockInService,
        private ClockOutService $clockOutService,
    ) {}

    public function toggleMissionsPolicy()
    {
        $policy = $this->systemSettingRepository->findFirstByKey('key', MissionsEnum::POLICY->value);
        if (isset($policy)) {
            $policy->update(['value' => ! $policy->value]);
            $policy->refresh();
        } else {
            $policy = $this->systemSettingRepository->add([
                'key' => MissionsEnum::POLICY->value,
                'value' => 1,
                'as_of_date' => date('Y-m-d'),
            ]);
        }

        if ($policy->value == 1) {

            $this->timecardTypeRepository->updateOrCreate(
                ['name' => MissionsEnum::FULL_DAY->value],
                ['name' => MissionsEnum::FULL_DAY->value, 'is_default' => 0]);
            $this->timecardTypeRepository->updateOrCreate(
                ['name' => MissionsEnum::HALF_DAY_AFTER_WORK->value],
                ['name' => MissionsEnum::HALF_DAY_AFTER_WORK->value, 'is_default' => 0]);
            $this->timecardTypeRepository->updateOrCreate(
                ['name' => MissionsEnum::HALF_DAY_BEFORE_WORK->value],
                ['name' => MissionsEnum::HALF_DAY_BEFORE_WORK->value, 'is_default' => 0]);
        } else {
            $this->timecardTypeRepository->deleteByNames(MissionsEnum::MissionTypes);
        }

        return $policy;
    }

    public function assignTitlesToMissionsPolicy($data)
    {
        $policy = $this->systemSettingRepository->findFirstByKey('key', MissionsEnum::POLICY->value);

        if (! isset($policy) || $policy->value != 1) {
            throw new UnprocessableException('Missions policy is not enabled');
        }

        $allTitles = $this->titleRepository->get()->pluck('id')->toArray();
        $titlesToDisable = array_diff($allTitles, $data['title_ids']);
        $this->titleRepository->multipleUpdate($titlesToDisable, ['apply_missions' => false]);

        $this->titleRepository->multipleUpdate($data['title_ids'], ['apply_missions' => true]);
    }

    public function getMissionPolicy()
    {
        $policy = $this->systemSettingRepository->findFirstByKey('key', MissionsEnum::POLICY->value);

        $titles = [];
        if ($policy?->value == 1) {
            $titles = $this->titleRepository->getTitlesApplyMissionPolicy();
        }
        $allCompanyTitlesCount = Title::count();

        return ['apply_policy' => boolval($policy?->value) ?? false,
            'titles' => $titles,
            'all_titles' => count($titles) == $allCompanyTitlesCount];
    }

    public function requestMission($data)
    {
        $mission = $this->missionRepository->add($data);

        $requesterRoleIds = auth()->user()->roles->pluck('id')->toArray();

        AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('missions', $mission, $requesterRoleIds));

        return $mission;
    }

    public function editMission($missionId, $data)
    {
        $mission = $this->missionRepository->findOrFail($missionId);

        $mission->update($data);

        return $mission;
    }

    public function getOverlappingAttendance($id)
    {
        $mission = $this->missionRepository->findOrFail($id);

        $data = $mission->toArray();

        return $this->attendanceRepository->checkOverlappingAttendnacne($data['employee_id'], -1, $data['from'], $data['to']);
    }

    public function resolveForApprovedMission($mission, $attendanceData = null)
    {
        try {
            DB::transaction(function () use ($mission, $attendanceData) {
                $timecardType = $this->timecardTypeRepository->findFirstByKeys(['name' => $mission->mission_type, 'company_id' => $mission->company_id]);
                $mission->load(['employee', 'timecard.attendance.clockIn', 'timecard.attendance.attendanceDeductions']);

                $parentTimecard = null;
                $missionDate = Carbon::parse($mission->from)->toDateString();
                if ($mission->mission_type != MissionsEnum::FULL_DAY->value) {
                    $parentTimecard = $this->timecardRepository->getClosestTimecardForEmployeeOnDate($mission->employee_id, $missionDate);
                    Log::info('Parent timecard: '.$parentTimecard?->id);
                }

                $timecard = $this->timecardRepository->add([
                    'employee_id' => $mission->employee_id,
                    'branch_id' => $mission->employee->branch_id,
                    'created_by_id' => $mission?->employeeRequest?->requested_by,
                    'from' => $mission->from,
                    'to' => $mission->to,
                    'timecard_type_id' => $timecardType?->id,
                    'parent_timecard_id' => $parentTimecard?->id,
                    'any_location' => ($mission->mission_type == MissionsEnum::FULL_DAY->value) ?? false,
                    'out_of_company_in_location' => $mission->location,
                    'out_of_company_out_location' => $mission->location,
                ]);

                Log::info('Timecard created: '.$timecard->id);

                $mission->update(['timecard_id' => $timecard->id]);
                $mission->refresh();

                $this->handleOverlappingTimecardsAndLeaves($mission);

                $this->addEntityTag($timecard->id, $mission->company_id, 'time_card', AttendanceTags::MISSION);
                if (isset($attendanceData)) {
                    $this->addAttendanceOnMissionTimecard($mission, $attendanceData);
                } else {
                    if ($mission->mission_type == MissionsEnum::FULL_DAY->value) {
                        $tag = $missionDate < Carbon::now()->toDateString() ? AttendanceTags::ABSENT_WITHOUT_PERMISSION : AttendanceTags::SCHEDULED;
                        $this->addEntityTag($timecard->id, $mission->company_id, 'time_card', $tag);
                    }
                }
            });
        } catch (\Exception $e) {
            Log::info('Failed to approve mission !! ');
            Log::error($e);

        }
    }

    public function resolveForCancelledMission($mission)
    {
        $missionDate = Carbon::parse($mission->from)->toDateString();
        $attendance = $mission?->timecard?->attendance;
        $parentTimecard = $mission?->timecard?->parentTimecard;

        if (isset($attendance)) {
            if ($mission->mission_type == MissionsEnum::HALF_DAY_BEFORE_WORK->value) {
                $this->handleCancelHalfDayBeforeWork($attendance, $parentTimecard);
            } elseif ($mission->mission_type == MissionsEnum::HALF_DAY_AFTER_WORK->value) {
                $this->handleCancelHalfDayAfterWork($attendance, $parentTimecard);
            }
        }

        $mission?->timecard?->delete();

        if ($mission->mission_type == MissionsEnum::FULL_DAY->value) {
            $this->reAddTimecardForStaticWorkTypeEmployeesOnDate($mission->employee, $missionDate);
        }

    }

    public function reAddTimecardForStaticWorkTypeEmployeesOnDate($employee, $date): void // date format Y-m-d
    {
        $employeeWorkTypePolicy = $employee->title->workTypePolicy;
        if ($employeeWorkTypePolicy->work_days_type == UserWorkTypesUtil::DYNAMIC_ON_SITE) {  // only add for static work type employees
            return;
        }
        $defaultTimecardType = $this->timecardTypeRepository->getDefaultTimeCardTypeByCompanyId($employee->company_id); // append company id to the query because this function will be called inside the workflow "no auth user"
        $startTime = $employeeWorkTypePolicy->start_time;
        $endTime = $employeeWorkTypePolicy->end_time;
        $timecard = $this->timecardRepository->add(
            [
                'name' => 'Timecard',
                'employee_id' => $employee->id,
                'branch_id' => $employee->branch_id,
                'from' => Carbon::parse($date.' '.$startTime)->toDateTimeString(),
                'to' => Carbon::parse($date.' '.$endTime)->toDateTimeString(),
                'timecard_type_id' => $defaultTimecardType->id,
                'required_ci_branch_id' => $employee->branch_id,
                'required_co_branch_id' => $employee->branch_id,
            ]
        );

        $tag = $date < Carbon::now()->toDateString() ? AttendanceTags::ABSENT_WITHOUT_PERMISSION : AttendanceTags::SCHEDULED;

        $this->addEntityTag($timecard->id, $employee->company_id, 'time_card', $tag);

    }

    private function handleCancelHalfDayBeforeWork($attendance, $parentTimecard)
    {
        $attendanceDeduction = $attendance->attendanceDeductions()->withTrashed()->first();
        if (isset($attendanceDeduction)) {
            $attendanceDeduction->restore();
            if (isset($parentTimecard)) {
                $this->addEntityTag(
                    $attendance->id,
                    $attendance->company_id,
                    'attendance',
                    AttendanceTags::LATE->value
                );
            }
        }
    }

    private function handleCancelHalfDayAfterWork($attendance, $parentTimecard)
    {
        $clockOut = $attendance->clockOut;
        if ($clockOut->date < $parentTimecard->to) {
            $this->addEntityTag(
                $attendance->id,
                $attendance->company_id,
                'attendance',
                AttendanceTags::EARLY_CLOCK_OUT->value
            );
        }
    }

    private function addEntityTag($attendanceId, $companyId, $type, $tag)
    {
        $this->entityTagRepository->add([
            'tag' => $tag,
            'entity_id' => $attendanceId,
            'entity_type' => $type,
            'company' => $companyId,
        ]);
    }

    public function handleOverlappingTimecardsAndLeaves($mission)
    {
        $missionDate = Carbon::parse($mission->from)->toDateString();
        $timecards = $this->timecardRepository->getTimecardsWithoutAttendanceOnDateForEmployee($mission->employee_id, $missionDate, [$mission->timecard_id]);
        $leave = $this->employeeLeaveRequestRepository->approvedLeaveExistsAroundDateForEmployee($mission->employee_id, $missionDate);
        $parentTimecard = $mission?->timecard?->parentTimecard;

        config(['globals.company' => $mission->company]); // for company rule scope on models with company_id // no auth user because this is inside a workflow
        config(['globals.user' => $mission->employee->user]); // for company rule scope on models with company_id // no auth user because this is inside a workflow

        Log::info('in handleOverlappingTimecardsAndLeaves the mission id is '.$mission->id.' parent timecard id is '.$parentTimecard?->id.' has attendance ? '.isset($parentTimecard?->attendance));

        if ($mission->mission_type == MissionsEnum::FULL_DAY->value) {
            $this->handleApproveFullDayMission($mission, $timecards, $leave);
        } elseif (isset($parentTimecard) && isset($parentTimecard->attendance)) {
            $this->handleApproveHalfDayMission($mission, $parentTimecard);
        }
    }

    public function actionOnMission($id, $actionType, $attendanceData = null): void
    {
        $mission = $this->missionRepository->findOrFail($id);
        $roleIds = config('globals.user')->roles->pluck('id')->toArray();

        $userCanTakeAnAction = $this->canTakeAnAction($roleIds, $mission->id, config('globals.REQUEST_WORKFLOW_TYPES.MISSIONS'), $actionType);
        if (! $userCanTakeAnAction) {
            throw new UnprocessableException(trans('messages.can_not_take_this_action'));
        }

        if ($actionType != 'cancel') {
            if ($mission->status != config('globals.REQUEST_STATUSES.PENDING')) {
                throw new UnprocessableException(trans('messages.workflow_is_completed'));
            }
        }

        $this->doAnAction($actionType);
        if($this->checkRequestIsCompleted($mission->employeeRequest)){
            Log::info('in actionOnMission the mission id is ' . $mission->id . ' and the action type is ' . $actionType);
            Log::info('mission request approval cycle is completed');
            $finalStatus = $this->getFinalStatus($mission->employeeRequest, $actionType);
            $this->updateEntity($mission, $finalStatus);
            $this->updateRequest($mission->employeeRequest, $finalStatus);
            
            $jobData = [
                'mission_request' => $mission,
                'final_status' => $finalStatus,
                'attendance_data' => $attendanceData,
            ];

            HandleActionOnMissionJob::dispatch($jobData);
        }
    }

    public function validateMissionRequest($data)
    {
        $policy = $this->systemSettingRepository->findFirstByKey('key', MissionsEnum::POLICY->value);
        if (! isset($policy) || $policy->value == 0) {
            throw new UnprocessableException('Missions policy is not enabled');
        }

        $employee = $this->employeeRepository->findOrFail($data['employee_id']);
        $employee->load('title.workTypePolicy');
        if ($employee->title->apply_missions == false ||
            ($employee->title->workTypePolicy->work_days_type == WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value && $data['mission_type'] != MissionsEnum::FULL_DAY->value)) {
            throw new UnprocessableException(trans('messages.title_doesnt_apply_missions'));
        }

        if ($employee->status == config('globals.EMPLOYEE_STATUSES.TERMINATED')) {
            throw new UnprocessableException('A mission can not be added for terminated employee');
        }

        $partialLeaveExists = $this->employeeLeaveRequestRepository->getEmployeePartialLeaveAroundDate($data['employee_id'], $data['from']) !== null;
        if ($partialLeaveExists) {
            throw new UnprocessableException(trans('messages.partial_leave_exists'));
        }

        $missionDate = Carbon::parse($data['from'])->toDateString();

        $this->validatePayrollIsntFinalized($missionDate);

        $this->validateNoMissionTimecardOnDateForEmployee($data['employee_id'], $missionDate);

        $restdayLeaveTypeId = $employee->company->rest_day_leave_id;
        $timecard = $this->timecardRepository->getEmployeeTimecardOnDate($data['employee_id'], $missionDate);

        if ($data['mission_type'] != MissionsEnum::FULL_DAY->value) {

            if (! isset($timecard)) {
                throw new UnprocessableException('You must have a timecard on the mission date to request a half day mission');
            }
            $this->validateNoIntersectingRestday($data['employee_id'], $restdayLeaveTypeId, $missionDate);

            $this->validateNoIntersectingLeave($data['employee_id'], $missionDate);

            $this->validateHalfDayMissionTime($timecard, $data);
        }
    }

    public function validateEditMissionRequest($missionId)
    {
        $mission = $this->missionRepository->findOrFail($missionId);
        if ($mission->employee_id != auth()->user()->employee_id) {
            throw new UnprocessableException(trans('Only the employee of the mission can edit it'));
        }

        $approverTookAction = $this->workflowApprovalCycleRepository->actionHasBeenTaken($missionId, config('globals.REQUEST_WORKFLOW_TYPES.MISSIONS'));
        if ($approverTookAction || $mission->status != config('globals.REQUEST_STATUSES.PENDING')) {
            throw new UnprocessableException(trans('messages.cant_edit_mission_approver_took_action'));
        }
    }

    public function validateApproveMissionAction($id, $data)
    {
        $mission = $this->missionRepository->findOrFail($id);
        if ($data['with_attendance'] == true) {
            if ($mission->mission_type != MissionsEnum::FULL_DAY->value) {
                throw new UnprocessableException('You can add attendance to only full day missions');
            }
            $missionDate = Carbon::parse($mission->from);
            if ($missionDate->isFuture()) {
                throw new UnprocessableException('You can add attendance to only past missions');
            }
            if ($missionDate->toDateString() != Carbon::parse($data['clock_in'])->toDateString()) {
                throw new UnprocessableException('You can add attendance to only missions on the same day');
            }
        }

        $data = $mission->toArray();
        $data['from'] = $mission->from;
        $data['to'] = $mission->to;
        $this->validateMissionRequest($data);

        $this->validateOverlappingAttendance($data);
    }

    public function validateCancelMissionAction($id)
    {
        $mission = $this->missionRepository->findOrFail($id);

        $data = $mission->toArray();

        $this->validatePayrollIsntFinalized($data['from']);

        $this->validateMissionDoesntHaveAttendance($mission);
    }

    private function handleApproveHalfDayMission($mission, $parentTimecard)
    {
        $actualShiftStart = Carbon::parse($mission->to)->max(Carbon::parse($parentTimecard->from));
        $actualShiftEnd = Carbon::parse($mission->from)->min(Carbon::parse($parentTimecard->to));

        Log::info('in handle approve hald day mission for mission with id '.$mission->id.' the actual shift start is '.$actualShiftStart->toDateTimeString());

        if ($mission->mission_type == MissionsEnum::HALF_DAY_BEFORE_WORK->value && $actualShiftStart->toDateTimeString() > $parentTimecard->from) {
            $clockInDifference = $actualShiftStart->diffInMinutes(Carbon::parse($parentTimecard->attendance->clockIn->date));

            Log::info('clock in difference in handleApproveHalfDayMission is '.$clockInDifference);

            $oldDeduction = $parentTimecard?->attendance?->attendanceDeductions()->first();

            if (isset($oldDeduction)) {
                Log::info('in handleApproveHalfDayMission old deduction is '.$oldDeduction->id);

                if ($oldDeduction->excuse_applied) {
                    $latestExcuse = ExcusesBalance::where('employee_id', $mission->employee_id)->latest()->first();
                    ExcusesBalance::insert([
                        'employee_id' => $mission->employee_id,
                        'company_id' => $mission->company_id,
                        'policy_id' => $latestExcuse->policy_id,
                        'base_hours' => $latestExcuse->base_hours,
                        'remaining_hours' => $latestExcuse->remaining_hours + ceil($clockInDifference / 60),
                        'used_excuse_hours' => 0,
                        'from_date' => $latestExcuse->from_date,
                        'to_date' => $latestExcuse->to_date,
                    ]);
                }
                $oldDeduction->delete();
            }

            $deduction = $this->createAttendanceDeductionIfExist($mission->employee, $clockInDifference, $parentTimecard->attendance);

            Log::info('in handleApproveHalfDayMission new deduction is '.$deduction->id);

            if (! isset($deduction)) {
                $lateTag = $this->attendanceRepository->getTagOfAttendance($parentTimecard->attendance, config('globals.ATTENDANCE_TAGS.LATE'));
                $excusedTag = $this->attendanceRepository->getTagOfAttendance($parentTimecard->attendance, config('globals.ATTENDANCE_TAGS.EXCUSED'));
                if (isset($lateTag)) {
                    $lateTag->delete();
                }
                if (isset($excusedTag)) {
                    $excusedTag->delete();
                }
            }
        } elseif ($mission->mission_type == MissionsEnum::HALF_DAY_AFTER_WORK->value && $actualShiftEnd->toDateTimeString() < $parentTimecard->to) {
            $earlyClockOutTag = $this->attendanceRepository->getTagOfAttendance($parentTimecard->attendance, config('globals.ATTENDANCE_TAGS.EARLY_CLOCK_OUT'));
            if (isset($earlyClockOutTag)) {
                $earlyClockOutTag->delete();
            }
        }
    }

    private function handleApproveFullDayMission($mission, $timecards, $leave)
    {
        Log::info('in handleApproveFullDayMission the timecards');
        $timecards->each(function ($timecard) {
            $timecard->delete();
        });

        if (isset($leave)) {
            $leaveSplitterService = app(LeavesSplitterService::class);
            $leaveSplitterService->addMissionTimecardOnLeave($leave, $mission->from);
        }
    }

    public function addAttendanceOnMissionTimecard($mission, $data)
    {

        $dummyOutput = new \stdClass;

        config(['globals.company' => $mission->company]); // for company rule scope on models with company_id // no auth user because this is inside a workflow
        config(['globals.user' => $mission->employeeRequest->requestedBy->user]); // for company rule scope on models with company_id // no auth user because this is inside a workflow

        Log::info('in addAttendanceOnMissionTimecard the mission id is '.$mission->id.' employee id is '.$mission?->employee_id.' timecard id is '.$mission?->timecard_id);

        $clockInData = $this->prepareCicoOnMissionData($mission->employee_id, $mission->timecard_id, $data['clock_in']);

        Log::info('in addAttendanceOnMissionTimecard the clock in data is '.json_encode($clockInData));

        $this->clockInService->perform($clockInData, $dummyOutput);
        if (isset($dummyOutput->Error)) {
            Log::info($dummyOutput->Error[1]);
        }

        $clockOutData = $this->prepareCicoOnMissionData($mission->employee_id, $mission->timecard_id, $data['clock_out']);

        Log::info('in addAttendanceOnMissionTimecard the clock out data is '.json_encode($clockOutData));

        $this->clockOutService->perform($clockOutData, $dummyOutput);
        if (isset($dummyOutput->Error)) {
            Log::info($dummyOutput->Error[1]);
        }
    }

    public function validateNoPendingMissionRequest($data)
    {
        $missionDate = Carbon::parse($data['from'])->toDateString();
        $pendingMission = $this->missionRepository->getPendingMissionOnDateForEmployee($data['employee_id'], $missionDate);
        if (isset($pendingMission)) {
            throw new UnprocessableException(trans('messages.pending_mission_on_date'));
        }
    }
}
