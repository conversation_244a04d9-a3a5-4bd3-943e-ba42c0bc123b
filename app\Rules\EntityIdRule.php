<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class EntityIdRule implements Rule
{
    /**
     * The table name to check against.
     *
     * @var string
     */
    protected $tableName;

    /**
     * Optional employee ID to filter by (for employee-related entities).
     *
     * @var int|null
     */
    protected $employeeId;

    /**
     * Create a new rule instance.
     *
     * @param string $tableName The table name to check against
     * @param int|null $employeeId Optional employee ID to filter by
     */
    public function __construct(string $tableName, ?int $employeeId = null)
    {
        $this->tableName = $tableName;
        $this->employeeId = $employeeId;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if ($value === 'all' || $value === 'none') {
            return true;
        } elseif (is_numeric($value)) {
            $query = DB::table($this->tableName)
                ->where('id', $value)
                ->where('company_id', Auth::user()->company_id)
                ->whereNull('deleted_at');

            // If employee ID is provided, ensure the entity belongs to this employee
            if ($this->employeeId) {
                $query->where('employee_id', $this->employeeId);
            }

            return $query->exists();
        }

        return false;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        $entityName = str_replace('_', ' ', $this->tableName);
        $entityName = rtrim($entityName, 's'); // Remove trailing 's' to get singular form
        
        return __("validation.{$this->tableName}_id") ?: 
            "The selected :attribute must be a valid {$entityName} ID for your company.";
    }
}
