<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\Scope;
use Illuminate\Console\Command;

class FillScopes extends Command
{
    protected $signature = 'fill:scopes';

    protected $description = 'Create scopes for each company';

    public function handle()
    {
        $companies = Company::all();

        foreach ($companies as $company) {

            $existingScopes = Scope::where('company_id', $company->id)->exists();

            if (! $existingScopes) {
                Scope::create([
                    'key' => 'department',
                    'name_en' => 'Department',
                    'name_ar' => 'قسم',
                    // 'is_system_defined' => 1,
                    'company_id' => $company->id,
                ]);

                Scope::create([
                    'key' => 'branch',
                    'name_en' => 'Branch',
                    'name_ar' => 'فرع',
                    // 'is_system_defined' => 1,
                    'company_id' => $company->id,
                ]);

                Scope::create([
                    'key' => 'company',
                    'name_en' => 'Company',
                    'name_ar' => 'شركة',
                    // 'is_system_defined' => 1,
                    'company_id' => $company->id,
                ]);

                Scope::create([
                    'key' => 'sub_department',
                    'name_en' => 'Sub Department',
                    'name_ar' => 'جزء من قسم',
                    // 'is_system_defined' => 1,
                    'company_id' => $company->id,
                ]);
            }

        }

        $this->info('Scopes created successfully.');
    }
}
