<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberUtil;

class PhoneNumberByCountryRule implements Rule
{
    protected ?string $countryCode;

    protected ?string $originalCountryInput;

    /**
     * Create a new rule instance.
     *
     * @param  string  $countryInput  ISO code (EG) or Dial code (+20)
     */
    public function __construct(string $countryInput)
    {
        $this->originalCountryInput = $countryInput;

        // Determine if it's an ISO code (2 letters) or a dial code (+ prefix)
        if ($this->isDialCode($countryInput)) {
            $this->countryCode = $this->mapCountryDialCodeToISO($countryInput);
        } else {
            $this->countryCode = strtoupper($countryInput);
        }

        /*if (! $this->countryCode) {
            throw new InvalidArgumentException("Invalid or unsupported country code: {$countryInput}");
        }*/
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     */
    public function passes($attribute, $value): bool
    {
        $phoneUtil = PhoneNumberUtil::getInstance();
        try {
            $number = $phoneUtil->parse($value, $this->countryCode);

            return $phoneUtil->isValidNumber($number);
        } catch (NumberParseException $e) {
            return false;
        }
    }

    /**
     * Get the error message.
     */
    public function message(): string
    {
        return __('validation.phone_number_format', ['country_code' => $this->originalCountryInput]);
    }

    /**
     * Check if given input is a dial code (starts with +).
     */
    protected function isDialCode(string $code): bool
    {
        return preg_match('/^\+\d{1,4}$/', $code);
    }

    /**
     * Map country dial codes to ISO codes.
     */
    protected function mapCountryDialCodeToISO(string $dialCode): ?string
    {
        $map = [
            '+1' => 'US',
            '+20' => 'EG',
            '+44' => 'GB',
            '+971' => 'AE',
            '+91' => 'IN',
            '+33' => 'FR',
            '+49' => 'DE',
            '+81' => 'JP',
            '+61' => 'AU',
            '+966' => 'SA',
            '+965' => 'KW',
            '+973' => 'BH',
            '+974' => 'QA',
            '+353' => 'IE',
            '+39' => 'IT',
            '+34' => 'ES',
            '+55' => 'BR',
            '+27' => 'ZA',
            '+82' => 'KR',
            '+7' => 'RU',
            '+86' => 'CN',
        ];

        return $map[$dialCode] ?? null;
    }
}
