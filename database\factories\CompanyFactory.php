<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Company>
 */
class CompanyFactory extends Factory
{
    protected $model = Company::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company,
            'name_en' => $this->faker->company,
            'name_ar' => $this->faker->company,
            'company_code' => str_pad($this->faker->numberBetween(0, 9999), 4, '0', STR_PAD_LEFT),
            'monthly_disbursement_day' => $this->faker->numberBetween(1, 28),
            'monthly_end_day' => $this->faker->numberBetween(1, 30),
            'tax_exemption' => 0,
            'tax_equation' => 0,
            'is_ready' => 1,
            'status' => 'active',
            'default_probation_period' => $this->faker->numberBetween(1, 6),
            'default_working_hours' => 8,
            'country_id' => Country::factory(),
            'use_foodics' => 0,
            'industry_id' => null,
        ];
    }
}
