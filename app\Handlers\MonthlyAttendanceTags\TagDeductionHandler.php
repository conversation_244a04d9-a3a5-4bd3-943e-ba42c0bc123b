<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Models\Timecard;
use Illuminate\Support\Carbon;

class TagDeductionHandler implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        return [
            'name' => $employeeAttendance->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($employeeAttendance->from)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        $deductionValue = ! empty($employeeAttendance->attendance->attendanceDeductions[0]->updated_value) ?
        $employeeAttendance->attendance->attendanceDeductions[0]->updated_value :
        $employeeAttendance->attendance->attendanceDeductions[0]->deduction_value;

        return isset($tags[$employeeId]['tags']['deductions']) ?
            $tags[$employeeId]['tags']['deductions']['count'] +
            $deductionValue
            : $deductionValue;
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return $employeeAttendance instanceof Timecard &&
            $employeeAttendance->attendance &&
            count($employeeAttendance->attendance->attendanceDeductions) > 0;
    }

    public function handleTagUnit(): string
    {
        return 'days';
    }
}
