<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Models\Timecard;
use Illuminate\Support\Carbon;

class ManagerCicoHandler implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        $tag = $employeeAttendance->attendance->entityTags
            ->filter(function ($tag) {
                return $tag->tag === 'manager_clock_in' || $tag->tag === 'manager_clock_out';
            })
            ->first();

        return [
            'name' => $employeeAttendance->name,
            'id' => $employeeAttendance->id,
            'date' => Carbon::parse($employeeAttendance->from)->format('Y-m-d'),
            'from_date' => null,
            'to_date' => null,
        ];
    }

    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        return isset($tags[$employeeId]['tags']['manager_cico']) ? $tags[$employeeId]['tags']['manager_cico']['count'] + 1 : 1;
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        if ($employeeAttendance instanceof Timecard
            && $employeeAttendance->attendance &&
            $employeeAttendance->attendance->entityTags) {
            $tags = $employeeAttendance->attendance->entityTags->pluck('tag');

            return $tags->contains('manager_clock_in') || $tags->contains('manager_clock_out');
        }

        return false;
    }

    public function handleTagUnit(): string
    {
        return 'days';
    }
}
