<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class <PERSON>el extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('automated:clock:out')->cron('2,32 * * * *')
            ->sentryMonitor(monitorSlug: config('globals.SCHEDULE_SLUG_AUTO_CLOCK_OUT'));
        $schedule->command('trigger:noInYet:timeCards')->cron('1,16,31,46 * * * *')
            ->sentryMonitor(monitorSlug: config('globals.SCHEDULE_SLUG_NOT_IN_YET_TIME_CARD'));
        $schedule->command('leave:balances:fill')->monthlyOn(1, '04:00')
            ->sentryMonitor(monitorSlug: config('globals.SCHEDULE_SLUG_LEAVE_BALANCE_FILL'));
        $schedule->command('trigger:absent:timeCards')->cron('2,32 * * * *')
            ->sentryMonitor(monitorSlug: config('globals.SCHEDULE_SLUG_ABSENT_TIME_CARD'));
        $schedule->command('clock:in:push:notification')->everyMinute()
            ->sentryMonitor(monitorSlug: config('globals.CLOCK_IN_PUSH_NOTIFICATION'));
        $schedule->command('clock:out:push:notification')->everyMinute()
            ->sentryMonitor(monitorSlug: config('globals.CLOCK_OUT_PUSH_NOTIFICATION'));
        $schedule->command('branch:week:start:day')->dailyAt('18:00')
            ->sentryMonitor(monitorSlug: config('globals.BRANCH_WEEK_START_DAY_PUSH_NOTIFICATION'));
        $schedule->command('run:scheduled:transfers')->dailyAt('01:00')
            ->sentryMonitor(monitorSlug: config('globals.SCHEDULE_SLUG_TRANSFERS'));

        $schedule->command('change:files_statuses')->dailyAt('05:00');
        // $schedule->command('update:employee-file-status')->daily();
        $schedule->command('fill:scopes')->daily();
        $schedule->command('fill:uuid')->daily();
        $schedule->command('reset:num:device-id-changes')->monthlyOn(1, '05:00');
        // ->sentryMonitor(monitorSlug: config('globals.SCHEDULE_SLUG_LEAVE_BALANCE_FILL'));
        $schedule->command('change:employees_status')->dailyAt('05:00')
            ->sentryMonitor(monitorSlug: config('globals.SCHEDULE_SLUG_UPDATE_EMPLOYEE_STATUS'));

        $schedule->command('fill:public_holiday_absences')->dailyAt('05:05');
        //            ->sentryMonitor(monitorSlug: config('globals.SCHEDULE_SLUG_UPDATE_EMPLOYEE_STATUS'));

        // $schedule->command('send:unaccounted-days-notifications')->dailyAt('12:00');
        // TODO add sentry monitor to it when released to production

        // run yearly in 1 january
        $schedule->command('update:years-of-exprience')->cron('0 12 1 1 *');

        $schedule->command('app:handle-employee-probation')->dailyAt('05:00');

        // $schedule->command('integrations:foodics:sync-employees')->everyFiveMinutes();
        // $schedule->command('integrations:foodics:sync-shifts')->everyFiveMinutes();
        $schedule->command('send:schedule:notifications')->cron('0 2-23/6 * * *'); // this command will run on 2am,  8am, 2pm, 8pm of everyday
        // make this command  activitylog:clean  run every 3 months
        $schedule->command('activitylog:clean clock_in')->monthly();
        $schedule->command('activitylog:clean manual_cico')->monthly();
        $schedule->command('activitylog:clean clock_out')->monthly();
        $schedule->command('activitylog:clean assign_employee')->monthly();
        $schedule->command('activitylog:clean next_slot_updated')->monthly();
        $schedule->command('app:check-terminated-employees')->dailyAt('05:00');
        $schedule->command('app:add-any-location-balance')->monthlyOn(1, '05:00');
        $schedule->command('app:generate-timecards-to-static-titles')->monthlyOn(1, '05:00');
        $schedule->command('app:change-title')->dailyAt('05:00');
        $schedule->command('app:generate-bills')->monthlyOn(2, '05:00');

    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');
        $this->load(__DIR__.'/Commands/Integrations');

        require base_path('routes/conpsole.php');
    }
}
