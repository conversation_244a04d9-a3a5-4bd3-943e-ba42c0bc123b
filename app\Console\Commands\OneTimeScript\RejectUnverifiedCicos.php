<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Cico;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RejectUnverifiedCicos extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reject:unverified-cico';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This to remove unverified cico records for Eden company before 2023-10-26';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            Cico::whereHas('employee', function ($q) {
                $q->where('company_id', 8);
            })->where('status', 'unverified')->whereDate('date', '<', '2023-10-26')->update(['status' => 'rejected']);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
