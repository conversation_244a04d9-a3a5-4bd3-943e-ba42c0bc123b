<?php

namespace App\Http\Controllers\CompanySetup;

use App\DomainData\UserDto;
use App\Http\Controllers\Controller;
use App\Rules\PhoneNumber;
use App\Rules\PhoneNumberByCountryRule;
use App\Services\CompanySetup\BusinessServices\CompanyRegisterService;
use App\Services\CompanySetup\BusinessServices\EmployeeRegisterService;
use App\Services\CompanySetup\BusinessServices\ResetPasswordService;
use App\Services\CompanySetup\BusinessServices\SendVerificationCodeService;
use App\Services\CompanySetup\BusinessServices\UserLoginService;
use App\Services\CompanySetup\BusinessServices\VerifyMobileUserService;
use App\Services\CompanySetup\BusinessServices\VerifyUserService;
use Illuminate\Validation\Rule;

class LoginRegisterController extends Controller
{
    use UserDto;

    public function __construct(
        private CompanyRegisterService      $userRegisterService,
        private VerifyUserService           $verifyUserService,
        private UserLoginService            $userLoginService,
        private ResetPasswordService        $resetPasswordService,
        private SendVerificationCodeService $sendVerificationCodeService,
        private EmployeeRegisterService     $employeeRegisterService,
        private VerifyMobileUserService     $verifyMobileUserService
    )
    {
    }

    public function register(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['email']);
        $rules['name'] = ['required', 'string'];
        $rules['phone'] = ['required', new PhoneNumber];
        $rules['url'] = ['required', 'string'];
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        $this->userRegisterService->perform($request, $output);
    }

    public function mobileRegister(array $request, \stdClass &$output): void
    {
        $deviceType = request()->header('Device-Type', 'unknown');
        if ($deviceType == 'tablet' || !isset($request['country_code'])) {
            $request['country_code'] = '+20';
        }
        $rules['country_code'] = ['required', 'string'];
        $rules['phone'] = ['required', new PhoneNumberByCountryRule($request['country_code'])];
        $rules['device_id'] = ['string'];
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();
        if (!isset($request['device_id'])) {
            $request['device_id'] = null;
        }
        $this->employeeRegisterService->perform($request, $output);
    }

    public function verify(array $request, \stdClass &$output): void
    {
        $rules['code'] = ['required', 'string'];
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $request = $validator->validate();

        $this->verifyUserService->perform($request, $output);
    }

    public function verifyMobile(array $request, \stdClass &$output): void
    {
        $deviceType = request()->header('Device-Type', 'unknown');

        if ($deviceType == 'tablet' || !isset($request['country_code'])) {
            $request['country_code'] = '+20';
        }
        $countryCode = $request['country_code'] ?? '+20';

        // Define rules, including phone rule with dynamic country code
        $rules['phone'] = ['required', new PhoneNumberByCountryRule($countryCode)];
        $rules['code'] = ['required', 'string'];
        $rules['country_code'] = ['nullable', 'string'];

        $validator = \Validator::make($request, $rules);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $validatedRequest = $validator->validate();

        $this->verifyMobileUserService->perform($validatedRequest, $output);
    }

    public function login(array $request, \stdClass &$output): void
    {

        $validator = \Validator::make($request, [
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ]);

        if ($validator->fails()) {
            $output->Error = $this->failMessages($validator->messages());

            return;
        }
        $request = $validator->validate();

        $this->userLoginService->perform($request, $output);

    }

    public function resetPassword(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['password']);
        $rules['old_password'] = 'string';
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->resetPasswordService->perform($request, $output);
    }

    public function sendValidationCode(array $request, \stdClass &$output): void
    {
        $validator = \Validator::make($request, [
            'code' => ['required', 'string'],
        ]);
        if ($validator->fails()) {
            $output->Error = $this->failMessages($validator->messages());

            return;
        }
        $request = $validator->validate();

        $this->sendVerificationCodeService->perform($request, $output);
    }

    public function updateToken(array $request, \stdClass &$output): void
    {
        $validator = \Validator::make($request, [
            'token' => ['required', 'string'],
            'device_type' => ['required', 'string', Rule::in(['ios', 'android'])],
        ]);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }
        $user = auth()->user();
        $user->device_token = $request['token'];
        $user->device_type = $request['device_type'];
        $user->save();
    }

    public function updateLang(array $request, \stdClass &$output): void
    {
        $validator = \Validator::make($request, [
            'lang' => ['required', 'string', Rule::in(['en', 'ar'])],
        ], [
            'lang.in' => 'The :attribute must be either "en" or "ar".',
        ]);

        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $user = config('globals.user');
        $user->lang = $request['lang'];
        $user->save();
    }
}
