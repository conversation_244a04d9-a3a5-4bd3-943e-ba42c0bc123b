<?php

namespace App\Handlers;

use DateInterval;
use DatePeriod;
use DateTime;

trait HandlerHelper
{
    public function generateDaysFromTwoDates($from, $to)
    {
        $start = new DateTime($from);
        $end = new DateTime($to);
        // Modify end date to include the last day in the period, because DatePeriod excludes the end date.
        $end->modify('+1 day');

        $interval = new DateInterval('P1D');
        $dateRange = new DatePeriod($start, $interval, $end);

        $dates = [];
        foreach ($dateRange as $date) {
            $dates[$date->format('Y-m-d')] = false;
        }

        return $dates;
    }


    public function sortEntryByDate($employeeId, &$entries)
    {
        $entriesArr = $entries[$employeeId]['entries'] ?? [];
        if (!is_null($entries) && is_array($entries) && count($entries) > 0) {
            usort($entriesArr, function ($a, $b) {
                $dateA = new DateTime($a['date']);
                $dateB = new DateTime($b['date']);

                return $dateA <=> $dateB;
            });
        }
        $entries[$employeeId]['entries'] = $entriesArr;
    }
}
