<?php

namespace App\DomainData;

trait RoleDto
{
    public function getRules(array $fields = []): array
    {
        $data = [
            'name' => 'required|string',
            'name_ar' => 'required|string',
            'name_en' => 'string',
            'is_system_role' => 'boolean',
            'description' => 'string',
            'guard_name' => 'string',
            'company_id' => 'numeric',
            'is_super_admin' => 'numeric',
        ];

        if (count($fields) == 0) {
            return $data;
        }

        return array_intersect_key($data, array_flip($fields));
    }

    public function initializeRoleDto(): void
    {
        $this->fillable = array_keys($this->getRules());
    }
}
