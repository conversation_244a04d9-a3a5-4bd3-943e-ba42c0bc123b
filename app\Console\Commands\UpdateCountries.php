<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\Country;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use RuntimeException;

class UpdateCountries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-arabic-countries';

    /**
     * The console command description.
     */
    protected $description = 'Add Arabic countries with English names, correct timezones, and dial codes into the countries table, and set all companies to Egypt.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Starting to insert Arabic countries into the countries table...');

        DB::transaction(function () {
            $arabicCountries = [
                ['name_ar' => 'مصر', 'name_en' => 'Egypt', 'timezone' => 'Africa/Cairo', 'dial_code' => '+20'],
                ['name_ar' => 'السعودية', 'name_en' => 'Saudi Arabia', 'timezone' => 'Asia/Riyadh', 'dial_code' => '+966'],
                ['name_ar' => 'الإمارات', 'name_en' => 'United Arab Emirates', 'timezone' => 'Asia/Dubai', 'dial_code' => '+971'],
                ['name_ar' => 'الكويت', 'name_en' => 'Kuwait', 'timezone' => 'Asia/Kuwait', 'dial_code' => '+965'],
                ['name_ar' => 'قطر', 'name_en' => 'Qatar', 'timezone' => 'Asia/Qatar', 'dial_code' => '+974'],
                ['name_ar' => 'البحرين', 'name_en' => 'Bahrain', 'timezone' => 'Asia/Bahrain', 'dial_code' => '+973'],
                ['name_ar' => 'عمان', 'name_en' => 'Oman', 'timezone' => 'Asia/Muscat', 'dial_code' => '+968'],
                ['name_ar' => 'الأردن', 'name_en' => 'Jordan', 'timezone' => 'Asia/Amman', 'dial_code' => '+962'],
                ['name_ar' => 'فلسطين', 'name_en' => 'Palestine', 'timezone' => 'Asia/Hebron', 'dial_code' => '+970'],
                ['name_ar' => 'لبنان', 'name_en' => 'Lebanon', 'timezone' => 'Asia/Beirut', 'dial_code' => '+961'],
                ['name_ar' => 'سوريا', 'name_en' => 'Syria', 'timezone' => 'Asia/Damascus', 'dial_code' => '+963'],
                ['name_ar' => 'العراق', 'name_en' => 'Iraq', 'timezone' => 'Asia/Baghdad', 'dial_code' => '+964'],
                ['name_ar' => 'اليمن', 'name_en' => 'Yemen', 'timezone' => 'Asia/Aden', 'dial_code' => '+967'],
                ['name_ar' => 'السودان', 'name_en' => 'Sudan', 'timezone' => 'Africa/Khartoum', 'dial_code' => '+249'],
                ['name_ar' => 'ليبيا', 'name_en' => 'Libya', 'timezone' => 'Africa/Tripoli', 'dial_code' => '+218'],
                ['name_ar' => 'المغرب', 'name_en' => 'Morocco', 'timezone' => 'Africa/Casablanca', 'dial_code' => '+212'],
                ['name_ar' => 'تونس', 'name_en' => 'Tunisia', 'timezone' => 'Africa/Tunis', 'dial_code' => '+216'],
                ['name_ar' => 'الجزائر', 'name_en' => 'Algeria', 'timezone' => 'Africa/Algiers', 'dial_code' => '+213'],
                ['name_ar' => 'موريتانيا', 'name_en' => 'Mauritania', 'timezone' => 'Africa/Nouakchott', 'dial_code' => '+222'],
            ];

            foreach ($arabicCountries as $countryData) {
                Country::updateOrCreate(
                    ['name_ar' => $countryData['name_ar']],
                    $countryData
                );

                $this->info("Inserted/Updated {$countryData['name_ar']} (English: {$countryData['name_en']}, Timezone: {$countryData['timezone']}, Dial Code: {$countryData['dial_code']})");
            }

            $egypt = Country::where('name_en', 'Egypt')->first();

            if (! $egypt) {

                throw new RuntimeException('Egypt not found in countries table after insertion!');
            }

            Company::query()->update(['country_id' => $egypt->id]);

            $this->info("All companies updated to belong to Egypt (country_id = {$egypt->id}).");
        });

        $this->info('Arabic countries inserted and companies updated successfully!');
    }
}
