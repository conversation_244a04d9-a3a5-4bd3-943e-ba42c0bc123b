<?php

namespace App\Console\Commands;

use App\Models\ActivityLog;
use App\Models\EmployeeRequest;
use App\Traits\V1\NotificationRedirection;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixSuperAdminStatusRequests extends Command
{
    use NotificationRedirection;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-super-admin-requests 
        {company_id? : Optional company ID to filter requests}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update status of requests based on activity logs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("\n🚀 Starting status fix process for super admin requests...");
        $this->line('----------------------------------------');

        try {
            $companyId = $this->argument('company_id') ?? null;

            $requests = EmployeeRequest::whereNotNull('decider_admin_id')
                ->whereDate('updated_at', '2025-05-22')
                ->when(! empty($companyId), fn ($q) => $q->where('company_id', $companyId))
                ->whereIn('requestable_type', ['attendance_overtime', 'penalty'])
                ->get();

            $counter = 0;

            foreach ($requests as $request) {
                DB::beginTransaction();

                $activityLog = ActivityLog::where('subject_id', $request->id)
                    ->where('subject_type', 'employee_request')
                    ->where('causer_type', 'App\Models\User')
                    ->where('event', 'updated')
                    ->orderBy('id', 'desc')
                    ->first();
                if (empty($activityLog)) {
                    $counter++;
                    DB::rollBack();

                    continue;
                }

                $status = $activityLog->properties['attributes']['status'];

                $this->line("\n🔍 Processing request ID: {$request->id}");
                $this->info("🕵️  Old employee status: {$request->status}");
                $this->info("🕵️  Old entity status: {$request->requestable->status}");

                $request->update(['status' => $status]);
                $request->requestable->update(['status' => $status]);

                $this->info("✅ New status applied: {$request->requestable->status}");

                ActivityLog::where('subject_id', $request->id)
                    ->where('subject_type', 'employee_request')
                    ->whereNull('causer_type')
                    ->where('event', 'updated')
                    ->delete();

                DB::commit();
            }

            $this->info('✅ Fix complete!');
            $this->info("🔢 Total requests processed:   {$requests->count()}");
            $this->info("⚠️  Requests with missing logs: {$counter}");

        } catch (\Exception $e) {
            $this->error("\n❌ Error encountered: ".$e->getMessage());
            $this->warn('⏪ Rolling back database changes...');
            DB::rollBack();

            Log::error('FixRequestsStatus failed: '.$e->getMessage());

            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
