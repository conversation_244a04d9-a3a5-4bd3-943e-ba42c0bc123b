<?php

namespace App\Http\Requests\V1\Billing;

use Illuminate\Foundation\Http\FormRequest;

class CancelContractRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'contract_end_date' => [
                'required',
                'date',
                'after_or_equal:today',
            ],

        ];
    }
}
