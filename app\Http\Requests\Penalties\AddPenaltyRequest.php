<?php

namespace App\Http\Requests\Penalties;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;

class AddPenaltyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'employee_id' => [
                'required',
                'integer',
                (new Exists('employees', 'id'))->where(function ($query) {
                    $query->where('company_id', auth()->user()->company_id) // company rule doesn't apply here
                        ->whereNull('deleted_at');
                }),
            ],
            'penalty_group_id' => [
                'integer',
                (new Exists('penalty_groups', 'id'))->where(function ($query) {
                    $query->where('company_id', auth()->user()->company_id) // company rule doesn't apply here
                        ->whereNull('deleted_at');
                }),
            ],
            'reason' => 'required|string',
            'date' => 'required|date_format:Y-m-d',
        ];
    }
}
