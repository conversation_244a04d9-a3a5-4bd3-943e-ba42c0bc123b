<?php

namespace App\Http\Middleware;

use App\Exceptions\UnauthorizedException;
use App\Models\User;
use App\Traits\QueriesHelper;
use App\Util\ScopeUtil;
use Closure;
use Illuminate\Http\Request;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;

class handleAppConfigs
{
    use QueriesHelper;

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        //        $this->authByFaceId($request->header('face-id'));
        $user = auth()->user();
        $company = auth()->user()->company;

        if (! is_null($user->employee_id)) {
            config(['globals.branchId' => $user->employee->branch_id]);
        }

        $userRoles = auth()->user()?->roles;

        $isSuperAdmin = $userRoles->contains('is_super_admin', 1);
        $userRoleIds = $userRoles->pluck('id')->toArray() ?? [];

        $scopeKey = $this->getUserHighestScopeKey(auth()->user());

        $scopeBranchIds = [];
        $scopeDepartmentIds = [];
        $scopeSubDepartmentIds = [];

        $isMeScope = $scopeKey == ScopeUtil::ME_SCOPE;

        if (! $isMeScope) {
            $scopeBranchIds = $user->employee->branches->pluck('id')->toArray();

            $scopeDepartmentIds = $scopeKey == ScopeUtil::DEPARTMENT_SCOPE ?
                $user->employee->managedDepartments->pluck('id')->toArray()
                : $user->company->departments->pluck('id')->toArray();

            $scopeSubDepartmentIds = $scopeKey == ScopeUtil::SUB_DEPARTMENT_SCOPE ?
                $user->employee->managedSubDepartments->pluck('id')->toArray()
                : $user->company->subDepartments->pluck('id')->toArray();
        }

        config(['globals.user' => $user]);
        config(['globals.company' => $company]);
        config(['globals.rest_day_leave_id' => $company->rest_day_leave_id]);
        config(['globals.annual_leave_id' => $company->annual_leave_id]);
        config(['globals.sick_leave_id' => $company->sick_leave_id]);
        config(['globals.scope_branch_ids' => $scopeBranchIds]);
        config(['globals.scope_department_ids' => $scopeDepartmentIds]);
        config(['globals.scope_sub_department_ids' => $scopeSubDepartmentIds]);
        config(['globals.user_role_ids' => $userRoleIds]);
        config(['globals.scope_key' => $scopeKey]);
        config(['globals.is_super_admin' => $isSuperAdmin]);

        return $next($request);
    }

    public function authByFaceId($faceId = null)
    {
        if (! is_null($faceId)) {
            $user = User::where('face_id', $faceId)->first();
            if (! isset($user)) {
                throw new UnauthorizedException('Unauthorized face id');
            }
            // Get the current token
            $token = JWTAuth::getToken();

            // Invalidate the current token (logs out the current user)
            JWTAuth::invalidate($token);

            $expirationTime = 60 * 24 * 700;
            JWTAuth::factory()->setTTL($expirationTime);
            JWTAuth::fromUser($user);
            auth()->login($user);
        }
    }
}
