<?php

namespace App\Console\Commands;

use App\Exports\EmployeeBranchesExport;
use App\Models\User;
use App\Services\ExportEmployeeBranchesService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ExportAllUsersWithMoreThanOnePermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'export:users-with-more-than-one-permission';

    private ExportEmployeeBranchesService $exportEmployeeBranchesService;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(
        ExportEmployeeBranchesService $exportEmployeeBranchesService
    ) {

        $this->exportEmployeeBranchesService = $exportEmployeeBranchesService;
        try {
            $usersWithMoreThanOnePermission = User::withoutGlobalScopes()->get()->filter(function ($user) {
                $permissions = $user->getAllPermissions();

                return count($permissions) > 1;
            });
            $data = $this->exportEmployeeBranchesService->prepareData($usersWithMoreThanOnePermission);

            $s3Path = $this->generateFilePath();
            Excel::store(new EmployeeBranchesExport($data), $s3Path, 's3');

            $downloadableLink = $this->generateDownloadableLink($s3Path);

            $this->info("File has been exported successfully. Download link: {$downloadableLink}");

        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
        }

    }

    public function generateFilePath()
    {
        $date = Carbon::now()->toDateTimeLocalString();
        $fileName = "EmployeeBranches({$date}).xlsx";

        return "excels/{$fileName}";

    }

    public function generateDownloadableLink(string $s3Path)
    {
        return Storage::disk('s3')->url($s3Path);
    }
}
