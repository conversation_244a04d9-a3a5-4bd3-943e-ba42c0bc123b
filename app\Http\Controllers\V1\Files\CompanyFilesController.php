<?php

namespace App\Http\Controllers\V1\Files;

use App\Http\Controllers\Controller;
use App\Http\Requests\V1\AddCompanyFileRequest;
use App\Http\Requests\V1\EditCompanyFileRequest;
use App\Http\Requests\V1\GetCompanyFilesByFilterRequest;
use App\Http\Requests\V1\GetCompanyFilesForWorkerRequest;
use App\Services\V1\Files\CompanyFilesService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CompanyFilesController extends Controller
{
    protected $companyFilesService;

    public function __construct(CompanyFilesService $companyFilesService)
    {
        $this->companyFilesService = $companyFilesService;

    }

    public function add(AddCompanyFileRequest $request)
    {
        $data = $request->validated();

        // Initialize a variable to store the count

        DB::beginTransaction();
        try {

            $companyFiles = $this->companyFilesService->saveFile($data);
            DB::commit();
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => []],
            HttpStatusCodeUtil::OK, ' Company File Uploaded successfully'
        );
    }

    public function getCompanyFilesForWorker(GetCompanyFilesForWorkerRequest $request)
    {

        $data = $request->validated();

        // $data['employee_id'] = auth()->user()->employee_id;
        // $data['page_size'] = $data['page_size'] ?? 10;

        // $companyFiles = $this->companyFilesService->getCompanyFiles($data);

        return getResponseStructure([],
            HttpStatusCodeUtil::FORBIDDEN, 'Please update your app to the latest version');
    }

    public function getNewCompanyFilesForWorker(GetCompanyFilesForWorkerRequest $request)
    {

        $data = $request->validated();

        $data['employee_id'] = auth()->user()->employee_id;
        $data['page_size'] = $data['page_size'] ?? 10;

        $companyFiles = $this->companyFilesService->getCompanyFiles($data);

        return getResponseStructure(['data' => $companyFiles->data, 'pagination' => $companyFiles->meta],
            HttpStatusCodeUtil::OK, 'Worker Company Files Retrived successfully');
    }

    public function getCompanyFile(int $id)
    {
        $data['file_id'] = $id;
        $companyFile = $this->companyFilesService->getCompanyFile($id);

        return getResponseStructure(['data' => $companyFile],
            HttpStatusCodeUtil::OK, 'Company File Retrived successfully');
    }

    public function getCompanyFiles(GetCompanyFilesByFilterRequest $request)
    {
        $data = $request->validated();
        // ## to do pag 10
        $data['page_size'] = $data['page_size'] ?? 10000;
        $companyFiles = $this->companyFilesService->getCompanyFiles($data);

        return getResponseStructure(['data' => $companyFiles->data, 'pagination' => $companyFiles->meta],
            HttpStatusCodeUtil::OK, 'Company Files Retrived successfully');
    }

    public function update($id, EditCompanyFileRequest $request)
    {

        $data = $request->validated();

        $companyFile = null;

        $data['existing_attachment_ids'] = $data['existing_attachment_ids'] ?? [];

        DB::beginTransaction();
        try {
            $companyFile = $this->companyFilesService->update($id, $data);
            DB::commit();
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => $companyFile],
            HttpStatusCodeUtil::OK, 'Company File Has Successfully Updated');
    }

    public function delete(int $id)
    {
        DB::beginTransaction();
        try {
            $companyFile = $this->companyFilesService->delete($id);

            DB::commit();
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            Log::info($e->getMessage());
            DB::rollBack();
            throw $e;
        }

        return getResponseStructure(['data' => $companyFile],
            HttpStatusCodeUtil::OK, 'Company File Has Successfully Deleted');
    }
}
