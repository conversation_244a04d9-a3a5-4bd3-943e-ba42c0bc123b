<?php

namespace App\Enums\EmployeeInfo;

enum ReligionEnum: string
{
    case MUSLIM = 'muslim';
    case CHRISTIAN = 'christian';
    case OTHER = 'other';

    public static function all(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    public static function getTranslatedValues(): array
    {
        return array_map(fn ($case) => trans('employeeexport.' . $case->value), self::cases());
    }

    public static function getTranslatedValuesForBulkTemplate(): array
    {
        $cases = array_filter(self::cases(), fn ($case) => $case !== self::OTHER);
        return array_map(fn ($case) => trans('employeeexport.' . $case->value), $cases);
    }

    public static function getLabel(self $value): string
    {
        return trans('employeeexport.' . $value->value);
    }
}