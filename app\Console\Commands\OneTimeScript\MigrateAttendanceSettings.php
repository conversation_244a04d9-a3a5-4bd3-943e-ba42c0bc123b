<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\AbsenceDeductionPolicy;
use App\Models\AttendanceGroup;
use App\Models\AttendanceSetting;
use App\Models\DailyAttendancePolicy;
use App\Models\LateDeductionGroup;
use App\Models\LateDeductionGroupPolicy;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateAttendanceSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:attendance-settings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {

            $this->MigrateAttendanceSettingsTable();
            echo "Attendance Settings table migrated successfully.\n";

            $this->MigrateAttendanceGroupsTable();
            echo "Attendance Groups table migrated successfully.\n";

            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
            // dd($e);
        }
    }

    private function MigrateAttendanceSettingsTable()
    {
        $applyDeductionSettingEntities = AttendanceSetting::where('key', 'apply_deduction')->get();

        foreach ($applyDeductionSettingEntities as $applyDeductionSettingEntity) {
            $lateSetting = (['key' => 'apply_late_deduction', 'value' => $applyDeductionSettingEntity->value, 'description' => $applyDeductionSettingEntity->desciption ?? '',
                'is_used' => $applyDeductionSettingEntity->is_used, 'company_id' => $applyDeductionSettingEntity->company_id]);
            AttendanceSetting::create($lateSetting);

            $absenceSetting = (['key' => 'apply_absence_deduction', 'value' => $applyDeductionSettingEntity->value, 'description' => $applyDeductionSettingEntity->desciption ?? '',
                'is_used' => $applyDeductionSettingEntity->is_used, 'company_id' => $applyDeductionSettingEntity->company_id]);
            AttendanceSetting::create($absenceSetting);
        }
    }

    public function MigrateAttendanceGroupsTable()
    {
        $attendanceGroups = AttendanceGroup::get();

        foreach ($attendanceGroups as $attendanceGroup) {
            $absenceDeductionPolicy = (['policy_name' => $attendanceGroup->name, 'company_id' => $attendanceGroup->company_id,
                'absent_deduction_days' => $attendanceGroup->deduct_days_monthly,
                'absent_deduction_days_without_permission' => $attendanceGroup->deduct_days_without_permission]);
            $absenceDeductionPolicy = AbsenceDeductionPolicy::create($absenceDeductionPolicy);

            $relatedTitles = $attendanceGroup->titles;
            foreach ($relatedTitles as $title) {
                $title->update(['absence_deduction_policy_id' => $absenceDeductionPolicy->id]);
            }
            echo 'Migrated Attendance Group with id '.$attendanceGroup->id." with it's related titles to ABSENCE DEDUCTION POLICY "."\n";

            $lateDeductionGroup = LateDeductionGroup::create(['policy_name' => $attendanceGroup->name, 'company_id' => $attendanceGroup->company_id]);
            foreach ($relatedTitles as $title) {
                $title->update(['late_deduction_group_id' => $lateDeductionGroup->id]);
            }

            $dailyAttendancePolicies = DailyAttendancePolicy::where('attendance_group_id', $attendanceGroup->id)->orderBy('from', 'desc')
                ->orderBy('sequence', 'desc')->get();
            $deductDayssequence = [];

            $numPolicies = 0;

            $dailyAttendanceToLatePolicyMap = [];

            $dailyAttendancePolicyIdsToBeLinked = [];

            foreach ($dailyAttendancePolicies as $dailyAttendancePolicy) {
                $deductDayssequence[] = $dailyAttendancePolicy->deduct_days;

                $dailyAttendancePolicyIdsToBeLinked[] = $dailyAttendancePolicy->id;

                if ($dailyAttendancePolicy->sequence == 1) {
                    sort($deductDayssequence);
                    $lateDeductionGroupPolicyData = [
                        'from' => $dailyAttendancePolicy->from,
                        'to' => $dailyAttendancePolicy->to,
                        'company_id' => $dailyAttendancePolicy->company_id,
                        'sequence' => $deductDayssequence,
                        'late_deduction_group_id' => $lateDeductionGroup->id,
                    ];

                    $latePolicy = LateDeductionGroupPolicy::create($lateDeductionGroupPolicyData);

                    $dailyAttendanceToLatePolicyMap[$dailyAttendancePolicy->id] = $latePolicy->id;

                    foreach ($dailyAttendancePolicyIdsToBeLinked as $dailyAttendancePolicyId) {
                        $dailyAttendanceToLatePolicyMap[$dailyAttendancePolicyId] = $latePolicy->id;
                    }

                    $numPolicies++;
                    $deductDayssequence = [];
                    $dailyAttendancePolicyIdsToBeLinked = [];
                }
            }

            foreach ($dailyAttendancePolicies as $dailyAttendancePolicy) {
                $attendanceDeductions = $dailyAttendancePolicy->attendanceDeductions;
                foreach ($attendanceDeductions as $attendanceDeduction) {
                    $attendanceDeduction->update(['late_deduction_group_policy_id' => $dailyAttendanceToLatePolicyMap[$dailyAttendancePolicy->id]]);
                }

            }

            echo 'Migrated Attendance Group with id '.$attendanceGroup->id." with it's related titles and with ".$numPolicies.' policies to LATE DEDUCTION GROUP'."\n";

        }

    }
}
