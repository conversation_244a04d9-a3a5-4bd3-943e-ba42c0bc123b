<?php

namespace App\Http\Requests\V1\Loans;

use Illuminate\Foundation\Http\FormRequest;

class AddLoanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'loan_amount' => 'required|numeric|min:1',
            'duration_in_months' => 'required|integer|min:1|max:12',
            'comment' => 'string',
            'attachments' => 'array|max:5',
            'attachments.*' => 'required|mimes:pdf,doc,docx,png,jpg,jpeg|max:'.config('globals.MAX_FILE_SIZE_KB'),
        ];

    }
}
