<?php

namespace App\Repositories\V1\Employee;

use App\Models\EmployeeEducation;
use App\Repositories\BaseRepository;

class EmployeeEducationRepository extends BaseRepository
{
    public function model(): string
    {
        return EmployeeEducation::class;
    }

    public function getByEmployeeId(int $employeeId)
    {
        return $this->model->where('employee_id', $employeeId)->get();
    }
}