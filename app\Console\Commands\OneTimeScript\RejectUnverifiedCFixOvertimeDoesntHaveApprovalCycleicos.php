<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Attendance;
use App\Models\AttendanceOvertime;
use App\Models\AttendanceSetting;
use App\Repositories\Repository;
use App\Services\TimeTracking\BusinessServices\AddRequestsToEmployeeRequest;
use App\Services\TimeTracking\BusinessServices\AssignApprovalRequestToEmployeesService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RejectUnverifiedCFixOvertimeDoesntHaveApprovalCycleicos extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:overtime:no_approval_cycle';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix overtime that does not have approval cycle and ';

    public function handle()
    {
        $cicoRepository = Repository::getRepository('Cico');
        $companyId = 29;
        $attendancesExists = false;
        $attendanceSettingsExists = false;
        $titleWorkingInMinutesArr = [];
        $overtimeValues = [];
        $overtimes = [];
        DB::beginTransaction();
        try {
            $attendancesWithOvertimeTag = Attendance::whereHas('entityTags', function ($q) {
                $q->where('tag', 'overtime');
            })->where('company_id', $companyId)->get();

            $attendanceSetting = AttendanceSetting::where('company_id', $companyId)->where('key', 'apply_overtime')->first();
            if (isset($attendanceSetting) && $attendanceSetting->is_used) {
                Log::info('attendance setting exists');
                $clockOutAllowedTimePolicy = AttendanceSetting::where('key', config('globals.ATTENDANCE_SETTINGS.ALLOWED_MINUTES_TO_APPLY_OVERTIME'))->first();

                $workedDurationInMinutes = 0;
                foreach ($attendancesWithOvertimeTag as $attendance) {
                    Log::info('attendance exists');
                    $ci_id = $attendance->ci_id;
                    $clockIn = $cicoRepository->find($ci_id);

                    $clockOut = $cicoRepository->find($clockIn->paired_clock_id);
                    if (isset($clockIn) && isset($clockOut)) {
                        $workedDurationInMinutes = Carbon::parse($clockIn->date)->diffInMinutes(Carbon::parse($clockOut->date));
                    }
                    $titleWorkingInMinutes = ((optional($attendance->employee)->title->working_hours) * 60);
                    Log::info('worked duration in minutes is '.$workedDurationInMinutes.'For Employee Title'.$attendance->employee->title);
                    Log::info('title working in minutes is '.$titleWorkingInMinutes);
                    if ($workedDurationInMinutes && $workedDurationInMinutes > $titleWorkingInMinutes && isset($clockOutAllowedTimePolicy) && $clockOutAllowedTimePolicy->value < ($workedDurationInMinutes - $titleWorkingInMinutes)) {
                        $overTimeValue = max(0, $workedDurationInMinutes - $clockOutAllowedTimePolicy->value);
                        Log::info('overtime value is '.$overTimeValue);
                        $policyObj = $this->getOverTimePolicy($overTimeValue, $attendance->employee);
                        $overtime = $this->createAttendanceOvertimeIfExist($overTimeValue, $attendance, $policyObj, $attendance->employee, $companyId);
                        Log::info('overtime created ');
                    }
                }
            }
            DB::commit();
        } catch (Exception $e) {
            Log::info($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }

    public function getOverTimePolicy($overTimeValue, $employee)
    {
        $titleId = $employee->title->id;
        $overtimeGroupRepository = Repository::getRepository('OvertimeGroup');
        // if clock out difference time between overtime polices range
        $overtimeGroup = $overtimeGroupRepository->getOvertimeGroupRelatedToTitleWithOvertimePolicy($titleId, $overTimeValue);
        if (is_null($overtimeGroup)) {
            // if the worker work more than all overtime ranges, so get max policy
            $overtimeGroup = $overtimeGroupRepository->getOvertimeGroupRelatedToTitleWithMaxOvertimePolicy($titleId);
        }
        if (! is_null($overtimeGroup) and count($overtimeGroup->dailyOvertimePolicies) > 0) {
            return $overtimeGroup->dailyOvertimePolicies[0];
        }
    }

    public function createAttendanceOvertimeIfExist($overtimeMinutes, $attendance, $policyObj, $employee, $companyId)
    {
        $employeeRepository = Repository::getRepository('Employee');
        $requestCycleRepository = Repository::getRepository('RequestCycle');
        $addRequestsToEmployeeRequest = new AddRequestsToEmployeeRequest;
        $assignApprovalRequestToEmployeesService = new AssignApprovalRequestToEmployeesService;
        $dummyOutput = new \stdClass;
        if (! is_null($policyObj)) {
            $attendanceOvertimeData = $this->prepareAttendanceOvertimeData($overtimeMinutes, $attendance, $policyObj, $employee, $companyId);
            $attendanceOvertime = AttendanceOvertime::create($attendanceOvertimeData);

            if (! isset($attendanceOvertime)) {
                return;
            }

            $employee = $employeeRepository->getById($employee->id, ['branch']);
            if (is_null($employee->branch)) {
                return;
            }

            $systemOvertimeRequestData = $this->prepareEmployeeRequestData(null, $attendanceOvertime, config('globals.EMPLOYEE_REQUEST_NAMES.SYSTEM_OVERTIME'), $companyId);

            $addRequestsToEmployeeRequest->perform($systemOvertimeRequestData, $dummyOutput);

            $cycle = $requestCycleRepository->getRelatedCycle(
                'attendance_overtime'
            );

            // these titles already have a permission to do an action on overtime request
            $titlesIds = array_column($cycle->toArray(), 'title_id');

            $approvingEmployees = $employeeRepository->getEmployeesWithCommonBranchAndTitles($employee->branch->id, $titlesIds);

            if (count($approvingEmployees) > 0) {
                $overtimeRequest = $this->prepareApprovalCycleRequests($cycle, $approvingEmployees, $attendanceOvertime, $dummyOutput->employee_request->id);
                $assignApprovalRequestToEmployeesService->perform($overtimeRequest, $dummyOutput);
            }

            return $attendanceOvertime;
        }

        return [];
    }

    public function prepareAttendanceOverTimeData($overtimeMinutes, $attendance, $policyObj, $employee, $companyId)
    {

        return [
            'date' => date($attendance->date),
            'overtime_value' => $policyObj->percentage,
            'status' => 'pending',
            'employee_id' => $employee->id,
            'attendance_id' => $attendance->id,
            'daily_overtime_policy_id' => $policyObj->id,
            'company_id' => $companyId,
            'overtime_minutes' => $overtimeMinutes,
        ];
    }

    public function prepareEmployeeRequestData($requestedBy, $entity, $requestName, $companyId)
    {
        $request['request_to_be_created'] = $entity;

        $request['request_data'] = [
            'company_id' => $companyId,
            'requested_by' => $requestedBy,
            'request_name' => $requestName,
        ];

        return $request;

    }

    public function prepareApprovalCycleRequests($cycle, $employees, $entity, $employeeRequestId)
    {

        $request['request_needs_action'] = $entity;
        $request['cycle_elements'] = [];
        foreach ($employees as $employee) {
            $singleData = [];
            $singleData['company_id'] = $employee['company_id'];
            $singleData['status'] = 'pending';

            if (! isset($singleData['status_date']) || is_null($singleData['status_date'])) {
                $singleData['status_date'] = \Carbon\Carbon::parse($entity->created_at)->format('Y-m-d');
            } // leave request doesn't have a date column, the date of it is when it was created
            else {
                $singleData['status_date'] = $entity->date;
            }

            $singleData['employee_request_id'] = $employeeRequestId;

            foreach ($cycle as $cycleElement) {
                if ($cycleElement['title_id'] == $employee['title_id']) {
                    $singleData['operator'] = $cycleElement['operator'];
                    $singleData['order'] = $cycleElement['order'];
                    break;
                }
            }

            $singleRequest['single_data'] = $singleData;
            $singleRequest['employee_id'] = $employee['id'];

            array_push($request['cycle_elements'], $singleRequest);
        }

        return $request;
    }
}
