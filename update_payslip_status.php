<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Repositories\V1\Payroll\PayslipRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    // Configuration - UPDATE THESE VALUES
    $employeeIds = [1, 2, 3, 4, 5]; // Employee IDs to mark as downloaded
    $payrollData = [
        'payroll_id' => 1,    // Your payroll ID
        'month' => 12,        // Month (1-12)
        'year' => 2024        // Year
    ];

    echo "=== Payslip Status Update Script ===\n";
    echo "Employee IDs: " . implode(', ', $employeeIds) . "\n";
    echo "Payroll ID: {$payrollData['payroll_id']}\n";
    echo "Month: {$payrollData['month']}\n";
    echo "Year: {$payrollData['year']}\n";
    echo "Status: downloaded\n\n";

    // Show current status
    echo "Current status of employees:\n";
    $currentRecords = DB::table('employee_payroll_summaries')
        ->where('payroll_id', $payrollData['payroll_id'])
        ->where('month', $payrollData['month'])
        ->where('year', $payrollData['year'])
        ->whereIn('employee_id', $employeeIds)
        ->get(['employee_id', 'payslip_status']);

    if ($currentRecords->isEmpty()) {
        echo "❌ No records found for the specified criteria.\n";
        exit(1);
    }

    foreach ($currentRecords as $record) {
        echo "Employee ID {$record->employee_id}: {$record->payslip_status}\n";
    }

    echo "\nProceed with update? (y/n): ";
    $handle = fopen("php://stdin", "r");
    $confirmation = trim(fgets($handle));
    fclose($handle);

    if (strtolower($confirmation) !== 'y') {
        echo "Operation cancelled.\n";
        exit(0);
    }

    // Create repository instance
    $payslipRepository = app(PayslipRepository::class);

    // Use the existing markEmployeesAsDownloaded method
    echo "\nUpdating employee status...\n";
    $result = $payslipRepository->markEmployeesAsDownloaded($employeeIds, $payrollData);

    if ($result) {
        echo "✅ Successfully marked employees as downloaded!\n";

        // Show updated status
        echo "\nUpdated status:\n";
        $updatedRecords = DB::table('employee_payroll_summaries')
            ->where('payroll_id', $payrollData['payroll_id'])
            ->where('month', $payrollData['month'])
            ->where('year', $payrollData['year'])
            ->whereIn('employee_id', $employeeIds)
            ->get(['employee_id', 'payslip_status']);

        foreach ($updatedRecords as $record) {
            echo "Employee ID {$record->employee_id}: {$record->payslip_status}\n";
        }

        echo "\n📊 Summary:\n";
        echo "- Total employees updated: " . count($employeeIds) . "\n";
        echo "- Status changed to: downloaded\n";
        echo "- Operation completed successfully\n";

    } else {
        echo "❌ Update failed - no rows were affected.\n";
        exit(1);
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    Log::error('Payslip status update script failed', [
        'error' => $e->getMessage(),
        'employee_ids' => $employeeIds ?? [],
        'payroll_data' => $payrollData ?? []
    ]);
    exit(1);
}

echo "\n🎉 Script completed successfully!\n";