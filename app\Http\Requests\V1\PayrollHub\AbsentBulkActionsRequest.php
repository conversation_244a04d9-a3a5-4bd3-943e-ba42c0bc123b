<?php

namespace App\Http\Requests\V1\PayrollHub;

use Illuminate\Foundation\Http\FormRequest;

class AbsentBulkActionsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'employee_id' => [
                'required',
                'integer',
            ],
            'dates' => [
                'required',
                'array',
            ],
            'dates.*' => [
                'required',
                'date_format:Y-m-d',
            ],
            'company_leave_type_id' => [
                'nullable',
                'integer',
            ],
        ];
    }
}
