<?php

namespace App\Http\Requests\V1\KPIs;

use Illuminate\Foundation\Http\FormRequest;

class AddScoresPerEmployeeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'status' => 'required|string|in:finalized,drafted',
            'month' => 'required|string',
            'year' => 'required|string',
            'incentive_settings' => 'required|array',
            'incentive_settings.*.id' => 'required|integer|exists:incentive_settings,id',
            'incentive_settings.*.branch_id' => 'required|integer',
            'incentive_settings.*.incentive_parameters' => 'required|array',
            'incentive_settings.*.incentive_parameters.*.id' => 'required|integer',
            'incentive_settings.*.incentive_parameters.*.score' => 'nullable|min:0|required_if:status,finalized',
        ];
    }

    public function messages()
    {
        return [
            'incentive_settings.*.incentive_parameters.*.score.required_if' => 'The score field is required when the status is finalized.',
            'incentive_settings.*.incentive_parameters.*.score.min' => 'The score must be at least :min.',
//            'incentive_settings.*.incentive_parameters.*.score.integer' => 'The score must be an integer.',
        ];
    }
}
