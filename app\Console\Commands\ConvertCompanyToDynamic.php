<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\EmployeeLeaveBalance;
use App\Models\Payroll;
use App\Models\SystemSetting;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class ConvertCompanyToDynamic extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:convert-to-dynamic {company_id} {date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Convert company to dynamic closing payroll day.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $companyId = (int) $this->argument('company_id');
        $date = Carbon::parse($this->argument('date'));

        /** @var Company|null $company */
        $company = Company::find($companyId);

        if (! $company) {
            $this->error("Company with ID {$companyId} not found.");

            return;
        }

        DB::transaction(function () use ($company, $date) {
            $this->updateSystemSettings($company, $date);
            $this->updateDraftPayrolls($company);
            $this->updateEmployeeLeaveBalances($company);
        });

        $this->info("Company {$company->name} converted to dynamic closing day.");
    }

    private function updateSystemSettings(Company $company, Carbon $date): void
    {
        SystemSetting::updateOrCreate(
            [
                'company_id' => $company->id,
                'key' => 'apply_dynamic_closing_day',
            ],
            [
                'as_of_date' => $date->format('Y-m-d'),
            ]
        );

        SystemSetting::where('company_id', $company->id)
            ->where('key', 'payroll_monthly_closing_day')
            ->delete();
    }

    private function updateDraftPayrolls(Company $company): void
    {
        Payroll::where('company_id', $company->id)
            ->where('status', 'draft')
            ->get()
            ->each(function (Payroll $payroll) {
                $payroll->update([
                    'start' => Carbon::parse($payroll->end)->startOfMonth()->toDateString(),
                    'end' => Carbon::parse($payroll->end)->endOfMonth()->toDateString(),
                ]);
            });
    }

    private function updateEmployeeLeaveBalances(Company $company): void
    {
        if (! $company->rest_day_leave_id) {
            return;
        }

        // Eloquent batch update
        EmployeeLeaveBalance::where('company_leave_type_id', $company->rest_day_leave_id)
            ->get()
            ->each(function (EmployeeLeaveBalance $balance) {
                $balance->update([
                    'start' => Carbon::parse($balance->end)->startOfMonth()->format('Y-m-d 00:00:00'),
                    'end' => Carbon::parse($balance->end)->endOfMonth()->format('Y-m-d 23:59:59'),
                ]);
            });

    }
}
