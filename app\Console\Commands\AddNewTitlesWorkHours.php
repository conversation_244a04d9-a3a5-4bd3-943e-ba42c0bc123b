<?php

namespace App\Console\Commands;

use App\Models\Title;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddNewTitlesWorkHours extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'titles:work:hours';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            Title::query()->update(['working_hours' => 9]);
            Title::query()->whereIn('id', [48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 61, 65, 66])->update(['working_hours' => 12]);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
