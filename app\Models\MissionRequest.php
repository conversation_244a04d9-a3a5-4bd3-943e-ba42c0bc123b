<?php

namespace App\Models;

use App\Models\StateMachines\RequestState;
use App\Traits\CompanyRule;
use App\Traits\V1\TimezoneTrait;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class MissionRequest extends BaseModel
{
    use CompanyRule, LogsActivity, softDeletes, TimezoneTrait;

    protected static $timezoneColumns = ['from', 'to'];

    protected $casts = [
        'from' => 'datetime',
        'to' => 'datetime',
        'status' => RequestState::class,
    ];

    protected $guarded = ['id', 'created_at', 'updated_at'];

    // public function getFromAttribute($value)
    // {
    //     return Carbon::parse($value, 'UTC')->setTimezone(config('app.timezone'))->format('Y-m-d H:i:s');
    // }

    // public function getToAttribute($value)
    // {
    //     return Carbon::parse($value, 'UTC')->setTimezone(config('app.timezone'))->format('Y-m-d H:i:s');
    // }

    // public function setFromAttribute($value)
    // {
    //     if (is_null($value)) {
    //         return null;
    //     }
    //     $this->attributes['from'] = Carbon::parse($value, config('app.timezone'))->setTimezone('UTC')->format('Y-m-d H:i:s');
    // }

    // public function setToAttribute($value)
    // {
    //     if (is_null($value)) {
    //         return null;
    //     }
    //     $this->attributes['to'] = Carbon::parse($value, config('app.timezone'))->setTimezone('UTC')->format('Y-m-d H:i:s');
    // }

    protected static function boot()
    {
        parent::boot();

        static::created(function ($mission) {
            EmployeeRequest::create([
                'date' => $mission->from,
                'employee_id' => $mission->employee_id,
                'status' => $mission->status,
                'comment' => $mission->reason,
                'request_name' => 'mission_request',
                'requestable_id' => $mission->id,
                'requestable_type' => 'mission_request',
                'requested_by' => auth()->user()->employee_id,
                'company_id' => $mission->company_id,
            ]);
        });

    }

    public function timecard()
    {
        return $this->belongsTo(Timecard::class);
    }

    public function employeeRequest()
    {
        return $this->morphOne(EmployeeRequest::class, 'requestable')->latest('id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll();
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function employeeApproves()
    {
        return $this->morphToMany(Role::class, 'requestable', 'workflow_approval_cycle',
            'requestable_id', 'role_id')
            ->withPivot(['id', 'company_id', 'status', 'date', 'order', 'operator', 'requestable_type', 'request_workflow_id', 'branch_id', 'department_id'])
            ->withTimestamps();
    }

    public function workflow()
    {
        return $this->belongsTo(Workflow::class);
    }

    public function workflowApprovalCycles()
    {
        return $this->morphMany(WorkflowApprovalCycle::class, 'requestable');
    }
}
