<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use App\Models\EntityTag;
use App\Models\PublicHoliday;
use App\Services\V1\Holidays\PublicHolidayAbsenceService;
use App\Services\V1\Holidays\PublicHolidaysAttendanceService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessPublicHolidayOffShiftAttendances extends Command
{
    protected $signature = 'attendance:process-public-holiday-offshift {--from=2025-01-01}';

    protected $description = 'Process public holiday attendances with off_shift tags';

    private PublicHolidaysAttendanceService $publicHolidaysAttendanceService;
    private PublicHolidayAbsenceService $publicHolidayAbsenceService;

    public function __construct(
        PublicHolidaysAttendanceService $publicHolidaysAttendanceService,
        PublicHolidayAbsenceService $publicHolidayAbsenceService
    ) {
        parent::__construct();
        $this->publicHolidaysAttendanceService = $publicHolidaysAttendanceService;
        $this->publicHolidayAbsenceService = $publicHolidayAbsenceService;
    }

    public function handle()
    {
        $fromDate = $this->option('from');
        $this->info("Starting to process public holiday attendances with off_shift tags from {$fromDate}");

        $attendances = Attendance::whereHas('entityTags', function ($query) {
            $query->where('tag', 'public_holiday');
        })
        ->whereHas('entityTags', function ($query) {
            $query->where('tag', 'off_shift');
        })
        ->where('date', '>=', $fromDate)
        ->with(['employee', 'entityTags'])
        ->get();

        $this->info("Found {$attendances->count()} attendances to process");

        $processedCount = 0;
        $errorCount = 0;

        foreach ($attendances as $attendance) {
            DB::beginTransaction();
            try {
                $publicHoliday = PublicHoliday::where('start', '<=', $attendance->date)
                    ->where('end', '>=', $attendance->date)
                    ->first();

                if (!$publicHoliday) {
                    $this->warn("No public holiday found for attendance ID: {$attendance->id} on date: {$attendance->date}");
                    continue;
                }

                $this->publicHolidayAbsenceService->splitHolidayAbsenceBasedOnAttendance(
                    $attendance->employee,
                    $publicHoliday->id,
                    $attendance->date
                );

                DB::commit();
                $processedCount++;
                $this->info("Successfully processed attendance ID: {$attendance->id} for employee: {$attendance->employee->id}");
            } catch (Exception $e) {
                DB::rollBack();
                $errorCount++;
                Log::error("Error processing attendance ID: {$attendance->id}", [
                    'error' => $e->getMessage(),
                    'employee_id' => $attendance->employee->id,
                    'date' => $attendance->date
                ]);
                $this->error("Error processing attendance ID: {$attendance->id} - {$e->getMessage()}");
            }
        }

        $this->info("Processing completed. Successfully processed: {$processedCount}, Errors: {$errorCount}");
    }
} 