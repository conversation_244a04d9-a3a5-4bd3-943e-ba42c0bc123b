<?php

namespace App\Console\Commands;

use App\Models\EmployeeFile;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateEmployeeFileStatus extends Command
{
    protected $signature = 'update:employee-file-status';

    protected $description = 'Update employee file statuses';

    public function handle()
    {

        try {
            DB::beginTransaction();
            $files = EmployeeFile::whereNotNull('expiry_date')->get();

            foreach ($files as $file) {
                $expiryDate = Carbon::parse($file->expiry_date);
                $notificationPeriod = max($file->employeeFileCategory?->notification_periods);
                $differenceInDays = $expiryDate->diffInDays(Carbon::now());

                if ($expiryDate->isPast()) {
                    $file->status = 'expired';
                } elseif ($differenceInDays <= $notificationPeriod) {
                    $file->status = 'expiring_soon';
                } else {
                    $file->status = 'completed';
                }

                $file->save();
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
        }

    }
}
