<?php

namespace App\Console\Commands\OneTimeScript;

use App\Exceptions\UnprocessableException;
use App\FeatureToggles\Unleash;
use App\Jobs\OvertimeCalculationService;
use App\Models\AttendanceSetting;
use App\Models\EntityTag;
use App\Models\NewShift;
use App\Models\Timecard;
use App\Traits\CICOHelper;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RemoveDuplicateTimecards extends Command
{
    use CICOHelper;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-duplicate-timecards';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Removing duplicate timecards started');
            DB::beginTransaction();
            Timecard::whereDate('from', '2024-12-01')
                ->whereHas('entityTags', function ($q) {
                    $q->where('tag', config('globals.ATTENDANCE_TAGS.ABSENT'));
                })
                ->doesntHave('attendance')
                ->whereHas('employee', function ($q) {
                    $q->whereHas('attendance', function ($q) {
                        $q->whereDate('date', '2024-12-01');
                    });
                })
                ->delete();

            $this->info('deleting duplicate timecards finished');

            $shift = Timecard::select('id', 'employee_id', 'shift_id')
                ->whereDate('from', '>', '2024-12-01')
                ->withWhereHas('shift')
                ->first();
            // $shiftsGroupedByEmployeeId = $timecardsToProvideTheShift->groupBy('employee_id');

            $timecardsToBeChanged = Timecard::whereDate('from', '2024-12-01')
                ->where('name', 'unscheduled timecard')
                ->whereHas('attendance')
                ->with('attendance.attendanceOvertimes', 'employee.user')
                ->get();

            $this->info('count of timecards to be changed finished: '.$timecardsToBeChanged->count());
            $shiftsCount = 0;
            $shiftNotFoundCount = 0;
            foreach ($timecardsToBeChanged as $timecard) {
                $attendanceOvertime = $timecard->attendance->attendanceOvertimes?->first();
                if ($attendanceOvertime) {
                    EntityTag::where('entity_id', $attendanceOvertime->attendance_id)
                        ->where('entity_type', 'attendance')
                        ->where('tag', config('globals.ATTENDANCE_TAGS.OVERTIME'))
                        ->delete();
                    $attendanceOvertime->delete();
                }
                $timecardToBeApplied = Timecard::select('id', 'employee_id', 'from', 'to', 'name')
                    ->where('employee_id', $timecard->employee_id)
                    ->whereDate('from', '>', '2024-12-01')
                    ->where('name', '!=', 'Unscheduled Timecard')
                    ->orderBy('from', 'desc')
                    ->first();

                if (! $timecardToBeApplied) {
                    $shiftNotFoundCount++;
                    $this->info('shift not found for employee id: '.$timecard->employee_id);

                    continue;
                }

                // $shiftId = $shiftsGroupedByEmployeeId->get($timecard->employee_id)?->first()?->shift_id ?? null;
                $employee = $timecard->employee ?? null;
                if (! $employee) {
                    $this->info('employee not found for timecard id: '.$timecard->id);

                    continue;
                }
                $user = $employee->user ?? null;
                if (! $user) {
                    $this->info('user not found for employee id: '.$employee->id);

                    continue;
                }
                $shiftsCount++;
                config(['globals.user' => $user]);
                $this->assignShiftToTimecard($timecard, $timecardToBeApplied);
            }

            $this->deleteNotInYetTagBeforeToday();

            $this->info('assigning shifts to timecards finished');
            $this->info('shifts count: '.$shiftsCount);
            $this->info('shift not found count: '.$shiftNotFoundCount);
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            $this->error($th);
        }
    }

    public function assignShiftToTimecard($timecard, $timecardToBeApplied)
    {
        // $shift = NewShift::findOrFail($shiftId);
        $timecard->load('attendance');
        $timecard->load('employee');

        if (isset($timecard->shift_id) || ! isset($timecard->attendance)) {
            throw new UnprocessableException('You can only assign a shift to an off-shift timecard');
        }

        $clockInDate = Carbon::parse($timecard->attendance?->clockIn?->date);

        // $date = Carbon::parse($clockInDate);
        // $dayName = strtolower($date->format('D'));
        // $date = Carbon::parse($date)->toDateString();
        // $shiftFromTime = $shift->{$dayName.'_from_time'};
        // $timecardStart = $date.' '.$shiftFromTime;
        // $shiftDuration = $shift->{$dayName.'_duration'};
        // $timecardEnd = Carbon::parse($timecardStart)->addHours($shiftDuration)->format('Y-m-d H:i:s');
        $timecardStart = $timecardToBeApplied->from;
        $timecardEnd = $timecardToBeApplied->to;

        if ($clockInDate > $timecardStart && $clockInDate < $timecardEnd) {
            $lateDeductionPolicy = $this->getLateDeductionPolicy();
            $clockInDifferenceTime = $clockInDate->diffInMinutes($timecardStart);

            $isLate = isset($lateDeductionPolicy) && $lateDeductionPolicy->value < $clockInDifferenceTime;

            if ($isLate) {

                if (isset($timecard->attendance)) {
                    $this->createEntityTagForAttendance($timecard->attendance, config('globals.ATTENDANCE_TAGS.LATE'));
                }
                $companyId = $timecard->attendance->company_id ?? null;
                if ($lateDeductionPolicy->is_used && isset($companyId)) {

                    $deduction = $this->createAttendanceDeductionIfExist($timecard->employee, $clockInDifferenceTime, $timecard->attendance);
                    if ($deduction) {
                        $this->deleteEntityTags([config('globals.ATTENDANCE_TAGS.ON_TIME')], 'attendance', $timecard->attendance->id);
                    }
                }
            }
        }

        dispatch(new OvertimeCalculationService($timecard->employee_id, $timecard->attendance->date))
            ->onConnection(config('globals.OVERTIME_JOB.CONNECTION'))
            ->onQueue(config('globals.OVERTIME_JOB.QUEUE'))->afterCommit();

        $timecard->from = $timecardStart;
        $timecard->to = $timecardEnd;
        $timecard->name = $timecardToBeApplied?->name ?? 'Timecard';
        // $timecard->shift_id = $timecardToBeApplied?->id;
        $timecard->save();

    }

    private function getLateDeductionPolicy()
    {
        $featureFlag = $this->getAttendancePoliciesFlag();

        return $featureFlag == 0 ?
        AttendanceSetting::where('key', config('globals.ATTENDANCE_SETTINGS.APPLY_DEDUCTION'))->first()
            : AttendanceSetting::where('key', config('globals.ATTENDANCE_SETTINGS.APPLY_LATE_DEDUCTION'))->first();
    }

    public function getAttendancePoliciesFlag()
    {
        $unleash = app(Unleash::class);

        return $unleash->getAbsenceLateEditsFeatureFlag() ?? 0;
    }

    private function createEntityTagForAttendance($attendance, $tag)
    {
        $entityTag = EntityTag::create([
            'tag' => $tag,
            'company' => $attendance->company_id,
        ]);
        $attendance->entityTags()->save($entityTag);
    }

    public function deleteEntityTags($tags, $entityType, $entityId)
    {
        EntityTag::whereIn('tag', $tags)->where('entity_type', $entityType)->where('entity_id', $entityId)->delete();
    }

    public function deleteNotInYetTagBeforeToday()
    {
        EntityTag::where('tag', config('globals.ATTENDANCE_TAGS.NOT_IN_YET'))->where('created_at', '<', Carbon::now()->format('Y-m-d'))->delete();
    }
}
