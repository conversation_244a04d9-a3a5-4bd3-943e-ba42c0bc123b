<?php

namespace App\Http\Resources;

use App\Http\Resources\V1\EmployeeFiles\AttachmentCollection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LoanProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status,
            'request_date' => \Carbon\Carbon::parse($this->created_at)->format('Y-m-d'),
            'type' => $this->type,
            'loan_amount' => $this->amount,
            'paid_amount' => $this->paid_amount,
            'remaining_amount' => $this->remaining_amount,
            'payout_date' => $this->disbursement_date,
            'comment' => $this->comment,
            'final_payment_date' => $this->last_installment_date,
            'installments' => InstallmentResource::collection($this->whenLoaded('installments')),
            'attachments' => new AttachmentCollection($this->attachments),
        ];
    }
}
