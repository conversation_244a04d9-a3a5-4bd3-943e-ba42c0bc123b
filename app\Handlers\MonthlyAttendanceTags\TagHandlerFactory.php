<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\FeatureToggles\Unleash;

class TagHandlerFactory
{
    public function createTagHandlers(): array
    {
        $tags = [
            'absent' => new AbsentTagHandler,
            'late' => new LateTagHandler,
            'absent_without_permission' => new AbsentWithoutPermissionTagHandler,
            'present' => new PresentTagHandler,
            'unaccounted' => new UnAccountedTagHandler,
            'worked_hours' => new WorkedHoursTagHandler,
            'unverified' => new UnverifiedTagHandler,
            'rest_day' => new RestDayTagHandler,
            'leaves' => new LeaveTagHandler,
            'no_clockout' => new NoClockOutTagHandler,
            'total' => new TotalTagHandler,
            'mission' => new MissionTagHandler,
            'manager_cico' => new ManagerCicoHandler,
            'overtime' => new TagOverTimeHandler,
            'deductions' => new TagDeductionHandler,
            // 'public_holidays' => new TagPublicHolidaysHandler(),
        ];

        $unleash = app(Unleash::class);
        if ($unleash->getUnscheduledShiftsFeatureFlag()) {
            $tags['off_shift'] = new OffShiftTagHandler;
            $tags['assigned_to_shift'] = new AssignedToShiftTagHandler;
        }

        return $tags;
    }
}
