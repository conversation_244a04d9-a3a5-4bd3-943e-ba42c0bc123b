<?php

namespace App\Http\Requests\V1\Holidays;

use Illuminate\Foundation\Http\FormRequest;

class GetAllHolidaysRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'year' => ['integer', 'min:1900', 'max:2100'],
            'page' => 'integer|min:1',
            'page_size' => 'integer|min:1|max:100',

        ];
    }
}
