<?php

namespace App\Http\Controllers\V1\CostCenter;

use App\Exceptions\UnprocessableException;
use App\Exports\V1\CostCenter\DepartmentsViewExport;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\CostCenter\GetDepartmentViewRequest;
use App\Http\Resources\V1\CostCenter\DepartmentsDetailsResource;
use App\Http\Resources\V1\CostCenter\DepartmentsViewResource;
use App\Services\V1\CostCenter\DepartmentsViewService;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class DepartmentViewController extends NewController
{
    public function __construct(protected DepartmentsViewService $departmentsViewService) {}

    public function getDepartmentsCosts(GetDepartmentViewRequest $request)
    {
        try {
            $data = $this->departmentsViewService->getDepartmentsCosts($request->validated());

            return getResponseStructure(
                ['data' => DepartmentsViewResource::collection($data)],
                HttpStatusCodeUtil::OK,
                'departments retrieved successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException($e->getMessage());
        }
    }

    public function exportDepartmentsCosts(GetDepartmentViewRequest $request)
    {
        $data = $this->departmentsViewService->getDepartmentsCosts($request->validated());

        return Excel::download(new DepartmentsViewExport($data->toArray()), 'Departments-Costs-'.$request->validated('month').'-'.$request->validated('year').'.xlsx');
    }

    public function getDepartmentCostDetails(int $id)
    {

        $data = $this->departmentsViewService->getDepartmentCostDetails($id);

        return getResponseStructure(
            ['data' => new DepartmentsDetailsResource($data)],
            HttpStatusCodeUtil::OK,
            'departments retrieved successfully');

    }
}
