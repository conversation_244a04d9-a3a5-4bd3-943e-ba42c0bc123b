<?php

namespace App\Services\V1\Loans;

use App\Exceptions\UnprocessableException;
use App\Models\StateMachines\LoanRequest\LoanRequestCancelled;
use App\Models\StateMachines\LoanRequest\RequestDisbursed;
use App\Models\StateMachines\LoanRequest\RequestNotRepaid;
use App\Models\StateMachines\LoanRequest\RequestPaid;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\NewTitleRepository;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\PayrollRepositories\SystemSettingRepository;
use App\Repositories\V1\EmployeeRequestRepository;
use App\Repositories\V1\Files\AttachmentRepository;
use App\Repositories\V1\Loans\InstallmentRepository;
use App\Repositories\V1\Loans\LoanPolicyRepository;
use App\Repositories\V1\Loans\LoanRepository;
use App\Repositories\V1\Loans\SalaryAdvancePolicyRepository;
use App\Repositories\V1\Loans\SalaryAdvanceRepository;
use App\Services\BaseService;
use App\Services\PayrollSetup\SalaryComponentsCategoriesService;
use App\Traits\QueriesHelper;
use App\Traits\RequestsHelper;
use App\Traits\UploadFile\UploadFile;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Traits\WorkflowTrait;
use App\Util\LoansUtil;
use App\Util\PayrollUtil;
use Carbon\Carbon;
use http\Client\Request;
use App\Traits\V1\EmployeeRequestsTrait;
use Illuminate\Support\Facades\Log;
use Workflow\WorkflowStub;
use App\Jobs\AssignApprovalsJob;
use App\Traits\V1\NotificationRedirection;
use App\Models\StateMachines\LoanRequest\RequestReadyToDisburse;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;

class LoanService extends BaseService
{
    use PayrollHelper, QueriesHelper, RequestsHelper, UploadFile, WorkflowTrait, EmployeeRequestsTrait, NotificationRedirection, PrepareAssignRequestCycleDataTrait;

    private LoanRepository $loanRepository;

    private InstallmentRepository $installmentRepository;

    private NewEmployeeRepository $employeeRepository;

    private LoanPolicyRepository $loanPolicyRepository;

    private NewTitleRepository $titleRepository;

    private AttachmentRepository $attachmentRepository;

    private SystemSettingRepository $systemSettingRepository;

    private PayrollsRepository $payrollsRepository;

    private SalaryAdvancePolicyRepository $salaryAdvancePolicyRepository;

    private EmployeeRequestRepository $employeeRequestRepository;

    private SalaryAdvanceRepository $salaryAdvanceRepository;

    private $loanPolicy;

    public function __construct(
        LoanRepository $loanRepository
    ) {
        parent::__construct($loanRepository);
        $this->installmentRepository = new InstallmentRepository;
        $this->employeeRepository = new NewEmployeeRepository;
        $this->loanPolicyRepository = new LoanPolicyRepository;
        $this->titleRepository = new NewTitleRepository;
        $this->attachmentRepository = new AttachmentRepository;
        $this->systemSettingRepository = new SystemSettingRepository;
        $this->payrollsRepository = new PayrollsRepository;
        $this->salaryAdvancePolicyRepository = new SalaryAdvancePolicyRepository;
        $this->employeeRequestRepository = new EmployeeRequestRepository;
        $this->salaryAdvanceRepository = new SalaryAdvanceRepository;
    }

    public function getEmployeeLoan($request)
    {
        $employeeId = $request['employeeId'] ?? auth()->user()->employee->id;
        $loans = $this->repository->getLoansByEmployeeId($employeeId);
        if ($loans->isEmpty()) {
            return collect([]);
        }
        foreach ($loans as $loan) {
            $paidAmount = 0;
            foreach ($loan->installments as $installment) {
                if ($installment->status == 1) {
                    $paidAmount += $installment->amount;
                }
            }
            $loan->paid_amount = $paidAmount;
            $loan->remaining_amount = $loan->amount - $paidAmount;
            $loan->last_installment_date = $loan->installments->max('date') ?: null;
            $loan->type = 'loan';
        }

        return $loans;
    }

    public function getMaxLoanAmount($request)
    {
        $employeeId = $request['employee_id'] ?? auth()->user()->employee->id;
        $employee = $this->employeeRepository->findOrFail($employeeId);
        $employeeNetSalary = $employee?->employeeSalary?->net_salary ?? 0;
        if (! $employeeNetSalary) {
            throw new UnprocessableException('Employee salary info not found');
        }
        $title = $this->titleRepository->findOrFail($employee->title_id);
        $loanPolicy = $title?->loanPolicy;
        if (! isset($loanPolicy)) {
            throw new UnprocessableException('There is no loan policy assigned to this title.');
        }
        $maxLoanAmount = $employeeNetSalary * ($loanPolicy->max_percentage_of_salary / 100);
        $cappedAmount = $loanPolicy->capped_amount;

        return min($maxLoanAmount, $cappedAmount);
    }

    public function getMaxLoanTenors($request)
    {
        $employeeId = $request['employee_id'] ?? auth()->user()->employee->id;
        $employee = $this->employeeRepository->findOrFail($employeeId);
        $loanPolicy = $employee->title->loanPolicy;
        if (! isset($loanPolicy)) {
            throw new UnprocessableException('There is no policy assigned to this title.');
        }
        $maxTenor = $loanPolicy->max_tenor;

        return $maxTenor;
    }

    public function employeeCanRequestLoan($employee)
    {
        $title = $this->titleRepository->findOrFail($employee->title_id);
        $loanPolicy = $title?->loanPolicy;
        $salaryAdvancePolicy = $title?->salaryAdvancePolicy;
        $pendingLoan = $this->repository->getNotPaidLoansForEmployee($employee->id);
        $pendingSalaryAdvance = $this->salaryAdvanceRepository->getNotPaidSalaryAdvancesForEmployee($employee->id);
        $loanPolicyEnabled = (bool) $this->systemSettingRepository->findFirstByKey('key', 'loan_policy')?->value;
        $salaryAdvancePolicyEnabled = (bool) $this->systemSettingRepository->findFirstByKey('key', 'salary_advance_policy')?->value;
        $employeeNetSalary = $employee?->employeeSalary?->net_salary ?? 0;
        $employeeOnProbation = $employee?->status ? $employee?->status == 'on_probation' : false;
        
        if (!$loanPolicyEnabled && !$salaryAdvancePolicyEnabled) {
            return ['can_request' => false, 'error' => trans('messages.loan_salary_advance_policies_disabled')];
        }

        if (!$loanPolicy && !$salaryAdvancePolicy) {
            return ['can_request' => false, 'error' => trans('messages.no_loan_salary_advance_policy_for_title')];
        }

        if (!$employeeNetSalary) {
            return ['can_request' => false, 'error' => trans('messages.employee_salary_not_found')];
        }
        
        if ($pendingLoan) {
            return ['can_request' => false, 'error' => trans('messages.pending_loan_exists')];
        }

        if ($pendingSalaryAdvance) {
            return ['can_request' => false, 'error' => trans('messages.pending_salary_advance_exists')];
        }

        if ($employeeOnProbation) {
            $probationAllowedForLoan = $loanPolicy?->apply_on_probation ?? false;
            $probationAllowedForSalaryAdvance = $salaryAdvancePolicy?->apply_on_probation ?? false;

            if (!$probationAllowedForLoan && !$probationAllowedForSalaryAdvance) {
                return ['can_request' => false, 'error' => trans('messages.loan_request_on_probation')];
            }
        }

        return ['can_request' => true, 'error' => null];
    }

    public function terminationWarning()
    {
        // Get the authenticated employee's ID
        $employeeId = auth()->user()->employee->id;
        $loan = $this->repository->getDisbursedLoansByEmployeeId($employeeId);
        if (! $loan) {
            return collect([]);
        }
        $unpaidAmount = 0;
        foreach ($loan->installments as $installment) {
            if ($installment->status == 0) {
                $unpaidAmount += $installment->amount;
            }
        }

        return $unpaidAmount;
    }

    public function repayLoanOnTermination($loanId)
    {
        $loan = $this->repository->getDisbursedLoansById($loanId);
        if (! isset($loan)) {
            throw new UnprocessableException('No outstanding loan found');
        }

        $unpaidInstallments = [];
        foreach ($loan->installments as $installment) {
            if ($installment->status == 0) {
                $unpaidInstallments[] = $installment;
            }
        }
        if ($unpaidInstallments == []) {
            throw new UnprocessableException('No unpaid installments found');
        }

        $loan->status->transitionTo(RequestPaid::class);
        $loan->save();
        foreach ($unpaidInstallments as $installment) {
            $installment->status = 1;
            $installment->save();
        }
    }

    public function notRepayLoanOnTermination($loanId)
    {
        $loan = $this->repository->getDisbursedLoansById($loanId);
        $loan->status->transitionTo(RequestNotRepaid::class);
        $loan->save();
    }

    public function requestLoan($request, $employeeId = null)
    {
        $employee = $employeeId ?
         $this->employeeRepository->findOrFail($employeeId) :
         auth()->user()->employee;

        $this->checkIsValidLoanRequest($employee, $request);

        $loan = $this->repository->add([
            'employee_id' => $employee->id,
            'amount' => $request['loan_amount'],
            'status' => 'pending',
            'number_of_installments' => $request['duration_in_months'],
            'comment' => $request['comment'] ?? null,
            'policy_id' => $this->loanPolicy->id
        ]);

        $this->saveLoanInstallments($loan);

        $this->uploadLoanAttachments($loan, $request);
        $requesterRoleIds = auth()->user()->roles->pluck('id')->toArray();

        AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('loan', $loan, $requesterRoleIds));

        return $loan;
    }

    public function disburseLoan($id, $request)
    {
        $loan = $this->repository->findOrFail($id);
        $currentPayroll = $this->payrollsRepository->payrollCoversDate($request['disbursement_date']);

        if (isset($currentPayroll) && $currentPayroll->status == PayrollUtil::PAYROLL_STATUS['FINALIZED']) {
            throw new UnprocessableException('you cannot disburse on a finalized payroll');
        }
        if ($loan->status != LoansUtil::READY_TO_DISBURSE) {

            throw new UnprocessableException('Loan with id: '.$id.' not approved yet');
        }
        $loan->status->transitionTo(RequestDisbursed::class);  // employee request for loan will get the status approved after the loan is disbursed // "boot method in Loan model will handle this"

        $loan->disbursement_date = $request['disbursement_date'];

        $loan->save();

        return $loan;
    }

    public function uploadLoanAttachments($loan, $request)
    {
        $employee = $loan->employee;
        $attachments = $request['attachments'] ?? [];
        if (! empty($attachments) && isset($loan)) {
            $companyUuid = $employee->company->uuid;
            $employeeUuid = $employee->uuid;

            $filePaths = [];

            foreach ($attachments as $attachment) {
                $filePath = $this->uploadFile($attachment, 's3', "{$companyUuid}/employee-files/{$employeeUuid}/loans");
                $filePaths[] = $filePath;
            }
            $this->saveAttachmets($filePaths, $loan);

            $loan->save();

        }
    }

    public function saveLoanInstallments($loan)
    {
        $installments = [];
        $installmentAmount = $loan->amount / $loan->number_of_installments;

        $installmentDate = Carbon::parse($loan->disbursement_date)->addMonth();

        for ($i = 0; $i < $loan->number_of_installments; $i++) {
            $installmentDate->day = $this->getMonthlyClosingDay($installmentDate);

            $installments[] = [
                'loan_id' => $loan->id,
                'employee_id' => $loan->employee_id,
                'amount' => $installmentAmount,
                'status' => 0,
                'date' => $installmentDate->toDateString(),
            ];
            $installmentDate->addMonth();
        }

        $this->installmentRepository->createMany($installments);
    }

    public function checkIsValidInstallmentsEdit($loan, &$installments)
    {

        $prevInstallmentDate = null;
        foreach ($installments as &$installment) {
            $installmentDate = Carbon::parse($installment['date'].'-'.$this->getMonthlyClosingDay($installment['date']));
            $installment['date'] = $installmentDate->toDateString();
            $installment['status'] = 0;
            $installment['id'] = null;

            $oldInstallment = $loan->installments->where('date', $installment['date'])->first();
            if (isset($oldInstallment)) {
                $installment['id'] = $oldInstallment->id;
                if ($oldInstallment->status == 1 && $oldInstallment->amount != $installment['amount']) {
                    throw new UnprocessableException('installment at date '.$oldInstallment->date.' is already paid and cannot edit its amount');
                }
                $installment['status'] = $oldInstallment->status;
            }

            // check if previous installment date isset and less than the current installment date and the difference is 1 month
            //            if (isset($prevInstallmentDate) && $prevInstallmentDate->diffInMonths($installmentDate) != 1) { #TODO fix this
            //                throw new UnprocessableException('Installments dates must be consecutive months');
            //            }
            $prevInstallmentDate = $installmentDate;
        }
    }

    public function checkIsValidLoanRequest($employee, $request, $isEditAction = true)
    {
        $policyEnabled = $this->systemSettingRepository->findFirstByKey('key', 'loan_policy')?->value;
        if (! $policyEnabled) {
            throw new UnprocessableException('Loan policy is not enabled');
        }

        if ($employee->status == config('globals.EMPLOYEE_STATUSES.TERMINATED')) {
            throw new UnprocessableException('You are not an active employee');
        }

        $this->loanPolicy = $employee->title?->loanPolicy;
        if (! $this->loanPolicy) {
            throw new UnprocessableException('Loan policy not found for your title');
        }
        $onProbationAllowed = $this->loanPolicy?->apply_on_probation ?? false;
        $employeeOnProbation = $employee?->status ? $employee?->status == 'on_probation' : false;

        if (! $onProbationAllowed && $employeeOnProbation) {
            throw new UnprocessableException(trans('messages.loan_request_on_probation'));
        }

        $employeeHasAPendingLoan = $isEditAction ? $this->repository->getNotPaidLoansForEmployee($employee->id) : null;
        $pendingSalaryAdvance = $isEditAction ? $this->salaryAdvanceRepository->getNotPaidSalaryAdvancesForEmployee($employee->id) : null;
        if ($employeeHasAPendingLoan || $pendingSalaryAdvance) {
            throw new UnprocessableException(trans('messages.ongoing_loan'));
        }
        $employeeNetSalary = $employee->employeeSalary?->net_salary;
        $maxLoanAmount = $employeeNetSalary * ($this->loanPolicy->max_percentage_of_salary / 100);
        $cappedAmount = $this->loanPolicy->capped_amount;
        $allowedAmount = min($maxLoanAmount, $cappedAmount);
        if ($request['loan_amount'] > $allowedAmount) {
            throw new UnprocessableException(trans('messages.loan_amount_exceeds_allowed_amount'));
        }

        if ($request['duration_in_months'] > $this->loanPolicy->max_tenor) {
            throw new UnprocessableException(trans('messages.loan_duration_exceeds_allowed_duration'));
        }
    }

    public function enableLoanPolicyToggle()
    {
        $loanPolicyExists = $this->systemSettingRepository->companyHasKey('loan_policy');
        $companyId = auth()->user()->company->id;
        if ($loanPolicyExists) {
            $loanPolicySetting = $this->systemSettingRepository->getSettingByKey('loan_policy', $companyId);
            $loanPolicySetting->value = ! $loanPolicySetting->value;
            $loanPolicySetting->save();
        } else {
            $this->systemSettingRepository->add([
                'as_of_date' => date('Y-m-d'),
                'key' => 'loan_policy',
                'value' => 1,
                'company_id' => $companyId,
            ]);
            $salaryComponentCategoryService = app(SalaryComponentsCategoriesService::class);
            $salaryComponentCategoryService->setDefaultLoansAndSalaryAdvanceCategory();
        }
    }

    public function createLoanPolicy($request)
    {
        $companyId = auth()->user()->company->id;
        // Create the loan policy
        $loanPolicy = $this->loanPolicyRepository->add([
            'company_id' => $companyId,
            'name_en' => $request->name_en,
            'name_ar' => $request->name_ar,
            'max_percentage_of_salary' => $request->max_percentage_of_salary,
            'max_tenor' => $request->max_tenor,
            'capped_amount' => $request->capped_amount,
            'apply_on_probation' => $request->apply_on_probation,
        ]);
        $titleIds = $request->title_ids ?? [];
        if (is_array($titleIds) && count($titleIds) > 0) {
            foreach ($titleIds as $titleId) {
                $title = $this->titleRepository->find($titleId);
                if ($title) {
                    $title->loan_policy_id = $loanPolicy->id;
                    $title->save();
                }
            }
        }

        return $loanPolicy;
    }

    public function loanPolicyWarning(array $titleIds)
    {
        $assignedTitles = [];
        $lang = (config('globals.lang') ?? 'ar');

        foreach ($titleIds as $titleId) {
            $title = $this->titleRepository->find($titleId);
            if ($title && isset($title->loan_policy_id)) {
                $loanPolicy = $title->loanPolicy;
                $assignedTitles[] = [
                    'id' => $title->id,
                    'name' => $lang == 'ar' ? $title->name_ar : $title->name_en,
                    'loan_policy_name' => $lang == 'ar' ? $loanPolicy->name_ar : $loanPolicy->name_en,
                ];
            }
        }

        return $assignedTitles;
    }

    public function editLoanPolicy($request, $loanPolicyId)
    {
        $companyId = auth()->user()->company->id;

        $loanPolicy = $this->loanPolicyRepository->find($loanPolicyId);
        $loanPolicy->is_active = false;
        $loanPolicy->save();

        $updatedLoanPolicy = $this->loanPolicyRepository->add([
            'company_id' => $companyId,
            'name_en' => $request->name_en,
            'name_ar' => $request->name_ar,
            'max_percentage_of_salary' => $request->max_percentage_of_salary,
            'max_tenor' => $request->max_tenor,
            'capped_amount' => $request->capped_amount,
            'apply_on_probation' => $request->apply_on_probation,
        ]);

        $titleIds = $request->title_ids ?? [];
        $existingTitlesQuery = $this->titleRepository->findFirstByKey('loan_policy_id', $loanPolicyId);
        $existingTitles = $existingTitlesQuery ? $existingTitlesQuery->get() : collect(); // Use collect() to return an empty collection if no results
        $titleIdSet = collect($titleIds)->flip();

        foreach ($existingTitles as $title) {
            if ($titleIdSet->has($title->id)) {
                $title->loan_policy_id = $updatedLoanPolicy->id;
            } else {
                $title->loan_policy_id = null;
            }
            $title->save();
        }

        foreach ($titleIds as $titleId) {
            if (! $existingTitles->contains('id', $titleId)) {
                $title = $this->titleRepository->find($titleId);
                if ($title) {
                    $title->loan_policy_id = $updatedLoanPolicy->id;
                    $title->save();
                }
            }
        }

        return $updatedLoanPolicy;
    }

    public function showLoanAndSalaryAdvancePolicies()
    {
        $companyId = auth()->user()->company->id;

        // Fetch the monthly closing day
        $monthlyClosingDay = $this->getMonthlyClosingDay()
            ?? throw new UnprocessableException('Monthly closing day not set');

        // Check if loan policies exist and retrieve the toggle
        $loanPolicyExists = $this->systemSettingRepository->companyHasKey('loan_policy');
        $loanToggle = $loanPolicyExists && (bool) $this->systemSettingRepository->getSettingByKey('loan_policy', $companyId)->value;

        // Check if salary advance policies exist and retrieve the toggle
        $salaryAdvancePolicyExists = $this->systemSettingRepository->companyHasKey('salary_advance_policy');
        $salaryAdvanceToggle = $salaryAdvancePolicyExists && (bool) $this->systemSettingRepository->getSettingByKey('salary_advance_policy', $companyId)->value;

        // Fetch the loan policies
        $loanPolicies = $this->loanPolicyRepository->getActiveLoanPolicies();

        // Fetch the salary advance policies and set payroll_end_day
        $salaryAdvancePolicies = $this->salaryAdvancePolicyRepository->getActiveSalaryAdvancePolicies()->map(function ($policy) use ($monthlyClosingDay) {
            $policy->payroll_end_day = (int) $monthlyClosingDay; // Set payroll_end_day

            return $policy;
        });

        $allTitles = $this->titleRepository->all()->count();

        foreach ($loanPolicies as $loanPolicy) {
            $policyTitles = $this->titleRepository->findByKey('loan_policy_id', $loanPolicy->id)->count();
            if ($policyTitles == $allTitles) {
                $loanPolicy->all_titles = true;
            } else {
                $loanPolicy->all_titles = false;
            }
        }

        foreach ($salaryAdvancePolicies as $salaryAdvancePolicy) {
            $policyTitles = $this->titleRepository->findByKey('salary_advance_policy_id', $salaryAdvancePolicy->id)->count();
            if ($policyTitles == $allTitles) {
                $salaryAdvancePolicy->all_titles = true;
            } else {
                $salaryAdvancePolicy->all_titles = false;
            }
        }

        $locale = app()->getLocale();
        $sortByField = $locale === 'ar' ? 'name_ar' : 'name_en';

        $loanPolicies = $loanPolicies->sortBy($sortByField);

        $salaryAdvancePolicies = $salaryAdvancePolicies->sortBy($sortByField);

        // Prepare the policies response
        $policies = new \stdClass;
        $policies->salary_advance_policies = $salaryAdvancePolicies;
        $policies->loan_policies = $loanPolicies;
        $policies->loan_policy_toggle = $loanToggle;
        $policies->salary_advance_policy_toggle = $salaryAdvanceToggle;

        return $policies;
    }

    public function approveLoan($id)
    {
        $loan = $this->repository->findOrFail($id);

        $employeeHasAPendingLoan = $this->repository->getNotPaidLoansForEmployee($loan->employee_id, [LoansUtil::DISBURSED, LoansUtil::READY_TO_DISBURSE]);
        if ($employeeHasAPendingLoan) {
            throw new UnprocessableException(trans('messages.ongoing_loan'));
        }

        $this->actionOnLoan($loan, 'approve');

        return $loan;
    }

    public function forceCancelLoan($id)
    {
        $loan = $this->repository->findOrFail($id);

        if ($loan->status != LoansUtil::READY_TO_DISBURSE) {
            throw new UnprocessableException('You can not cancel this loan');
        }

        $this->actionOnLoan($loan, 'cancel');

        return $loan;
    }

    public function rejectLoan($id)
    {
        $loan = $this->repository->findOrFail($id);

        $this->actionOnLoan($loan, 'reject');

        return $loan;
    }

    public function editLoan($id, $request)
    {
        $loan = $this->repository->findOrFail($id);

        if ($loan->status != LoansUtil::DISBURSED) {
            throw new UnprocessableException(trans('messages.edit_disbursed_loans_only'));
        }

        $this->checkIsValidLoanRequest($loan->employee, $request, false);
        $installments = $request['installments'];
        $this->checkIsValidInstallmentsEdit($loan, $installments);

        $installmentIds = [];
        foreach ($installments as $installment) {
            if (isset($installment['id'])) {
                $oldInstallment = $this->installmentRepository->findOrFail($installment['id']);
                $oldInstallment->amount = $installment['amount'];
                $oldInstallment->status = $installment['status'];
                $oldInstallment->date = $installment['date'];
                $oldInstallment->save();
                $installmentIds[] = $oldInstallment->id;
            } else {
                $installment['loan_id'] = $loan->id;
                $installment['employee_id'] = $loan->employee_id;
                $newInstallment = $this->installmentRepository->add($installment);
                $installmentIds[] = $newInstallment->id;
            }
        }
        $this->installmentRepository->deleteLoanInstallmentsNotInIds($loan->id, $installmentIds);
        $loan->amount = $request['loan_amount'];
        $loan->number_of_installments = $request['duration_in_months'];
        $loan->save();
        $loan->refresh();

        return $loan;
    }

    public function actionOnLoan($loan, $actionType)
    {
        try {
            $roleIds = config('globals.user')->roles->pluck('id')->toArray();
            $userCanTakeAnAction = $this->canTakeAnAction($roleIds, $loan->id, config('globals.REQUEST_WORKFLOW_TYPES.LOAN'), $actionType);
            if (! $userCanTakeAnAction) {
                throw new UnprocessableException(trans('messages.can_not_take_this_action'));
            }

            if ($actionType != 'cancel' && $loan->status != config('globals.REQUEST_STATUSES.PENDING')) {
                throw new UnprocessableException(trans('messages.workflow_is_completed'));
            }

            $this->doAnAction($actionType);
            if($this->checkRequestIsCompleted($loan->employeeRequest)) {
                $finalStatus = $this->getFinalStatus($loan->employeeRequest, $actionType);
                $this->updateRequest($loan->employeeRequest, $finalStatus);
                $finalStatus = $finalStatus == 'approved' ? "ready_to_disburse" : $finalStatus;
                $this->updateEntity($loan, $finalStatus);
                if($finalStatus != "ready_to_disburse") {
                    $loan->installments()?->each(function ($installment) {
                        $installment->delete();
                    });
                }
                $this->redirectNotificationsAfterRequestFinalized($loan, $finalStatus);
            }
        } catch (\Exception $e) {
            Log::error($e);
            throw new UnprocessableException('Something went wrong while taking an action on the loan');
        }
    }

    //    public function getLoansByFilter($data)
    //    {
    //        $loans = $this->repository->getLoanByFilters($data);
    //        foreach ($loans as $loan) {
    //            $approvalCycle = $this->getApprovalCycle($loan, $loan->employee);
    //            $loan->actionable = $loan != config('globals.REQUEST_STATUSES.PENDING') && $this->isActionable($approvalCycle);
    //
    //            $loan->installment_count = $loan->installments->count();
    //            $loan->type = 'loan';
    //
    //            $loan->last_installment_date = $loan->installments->max('date') ?: null;
    //
    //            $paidInstallments = [];
    //            $remainingInstallments = [];
    //
    //            $paidAmount = 0;
    //            $remainingAmount = 0;
    //
    //            foreach ($loan->installments as $installment) {
    //                if ($installment->status) {
    //                    $paidInstallments[] = $installment;
    //                    $paidAmount += $installment->amount;
    //                } else {
    //                    $remainingInstallments[] = $installment;
    //                    $remainingAmount += $installment->amount;
    //                }
    //            }
    //
    //            $loan->paid_installments = $paidInstallments;
    //            $loan->remaining_installments = $remainingInstallments;
    //            $loan->paid_installments_count = count($paidInstallments);
    //            $loan->remaining_installments_count = count($remainingInstallments);
    //
    //            $loan->paid_amount = $paidAmount;
    //            $loan->remaining_amount = $remainingAmount;
    //
    //            $loan->disbursable = ($loan->status == LoansUtil::READY_TO_DISBURSE);
    //            $loan->editable = ($loan->status == LoansUtil::DISBURSED);
    //        }
    //
    //        return $loans;
    //
    //    }

    public function getLoanAndSalaryAdvanceByFilter($data)
    {
        $requests = $this->employeeRequestRepository->getLoanAndSalaryAdvanceRequestsByFilters($data);
        $user = auth()->user();
        $hasPermissionToEdit = $user->hasPermissionTo('extend_loans', 'user-api');
        $hasPermissionToDisburse = $user->hasPermissionTo('edit_loans', 'user-api');
        $hasPermissionToCancel = $user->hasPermissionTo('cancel_loans', 'user-api');
        foreach ($requests as $request) {
            $requestable = $request->requestable;

            if ($request->requestable_type === 'loan') {
                $approvalCycle = $this->getApprovalCycle($requestable, $request->employee);
                $requestable->actionable = $requestable->status == config('globals.REQUEST_STATUSES.PENDING') && $this->isActionable($approvalCycle, $request->status);
                $request->type = 'loan';

                $requestable->installment_count = $requestable->number_of_installments;
                $requestable->last_installment_date = $requestable->installments->max('date') ?: null;

                $paidInstallments = [];
                $remainingInstallments = [];
                $paidAmount = 0;
                $remainingAmount = 0;

                foreach ($requestable->installments as $installment) {
                    if ($installment->status) {
                        $paidInstallments[] = $installment;
                        $paidAmount += $installment->amount;
                    } else {
                        $remainingInstallments[] = $installment;
                        $remainingAmount += $installment->amount;
                    }
                }

                $requestable->paid_installments = $paidInstallments;
                $requestable->remaining_installments = $remainingInstallments;
                $requestable->paid_installments_count = count($paidInstallments);
                $requestable->remaining_installments_count = count($remainingInstallments);

                $requestable->paid_amount = $paidAmount;
                $requestable->remaining_amount = $remainingAmount;

            } elseif ($request->requestable_type === 'salary_advance') {
                $approvalCycle = $this->getApprovalCycle($requestable, $request->employee);
                $requestable->actionable = $requestable->status == config('globals.REQUEST_STATUSES.PENDING') && $this->isActionable($approvalCycle, $request->status);
                $request->type = 'salary_advance';
                $request->amount = $requestable->amount;
                $payroll = $this->payrollsRepository->find($requestable->payroll_id);
                $requestable->final_payment_date = $payroll?->end;

            }
            $requestable->disbursable = ($requestable->status == LoansUtil::READY_TO_DISBURSE && ($hasPermissionToDisburse || $this->isAuthorizedToForceApprove()));
            $requestable->editable = ($requestable->status == LoansUtil::DISBURSED && ($hasPermissionToEdit || $this->isAuthorizedToForceApprove()));
            $requestable->can_cancel = ($requestable->status == LoansUtil::READY_TO_DISBURSE && ($hasPermissionToCancel || $this->isAuthorizedToForceApprove()));
        }

        return $requests;
    }

    public function getReadyToDisburseLoansForTerminatedAndActiveEmployees()
    {
        $readyLoans = $this->repository->getReadyToDisburseLoans();
        $terminatedEmployeeLoans = [];
        $activeEmployeeLoans = [];
        foreach ($readyLoans as $loan) {
            if (isset($loan->employee->employeeInfo->termination_date)) {
                $terminatedEmployeeLoans[] = $loan;
            } else {
                $activeEmployeeLoans[] = $loan;
            }
        }

        return [
            'terminated_employee_loans' => $terminatedEmployeeLoans,
            'active_employee_loans' => $activeEmployeeLoans,
        ];
    }

    public function getReadyToDisburseLoansCount()
    {
        return $this->repository->getReadyToDisburseLoansCount();
    }

    public function disburseAllReadyToDisburseLoans(): void
    {
        $readyLoans = $this->getReadyToDisburseLoansForTerminatedAndActiveEmployees();
        $activeEmployeeLoans = $readyLoans['active_employee_loans'];
        $data['disbursement_date'] = date('Y-m-d');
        foreach ($activeEmployeeLoans as $loan) {
            $loan_id = $loan->id;
            $this->disburseLoan($loan_id, $data);
        }
    }


}
