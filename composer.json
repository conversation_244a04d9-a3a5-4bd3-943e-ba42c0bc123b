{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "ext-curl": "*", "ankurk91/fcm-notification-channel": "^1.5", "aws/aws-sdk-php": "^3.281", "barryvdh/laravel-dompdf": "^3.1", "doctrine/dbal": "^3.0", "fakerphp/faker": "^1.24", "giggsey/libphonenumber-for-php": "^9.0", "google/auth": "^1.29", "guzzlehttp/guzzle": "^7.2", "khaled.alshamaa/ar-php": "^7.0", "kreait/firebase-php": "7.0", "laravel-workflow/laravel-workflow": "^1.0", "laravel/framework": "^10.0", "laravel/sanctum": "^3.2", "laravel/telescope": "^5.3", "laravel/tinker": "^2.7", "league/flysystem-aws-s3-v3": "^3.21", "maatwebsite/excel": "^3.1", "monolog/monolog": "^3.8", "opcodesio/log-viewer": "^3.12", "php-ds/php-ds": "^1.4", "php-open-source-saver/jwt-auth": "^2.0", "phpoffice/phpspreadsheet": "^1.27", "predis/predis": "^2.2", "psr/simple-cache": "2.0", "ramsey/uuid": "^4.7", "sentry/sentry-laravel": "^3.7", "spatie/laravel-activitylog": "^4.7", "spatie/laravel-ignition": "^2.0", "spatie/laravel-model-states": "^2.6", "spatie/laravel-permission": "^5.9", "tcg/voyager": "^1.6", "unleash/client": "^2.4"}, "require-dev": {"laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "laravel/telescope": "^5.3", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "pestphp/pest": "^1.23", "pestphp/pest-plugin-laravel": "^1.4", "pestphp/pest-plugin-parallel": "^1.2"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}