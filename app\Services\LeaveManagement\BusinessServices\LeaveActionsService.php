<?php

namespace App\Services\LeaveManagement\BusinessServices;

use App\Exceptions\UnprocessableException;
use App\Http\Resources\EmployeeLeaveRequestResource;
use App\Models\StateMachines\RequestApproved;
use App\Models\StateMachines\RequestState;
use App\Repositories\Repository;
use App\Services\IBusinessService;
use App\Services\Schedule\CrudServices\TitleShiftEmployeeCrudService;
use App\Services\TimeTracking\BusinessServices\ApplyRequestCycleRulesService;
use App\Services\TimeTracking\CrudServices\TimecardCrudService;
use App\Traits\DataPreparation;
use App\Traits\V1\EmployeeRequestsTrait;
use App\Traits\WorkflowTrait;
use App\Jobs\HandleActionOnLeaveJob;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use stdClass;
use Workflow\WorkflowStub;

class LeaveActionsService implements IBusinessService
{
    use DataPreparation, EmployeeRequestsTrait, WorkflowTrait;

    private $leave;

    private $leaveBalance;

    private $timeCards;

    private bool $isHourly;

    private bool $isDailyOrPartialDaily;

    private Repository $employeeLeaveRequestRepository;

    private Repository $employeeLeaveBalanceRepository;

    private Repository $attendanceRepository;

    private Repository $restEmployeeRepository;

    private Repository $timecardRepository;

    private $workflow;

    public function __construct(
        private ApplyRequestCycleRulesService $applyRequestCycleRulesService,
        private BalanceAggregatorService $balanceAggregatorService,
        private TitleShiftEmployeeCrudService $titleShiftEmployeeCrudService,
        private TimecardCrudService $timecardCrudService,
        private CancelLeavesService $cancelLeavesService,
    ) {
        $this->employeeLeaveRequestRepository = Repository::getRepository('EmployeeLeaveRequest');
        $this->employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
        $this->attendanceRepository = Repository::getRepository('Attendance');
        $this->timecardRepository = Repository::getRepository('Timecard');

    }

    public function isValid(array $request, stdClass &$output): bool
    {
        $this->leave = $this->employeeLeaveRequestRepository->getWithLeaveApprovalCycle($request['id']);
        $this->leaveBalance = $this->employeeLeaveBalanceRepository->getBalanceOfLeave($this->leave);
        $this->isDailyOrPartialDaily = ! ($this->leaveBalance->CompanyLeaveTypePolicy?->unit == config('globals.LEAVE_UNITS.HOURS'));

        if (! isset($this->leave)) {
            $output->Error = ['Invalid leave Id', 'المعرف الخاص بالأجازة غير صحيح'];

            return false;
        }

        if ($request['action'] != 'cancel') {
            if ($this->leave->status != config('globals.REQUEST_STATUSES.PENDING')) {
                $output->Error = ['This request is already completed', 'هذا الطلب مكتمل بالفعل'];
                return false;
            }
        }

        if ($this->leave->status == config('globals.REQUEST_STATUSES.CANCELLED')) {
            $output->Error = ['You can not change leave status because it has been cancelled',
                'لا يمكنك تغيير حالة الأجازة حيث تم إلغاءها'];

            return false;
        }

        if ($request['action'] == 'reject' || $request['action'] == 'cancel') {
            return true;
        }

        if ($this->leave->net_quantity > $this->leaveBalance->balance && $request['action'] == 'approve') {
            $output->Error = ['This Employee does not have enough balance to request this leave',
                ' لا يوجد لدى هذا الموظف رصيد كافٍ لطلب هذه الأجازة '];

            return false;
        }

        $leaveInRange = $this->employeeLeaveRequestRepository->hasLeaveInRange($this->leave->employee_id,
            $this->leave->from, $this->leave->to, [config('globals.REQUEST_STATUSES.APPROVED')]);
        if (! is_null($leaveInRange) && $request['action'] == 'approve') {
            $leaveFrom = Carbon::parse($leaveInRange->from)->toDateString();
            $leaveTo = Carbon::parse($leaveInRange->to)->toDateString();
            $output->Error = ['This employee already have a leave from '.$leaveFrom.' to '.$leaveTo,
                ' هذا الموظف لديه بالفعل أجازة من '.$leaveFrom.' إلى '.$leaveTo];

            return false;
        }
        $fromDate = Carbon::parse($this->leave->from)->toDateString();
        $toDate = Carbon::parse($this->leave->to)->toDateString();
        $isEmployeeOnRest = $this->employeeLeaveRequestRepository
            ->restDayExistsOnDate($this->leave->employee_id, $fromDate);
        if ($this->leaveBalance->CompanyLeaveTypePolicy?->unit == config('globals.LEAVE_UNITS.HOURS') && $isEmployeeOnRest) {
            $output->Error = ['This employee already have a rest day on this day', ' هذا الموظف لديه بالفعل راحة فى هذا اليوم '];

            return false;
        }

        $verifiedAttendancesOnRange = $this->attendanceRepository->verifiedAttendancesExistsOnRange($this->leave->employee_id,
            $fromDate, $toDate);
        if (! isset($this->leave->partial_leave_type) && ! is_null($verifiedAttendancesOnRange) && count($verifiedAttendancesOnRange)) {
            $attendanceDates = implode("\n", array_column($verifiedAttendancesOnRange->toArray(), 'date'));
            $output->Error = ['This employee '.$this->leave->employee->name.' has attended already on \n'.$attendanceDates,
                ' لقد حضر هذا الموظف'.$this->leave->employee->name.' بالفعل فى \n'.$attendanceDates];

            return false;
        }

        $this->timeCards = $this->timecardRepository->timecardsInRange($this->leave->employee_id, $fromDate, $toDate);
        if ($request['action'] == 'approve' && $request['check_warning'] && $this->isDailyOrPartialDaily && count($this->timeCards)) {
            $timecardDates = implode("\n", array_column($this->timeCards->toArray(), 'from'));
            $output->Warning[] = ['This employee will be removed from shifts on this days '.$timecardDates,
                ' سوف يتم إزالة هذا الموظف من هذه الشيفتات '.$timecardDates];

            return false;
        }

        // $timeCardOnDate = $this->attendanceRepository->timeCardOnDate($this->leave->employee_id, $this->leave->from);
        // if($this->leaveBalance->CompanyLeaveTypePolicy->unit == config('globals.LEAVE_UNITS.HOURS') && $timeCardOnDate){
        //         $fromTime = explode(" ", $this->leave->from)[1];
        //         $toTime = explode(" ", $this->leave->to)[1];
        //         if($timeCardOnDate->actual_start != $fromTime && $timeCardOnDate->actual_end != $toTime)
        //         {
        //             $output->Error = ['You can only approve request at the start of the shift or at the end of it',
        //             'يمكن فقط الطلب فى بداية الشيفت أو نهايته ' ];
        //             return false;
        //         }
        // }
        return true;
    }

    public function perform(array $request, stdClass &$output): void
    {
        if (! $this->isValid($request, $output)) {
            return;
        }

        if ($request['action'] == 'approve') {
            $this->actionOnLeave($output, 'approve');
        } else {
            $this->actionOnLeave($output, $request['action']);
        }

        $this->leave->balanceEntity = $this->leaveBalance;
        $output->leave = new EmployeeLeaveRequestResource($this->leave);

    }

    public function actionOnLeave(stdClass &$output, $actionType): void
    {
        try {
            $user = config('globals.user');
            if ($this->leave->employee_id == $user->employee_id && $actionType == 'cancel') {

                $this->employeeCancelLeaveForHimSelf($output);

                return;
            }

            $roleIds = $user->roles->pluck('id')->toArray();
            $userCanTakeAnAction = $this->canTakeAnAction($roleIds, $this->leave->id, config('globals.REQUEST_WORKFLOW_TYPES.LEAVE'), $actionType);
            if (! $userCanTakeAnAction) {
                Log::info('actionOnLeave217');

                $output->Error = ['You are not allowed to take this action', 'غير مسموح لك باتخاذ هذا الاجراء'];

                return;
            }

            if ($actionType != 'cancel' && $this->leave->status != config('globals.REQUEST_STATUSES.PENDING')) {
                throw new UnprocessableException(trans('messages.workflow_is_completed'));
            }

            $this->doAnAction($actionType);

            if ($actionType == 'cancel') {
                $this->cancelLeavesService->perform(['id' => $this->leave->id], $output);
                if (isset($this->leave->employeeRequest)) {
                    $this->updateRequest($this->leave->employeeRequest, config('globals.REQUEST_STATUSES.CANCELLED'));
                }
            } else {
                if($this->checkRequestIsCompleted($this->leave->employeeRequest)){
                    $finalStatus = $this->getFinalStatus($this->leave->employeeRequest, $actionType);
                    $this->updateRequest($this->leave->employeeRequest, $finalStatus);
                    $this->updateEntity($this->leave, $finalStatus);
                    if($finalStatus == 'approved'){
                        Log::info('in approved condition the request is ' . $this->leave->id);
                        $jobData = [
                            'final_status' => $finalStatus,
                            'request' => $this->leave,
                            'timecards' => $this->timeCards->pluck('id')->toArray() ?? [],
                        ];
                        HandleActionOnLeaveJob::dispatch($jobData)->afterCommit();
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error($e);
            $output->Error = ['You are not allowed to take this action', 'غير مسموح لك باتخاذ هذا الاجراء'];

            return;
        }

    }

    public function employeeCancelLeaveForHimSelf(stdClass &$output): void
    {
        if (! $this->employeeCanCancelLeaveForHimSelf($this->leave->id)) {
            $output->Error = ['You are not allowed to cancel this leave because an approver has took an action', 'لا يمكنك إلغاء هذه الأجازة حيث تم أخد قرار بشأنها'];

            return;
        }
        $this->cancelLeavesService->perform(['id' => $this->leave->id], $output);
        if (isset($this->leave->employeeRequest)) {
            $this->leave->employeeRequest->update(['status' => config('globals.REQUEST_STATUSES.CANCELLED')]);
        }

    }

    public function forceRejectLeaves(array $leaveRequestIds)
    {
        $countRejected = 0;

        foreach ($leaveRequestIds as $leaveRequestId) {
            $leaveRequest = $this->employeeLeaveRequestRepository->getById($leaveRequestId);
            $leaveBalance = $this->employeeLeaveBalanceRepository->getBalanceOfLeave($leaveRequest);
            if (isset($leaveRequest) && isset($leaveBalance)) {
                $leaveRequest->status = config('globals.REQUEST_STATUSES.REJECTED');
                $leaveRequest->save();
                $aggregateBalanceRequest = $this->prepareBalanceAggregatorRequest($leaveBalance->id,
                    $leaveRequest->employee_id);
                $dummyOutput = new stdClass;
                $this->balanceAggregatorService->perform($aggregateBalanceRequest, $dummyOutput);
                $countRejected += 1;
            }
        }

        return $countRejected;
    }

    public function getRequestData()
    {
        return [
            'requestable_id' => $this->leave->id,
            'requestable_type' => config('globals.REQUEST_WORKFLOW_TYPES.LEAVE'),
            'role_ids' => auth()->user()->roles->pluck('id')->toArray(),
        ];
    }
}
