<?php

namespace App\Console\Commands;

use App\Models\Branch;
use Illuminate\Console\Command;

class FixBranchLocationLink extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-branch-location-link';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Convert branch location links to coordinates for company 242 using built-in functions and OpenStreetMap Nominatim';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get branches for company_id 242
        $branches = Branch::where('company_id', 242)->get();
        $this->info("Found {$branches->count()} branches for company 242.");

        foreach ($branches as $branch) {
            // Check if the location field is a valid URL
            if (filter_var($branch->location, FILTER_VALIDATE_URL)) {
                $this->info("Processing branch ID {$branch->id} with location link: {$branch->location}");
                $coordinates = $this->getCoordinatesFromGoogleMapsLink($branch->location);

                if ($coordinates) {
                    $branch->location = $coordinates;
                    $branch->save();
                    $this->info("Updated branch ID {$branch->id} with coordinates: {$coordinates}");
                } else {
                    $this->info("Could not extract coordinates from the link for branch ID {$branch->id}.");
                }
            } else {
                $this->info("Branch ID {$branch->id} location is not a link. Skipping...");
            }
        }

        $this->info('Completed processing branches.');
    }

    /**
     * Get coordinates from a Google Maps link using built-in PHP functions.
     *
     * This method first uses get_headers() to obtain the final URL after redirection.
     * It then attempts to extract coordinates from the URL using regular expressions.
     * If that fails, it extracts an address (if available) and queries the OpenStreetMap Nominatim API.
     *
     * @param string $link
     * @return string|null Returns coordinates as "lat,lon" or null if not found.
     */
    private function getCoordinatesFromGoogleMapsLink($link)
    {
        // Use get_headers() to follow redirection and get the final URL.
        $headers = @get_headers($link, 1);
        if ($headers === false) {
            $this->error("Failed to get headers for {$link}");
            return null;
        }
        $finalUrl = $link;
        if (isset($headers['Location'])) {
            $finalUrl = is_array($headers['Location']) ? end($headers['Location']) : $headers['Location'];
        }

        // First try: Extract coordinates directly from the final URL.
        if (preg_match('/@(-?\d+\.\d+),(-?\d+\.\d+)/', $finalUrl, $matches)) {
            return $matches[1] . ',' . $matches[2];
        }
        if (preg_match('/\?q=(-?\d+\.\d+),(-?\d+\.\d+)/', $finalUrl, $matches)) {
            return $matches[1] . ',' . $matches[2];
        }

        // Second try: Extract an address from the URL (if available).
        $address = null;
        if (preg_match('/\?q=([^&]+)/', $finalUrl, $matches)) {
            $address = urldecode($matches[1]);
        } else {
            // Fallback: use the final URL itself as the search term.
            $address = $finalUrl;
        }

        // Use OpenStreetMap's Nominatim API to geocode the address.
        $nominatimUrl = "https://nominatim.openstreetmap.org/search?q=" . urlencode($address) . "&format=json&limit=1";
        $json = @file_get_contents($nominatimUrl);
        if ($json !== false) {
            $data = json_decode($json, true);
            if (!empty($data) && isset($data[0]['lat']) && isset($data[0]['lon'])) {
                return $data[0]['lat'] . ',' . $data[0]['lon'];
            }
        }

        return null;
    }
}
