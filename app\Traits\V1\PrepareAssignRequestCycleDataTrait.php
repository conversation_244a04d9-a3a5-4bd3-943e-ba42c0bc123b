<?php

namespace App\Traits\V1;

use InvalidArgumentException;

trait PrepareAssignRequestCycleDataTrait
{
    public function prepareAssignRequestCycleData($requestName, $request, ?array $requester_role_ids = null): array
    {
        $requestData = [
            'request' => $request,
            'title_id' => $request->employee->title_id,
            'employee_id' => $request->employee->id,
            'requester_role_ids' => $requester_role_ids,
            'request_type' => $this->getRequestType($requestName, $request),
            'workflow_type' => config('globals.REQUEST_WORKFLOW_TYPES.'.strtoupper($requestName)),
        ];

        return $requestData;
    }

    private function getRequestType(string $requestName, $request): string
    {
        if ($requestName === 'employee_leave_request') {
            return $request->companyLeaveType->uuid;
        }

        return config('globals.REQUEST_CYCLE_TYPES.'.strtoupper($requestName));
    }
}
