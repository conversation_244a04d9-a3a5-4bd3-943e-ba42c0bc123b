<?php

namespace App\Http\Controllers\CompanySetup;

use App\DomainData\RoleDto;
use App\Http\Controllers\Controller;
use App\Services\CompanySetup\BusinessServices\AssignRoleToTitleService;
use App\Services\CompanySetup\BusinessServices\UpdateRoleToTitleService;

class RoleTitleController extends Controller
{
    use RoleDto;

    public function __construct(
        private AssignRoleToTitleService $assignRoleToTitleService,
        private UpdateRoleToTitleService $updateRoleToTitleService
    ) {}

    public function AssignRoleToTitle(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['name', 'description']);
        $rules['title_ids'] = ['present', 'array'];
        $rules['title_ids.*'] = ['required', 'numeric', 'distinct'];
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->assignRoleToTitleService->perform($request, $output);
    }

    public function UpdateRoleToTitle(array $request, \stdClass &$output): void
    {
        $rules = $this->getRules(['name', 'description']);
        $rules['role_id'] = ['required', 'numeric'];
        $rules['title_ids'] = ['present', 'array'];
        $rules['title_ids.*'] = ['required', 'numeric', 'distinct'];
        $validator = \Validator::make($request, $rules);
        if ($validator->fails()) {
            $output->Error = $validator->messages();

            return;
        }

        $request = $validator->validate();

        $this->updateRoleToTitleService->perform($request, $output);
    }
}
