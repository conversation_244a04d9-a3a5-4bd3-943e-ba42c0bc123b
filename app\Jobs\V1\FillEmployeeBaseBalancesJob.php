<?php

namespace App\Jobs\V1;

use App\FeatureToggles\Unleash;
use App\Services\LeaveManagement\FillEmployeeBalancesService as OldFillEmployeeBalancesService;
use App\Services\V1\LeaveManagement\FillEmployeeBalancesService;
use App\Traits\WorkflowTrait;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FillEmployeeBaseBalancesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, WorkflowTrait;

    private $employees;

    private $companyIds;

    private $leaveTypeId;

    private $oldFillEmployeeBalancesService;

    private $fillEmployeeBalancesService;

    /**
     * Create a new job instance.
     */
    public function __construct($employees, $companyIds = null, $leaveTypeId = null)
    {
        $this->employees = $employees;
        $this->companyIds = $companyIds;
        $this->leaveTypeId = $leaveTypeId;
        $this->oldFillEmployeeBalancesService = app(OldFillEmployeeBalancesService::class);
        $this->fillEmployeeBalancesService = app(FillEmployeeBalancesService::class);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::beginTransaction();
        try {
            $unleash = app(Unleash::class);

            if ($unleash->isLeaveBalanceChangesFlagEnabled()) {
                $this->fillEmployeeBalancesService->fill($this->employees, $this->companyIds, $this->leaveTypeId);
            } else {
                $this->oldFillEmployeeBalancesService->fill($this->employees);
            }
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
            // dd($e);
        }
    }
}
