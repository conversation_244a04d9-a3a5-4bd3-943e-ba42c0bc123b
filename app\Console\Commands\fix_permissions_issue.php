<?php

namespace App\Console\Commands;

use App\Models\Role;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class fix_permissions_issue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix_permissions_issue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $manageWorkerCount = Role::get()->filter(
                fn ($role) => $role->hasPermissionTo('manage_worker')
            )->count();

            $this->info('before, manage worker count: '.$manageWorkerCount);

            $roles = Role::get();
            foreach ($roles as $role) {
                if (! $role->hasPermissionTo('manage_worker')) {
                    $role->givePermissionTo('manage_worker');
                }
            }

            $manageWorkerCount = Role::get()->filter(
                fn ($role) => $role->hasPermissionTo('manage_worker')
            )->count();

            $this->info('after, manage worker count: '.$manageWorkerCount);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }

    }
}
