<?php

namespace App\Jobs;

use App\FeatureToggles\Unleash;
use App\Models\Employee;
use App\Repositories\IRepository;
use App\Repositories\NewAttendanceSettingRepository;
use App\Repositories\Repository;
use App\Repositories\V1\Leaves\EmployeeLeaveBalancesRepository;
use App\Services\TimeTracking\BusinessServices\AddRequestsToEmployeeRequest;
use App\Services\TimeTracking\CrudServices\AttendanceOvertimeCrudService;
use App\Services\V1\RequestOvertime\RequestOvertimeService;
use App\Traits\V1\AttendancePoliciesTrait;
use App\Traits\V1\HandlesTimezones;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;
use App\Traits\V2\WorkTypesTrait;
use App\Traits\WorkflowTrait;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OvertimeCalculationService implements ShouldQueue
{
    use AttendancePoliciesTrait, Dispatchable, HandlesTimezones, InteractsWithQueue, Queueable, SerializesModels, WorkflowTrait, WorkTypesTrait, PrepareAssignRequestCycleDataTrait;

    private $employeeId;

    private $date;

    private $employee;

    private $companyId;

    private NewAttendanceSettingRepository $attendanceSettingRepository;

    private IRepository $overtimeGroupRepository;

    private IRepository $attendanceRepository;

    private IRepository $entityTagRepository;

    private AttendanceOvertimeCrudService $attendanceOvertimeCrudService;

    private AddRequestsToEmployeeRequest $addRequestsToEmployeeRequest;

    private EmployeeLeaveBalancesRepository $employeeLeaveBalancesRepository;

    /**
     * Create a new job instance.
     */
    public function __construct($employeeId, $date)
    {
        $this->employeeId = $employeeId;
        $this->date = $date;
        $this->attendanceSettingRepository = new NewAttendanceSettingRepository;
        $this->overtimeGroupRepository = Repository::getRepository('OvertimeGroup');
        $this->attendanceRepository = Repository::getRepository('Attendance');
        $this->entityTagRepository = Repository::getRepository('EntityTag');
        $this->attendanceOvertimeCrudService = new AttendanceOvertimeCrudService;
        $this->addRequestsToEmployeeRequest = new AddRequestsToEmployeeRequest;
        $this->employeeLeaveBalancesRepository = new EmployeeLeaveBalancesRepository;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::beginTransaction();
        try {
            $this->employee = Employee::find($this->employeeId);
            Log::info('employee '.$this->employeeId);
            if (isset($this->employee)) {
                $title = $this->employee->title;
                $this->companyId = $title->company_id;

                if ($this->isCompanyApplyOvertimePolicy() == 1 && $this->isCompanyApplyOvertimeOnThisTitle($title->id) == 1) {
                    Log::info('employee is eligible for overtime');

                    $attendances = $this->attendanceRepository->getAttendanceForEmployeeOnDate($this->date, $this->employeeId);
                    Log::info('attendances count is '.count($attendances));

                    $this->deleteOvertimeForAllAttendances($attendances);
                    $sortedAttendances = $attendances->sortBy(function ($item) {
                        return $item['clockIn']['date'];
                    });
                    $this->calculateAttendanceOvertimes($sortedAttendances, $title);
                }
            }
            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }

    public function isCompanyApplyOvertimePolicy()
    {
        $applyOvertimePolicy = $this->attendanceSettingRepository->getSettingsByCompanyIdAndKey('apply_overtime', $this->companyId);
        if (isset($applyOvertimePolicy) && $applyOvertimePolicy->is_used) {
            return 1;
        }

        return 0;
    }

    public function isCompanyApplyOvertimeOnThisTitle($titleId)
    {
        return $this->overtimeGroupRepository->isCompanyApplyOvertimeOnThisTitle($titleId);
    }

    public function deleteOvertimeForAllAttendances($attendances)
    {
        foreach ($attendances as $attendance) {
            if (count($attendance->attendanceOvertimes) > 0) {
                Log::info('deleting overtime for attendance id: '.$attendance->id);
                // $attendance->attendanceOvertimes[0]->workflowApprovalCycles()->delete();
                $attendance->attendanceOvertimes[0]->delete();
                $this->entityTagRepository->deleteTag($attendance->id, 'attendance', 'overtime');
            }
        }
    }

    public function calculateAttendanceOvertimes($attendances, $title)
    {
        Log::info('inside calculateAttendanceOvertimes');

        $totalAppliedOvertimeMins = 0;
        $titleWorkingInMinutes = $this->getTitleWorkingDurationInMinutes($title, $this->date);
        $overtimeGroup = $this->overtimeGroupRepository->getOvertimeGroupRelatedToTitle($title->id);
        $overtimePolicies = $overtimeGroup?->dailyOvertimePolicies;
        $totalWorkedDurationInMinutes = 0;
        foreach ($attendances as $attendance) {
            $clockIn = $attendance->clockIn;
            $clockOut = $attendance->clockOut;
            $totalWorkedDurationInMinutes += Carbon::parse($clockIn->date)->diffInMinutes(Carbon::parse($clockOut->date));

            if (! isset($overtimeGroup) || array_intersect($overtimeGroup->excludedBranches->pluck('id')->toArray(), [$clockOut->branch_id])) {
                Log::info('no overtime group or the branch is excluded from this policy');

                continue;
            }
            Log::info('total overtime mins: '.$totalWorkedDurationInMinutes.'  '.$titleWorkingInMinutes);
            if ($totalWorkedDurationInMinutes > $titleWorkingInMinutes) {
                $overtimeMins = ($totalWorkedDurationInMinutes - $titleWorkingInMinutes);
                if ($overtimeMins > 0) {
                    Log::info('total overtime mins: '.$overtimeMins);
                    $remainingOvertimeMins = $overtimeMins - $totalAppliedOvertimeMins;
                    Log::info('remaining overtime mins: '.$remainingOvertimeMins);

                    $policy = $this->getPolicyForOvertimeMins($overtimePolicies, $remainingOvertimeMins);
                    if (! is_null($policy)) {
                        Log::info('found policy for attendance id: '.$attendance->id);

                        $totalAppliedOvertimeMins += $remainingOvertimeMins;
                        Log::info('total applied overtime mins: '.$totalAppliedOvertimeMins);
                        $this->entityTagRepository->createTag($attendance->id, 'attendance', 'overtime', $title->company_id);
                        $this->createAttendanceOvertime($attendance, $remainingOvertimeMins, $policy, $overtimeGroup?->calculation_method ?? 'rate');
                    }
                }
            }
        }
    }

    public function getPolicyForOvertimeMins($overtimePolicies, $overtimeMins)
    {
        $lastPolicy = null;
        foreach ($overtimePolicies as $policy) {
            if ($overtimeMins < $policy->from) {
                return null;
            } elseif ($overtimeMins >= $policy->from && $overtimeMins <= $policy->to) {
                return $policy;
            }
            $lastPolicy = $policy;
        }

        return $lastPolicy;
    }

    public function getTitleWorkingDurationInMinutes($title, $clockOutDate)
    {
        $event = $title->events->where('start_date', '<=', $clockOutDate)->where('end_date', '>=', $clockOutDate)->first();
        $isThereEventRunning = isset($event);
        if ($isThereEventRunning) {
            return $event->working_minutes;
        }

        $workTypePolicy = $title->workTypePolicy;
        if (isset($workTypePolicy) && in_array($workTypePolicy->work_days_type, $this->getFixedTypes($title->company_id)) && ! empty($workTypePolicy->start_time) && ! empty($workTypePolicy->end_time)) {

            $startTime = $workTypePolicy->start_time;
            $endTime = $workTypePolicy->end_time;
            $startDateTime = Carbon::parse($startTime);
            $endDateTime = $endTime < $startTime ? Carbon::parse($endTime)->addDay() : Carbon::parse($endTime);
            $totalMinutes = $startDateTime->diffInMinutes($endDateTime);
            Log::info('title id:'.$title->id.'inside getTitleWorkingDurationInMinutes '.$totalMinutes);

            return $totalMinutes;
        }

        return optional($title)->working_hours * 60;
    }

    public function createAttendanceOvertime($attendance, $overtimeMins, $policy, $calculationMethod)
    {
        Log::info('inside createAttendanceOvertime');

        $output = new \stdClass;
        $attendanceOvertimeData = $this->prepareAttendanceOvertimeData($attendance, $overtimeMins, $policy, $calculationMethod);
        $this->attendanceOvertimeCrudService->create($attendanceOvertimeData, $output);
        if (! empty($output->Error)) {
            throw new Exception($output->Error);
        }

        $unleash = app(Unleash::class);
        if ($unleash->isRequestOvertimeHoursEnabled($this->companyId)) {
            $requestOvertimeService = app(RequestOvertimeService::class);
            $approved = $requestOvertimeService->getApprovedRequestByDate($output->overtime->employee_id, $output->overtime->date);
            if (! empty($approved)) {
                $output->overtime->update(['status' => 'approved']);
                $systemOvertimeRequestData = $this->prepareEmployeeRequestData($output->overtime, 'system_overtime', 'approved');
                $this->addRequestsToEmployeeRequest->perform($systemOvertimeRequestData, $output);
                if (! empty($output->Error)) {
                    throw new Exception($output->Error);
                }
                $overtimeLeaveBalance = $this->employeeLeaveBalancesRepository->getLeaveTypeBalanceOrCreate($output->overtime->employee_id, $attendance->company->OvertimeLeaveType);
                $overtimeLeaveBalance->update(['balance' => $overtimeLeaveBalance->balance + ($overtimeMins / 60)]);

            }

        } else {
            $systemOvertimeRequestData = $this->prepareEmployeeRequestData($output->overtime, 'system_overtime');
            $this->addRequestsToEmployeeRequest->perform($systemOvertimeRequestData, $output);
            if (! empty($output->Error)) {
                throw new Exception($output->Error);
            }
            AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('attendance_overtime', $output->overtime));
        }
    }

    public function prepareEmployeeRequestData($entity, $requestName, $status = null)
    {
        $request['request_to_be_created'] = $entity;

        $request['request_data'] = [
            'company_id' => $this->companyId,
            'requested_by' => null,
            'request_name' => $requestName,
            'status' => $status ?? 'pending',
            'employee_id' => $entity?->employee_id ?? null,
            'date' => $entity?->date ?? null,
        ];

        return $request;
    }

    public function prepareAttendanceOverTimeData($attendance, $overtimeMinutes, $policy, $calculationMethod)
    {
        return [
            'date' => $this->date,
            'overtime_value' => $policy->percentage,
            'status' => 'pending',
            'employee_id' => $attendance->employee_id,
            'attendance_id' => $attendance->id,
            'daily_overtime_policy_id' => $policy->id,
            'company_id' => $this->companyId,
            'overtime_minutes' => $overtimeMinutes,
            'calculation_method' => $calculationMethod,
        ];
    }
}
