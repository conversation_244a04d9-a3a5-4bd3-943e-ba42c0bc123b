<?php

namespace App\Console\Commands;

use App\Imports\EmployeeBranchesImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class DropEmployeeBranchesFromExcel extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'drop-employee-branches:excel {file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add s3 file path to drop employee branches from excel sheet.';

    /**
     * Execute the console command.
     */
    public function handle(

    ) {
        DB::beginTransaction();
        try {
            $s3Path = $this->argument('file');
            $fileContents = Storage::disk('s3')->get($s3Path);
            $tempDir = tempnam(sys_get_temp_dir(), 'employee_branches.xlsx');
            file_put_contents($tempDir, $fileContents);
            Excel::import(new EmployeeBranchesImport, $tempDir);
            $this->info('Excel sheet parsed successfully.');
            unlink($tempDir);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
        }

    }
}
