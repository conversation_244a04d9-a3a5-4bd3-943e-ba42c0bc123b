<?php

namespace App\Services\V1\Attendance;

use App\Enums\V2\WorkTypes\WorkTypesEnum;
use App\Exceptions\UnprocessableException;
use App\FeatureToggles\Unleash;
use App\Models\AttendanceDeduction;
use App\Models\AttendanceOvertime;
use App\Models\EmployeeRequest;
use App\Services\LeaveManagement\BusinessServices\BalanceAggregatorService;
use App\Jobs\AssignApprovalsJob;
use App\Traits\V1\PrepareAssignRequestCycleDataTrait;
use App\Traits\V2\WorkTypesTrait;
use App\Traits\WorkflowTrait;
use App\Util\AttendanceUtil;
use App\Util\UserWorkTypesUtil;
use stdClass;

class NewAttendanceService
{
    use WorkflowTrait, WorkTypesTrait, PrepareAssignRequestCycleDataTrait;

    public function __construct() {}

    public function prepareEmployeeRequestForUpsert($deductionEntity, string $type, array $data, $status = "pending")
    {
        $requestName = $type === AttendanceUtil::ATTENDANCE_DEDUCTION ? 'waive_deduction' : 'edit_attendance_overtime';

        $uniqueData = [
            'requestable_id' => $deductionEntity->id,
            'requestable_type' => $type,
            // 'request_name' => $requestName,
            'company_id' => auth()->user()->company_id,
        ];

        $dataToUpdate = [
            'requested_by' => auth()->user()->employee_id,
            'status' => $status,
            'request_name' => $requestName,
            'comment' => $data['reason'] ?? '',
            'date' => $deductionEntity->date,
            'employee_id' => $deductionEntity->employee_id,
        ];

        return [$uniqueData, $dataToUpdate];
    }

    public function upsertEmployeeRequest(array $uniqueData, array $dataToUpdate)
    {
        $employeeRequset = EmployeeRequest::updateOrCreate($uniqueData, $dataToUpdate);

        return $employeeRequset;
    }

    public function addOvertimeReason($id, $data)
    {
        $attendanceOvertime = AttendanceOvertime::find($id);
        if (! $attendanceOvertime) {
            throw new UnprocessableException('Attendance Overtime not found');
        }
        $attendanceOvertime->employeeRequests->first()->update(
            [
                'comment' => $data['reason'],
                'requested_by' => auth()->user()->employee_id,
                'request_name' => 'edit_attendance_overtime',
            ]
        );
    }

    public function editAttendanceDeduction($id, $data)
    {
        $attendanceDeduction = AttendanceDeduction::find($id);
        if (! $attendanceDeduction) {
            throw new UnprocessableException('Attendance Deduction not found');
        }
        $employee = $attendanceDeduction->employee;
        $deductionBaseValue = $attendanceDeduction->deduction_value;

        [$uniqueData, $dataToUpdate] = $this->prepareEmployeeRequestForUpsert($attendanceDeduction, AttendanceUtil::ATTENDANCE_DEDUCTION, $data);

        $this->upsertEmployeeRequest($uniqueData, $dataToUpdate);
        if ($deductionBaseValue != $data['deduction_value']) {
            $attendanceDeduction->update(
                [
                    'updated_value' => $data['deduction_value'] ?? '',
                ]
            );
            $attendanceDeduction->save();

            if (isset($attendanceDeduction->employee_leave_request_id) && $data['deduction_value'] == 0) {
                $this->updateLeaveOfDeduction($attendanceDeduction);
            }
        }

        $requesterRoleIds = config('globals.user')->roles->pluck('id')->toArray();
        AssignApprovalsJob::dispatch($this->prepareAssignRequestCycleData('edit_attendance_deduction', $attendanceDeduction, $requesterRoleIds));

    }

    public function updateLeaveOfDeduction($attendanceDeduction)
    {
        $leave = $attendanceDeduction->employeeLeaveRequest;
        if ($leave) {
            $leave->update(['net_quantity' => 0]);
            $leaveBalanceAggregator = app(BalanceAggregatorService::class);
            $leaveBalanceAggregator->performByLeaveRequest($leave);
        }
    }

    public function clockIn($data, stdClass &$output)
    {
        $unleash = app(Unleash::class);
        $employee = config('globals.user')->employee;
        $employee->load('title.workTypePolicy');
        $userWorkType = $this->getUserWorkType($employee);
        $userWorkType->buildNextSlot($data['lat'], $data['long']);
        $userWorkType->canTakeAction();
        $workType = $employee->title->workTypePolicy->work_days_type;
        if ($workType == WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value) {
            $userWorkType->createDefaultTimecard();
        } elseif (! $unleash->getNewWorkTypesFeatureFlag()) { // $workType == UserWorkTypesUtil::DYNAMIC_ON_SITE
            $userWorkType->createTimecardIfNeeded();
        }
        $userWorkType->setClockInActionService();
        $userWorkType->applyOutsideOfficeTagIfExists();
        $userWorkType->performClockIn($data, $output);
        $userWorkType->decreaseAnyLocationBalanceIfExists();
    }

    public function clockOut($data, stdClass &$output)
    {
        $employee = config('globals.user')->employee;
        $employee->load('title.workTypePolicy');
        $userWorkType = $this->getUserWorkType($employee);
        $userWorkType->buildNextSlot($data['lat'], $data['long']);
        $userWorkType->canTakeAction();
        $userWorkType->setClockOutActionService();
        $userWorkType->performClockOut($data, $output);
    }
}
