<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\FeatureToggles\Unleash;
use App\Models\Employee;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\PayrollRepositories\PayrollsRepository;
use App\Repositories\V1\Attendance\AttendanceRepository;
use App\Repositories\V1\Attendance\TimecardRepository;
use App\Repositories\V1\Excuse\ExcusesBalanceRepository;
use App\Repositories\V1\ExtraWorkdayRequestRepository;
use App\Repositories\V1\Holidays\PublicHolidaysRepository;
use App\Repositories\V1\Leaves\EmployeeLeaveRequestRepository;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use DateInterval;
use DatePeriod;
use Exception;
use Illuminate\Support\Arr;
use Nette\Utils\DateTime;

class AttendanceTagHandler
{
    protected array $tagHandlers = [];

    private $unleash;

    private $attendanceRepository;

    private NewEmployeeRepository $newEmployeeRepository;

    private ExtraWorkdayRequestRepository $extraWorkdayRequestRepository;

    private $excuseBalanceRepository;

    private $payrollRepository;

    private $timecardRepository;

    private $publicHolidaysRepository;

    public function __construct(TagHandlerFactory $factory)
    {
        $this->tagHandlers = $factory->createTagHandlers();
        $this->attendanceRepository = new AttendanceRepository;
        $this->unleash = app(Unleash::class);
        $this->newEmployeeRepository = new NewEmployeeRepository;
        $this->extraWorkdayRequestRepository = new ExtraWorkdayRequestRepository;
        $this->excuseBalanceRepository = new ExcusesBalanceRepository;
        $this->payrollRepository = new PayrollsRepository;
        $this->timecardRepository = new TimecardRepository;
        $this->publicHolidaysRepository = new PublicHolidaysRepository;
    }

    /**
     * @throws Exception
     */
    public function handleTags($attendances, $data): array
    {
        $tags = [];
        $employeeIds = array_unique($attendances->keys()->toArray());
        $timecards = $this->timecardRepository->getTimecardsWithDateRangeAndBranch($data, $employeeIds);
        $timecardsGroupedByEmployee = $timecards->groupBy('employee_id');
        $this->handleOffBranchTag($employeeIds, $data, $tags);
        $this->handleExtraWorkdayTag($employeeIds, $data, $tags);
        $this->handleExcuseBalanceTag($employeeIds, $tags);
        $employees = Employee::with('employeeInfo', 'branch', 'title', 'profilePicture')->whereIn('id', $employeeIds)->get();
        $publicHolidays = $this->publicHolidaysRepository->getHolidaysMonthlyView($data);

        foreach ($employees as $employee) {
            $timecardsByEmployee = Arr::get($timecardsGroupedByEmployee, $employee->id, []);
            $this->handlePublicHolidayTag($publicHolidays, $employee, $data, $tags, $timecardsByEmployee);
            $this->handleUnAccountedTag($tags, $employee, $data);
            $this->handleEmployeeInfo($employee, $tags);
            $this->handleMissingWorkingHours($employee->id, $timecards, $tags);
        }

        foreach ($attendances as $employeeId => $employeeAttendances) {

            foreach ($employeeAttendances as $employeeAttendance) {
                $this->handleTimeCardsTags($employeeAttendance, $employeeId, $tags);
            }
        }

        $tags = $this->removeTagsWithoutEmployeeInfo($tags);

        return array_values($tags);
    }

    private function removeTagsWithoutEmployeeInfo($tags)
    {
        foreach ($tags as $employeeId => $tag) {
            if (!isset($tag['employee_info'])) {
                unset($tags[$employeeId]);
            }
        }

        return $tags;
    }

    /**
     * @throws Exception
     */
    private function handleUnAccountedTag(&$tags, $employee, $data): void
    {
        $this->initializeUnaccountedTag($tags, $employee->id);
        $this->populateUnaccountedDates($tags, $employee, $data['from_date'], $data['to_date']);
        $this->removePublicHolidayDates($tags, $employee->id);
    }

    private function removePublicHolidayDates(&$tags, $employeeId): void
    {
        if (isset($tags[$employeeId]['tags']['public_holidays']['data'])) {
            foreach ($tags[$employeeId]['tags']['public_holidays']['data'] as $holiday) {
                $holidayDate = $holiday['date'];
                $this->removeUnaccountedDate($tags, $employeeId, $holidayDate);
            }
        }
    }

    private function initializeUnaccountedTag(&$tags, $employeeId): void
    {
        if (!isset($tags[$employeeId]['tags']['unaccounted'])) {
            $tags[$employeeId]['tags']['unaccounted']['count'] = 0;
            $tags[$employeeId]['tags']['unaccounted']['data'] = [];
            $tags[$employeeId]['tags']['unaccounted']['unit'] = 'days';
        }
    }

    /**
     * @throws Exception
     */
    private function populateUnaccountedDates(&$tags, $employee, $fromDate, $toDate): void
    {
        $employeeId = $employee->id;
        // Parse the dates using Carbon
        $fromDate = Carbon::parse($fromDate);
        $toDate = Carbon::parse($toDate);
        $today = Carbon::today();

        // Adjust toDate if it is later than today
        if ($toDate->greaterThan($today)) {
            $toDate = $today->subDay();
        }

        // Find the employee
        // $employee = $this->newEmployeeRepository->findOrFail($employeeId);
        if ($employee->is_trackable == false) {
            return;
        }
        // Convert termination_date to Carbon if it exists and is not null
        if (isset($employee->employeeInfo->termination_date)) {
            $terminationDate = Carbon::parse($employee->employeeInfo->termination_date);

            // Adjust toDate if the termination date is earlier
            if ($terminationDate->lessThan($toDate)) {
                $toDate = $terminationDate;
            }
        }

        if (isset($employee->employeeInfo->join_date)) {
            $joinDate = Carbon::parse($employee->employeeInfo->join_date);
            if ($joinDate->greaterThan($fromDate)) {
                $fromDate = $joinDate;
            }
        }

        // Create a CarbonPeriod instance for the date range
        $datePeriod = CarbonPeriod::create($fromDate, '1 day', $toDate);

        // Populate the tags array with unaccounted dates
        foreach ($datePeriod as $date) {
            $tags[$employeeId]['tags']['unaccounted']['data'][] = [
                'name' => null,
                'id' => null,
                'date' => $date->format('Y-m-d'),
                'from_date' => null,
                'to_date' => null,
            ];
        }

        // Set the count of unaccounted dates
        $tags[$employeeId]['tags']['unaccounted']['count'] = count($tags[$employeeId]['tags']['unaccounted']['data']);
    }

    /**
     * @throws Exception
     */
    protected function handleTimeCardsTags($employeeAttendance, $employeeId, &$tags): void
    {
        foreach ($this->tagHandlers as $tag => $handler) {
            if ($handler->handleTagCheck($employeeAttendance, $tag)) {
                if ($tag != 'present') {
                    $this->updateTagData($tags, $employeeId, $tag, $handler, $employeeAttendance);
                }

                if ($tag == 'leaves') {
                    $this->processLeaveDates($tags, $employeeId);
                }

                if ($tag == 'present') {
                    $this->updateTagData($tags, $employeeId, $tag, $handler, $employeeAttendance);
                }
            } elseif (!isset($tags[$employeeId]['tags'][$tag])) {
                $this->initializeTag($tags, $employeeId, $tag, $handler);
            }
        }

        // Reindex unaccounted data to maintain sequential keys
        if (isset($tags[$employeeId]['tags']['unaccounted']['data'])) {
            $tags[$employeeId]['tags']['unaccounted']['data'] = array_values($tags[$employeeId]['tags']['unaccounted']['data']);
            $tags[$employeeId]['tags']['unaccounted']['count'] = count($tags[$employeeId]['tags']['unaccounted']['data']);
        }
    }

    private function updateTagData(&$tags, $employeeId, $tag, $handler, $employeeAttendance): void
    {
        if ($tag == 'present') { // TODO: Refactor this to be more readable and clearer
            $tagData = $handler->handleTagData($employeeAttendance);
            if ($tagData && isset($tagData['date'])) {
                $this->removeUnaccountedDate($tags, $employeeId, $tagData['date']);
            }

            if ($tagData) {
                $tags[$employeeId]['tags'][$tag]['data'][] = $tagData;
            } else {
                $tags[$employeeId]['tags'][$tag]['data'] = [];
            }
        }

        $tags[$employeeId]['tags'][$tag]['count'] = $handler->handleTagCount($tags, $employeeId, $employeeAttendance);
        $tags[$employeeId]['tags'][$tag]['unit'] = $handler->handleTagUnit();

        if ($tag != 'present') {
            $tagData = $handler->handleTagData($employeeAttendance);
            if ($tagData && isset($tagData['date'])) {
                $this->removeUnaccountedDate($tags, $employeeId, $tagData['date']);
            }

            if ($tagData) {
                $tags[$employeeId]['tags'][$tag]['data'][] = $tagData;
            } else {
                $tags[$employeeId]['tags'][$tag]['data'] = [];
            }
        }

        //        $tags[$employeeId]['tags'][$tag]['count'] = count($tags[$employeeId]['tags'][$tag]['data']);
    }

    private function initializeTag(&$tags, $employeeId, $tag, $handler): void
    {
        $tags[$employeeId]['tags'][$tag]['count'] = 0;
        $tags[$employeeId]['tags'][$tag]['unit'] = $handler->handleTagUnit();
        $tags[$employeeId]['tags'][$tag]['data'] = [];
    }

    private function removeUnaccountedDate(&$tags, $employeeId, $date): void
    {
        if (isset($tags[$employeeId]['tags']['unaccounted']['data'])) {
            foreach ($tags[$employeeId]['tags']['unaccounted']['data'] as $index => $unaccountedDay) {
                if ($unaccountedDay['date'] === $date) {
                    unset($tags[$employeeId]['tags']['unaccounted']['data'][$index]);
                }
            }
            // Reindex the array to maintain sequential keys after removing elements
            $tags[$employeeId]['tags']['unaccounted']['data'] = array_values($tags[$employeeId]['tags']['unaccounted']['data']);
            $tags[$employeeId]['tags']['unaccounted']['count'] = count($tags[$employeeId]['tags']['unaccounted']['data']);
        }
    }

    private function processLeaveDates(&$tags, $employeeId): void
    {
        foreach ($tags[$employeeId]['tags']['leaves']['data'] as $leave) {
            $fromDate = new DateTime($leave['from_date']);
            $toDate = new DateTime($leave['to_date']);
            $dateInterval = new DateInterval('P1D'); // Interval of 1 day
            $datePeriod = new DatePeriod($fromDate, $dateInterval, $toDate->modify('+1 day')); // Inclusive of to_date

            foreach ($datePeriod as $date) {
                $formattedDate = $date->format('Y-m-d'); // Format the date as a string
                $this->removeUnaccountedDate($tags, $employeeId, $formattedDate);
            }
        }
    }

    protected function handleEmployeeInfo($employee, &$tags): void
    {
        $tags[$employee->id]['employee_info'] = [
            'first_name' => $employee->first_name,
            'second_name' => $employee->second_name,
            'profile_image' => $employee->profilePicture->attachment_url ?? null,

            'employee_number' => $employee->employee_number,
            'branch_name' => $employee->branch->name,
            'title' => [
                'name' => $employee->title->name,
                'color' => $employee->title->color,
            ],
            'hiring_date' => $employee->employeeInfo->join_date,
            'termination_date' => $employee->employeeInfo->termination_date,
            'hiring_status' => $employee->status,
            'employee_id' => $employee->id,
            'branch_id' => $employee->branch->id,
        ];
    }

    protected function handlePublicHolidayTag($publicHolidays, $employee, $data, &$tags, $timecards)
    {
        // $publicHolidays = $this->publicHolidaysRepository->getHolidaysMonthlyView($data);
        $timecardRepository = new TimecardRepository;
        $employeeLeaveRequestRepository = new EmployeeLeaveRequestRepository;
        $tags[$employee->id]['tags']['public_holidays']['count'] = 0;
        $tags[$employee->id]['tags']['public_holidays']['unit'] = 'days';
        $tags[$employee->id]['tags']['public_holidays']['data'] = [];
        $hiringDate = $employee?->employeeInfo?->join_date ?? '';
        $terminationDate = $employee?->employeeInfo?->termination_date ?? '2500-12-31';
        foreach ($publicHolidays as $publicHoliday) {
            $start = max($publicHoliday->start, $data['start_date'] ?? '');
            $end = min($publicHoliday->end, $data['end_date'] ?? '');
            $start = Carbon::parse($start);
            $end = Carbon::parse($end);
            for ($day = $start; $day <= $end; $day->addDay()) {
                $date = $day->format('Y-m-d');
                // $leaveOnDate = $employeeLeaveRequestRepository->approvedLeaveExistsAroundDateForEmployee($employee->id, $date);
                if ($date >= $hiringDate && $date < $terminationDate && !$this->employeeHasTimecardOnDate($date, $timecards)) {
                    $tags[$employee->id]['tags']['public_holidays']['count'] += 1;
                    $tags[$employee->id]['tags']['public_holidays']['unit'] = 'days';
                    $tags[$employee->id]['tags']['public_holidays']['data'][] = [
                        'name' => $publicHoliday->name,
                        'id' => $publicHoliday->id,
                        'date' => $date,
                        'from_date' => null,
                        'to_date' => null,
                    ];
                }
            }

        }
    }

    public function employeeHasTimecardOnDate($date, $timecards)
    {
        return collect($timecards)->contains(function ($timecard) use ($date) {
            return Carbon::parse($timecard->from)->toDateString() === $date;
        });
    }

    public function handleOffBranchTag($employeeIds, $data, &$tags)
    {
        if ($this->unleash->getUnscheduledShiftsFeatureFlag() == false) {
            return;
        }
        $offBranchIncidents = $this->attendanceRepository->getAttendancesUnionCicosForEmployee($employeeIds, $data['start_date'], $data['end_date']);
        foreach ($employeeIds as $employeeId) {
            $tags[$employeeId]['tags']['off_branch']['count'] = 0;
            $tags[$employeeId]['tags']['off_branch']['unit'] = 'incidents';
            $tags[$employeeId]['tags']['off_branch']['data'] = [];
        }
        foreach ($offBranchIncidents as $employeeId => $incidents) {
            $tags[$employeeId]['tags']['off_branch']['count'] = count($incidents);
            foreach ($incidents as $incident) {
                $tags[$employeeId]['tags']['off_branch']['data'][] = [
                    'type' => 'cico',
                    'id' => $incident->id,
                    'date' => $incident->date,
                ];
            }
        }
    }

    public function handleExtraWorkdayTag($employeeIds, $data, &$tags)
    {

        $attendancesAsExtraWorkdays = $this->attendanceRepository->getApprovedExtraWorkdaysGroupedByEmployeeIds($employeeIds, $data['start_date'], $data['end_date']);

        // $extraWorkdays = $this->extraWorkdayRequestRepository->getApprovedRequestsForEmployeeInDate($employeeIds, $data['start_date'], $data['end_date']);
        foreach ($employeeIds as $employeeId) {
            $tags[$employeeId]['tags']['extra_workday']['count'] = 0;
            $tags[$employeeId]['tags']['extra_workday']['unit'] = 'days';
            $tags[$employeeId]['tags']['extra_workday']['data'] = [];
        }
        foreach ($attendancesAsExtraWorkdays as $employeeId => $extraWorkdays) {
            $tags[$employeeId]['tags']['extra_workday']['count'] = count($extraWorkdays);

            // $workflowIds = $extraWorkdays->pluck('workflow_id')->toArray();
            // $attendanceDates = $extraWorkdaysObj->pluck('extra_work_day_date')->toArray();
            // $attendances = $this->attendanceRepository->getAttendancesOnDates($employeeId, $attendanceDates);
            // $attendanceDict = $extraWorkdaysObj->mapWithKeys(function ($item) {
            //     return [
            //         $item->date => $item->slotable_id,
            //     ];
            // });
            foreach ($extraWorkdays as $extraWorkday) {
                if (isset($extraWorkday->extra_work_day_date)) {
                    $tags[$employeeId]['tags']['extra_workday']['data'][] = [
                        'id' => $extraWorkday->slotable_id,
                        'compensation_rate' => $extraWorkday->compensation_rate,
                        'date' => $extraWorkday->extra_work_day_date,
                    ];
                }
            }

        }
    }

    public function handleExcuseBalanceTag($employeeIds, &$tags)
    {
        foreach ($employeeIds as $employeeId) {
            $tags[$employeeId]['tags']['excuse']['count'] = 0;
            $tags[$employeeId]['tags']['excuse']['unit'] = 'hours';
            $tags[$employeeId]['tags']['excuse']['data'] = [];
        }

        $currentPayroll = $this->payrollRepository->getLastDraftedPayroll();
        $excuseBalance = $this->excuseBalanceRepository->getRemainingBalanceByDateGroupedByEmployeeIds($employeeIds, $currentPayroll->start);
        foreach ($excuseBalance as $employeeId => $excuseBalanceObj) {
            if (isset($excuseBalanceObj) && isset($excuseBalanceObj->base_hours)) {
                $consumedBalance = $excuseBalanceObj?->base_hours - $excuseBalanceObj?->remaining_hours;
            } else {
                $consumedBalance = 0;
            }
            $tags[$employeeId]['tags']['excuse']['count'] = $consumedBalance;
            $tags[$employeeId]['tags']['excuse']['unit'] = 'hours';
            $tags[$employeeId]['tags']['excuse']['data'] = [];
        }
    }

    public function handleMissingWorkingHours($employeeId, $timecards, &$tags)
    {
        $missingWorkingHours = $this->getMissingWorkingHours($employeeId, $timecards);
        $tags[$employeeId]['tags']['missing_working_hours']['count'] = ($missingWorkingHours > 0) ? round($missingWorkingHours, 2) : 0;
        $tags[$employeeId]['tags']['missing_working_hours']['unit'] = 'hours';
        $tags[$employeeId]['tags']['missing_working_hours']['data'] = [];
    }

    private function getMissingWorkingHours($employeeId, $timecards)
    {
        $missingHours = 0;
        $employeeTimecards = $timecards->where('employee_id', $employeeId);

        foreach ($employeeTimecards as $timecard) {
            $this->calculateLateHours($timecard, $missingHours);
            $this->calculateOvertimeHours($timecard, $missingHours);
        }

        $earlyClockOutHoursDictionary = $this->calculateEarlyClockOutHours($employeeTimecards);
        foreach ($earlyClockOutHoursDictionary as $date => $hours) {
            $missingHours += $hours;
        }

        return $missingHours;
    }

    private function calculateAbsentHours($timecard, &$missingHours)
    {
        if ($timecard->entityTags->pluck('tag')->contains('absent') || $timecard->entityTags->pluck('tag')->contains('absent_without_permission')) {
            $employee = $timecard->employee;
            $worktypePolicy = $employee->title->worktypePolicy;
            if (isset($worktypePolicy->start_time) && isset($worktypePolicy->end_time)) {
                $missingHours += Carbon::parse($worktypePolicy->end_time)->diffInHours(Carbon::parse($worktypePolicy->start_time));
            } else {
                $missingHours += $employee->title->working_hours;
            }
        }
    }

    private function calculateLateHours($timecard, &$missingHours)
    {
        if (isset($timecard->attendance->attendanceDeductions) && $timecard->attendance->entityTags->pluck('tag')->contains('late')) {
            $deduction = $timecard->attendance->attendanceDeductions->first();

            if (isset($deduction)) {
                $deductionMinutes = $deduction->deduction_minutes;
                $missingHours += $deductionMinutes / 60;
            }
        }
    }

    private function calculateEarlyClockOutHours($timecards)
    {
        $timecardDictionary = [];

        foreach ($timecards as $timecard) {
            if (isset($timecard->attendance) && $timecard->attendance->entityTags->pluck('tag')->contains('early_clock_out')) {
                $shiftEnd = Carbon::parse($timecard->to);
                $clockOut = Carbon::parse($timecard->attendance->clockOut->date ?? $timecard->to);
                $dateKey = Carbon::parse($timecard->from)->format('Y-m-d');

                // Calculate early clock-out hours
                $earlyClockOutHours = $clockOut->diffInMinutes($shiftEnd, false) / 60;

                // Initialize the date key if not set
                if (!isset($timecardDictionary[$dateKey])) {
                    $timecardDictionary[$dateKey] = 0;
                }

                // Add the early clock-out hours to the dictionary
                $timecardDictionary[$dateKey] += $earlyClockOutHours;

                // Check other timecards on the same date
                $otherTimecards = $timecards
                    ->filter(fn($tc) => Carbon::parse($tc->from)->format('Y-m-d') === $dateKey && $tc->id !== $timecard->id);

                foreach ($otherTimecards as $otherTimecard) {
                    $clockIn = Carbon::parse($otherTimecard->attendance->clockIn->date ?? $otherTimecard->from);
                    $clockOutOther = Carbon::parse($otherTimecard->attendance->clockOut->date ?? $otherTimecard->to);

                    $workedHours = max(0, $clockOutOther->diffInMinutes($clockIn, false) / 60);

                    // Safely update the dictionary value
                    $timecardDictionary[$dateKey] = max(0, ($timecardDictionary[$dateKey] ?? 0) - $workedHours);
                }
            }
        }

        return $timecardDictionary;
    }

    private function calculateOvertimeHours($timecard, &$missingHours)
    {
        if (isset($timecard->attendance->attendanceOvertimes) && $timecard->attendance->entityTags->pluck('tag')->contains('overtime')) {
            if ($timecard?->attendance?->attendanceOvertimes?->first()?->status == 'approved') {
                $overtimeMinutes = $timecard->attendance->attendanceOvertimes->first()->overtime_minutes;
                $missingHours -= $overtimeMinutes / 60;
            }
        }
    }
}
