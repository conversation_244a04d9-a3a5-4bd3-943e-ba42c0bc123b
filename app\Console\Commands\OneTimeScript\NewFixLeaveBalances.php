<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Company;
use App\Models\CompanyLeaveType;
use App\Models\Employee;
use App\Models\EmployeeLeaveBalance;
use App\Models\EmployeeLeaveRequest;
use App\Models\PublicHolidaysAttendance;
use App\Repositories\Repository;
use App\Repositories\V1\Holidays\PublicHolidaysAttendanceRepository;
use App\Repositories\V1\Leaves\CompanyDefaultLeaveTypesRepository;
use App\Services\LeaveManagement\BusinessServices\BalanceAggregatorService;
use App\Util\HolidaysUtil;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NewFixLeaveBalances extends Command
{
    public function __construct(
        private BalanceAggregatorService $balanceAggregatorService,
        private CompanyDefaultLeaveTypesRepository $companyDefaultLeaveTypesRepository,
        private PublicHolidaysAttendanceRepository $publicHolidayAttendanceRepository
    ) {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'new:fix:leaves:balances';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $employeeLeaveBalanceRepository = Repository::getRepository('EmployeeLeaveBalance');
        DB::beginTransaction();
        try {
            $dummyOutput = new \stdClass;
            $employeeLeaveBalances = EmployeeLeaveBalance::with('employee:id,company_id', 'employee.user', 'employee.title.publicHolidaysPolicy')
                ->get();

            foreach ($employeeLeaveBalances as $employeeLeaveBalance) {
                if (isset($employeeLeaveBalance->employee) && isset($employeeLeaveBalance->employee->user)) {
                    $companyDefaultPublicHolidayId = $this->companyDefaultLeaveTypesRepository->getHolidayLeaveType($employeeLeaveBalance->employee->company_id)?->companyLeaveType?->id ?? null;

                    if (isset($companyDefaultPublicHolidayId) && $companyDefaultPublicHolidayId == $employeeLeaveBalance->company_leave_type_id) {
                        $isPublicHolidayCustomOrLeaveBalance = ($employeeLeaveBalance->employee->title?->publicHolidaysPolicy?->compensation_method != HolidaysUtil::EXTRA_WORK_DAY);
                        $totalRemaining = $this->publicHolidayAttendanceRepository->getSumRemainingBalanceInRangeForEmployee($employeeLeaveBalance->employee_id)?->net_quantity ?? 0;
                        if ($employeeLeaveBalance->balance != $totalRemaining + $employeeLeaveBalance->added_hours && $isPublicHolidayCustomOrLeaveBalance) {
                            $employeeLeaveBalance->balance = $totalRemaining + $employeeLeaveBalance->added_hours;
                            $employeeLeaveBalance->save();
                            echo 'updated balance for employee '.$employeeLeaveBalance->employee_id.PHP_EOL;
                        }

                    }
                }

            }

            //            foreach ($employeeLeaveBalances as $employeeLeaveBalance) {
            //                if (isset($employeeLeaveBalance->employee) && isset($employeeLeaveBalance->employee->user)) {
            //                    $companyDefaultPublicHolidayId = $this->companyDefaultLeaveTypesRepository->getHolidayLeaveType($employeeLeaveBalance->employee->company_id)?->companyLeaveType?->id ?? null;
            //                    $request = [];
            //
            //                    if (isset($companyDefaultPublicHolidayId) && $companyDefaultPublicHolidayId == $employeeLeaveBalance->company_leave_type_id) {
            //                        $request['is_public_holiday'] = true;
            //                    }
            //                    $request['id'] = $employeeLeaveBalance->id;
            //                    $request['employee_id'] = $employeeLeaveBalance->employee_id;
            //                    $this->balanceAggregatorService->perform($request, $dummyOutput);
            //                }
            //
            //            }
            //            $leavePayedMap = [];
            //            $publicHolidayAttendanceBalances = PublicHolidaysAttendance::where('days_used_as_leave', '>', 0)->get();
            //            foreach($publicHolidayAttendanceBalances as $balance){
            //                $companyDefaultPublicHolidayId = $this->companyDefaultLeaveTypesRepository->getHolidayLeaveType($balance->company_id)?->companyLeaveType?->id ?? null;
            //                $approvedLeaves = EmployeeLeaveRequest::where('company_leave_type_id', $companyDefaultPublicHolidayId)->where('employee_id', $balance->employee_id)->where('status', 'approved')->get();
            //                $approvedLeavesSum = 0;
            //                foreach($approvedLeaves as $approvedLeave){
            //                    if(!isset($leavePayedMap[$approvedLeave->id]) ) {
            //                        $approvedLeavesSum += $approvedLeave->net_quantity / 8;
            //                        $leavePayedMap[$approvedLeave->id] = 1;
            //                    }
            //                }
            //
            //                $balance->days_used_as_leave = $approvedLeavesSum;
            //                $balance->remaining_days = $balance->holiday_compensation - ($balance->days_used_as_leave + $balance->days_used_as_pay);
            //                $balance->remaining_days = max(0,$balance->remaining_days);
            //                $balance->save();
            //            }

            DB::commit();
        } catch (Exception $e) {

            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
            // dd($e);
        }
    }
}
