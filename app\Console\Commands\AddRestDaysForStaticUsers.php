<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Models\EmployeeLeaveRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AddRestDaysForStaticUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:add-rest-days-for-static-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Starting to add rest days for static users');
            $employeesAdded = [];
            DB::beginTransaction();
            $employees = Employee::whereIn('branch_id', [177, 185])->get();
            foreach ($employees as $employee) {
                $employeeHasRestDay = EmployeeLeaveRequest::where('employee_id', $employee->id)->where('company_leave_type_id', 97)->where('from', '2025-03-30')->where('to', '2025-03-30')->exists();
                if (! $employeeHasRestDay) {
                    $employeesAdded[] = $employee->id;
                    EmployeeLeaveRequest::create([
                        'employee_id' => $employee->id,
                        'company_leave_type_id' => 97,
                        'company_leave_type_policy_id' => 78,
                        'status' => 'approved',
                        'net_quantity' => 1,
                        'type' => 'regular',
                        'branch_id' => $employee->branch_id,
                        'from' => '2025-03-30',
                        'to' => '2025-03-30',
                        'status' => 'approved',
                    ]);
                }
            }
            $this->info('Added rest days for '.count($employeesAdded).' employees');
            $this->info('Employees: '.implode(', ', $employeesAdded));
            $this->info('Finished adding rest days for static users');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
        }
    }
}
