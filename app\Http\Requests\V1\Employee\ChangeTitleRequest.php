<?php

namespace App\Http\Requests\V1\Employee;

use App\Enums\Titles\ChangeTitleType;
use App\Enums\Titles\EffectiveOnType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;

class ChangeTitleRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'title_id' => [
                'required',
                'integer',
                'exists:titles,id',
            ],
            'role_id' => [
                'required',
                'integer',
                'exists:spatie_roles,id',
            ],
            'effective_on' => [
                'required',
                'string',
                Rule::enum(EffectiveOnType::class),
            ],
            'type' => [
                'required',
                'string',
                Rule::enum(ChangeTitleType::class),
            ],
            'net_salary' => [
                'nullable',
                'integer',
            ],
            'branch_ids' => [
                'sometimes',
                'array',
            ],
            'department_ids' => [
                'sometimes',
                'array',
            ],
            'sub_department_ids' => [
                'sometimes',
                'array',
            ],
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'net_salary' => Gate::allows('manage_company') ? $this->input('net_salary', 0) : 0,
        ]);
    }
}
