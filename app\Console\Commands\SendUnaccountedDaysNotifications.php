<?php

namespace App\Console\Commands;

use App\Services\V1\SendUnaccountedDaysNotificationsService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SendUnaccountedDaysNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:unaccounted-days-notifications';

    /**
     * The console command description.
     */
    public function __construct(private SendUnaccountedDaysNotificationsService $service)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        DB::beginTransaction();
        try {
            $this->service->send();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
