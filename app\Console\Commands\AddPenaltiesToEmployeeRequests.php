<?php

namespace App\Console\Commands;

use App\Models\EmployeeRequest;
use App\Models\Penalty;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AddPenaltiesToEmployeeRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:add-penalties-to-employee-requests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            echo "Adding penalties to employee requests\n";

            $penalties = Penalty::all();

            echo 'Penalties count: '.count($penalties)."\n";
            foreach ($penalties as $penalty) {
                if ($penalty->employeeRequest()->exists()) {
                    echo 'Penalty already added to employee request: '.$penalty->id."\n";

                    continue;
                }
                echo 'Adding penalty to employee request: '.$penalty->id."\n";
                EmployeeRequest::create([
                    'company_id' => $penalty->company_id,
                    'requestable_id' => $penalty->id,
                    'requestable_type' => 'penalty',
                    'request_name' => 'penalty',
                    'status' => $penalty->status,
                    'date' => $penalty->date,
                    'employee_id' => $penalty->employee_id,
                ]);
            }

            echo "Penalties added to employee requests\n";
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
        }
    }
}
