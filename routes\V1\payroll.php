<?php

use App\Http\Controllers\Payroll\PayrollController;
use App\Http\Controllers\Payroll\PayslipController;
use App\Http\Controllers\PayrollSetup\PayrollSetupController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Monolog\Handler\StreamHandler;
use Monolog\Level;
use Monolog\Logger;

Route::prefix('payroll')->group(function () {

    Route::get('calculation-details', [PayrollController::class, 'getEmployeePayrollCalculation'])
        ->middleware(['permission:manage_payroll|view_payroll']);

    Route::get('export-salaries', [PayrollSetupController::class, 'exportEmployeeSalariesByFilter'])
        ->middleware(['permission:manage_payroll_settings|manage_payroll_settings|manage_employee_salary_info|view_employee_salary_info']);

    Route::get('convert-net-to-gross', [PayrollController::class, 'calculateGrossSalary']);
    // ->middleware(['permission:manage_payroll|view_payroll']);

    Route::get('salary-calculator', [PayrollController::class, 'calculateSalaryThroughCalculator']);

    Route::prefix('payslip')->name('payslip.')->group(function () {
        Route::get('download', [PayslipController::class, 'download'])->name('download');

        // Get employees with filters for payslip management
        Route::get('employees', [PayslipController::class, 'getEmployees'])->name('employees');

        // Store ZIP file in storage/app and return download info
        Route::post('store', function (App\Http\Requests\V1\StorePayslipRequest $request) {
            try {
                $data = $request->validated();

                // Use the existing PayslipService to generate ZIP
                $payslipService = app(App\Services\V1\Payroll\PayslipService::class);

                $result = $payslipService->generatePayslipsZipForDownload([
                    'end_date' => $data['end_date'],
                    'employee_ids' => $data['employee_ids'] ?? null,
                    'lang' => $data['lang'] ?? 'en'
                ]);

                // Create storage path
                $companyId = auth()->user()?->company_id ?? 'default';
                $storagePath = "payslips/company_{$companyId}/" . date('Y/m');
                $fullPath = $storagePath . '/' . $result['filename'];

                // Store the ZIP file in storage/app
                Storage::disk('local')->makeDirectory($storagePath);
                Storage::disk('local')->put($fullPath, $result['content']);

                // Generate download URL
                $downloadUrl = url("/api/v1/payroll/payslip/download-stored/" . base64_encode($fullPath));

                return response()->json([
                    'success' => true,
                    'message' => 'Payslips ZIP file stored successfully',
                    'data' => [
                        'filename' => $result['filename'],
                        'storage_path' => $fullPath,
                        'download_url' => $downloadUrl,
                        'total_employees' => $result['total_employees'],
                        'successfully_downloaded' => $result['successfully_downloaded'],
                        'file_size' => strlen($result['content']) . ' bytes'
                    ]
                ]);

            } catch (\Exception $e) {
                \Log::error('Failed to store payslips ZIP', [
                    'error' => $e->getMessage(),
                    'request_data' => $request->all()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Failed to store payslips ZIP file',
                    'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
                ], 500);
            }
        })->name('store');

        // Download stored ZIP file
        Route::get('download-stored/{encodedPath}', function ($encodedPath) {
            try {
                $path = base64_decode($encodedPath);

                if (!$path || !Storage::disk('local')->exists($path)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'ZIP file not found or has been deleted'
                    ], 404);
                }

                $content = Storage::disk('local')->get($path);
                $filename = basename($path);

                return response($content)
                    ->header('Content-Type', 'application/zip')
                    ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                    ->header('Content-Length', strlen($content))
                    ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                    ->header('Pragma', 'no-cache')
                    ->header('Expires', '0');

            } catch (\Exception $e) {
                \Log::error('Failed to download stored ZIP', [
                    'encoded_path' => $encodedPath,
                    'error' => $e->getMessage()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Failed to download ZIP file',
                    'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
                ], 500);
            }
        })->name('download-stored');
    });

    Route::get('/test-logs', function () {
        $logger = new Logger('test-logger');
        $logger->pushHandler(new StreamHandler(storage_path('logs/datadog.log'), Level::Debug));

        $logger->info('This is an info test log');
        $logger->debug('This is a debug test log');
        $logger->error('This is an error test log');

        return response()->json(['message' => 'Logs have been written']);
    });

    require("payroll-hub.php");
});
