<?php

use App\Http\Controllers\Payroll\PayrollController;
use App\Http\Controllers\Payroll\PayslipController;
use App\Http\Controllers\PayrollSetup\PayrollSetupController;
use Illuminate\Support\Facades\Route;
use Monolog\Handler\StreamHandler;
use Monolog\Level;
use Monolog\Logger;

Route::prefix('payroll')->group(function () {

    Route::get('calculation-details', [PayrollController::class, 'getEmployeePayrollCalculation'])
        ->middleware(['permission:manage_payroll|view_payroll']);

    Route::get('export-salaries', [PayrollSetupController::class, 'exportEmployeeSalariesByFilter'])
        ->middleware(['permission:manage_payroll_settings|manage_payroll_settings|manage_employee_salary_info|view_employee_salary_info']);

    Route::get('convert-net-to-gross', [PayrollController::class, 'calculateGrossSalary']);
    // ->middleware(['permission:manage_payroll|view_payroll']);

    Route::get('salary-calculator', [PayrollController::class, 'calculateSalaryThroughCalculator']);

    Route::prefix('payslip')->name('payslip.')->group(function () {
        Route::post('download', [PayslipController::class, 'download'])->name('download');

        // Get employees with filters for payslip management
        Route::get('employees', [PayslipController::class, 'getEmployees'])->name('employees');
    });

    Route::get('/test-logs', function () {
        $logger = new Logger('test-logger');
        $logger->pushHandler(new StreamHandler(storage_path('logs/datadog.log'), Level::Debug));

        $logger->info('This is an info test log');
        $logger->debug('This is a debug test log');
        $logger->error('This is an error test log');

        return response()->json(['message' => 'Logs have been written']);
    });

    require("payroll-hub.php");
});
