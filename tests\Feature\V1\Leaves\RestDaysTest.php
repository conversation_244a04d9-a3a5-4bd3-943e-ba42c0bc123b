<?php

namespace Tests\Feature\V1\Leaves;

use App\Enums\V2\WorkTypes\AnyLocationTypesEnum;
use App\Enums\V2\WorkTypes\RestDaysTypesEnum;
use App\Enums\V2\WorkTypes\WorkTypesEnum;
use App\FeatureToggles\Unleash;
use App\Jobs\GenerateTimecardsForStaticTitles;
use App\Models\Attendance;
use App\Models\AttendanceSetting;
use App\Models\Branch;
use App\Models\Cico;
use App\Models\Company;
use App\Models\CompanyLeaveType;
use App\Models\CompanyLeaveTypePolicy;
use App\Models\Country;
use App\Models\EmployeeLeaveBalance;
use App\Models\EmployeeLeaveRequest;
use App\Models\EntityTag;
use App\Models\Permission;
use App\Models\PublicHoliday;
use App\Models\Role;
use App\Models\Timecard;
use App\Models\TimecardType;
use App\Models\Title;
use App\Models\User;
use App\Models\WorkTypePolicy;
use App\Services\V1\Attendance\GenerateTimecardsService;
use App\Util\AttendanceUtil;
use App\Util\UserWorkTypesUtil;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use Tests\TestCase;
use Tests\Traits\DatabaseSetupTrait;

uses(TestCase::class,
//    RefreshOnlyBluworksDatabase::class,
    DatabaseSetupTrait::class,
    WithFaker::class);

beforeEach(function () {
    // Setup database for tests
    $this->setUpDatabase();

    // Set to first day of the month
    $firstDayOfMonth = now()->startOfMonth();
    Carbon::setTestNow($firstDayOfMonth);

    // Create Egypt as the test country with all required attributes
    $this->country = Country::factory()->create();

    // Create a test company with the Egypt country
    $this->company = Company::factory()->create([
        'country_id' => $this->country->id,
    ]);

    // Create a company admin user for testing
    $this->user = User::factory()->state([
        'company_id' => $this->company->id,
        'is_admin' => 1,
    ])->create();

    // Mock Unleash feature toggle - enabled for new work types and unscheduled shifts
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag', 'getUnscheduledShiftsFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $mock
        ->expects($this->any())
        ->method('getUnscheduledShiftsFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create titles for the company
    $this->titles = Title::factory()->count(3)->create([
        'company_id' => $this->company->id,
    ]);

    // Create default leave types for the company with unique names
    $this->annualLeave = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة سنوية',
        'name_en' => 'Annual Leave',
        'name' => 'annual_leave_' . uniqid(), // Ensure unique name
        'is_primary' => true,
        'balance_period' => 'calendar_year',
        'gender' => 'all',
    ]);

    $this->sickLeave = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'إجازة مرضية',
        'name_en' => 'Sick Leave',
        'name' => 'sick_leave_' . uniqid(), // Ensure unique name
        'is_primary' => true,
        'balance_period' => 'calendar_year',
        'gender' => 'all',
    ]);

    $this->restDay = CompanyLeaveType::factory()->create([
        'company_id' => $this->company->id,
        'name_ar' => 'يوم راحة',
        'name_en' => 'Rest Day',
        'name' => 'rest_day_' . uniqid(), // Ensure unique name
        'is_primary' => true,
        'balance_period' => 'calendar_month',
        'gender' => 'all',
    ]);

    // Link leave types to company through specific columns
    $this->company->update([
        'annual_leave_id' => $this->annualLeave->id,
        'sick_leave_id' => $this->sickLeave->id,
        'rest_day_leave_id' => $this->restDay->id,
    ]);

    // Create policies for each leave type
    CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $this->annualLeave->id,
        'base_balance' => 168, // 21 days * 8 hours
        'unit' => 'hours',
    ]);

    CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $this->sickLeave->id,
        'base_balance' => 240, // 30 days * 8 hours
        'unit' => 'hours',
    ]);

    CompanyLeaveTypePolicy::factory()->create([
        'company_id' => $this->company->id,
        'company_leave_type_id' => $this->restDay->id,
        'base_balance' => 8, // 1 day * 8 hours
        'unit' => 'hours',
    ]);

    TimecardType::factory()->create([
        'company_id' => $this->company->id,
    ]);

    // Add helper methods to the test object
    $this->setupRolesAndPermissions = function () {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $manageWorkerPermission = Permission::where('name', 'manage_worker')->first() ?? null;
        if (!isset($manageWorkerPermission)) {
            Permission::factory()->state(['name' => 'manage_worker', 'company_id' => $this->company->id])->create();
        }

        $manageSchedulePermission = Permission::where('name', 'manage_schedule')->first() ?? null;
        if (!isset($manageSchedulePermission)) {
            Permission::factory()->state(['name' => 'manage_schedule', 'company_id' => $this->company->id])->create();
        }

        $permission = Permission::where('name', 'any_location')->first() ?? null;
        if (!isset($permission)) {
            Permission::factory()->state(['name' => 'any_location', 'company_id' => $this->company->id])->create();
        }

        $manualAttendancePermission = Permission::where('name', 'manual_attendnace')->first() ?? null;
        if (!isset($manualAttendancePermission)) {
            Permission::factory()->state(['name' => 'manual_attendnace', 'company_id' => $this->company->id])->create();
        }

        $viewCicoPermission = Permission::where('name', 'view_cico')->first() ?? null;
        if (!isset($viewCicoPermission)) {
            Permission::factory()->state(['name' => 'view_cico', 'company_id' => $this->company->id])->create();
        }

        $this->workerRole = Role::factory()->state(['name' => 'Worker Role', 'company_id' => $this->company->id])->create();
        $this->workerRole->givePermissionTo('manage_worker', 'manage_worker');

        // Create admin role and give it the manage_schedule permission
        $adminRole = Role::factory()->state(['name' => 'Admin Role', 'company_id' => $this->company->id])->create();
        $adminRole->givePermissionTo('manage_schedule', 'manual_attendnace', 'view_cico');

        // Assign admin role to the user making the API calls
        $this->user->assignRole($adminRole);

        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    };

    $this->setWorkerUser = function (string $workType = UserWorkTypesUtil::FIXED_HYBRID) {
        $this->worker = User::factory()->state(['company_id' => $this->company->id])->create();
        $this->workTypePolicy = WorkTypePolicy::factory()->state([
            'company_id' => $this->company->id,
            'work_days_type' => $workType,
            'work_days' => 'sun,mon,tue,wed',
            'rest_days' => 'thu,fri,sat',
        ])->create();

        $this->worker->assignRole($this->workerRole);
        $this->workerEmployee = $this->worker->employee;
        $this->workerEmployee->title->work_type_policy_id = $this->workTypePolicy->id;
        $this->workerEmployee->title->save();
    };

    $this->createSecondaryBranch = function () {
        $this->secondBranch = Branch::factory()->create(['company_id' => $this->company->id]);
    };

    $this->createTimecardAndAttendance = function () {
        $timecard = Timecard::factory()->create([
            'employee_id' => $this->workerEmployee->id,
            'branch_id' => $this->workerEmployee->branch_id,
            'from' => now(),
            'to' => now()->addHours(8),
            'required_ci_branch_id' => $this->workerEmployee->branch_id,
            'required_co_branch_id' => $this->workerEmployee->branch_id,
        ]);

        $cico = Cico::factory()->state([
            'employee_id' => $this->workerEmployee->id,
            'branch_id' => $this->workerEmployee->branch_id,
            'status' => AttendanceUtil::CICO_VERIFIED_STATUS,
            'date' => now()->subHour()->format('Y-m-d H:i:s'),
        ])->create();

        $attendance = Attendance::factory()->state([
            'employee_id' => $this->workerEmployee->id,
            'branch_id' => $this->workerEmployee->branch_id,
            'slotable_id' => $timecard->id,
            'slotable_type' => 'time_card',
            'company_id' => $this->workerEmployee->company_id,
            'ci_id' => $cico->id,
        ])->create();

        return ['timecard' => $timecard, 'cico' => $cico, 'attendance' => $attendance];
    };

    $this->createAttendanceSetting = function (string $key, int $value, bool $isUsed = true) {
        return AttendanceSetting::factory()->create([
            'company_id' => $this->company->id,
            'key' => $key,
            'value' => $value,
            'is_used' => $isUsed,
        ]);
    };
});

afterEach(function () {
    $this->tearDownDatabase();
    Carbon::setTestNow(); // Reset to real time
});

test('can generate rest days for fixed hybrid work type employees', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - disabled for old work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(false);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with fixed hybrid work type
    ($this->setWorkerUser)(UserWorkTypesUtil::FIXED_HYBRID);

    // Define specific rest days
    $this->workTypePolicy->update([
        'rest_days_count' => 2,
        'rest_days' => 'thu,fri',
        'work_days_count' => 5,
        'work_days' => 'sun,mon,tue,wed,sat',
    ]);

    // Find the next Sunday (a work day)
    $workDate = now()->next('Sunday');
    Carbon::setTestNow($workDate);

    // Generate timecards via the service
    $generateService = app(GenerateTimecardsService::class);
    $generateService->generateTimecards(
        [
            [
                'title_id' => $this->workerEmployee->title_id,
                'start_time' => '09:00',
                'end_time' => '17:00',
                'rest_days' => $this->workTypePolicy->rest_days,
                'work_days_type' => $this->workTypePolicy->work_days_type,
                'employee_id' => $this->workerEmployee->id,
            ],
        ],
        $workDate->format('Y-m-d')
    );

    // Check for timecard on work day (Sunday)
    $timecard = Timecard::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $workDate->format('Y-m-d'))
        ->first();

    expect($timecard)->not->toBeNull();

    // Find the next Thursday (a rest day)
    $restDate = now()->next('Thursday');
    Carbon::setTestNow($restDate);

    // Generate timecards for rest day
    // We need to use the day before the rest day since the service generates for the next day
    $dayBeforeRest = $restDate->copy()->subDay();
    $generateService->generateTimecards(
        [
            [
                'title_id' => $this->workerEmployee->title_id,
                'start_time' => '09:00',
                'end_time' => '17:00',
                'rest_days' => $this->workTypePolicy->rest_days,
                'work_days_type' => $this->workTypePolicy->work_days_type,
                'employee_id' => $this->workerEmployee->id,
            ],
        ],
        $dayBeforeRest->format('Y-m-d')
    );

    // Check that no timecard was created for rest day
    $restDayTimecard = Timecard::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->first();

    expect($restDayTimecard)->toBeNull();

    // But a rest day should be recorded in employee leaves
    $restDayLeave = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($restDayLeave)->not->toBeNull();
});

test('can generate rest days for fixed on-site work type employees', function () {
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - disabled for old work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(false);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with fixed on-site work type
    ($this->setWorkerUser)(UserWorkTypesUtil::FIXED_ON_SITE);

    // Define specific rest days
    $this->workTypePolicy->update([
        'rest_days' => 'fri,sat',
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Find the next Tuesday (a work day)
    $workDate = now()->next('Tuesday');
    Carbon::setTestNow($workDate);

    // Generate timecards via the service
    $generateService = app(GenerateTimecardsService::class);
    $generateService->generateTimecards(
        [
            [
                'title_id' => $this->workerEmployee->title_id,
                'start_time' => '09:00',
                'end_time' => '17:00',
                'rest_days' => $this->workTypePolicy->rest_days,
                'work_days_type' => $this->workTypePolicy->work_days_type,
                'employee_id' => $this->workerEmployee->id,
            ],
        ],
        $workDate->format('Y-m-d')
    );

    // Check for timecard on work day (Tuesday)
    $timecard = Timecard::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $workDate->format('Y-m-d'))
        ->first();

    expect($timecard)->not->toBeNull();

    // Find the next Friday (a rest day)
    $restDate = now()->next('Friday');
    Carbon::setTestNow($restDate);

    // Generate timecards for rest day
    // We need to use the day before the rest day since the service generates for the next day
    $dayBeforeRest = $restDate->copy()->subDay();
    $generateService->generateTimecards(
        [
            [
                'title_id' => $this->workerEmployee->title_id,
                'start_time' => '09:00',
                'end_time' => '17:00',
                'rest_days' => $this->workTypePolicy->rest_days,
                'work_days_type' => $this->workTypePolicy->work_days_type,
                'employee_id' => $this->workerEmployee->id,
            ],
        ],
        $dayBeforeRest->format('Y-m-d')
    );

    // Check that no timecard was created for rest day
    $restDayTimecard = Timecard::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->first();

    expect($restDayTimecard)->toBeNull();

    // But a rest day should be recorded in employee leaves
    $restDayLeave = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($restDayLeave)->not->toBeNull();
});

test('handles overlapping rest days correctly for multiple work types', function () {
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - disabled for old work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(false);

    $this->app->instance(Unleash::class, $mock);

    // Create workers with different work types
    ($this->setWorkerUser)(UserWorkTypesUtil::FIXED_HYBRID);
    $hybridEmployee = $this->workerEmployee;
    $hybridPolicy = $this->workTypePolicy;

    // Create a second worker with fixed on-site
    ($this->setWorkerUser)(UserWorkTypesUtil::FIXED_ON_SITE);
    $onsiteEmployee = $this->workerEmployee;
    $onsitePolicy = $this->workTypePolicy;

    // Set overlapping rest days
    $hybridPolicy->update([
        'rest_days' => 'thu,fri',
        'work_days' => 'sun,mon,tue,wed,sat',
    ]);

    $onsitePolicy->update([
        'rest_days' => 'fri,sat',
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Find the next Friday (a rest day)
    $restDate = now()->next('Friday');
    Carbon::setTestNow($restDate);

    // Generate timecards for both employees
    // We need to use the day before the rest day since the service generates for the next day
    $dayBeforeRest = $restDate->copy()->subDay();
    $generateService = app(GenerateTimecardsService::class);
    $generateService->generateTimecards(
        [
            [
                'title_id' => $hybridEmployee->title_id,
                'start_time' => '09:00',
                'end_time' => '17:00',
                'rest_days' => $hybridPolicy->rest_days,
                'work_days_type' => $hybridPolicy->work_days_type,
                'employee_id' => $hybridEmployee->id,
            ],
            [
                'title_id' => $onsiteEmployee->title_id,
                'start_time' => '09:00',
                'end_time' => '17:00',
                'rest_days' => $onsitePolicy->rest_days,
                'work_days_type' => $onsitePolicy->work_days_type,
                'employee_id' => $onsiteEmployee->id,
            ],
        ],
        $dayBeforeRest->format('Y-m-d')
    );

    // Check that both employees have rest days
    $hybridRestDay = EmployeeLeaveRequest::where('employee_id', $hybridEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    $onsiteRestDay = EmployeeLeaveRequest::where('employee_id', $onsiteEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($hybridRestDay)->not->toBeNull();
    expect($onsiteRestDay)->not->toBeNull();

    // Check they don't have timecards for rest days
    $hybridTimecard = Timecard::where('employee_id', $hybridEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->first();

    $onsiteTimecard = Timecard::where('employee_id', $onsiteEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->first();

    expect($hybridTimecard)->toBeNull();
    expect($onsiteTimecard)->toBeNull();
});

test('can generate rest days for fixed working hours work type', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with fixed working hours work type
    ($this->setWorkerUser)(WorkTypesEnum::FIXED_WORKING_HOURS->value);

    // Define specific rest days
    $this->workTypePolicy->update([
        'rest_days_type' => 'fixed',
        'rest_days' => 'fri,sat',
        'work_days' => 'sun,mon,tue,wed,thu',
        'work_days_type' => WorkTypesEnum::FIXED_WORKING_HOURS->value,
    ]);

    // Find the next Thursday (a work day)
    $workDate = now()->next('Thursday');
    Carbon::setTestNow($workDate);

    // Generate timecards via the service
    $generateService = app(GenerateTimecardsService::class);
    $timecardData = [
        [
            'title_id' => $this->workerEmployee->title_id,
            'start_time' => '09:00',
            'end_time' => '17:00',
            'rest_days' => $this->workTypePolicy->rest_days,
            'work_days_type' => $this->workTypePolicy->work_days_type,
            'employee_id' => $this->workerEmployee->id,
        ],
    ];

    $generateService->generateTimecards(
        $timecardData,
        $workDate->format('Y-m-d')
    );

    // Check for timecard on work day (Thursday)
    $workDayTimecard = Timecard::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $workDate->format('Y-m-d'))
        ->first();

    expect($workDayTimecard)->not->toBeNull();

    // Find the next Friday (a rest day)
    $restDate = $workDate->copy()->addDay();
    Carbon::setTestNow($restDate);

    // Check that no timecard was created for rest day (Friday)
    $restDayTimecard = Timecard::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->first();

    expect($restDayTimecard)->toBeNull();

    // But a rest day should be recorded in employee leaves
    $restDayLeave = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($restDayLeave)->not->toBeNull();
});

test('does not generate timecards for non-fixed rest days work types', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with flexible working hours work type
    ($this->setWorkerUser)(WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value);

    // Define specific rest days
    $this->workTypePolicy->update([
        'rest_days_type' => 'rotational',
        'rest_days' => 'fri,sat',
        'work_days' => 'sun,mon,tue,wed,thu',
        'work_days_type' => WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value,
    ]);

    // Find the next Thursday (a work day)
    $workDate = now()->next('Thursday');
    Carbon::setTestNow($workDate);

    // Generate timecards via the service
    $generateService = app(GenerateTimecardsService::class);
    $timecardData = [
        [
            'title_id' => $this->workerEmployee->title_id,
            'start_time' => '09:00',
            'end_time' => '17:00',
            'rest_days' => $this->workTypePolicy->rest_days,
            'work_days_type' => $this->workTypePolicy->work_days_type,
            'employee_id' => $this->workerEmployee->id,
        ],
    ];

    $generateService->generateTimecards(
        $timecardData,
        $workDate->format('Y-m-d')
    );

    // Check that no timecard was created for work day (Thursday)
    $workDayTimecard = Timecard::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $workDate->format('Y-m-d'))
        ->first();

    expect($workDayTimecard)->toBeNull();

    // Find the next Friday (a rest day)
    $restDate = $workDate->copy()->addDay();
    Carbon::setTestNow($restDate);

    // Generate timecards for rest day
    $generateService->generateTimecards(
        $timecardData,
        $workDate->format('Y-m-d')
    );

    // Check that no timecard was created for rest day (Friday)
    $restDayTimecard = Timecard::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->first();

    expect($restDayTimecard)->toBeNull();

    // And no rest day should be recorded in employee leaves
    $restDayLeave = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($restDayLeave)->toBeNull();
});

test('can generate rest days for fixed hours work type with fixed rest days', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle for this specific test
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with fixed hours work type
    ($this->setWorkerUser)(WorkTypesEnum::FIXED_WORKING_HOURS->value);

    // Define specific rest days
    $this->workTypePolicy->update([
        'rest_days_type' => 'fixed',
        'rest_days' => 'fri,sat',
        'work_days' => 'sun,mon,tue,wed,thu',
        'work_days_type' => WorkTypesEnum::FIXED_WORKING_HOURS->value,
    ]);

    // Find the next Thursday (a work day)
    $workDate = now()->next('Thursday');
    Carbon::setTestNow($workDate);

    // Generate timecards via the service
    $generateService = app(GenerateTimecardsService::class);
    $timecardData = [
        [
            'title_id' => $this->workerEmployee->title_id,
            'start_time' => '09:00',
            'end_time' => '17:00',
            'rest_days' => $this->workTypePolicy->rest_days,
            'work_days_type' => $this->workTypePolicy->work_days_type,
            'employee_id' => $this->workerEmployee->id,
        ],
    ];

    $generateService->generateTimecards(
        $timecardData,
        $workDate->format('Y-m-d')
    );

    // Check for timecard on work day (Thursday)
    $workDayTimecard = Timecard::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $workDate->format('Y-m-d'))
        ->first();

    expect($workDayTimecard)->not->toBeNull();

    // Find the next Friday (a rest day)
    $restDate = $workDate->copy()->addDay();
    Carbon::setTestNow($restDate);

    // Check that no timecard was created for rest day (Friday)
    $restDayTimecard = Timecard::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->first();

    expect($restDayTimecard)->toBeNull();

    // But a rest day should be recorded in employee leaves
    $restDayLeave = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDate->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($restDayLeave)->not->toBeNull();
});

test('can add work type policy with fixed hybrid work type and fixed rest days', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - disabled for old work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(false);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with fixed hybrid work type
    ($this->setWorkerUser)(UserWorkTypesUtil::FIXED_HYBRID);

    // Fake the bus to enable job assertion
    Bus::fake();

    // Add work type policy via V1 API
    $response = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/v1/admin/company/work-type-policies', [
            'name' => 'Fixed Hybrid Policy',
            'work_days_type' => UserWorkTypesUtil::FIXED_HYBRID,
            'work_days' => 'sun,mon,tue,wed,thu',
            'rest_days' => 'fri,sat',
            'start_time' => '09:00',
            'end_time' => '17:00',
            'title_ids' => [$this->workerEmployee->title_id],
            'apply_any_branch' => 0,
        ]);

    $response->assertStatus(200);

    // Verify that the job was dispatched with correct parameters
    Bus::assertDispatched(GenerateTimecardsForStaticTitles::class);
});

test('can add work type policy with fixed on-site work type and fixed rest days', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - disabled for old work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(false);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with fixed on-site work type
    ($this->setWorkerUser)(UserWorkTypesUtil::FIXED_ON_SITE);

    // Fake the bus to enable job assertion
    Bus::fake();

    // Add work type policy via V1 API
    $response = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/v1/admin/company/work-type-policies', [
            'name' => 'Fixed On-Site Policy',
            'work_days_type' => UserWorkTypesUtil::FIXED_ON_SITE,
            'work_days' => 'sun,mon,tue,wed,thu',
            'rest_days' => 'fri,sat',
            'start_time' => '09:00',
            'end_time' => '17:00',
            'title_ids' => [$this->workerEmployee->title_id],
            'apply_any_branch' => 0,
        ]);

    $response->assertStatus(200);

    // Verify that the job was dispatched with correct parameters
    Bus::assertDispatched(GenerateTimecardsForStaticTitles::class);
});

test('can add work type policy with fixed working hours work type and fixed rest days', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Fake the bus to enable job assertion
    Bus::fake();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with fixed working hours work type
    ($this->setWorkerUser)(WorkTypesEnum::FIXED_WORKING_HOURS->value);

    // Add work type policy via V2 API
    $response = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/v2/admin/company/work-type-policies', [
            'name' => 'Fixed Working Hours Policy',
            'work_days_type' => WorkTypesEnum::FIXED_WORKING_HOURS->value,
            'rest_days_type' => RestDaysTypesEnum::FIXED->value,
            'rest_days' => 'fri,sat',
            'work_days' => 'sun,mon,tue,wed,thu',
            'start_time' => '09:00',
            'end_time' => '17:00',
            'apply_any_branch' => false,
            'any_location_type' => AnyLocationTypesEnum::NEVER->value,
            'apply_any_location' => false,
            'title_ids' => [$this->workerEmployee->title_id],
        ]);

    $response->assertStatus(200);

    // Verify that the job was dispatched with correct parameters
    Bus::assertDispatched(GenerateTimecardsForStaticTitles::class);
});

test('can add work type policy with shift based work type and fixed rest days', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Fake the bus to enable job assertion
    Bus::fake();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with shift based work type
    ($this->setWorkerUser)(WorkTypesEnum::SHIFT_BASED->value);

    // Add work type policy via V2 API
    $response = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/v2/admin/company/work-type-policies', [
            'name' => 'Shift Based Policy',
            'work_days_type' => WorkTypesEnum::SHIFT_BASED->value,
            'rest_days_type' => RestDaysTypesEnum::FIXED->value,
            'rest_days' => 'fri,sat',
            'work_days' => 'sun,mon,tue,wed,thu',
            'apply_any_branch' => false,
            'any_location_type' => AnyLocationTypesEnum::NEVER->value,
            'apply_any_location' => false,
            'title_ids' => [$this->workerEmployee->title_id],
        ]);

    $response->assertStatus(200);

    // Verify that the job was dispatched with correct parameters
    Bus::assertDispatched(GenerateTimecardsForStaticTitles::class);
});

test('can add rest day for employee with rotational rest days', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with flexible working hours work type (rotational rest days)
    ($this->setWorkerUser)(WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value);

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value,
        'rest_days_type' => RestDaysTypesEnum::ROTATIONAL->value,
        'rest_days_count' => 2,
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Create rest day leave balance
    $restDayBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => $this->workTypePolicy->rest_days_count * 8, // Multiply rest days count by 8
    ]);

    // Try to add a rest day
    $restDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/addRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'date' => now()->addDay()->format('Y-m-d'),
        ]);

    $restDayResponse->assertStatus(200);

    // Verify the balance was consumed
    $updatedBalance = EmployeeLeaveBalance::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();
    expect($updatedBalance->balance)->toBe((float)($this->workTypePolicy->rest_days_count * 8) - 8); // Balance should decrease by 8 hours
});

test('can add rest day for employee with dynamic on-site work type', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(false);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with dynamic on-site work type
    ($this->setWorkerUser)('dynamic_onsite');

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => 'dynamic_onsite',
        'rest_days_count' => 2,
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Create rest day leave balance
    $restDayBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => $this->workTypePolicy->rest_days_count * 8, // Multiply rest days count by 8
    ]);

    // Try to add a rest day
    $restDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/addRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'date' => now()->addDay()->format('Y-m-d'),
        ]);

    $restDayResponse->assertStatus(200);

    // Verify the balance was consumed
    $updatedBalance = EmployeeLeaveBalance::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();
    expect($updatedBalance->balance)->toBe((float)($this->workTypePolicy->rest_days_count * 8) - 8); // Balance should decrease by 8 hours
});

test('cannot add rest day for employee with fixed on-site work type', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - disabled for old work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(false);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with fixed on-site work type
    ($this->setWorkerUser)(UserWorkTypesUtil::FIXED_ON_SITE);

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => UserWorkTypesUtil::FIXED_ON_SITE,
        'work_days' => 'sun,mon,tue,wed,thu',
        'rest_days' => 'fri,sat',
        'start_time' => '09:00',
        'end_time' => '17:00',
    ]);

    // Try to add a rest day
    $restDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/addRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'date' => now()->addDay()->format('Y-m-d'),
        ]);

    $restDayResponse->assertStatus(422);
});

test('cannot add rest day for employee with fixed rest days', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with fixed working hours work type
    ($this->setWorkerUser)(WorkTypesEnum::FIXED_WORKING_HOURS->value);

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => WorkTypesEnum::FIXED_WORKING_HOURS->value,
        'rest_days_type' => RestDaysTypesEnum::FIXED->value,
        'rest_days' => 'fri,sat',
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Try to add a rest day
    $restDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/addRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'date' => now()->addDay()->format('Y-m-d'),
        ]);

    $restDayResponse->assertStatus(422);
});

test('can delete rest day for employee with rotational rest days', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with flexible working hours work type (rotational rest days)
    ($this->setWorkerUser)(WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value);

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value,
        'rest_days_type' => RestDaysTypesEnum::ROTATIONAL->value,
        'rest_days_count' => 2,
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Create rest day leave balance with one rest day already consumed
    $restDayBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => ($this->workTypePolicy->rest_days_count * 8) - 8, // Start with one rest day already consumed
    ]);

    // Create a rest day first
    $restDay = EmployeeLeaveRequest::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'from' => now()->addDay()->startOfDay(),
        'to' => now()->addDay()->endOfDay(),
        'status' => 'approved',
        'branch_id' => $this->workerEmployee->branch_id,
        'net_quantity' => 8,
        'by_admin' => null,
    ]);

    // Try to delete the rest day
    $restDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/deleteRestDays', [
            'ids' => [$restDay->id],
        ]);

    $restDayResponse->assertStatus(200);

    // Verify the rest day was deleted
    $deletedRestDay = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', now()->addDay()->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($deletedRestDay)->toBeNull();

    // Verify the balance was returned
    $updatedBalance = EmployeeLeaveBalance::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();
    expect($updatedBalance->balance)->toBe((float)$this->workTypePolicy->rest_days_count * 8); // Balance should be fully restored
});

test('can delete rest day for employee with dynamic on-site work type', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with dynamic on-site work type
    ($this->setWorkerUser)('dynamic_onsite');

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => 'dynamic_onsite',
        'rest_days_type' => RestDaysTypesEnum::ROTATIONAL->value,
        'rest_days_count' => 2,
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Create rest day leave balance with one rest day already consumed
    $restDayBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => ($this->workTypePolicy->rest_days_count * 8) - 8, // Start with one rest day already consumed
    ]);

    // Create a rest day first
    $restDay = EmployeeLeaveRequest::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'from' => now()->addDay()->startOfDay(),
        'to' => now()->addDay()->endOfDay(),
        'status' => 'approved',
        'branch_id' => $this->workerEmployee->branch_id,
        'net_quantity' => 8,
        'by_admin' => null,
    ]);

    // Try to delete the rest day
    $restDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/deleteRestDays', [
            'ids' => [$restDay->id],
        ]);

    $restDayResponse->assertStatus(200);

    // Verify the rest day was deleted
    $deletedRestDay = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', now()->addDay()->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($deletedRestDay)->toBeNull();

    // Verify the balance was returned
    $updatedBalance = EmployeeLeaveBalance::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();
    expect($updatedBalance->balance)->toBe((float)$this->workTypePolicy->rest_days_count * 8); // Balance should be fully restored
});

test('cannot delete rest day for employee with fixed on-site work type', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - disabled for old work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(false);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with fixed on-site work type
    ($this->setWorkerUser)(UserWorkTypesUtil::FIXED_ON_SITE);

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => UserWorkTypesUtil::FIXED_ON_SITE,
        'work_days' => 'sun,mon,tue,wed,thu',
        'rest_days' => 'fri,sat',
        'start_time' => '09:00',
        'end_time' => '17:00',
    ]);

    // Create a rest day first
    $restDay = EmployeeLeaveRequest::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'from' => now()->addDay()->startOfDay(),
        'to' => now()->addDay()->endOfDay(),
        'status' => 'approved',
        'branch_id' => $this->workerEmployee->branch_id,
        'net_quantity' => 8,
        'by_admin' => null,
    ]);

    // Try to delete the rest day
    $restDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/deleteRestDays', [
            'ids' => [$restDay->id],
        ]);

    $restDayResponse->assertStatus(422);
});

test('cannot delete rest day for employee with fixed rest days', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with fixed working hours work type
    ($this->setWorkerUser)(WorkTypesEnum::FIXED_WORKING_HOURS->value);

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => WorkTypesEnum::FIXED_WORKING_HOURS->value,
        'rest_days_type' => RestDaysTypesEnum::FIXED->value,
        'rest_days' => 'fri,sat',
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Create a rest day first
    $restDay = EmployeeLeaveRequest::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'from' => now()->addDay()->startOfDay(),
        'to' => now()->addDay()->endOfDay(),
        'status' => 'approved',
        'branch_id' => $this->workerEmployee->branch_id,
        'net_quantity' => 8,
        'by_admin' => null,
    ]);

    // Try to delete the rest day
    $restDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/deleteRestDays', [
            'ids' => [$restDay->id],
        ]);

    $restDayResponse->assertStatus(422);
});

test('cannot add rest days beyond work type policy limit', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with flexible working hours work type (rotational rest days)
    ($this->setWorkerUser)(WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value);

    // Create and attach work type policy with 2 rest days
    $this->workTypePolicy->update([
        'work_days_type' => WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value,
        'rest_days_type' => RestDaysTypesEnum::ROTATIONAL->value,
        'rest_days_count' => 2,
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Create rest day leave balance
    $restDayBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => (float)$this->workTypePolicy->rest_days_count * 8, // 2 rest days * 8 hours = 16 hours
    ]);

    // Add first rest day
    $firstRestDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/addRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'date' => now()->addDay()->format('Y-m-d'),
        ]);

    $firstRestDayResponse->assertStatus(200);

    // Wait 2 seconds
    sleep(1);

    // Add second rest day
    $secondRestDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/addRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'date' => now()->addDays(2)->format('Y-m-d'),
        ]);

    $secondRestDayResponse->assertStatus(200);

    // Wait 2 seconds
    sleep(1);

    // Try to add third rest day (should fail)
    $thirdRestDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/addRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'date' => now()->addDays(3)->format('Y-m-d'),
        ]);

    $thirdRestDayResponse->assertStatus(422);

    // Verify the balance is at 0 (all rest days used)
    $updatedBalance = EmployeeLeaveBalance::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();
    expect($updatedBalance->balance)->toBe(0.0);

    // Verify only two rest days were created
    $restDaysCount = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->count();
    expect($restDaysCount)->toBe(2);
});

test('cannot add rest day on a date with annual leave', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with flexible working hours work type (rotational rest days)
    ($this->setWorkerUser)(WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value);

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value,
        'rest_days_type' => RestDaysTypesEnum::ROTATIONAL->value,
        'rest_days_count' => 2,
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Create rest day leave balance
    $restDayBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => (float)$this->workTypePolicy->rest_days_count * 8,
    ]);

    // Create annual leave balance
    $annualLeaveBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->annualLeave->id,
        'company_leave_type_policy_id' => $this->annualLeave->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => 168, // 21 days * 8 hours
    ]);

    // Create an annual leave for a specific date
    $leaveDate = now()->addDay();
    $annualLeave = EmployeeLeaveRequest::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->annualLeave->id,
        'company_leave_type_policy_id' => $this->annualLeave->companyLeaveTypePolicy->id,
        'from' => $leaveDate->startOfDay(),
        'to' => $leaveDate->endOfDay(),
        'status' => 'approved',
        'branch_id' => $this->workerEmployee->branch_id,
        'net_quantity' => 8,
        'by_admin' => null,
    ]);

    // Try to add a rest day on the same date as the annual leave
    $restDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/addRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'date' => $leaveDate->format('Y-m-d'),
        ]);

    $restDayResponse->assertStatus(422);

    // Verify no rest day was created
    $restDay = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $leaveDate->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($restDay)->toBeNull();

    // Verify the rest day balance was not consumed
    $updatedBalance = EmployeeLeaveBalance::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();
    expect($updatedBalance->balance)->toBe((float)$this->workTypePolicy->rest_days_count * 8);
});

test('cannot add rest day on a date with existing rest day', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with flexible working hours work type (rotational rest days)
    ($this->setWorkerUser)(WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value);

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value,
        'rest_days_type' => RestDaysTypesEnum::ROTATIONAL->value,
        'rest_days_count' => 2,
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Create rest day leave balance
    $restDayBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => (float)$this->workTypePolicy->rest_days_count * 8,
    ]);

    // Add first rest day
    $restDayDate = now()->addDay();
    $firstRestDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/addRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'date' => $restDayDate->format('Y-m-d'),
        ]);

    $firstRestDayResponse->assertStatus(200);

    // Wait 1 second
    sleep(1);

    // Try to add another rest day on the same date
    $secondRestDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/addRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'date' => $restDayDate->format('Y-m-d'),
        ]);

    $secondRestDayResponse->assertStatus(422);

    // Verify only one rest day was created
    $restDaysCount = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDayDate->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->count();
    expect($restDaysCount)->toBe(1);

    // Verify the rest day balance was only consumed once
    $updatedBalance = EmployeeLeaveBalance::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();
    expect($updatedBalance->balance)->toBe((float)($this->workTypePolicy->rest_days_count * 8) - 8);
});

test('can convert absent day to rest day for employee with rotational rest days', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with flexible working hours work type (rotational rest days)
    ($this->setWorkerUser)(WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value);

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value,
        'rest_days_type' => RestDaysTypesEnum::ROTATIONAL->value,
        'rest_days_count' => 2,
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Create rest day leave balance
    $restDayBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => (float)$this->workTypePolicy->rest_days_count * 8,
    ]);

    $timecardType = TimecardType::factory()->create([
        'company_id' => $this->workerEmployee->company_id,
        'name' => 'regular',
    ]);

    // Create an absent timecard
    $timecard = Timecard::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'branch_id' => $this->workerEmployee->branch_id,
        'by_admin' => null,
        'shift_id' => null,
        'from' => now()->addDay()->setTime(8, 0, 0),
        'to' => now()->addDay()->setTime(17, 0, 0),
        'timecard_type_id' => $timecardType->id,
        'required_ci_branch_id' => $this->workerEmployee->branch_id,
        'required_co_branch_id' => $this->workerEmployee->branch_id,
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    $entityTag = EntityTag::factory()->create([
        'entity_id' => $timecard->id,
        'entity_type' => 'timecard',
        'tag' => 'absent',
    ]);

    // Convert absent day to rest day
    $response = $this->actingAs($this->user, 'user-api')
        ->json('POST', '/api/v1/attendance/convertAbsentDayToRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'time_card_id' => $timecard->id,
        ]);

    $response->assertStatus(200);

    // Verify timecard was deleted
    $deletedTimecard = Timecard::find($timecard->id);
    expect($deletedTimecard)->toBeNull();

    // Verify rest day was created
    $restDay = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
//        ->whereDate('from', now()->addDay()->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($restDay)->not->toBeNull();
    expect($restDay->net_quantity)->toBe(8.0);

    // Verify balance was consumed
    $updatedBalance = EmployeeLeaveBalance::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();
    expect($updatedBalance->balance)->toBe((float)($this->workTypePolicy->rest_days_count * 8) - 8);
});

test('can convert absent day to rest day for employee with dynamic onsite work type', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(false);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with dynamic onsite work type
    ($this->setWorkerUser)('dynamic_onsite');

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => 'dynamic_onsite',
        'rest_days_type' => RestDaysTypesEnum::ROTATIONAL->value,
        'rest_days_count' => 2,
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Create rest day leave balance
    $restDayBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => (float)$this->workTypePolicy->rest_days_count * 8,
    ]);

    $timecardType = TimecardType::factory()->create([
        'company_id' => $this->workerEmployee->company_id,
        'name' => 'regular',
    ]);

    // Create an absent timecard
    $timecard = Timecard::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'branch_id' => $this->workerEmployee->branch_id,
        'by_admin' => null,
        'shift_id' => null,
        'from' => now()->addDay()->setTime(8, 0, 0),
        'to' => now()->addDay()->setTime(17, 0, 0),
        'timecard_type_id' => $timecardType->id,
        'required_ci_branch_id' => $this->workerEmployee->branch_id,
        'required_co_branch_id' => $this->workerEmployee->branch_id,
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    $entityTag = EntityTag::factory()->create([
        'entity_id' => $timecard->id,
        'entity_type' => 'timecard',
        'tag' => 'absent',
    ]);

    // Convert absent day to rest day
    $response = $this->actingAs($this->user, 'user-api')
        ->json('POST', '/api/v1/attendance/convertAbsentDayToRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'time_card_id' => $timecard->id,
        ]);

    $response->assertStatus(200);

    // Verify timecard was deleted
    $deletedTimecard = Timecard::find($timecard->id);
    expect($deletedTimecard)->toBeNull();

    // Verify rest day was created
    $restDay = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', now()->addDay()->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($restDay)->not->toBeNull();
    expect($restDay->net_quantity)->toBe(8.0);

    // Verify balance was consumed
    $updatedBalance = EmployeeLeaveBalance::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();
    expect($updatedBalance->balance)->toBe((float)($this->workTypePolicy->rest_days_count * 8) - 8);
});

test('cannot convert absent day to rest day when rest day limit is exceeded', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with flexible working hours work type (rotational rest days)
    ($this->setWorkerUser)(WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value);

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value,
        'rest_days_type' => RestDaysTypesEnum::ROTATIONAL->value,
        'rest_days_count' => 2,
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Create rest day leave balance with all rest days already used
    $restDayBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => 16, // No balance left
    ]);

    $timecardType = TimecardType::factory()->create([
        'company_id' => $this->workerEmployee->company_id,
        'name' => 'regular',
    ]);

    // Create multiple absent timecards for different days
    $timecards = [];
    for ($i = 1; $i <= 3; $i++) {
        $timecard = Timecard::factory()->create([
            'employee_id' => $this->workerEmployee->id,
            'branch_id' => $this->workerEmployee->branch_id,
            'by_admin' => null,
            'shift_id' => null,
            'from' => now()->addDays($i)->setTime(8, 0, 0),
            'to' => now()->addDays($i)->setTime(17, 0, 0),
            'timecard_type_id' => $timecardType->id,
            'required_ci_branch_id' => $this->workerEmployee->branch_id,
            'required_co_branch_id' => $this->workerEmployee->branch_id,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        EntityTag::factory()->create([
            'entity_id' => $timecard->id,
            'entity_type' => 'timecard',
            'tag' => 'absent',
        ]);

        $timecards[] = $timecard;
    }

    // Try to convert each absent day to rest day
    foreach ($timecards as $index => $timecard) {
        $response = $this->actingAs($this->user, 'user-api')
            ->json('POST', '/api/v1/attendance/convertAbsentDayToRestDay', [
                'employee_id' => $this->workerEmployee->id,
                'time_card_id' => $timecard->id,
            ]);
        sleep(1);

        if ($index < 2) {
            // First two conversions should succeed
            $response->assertStatus(200);

            // Verify timecard was deleted
            $deletedTimecard = Timecard::find($timecard->id);
            expect($deletedTimecard)->toBeNull();

            // Verify rest day was created
            $restDay = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
                ->whereDate('from', Carbon::parse($timecard->from)->format('Y-m-d'))
                ->where('company_leave_type_id', $this->restDay->id)
                ->first();

            expect($restDay)->not->toBeNull();
            expect($restDay->net_quantity)->toBe(8.0);
        } else {
            // Third conversion should fail
            $response->assertStatus(422);

            // Verify timecard still exists
            $existingTimecard = Timecard::find($timecard->id);
            expect($existingTimecard)->not->toBeNull();
        }
    }

    // Verify final balance is 0 (all rest days used)
    $updatedBalance = EmployeeLeaveBalance::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();
    expect($updatedBalance->balance)->toBe(0.0);

    // Verify total count of rest days is 2
    $totalRestDays = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->count();
    expect($totalRestDays)->toBe(2);
});

test('can add rest day on a public holiday', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with flexible working hours work type (rotational rest days)
    ($this->setWorkerUser)(WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value);

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value,
        'rest_days_type' => RestDaysTypesEnum::ROTATIONAL->value,
        'rest_days_count' => 2,
        'work_days' => 'sun,mon,tue,wed,thu',
    ]);

    // Create rest day leave balance
    $restDayBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => (float)$this->workTypePolicy->rest_days_count * 8,
    ]);

    // Create a public holiday
    $publicHolidayDate = now()->addDay();
    $publicHoliday = PublicHoliday::factory()->create([
        'name_ar' => 'عطلة اختبار',
        'name_en' => 'Test Holiday',
        'start' => $publicHolidayDate->format('Y-m-d'),
        'end' => $publicHolidayDate->format('Y-m-d'),
    ]);

    // Try to add a rest day on the public holiday
    $restDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/addRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'date' => $publicHolidayDate->format('Y-m-d'),
        ]);

    $restDayResponse->assertStatus(200);

    // Verify rest day was created
    $restDay = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $publicHolidayDate->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($restDay)->not->toBeNull();
    expect($restDay->net_quantity)->toBe(8.0);

    // Verify balance was consumed
    $updatedBalance = EmployeeLeaveBalance::where('employee_id', $this->workerEmployee->id)
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();
    expect($updatedBalance->balance)->toBe((float)($this->workTypePolicy->rest_days_count * 8) - 8);

});

//test('fillPublicHolidayAbsencesOnDateForAllEmployees skips employees with rest days', function () {
//    // Set up roles and permissions
//    ($this->setupRolesAndPermissions)();
//
//    // Mock Unleash feature toggle - enabled for new work types
//    $mock = $this->getMockBuilder(Unleash::class)
//        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
//        ->getMock();
//
//    $mock
//        ->expects($this->any())
//        ->method('getNewWorkTypesFeatureFlag')
//        ->willReturn(true);
//
//    $this->app->instance(Unleash::class, $mock);
//
//    // Create a worker with flexible working hours work type (rotational rest days)
//    ($this->setWorkerUser)(WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value);
//
//    // Create and attach work type policy
//    $this->workTypePolicy->update([
//        'work_days_type' => WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value,
//        'rest_days_type' => RestDaysTypesEnum::ROTATIONAL->value,
//        'rest_days_count' => 2,
//        'work_days' => 'sun,mon,tue,wed,thu',
//    ]);
//
//    // Create rest day leave balance
//    $restDayBalance = EmployeeLeaveBalance::factory()->create([
//        'employee_id' => $this->workerEmployee->id,
//        'company_leave_type_id' => $this->restDay->id,
//        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
//        'start' => now()->startOfMonth(),
//        'end' => now()->endOfMonth(),
//        'balance' => (float)$this->workTypePolicy->rest_days_count * 8,
//    ]);
//
//    // Create a public holiday
//    $publicHolidayDate = now()->addDay();
//    $publicHoliday = PublicHoliday::factory()->create([
//        'company_id' => $this->company->id,
//        'date' => $publicHolidayDate->format('Y-m-d'),
//        'name' => 'Test Holiday',
//    ]);
//
//    // Add a rest day on the public holiday
//    $restDayResponse = $this->actingAs($this->user, 'user-api')
//        ->json('POST', 'api/leave/addRestDay', [
//            'employee_id' => $this->workerEmployee->id,
//            'date' => $publicHolidayDate->format('Y-m-d'),
//        ]);
//
//    $restDayResponse->assertStatus(200);
//
//    // Create another employee without a rest day
//    $secondWorker = User::factory()->state(['company_id' => $this->company->id])->create();
//    $secondWorker->assignRole($this->workerRole);
//    $secondWorkerEmployee = $secondWorker->employee;
//    $secondWorkerEmployee->title->work_type_policy_id = $this->workTypePolicy->id;
//    $secondWorkerEmployee->title->save();
//
//    // Call the function to fill public holiday absences
//    $service = app(PublicHolidayAbsenceService::class);
//    $service->fillPublicHolidayAbsencesOnDateForAllEmployees($publicHolidayDate->format('Y-m-d'));
//
//    // Verify that the employee with rest day doesn't have a public holiday absence
//    $absenceForRestDayEmployee = PublicHolidayAbsence::where('employee_id', $this->workerEmployee->id)
//        ->whereDate('date', $publicHolidayDate->format('Y-m-d'))
//        ->first();
//    expect($absenceForRestDayEmployee)->toBeNull();
//
//    // Verify that the employee without rest day has a public holiday absence
//    $absenceForSecondEmployee = PublicHolidayAbsence::where('employee_id', $secondWorkerEmployee->id)
//        ->whereDate('date', $publicHolidayDate->format('Y-m-d'))
//        ->first();
//    expect($absenceForSecondEmployee)->not->toBeNull();
//    expect($absenceForSecondEmployee->public_holiday_id)->toBe($publicHoliday->id);
//    expect($absenceForSecondEmployee->status)->toBe('approved');
//
//    // Verify the rest day was properly recorded
//    $restDay = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
//        ->whereDate('from', $publicHolidayDate->format('Y-m-d'))
//        ->where('company_leave_type_id', $this->restDay->id)
//        ->first();
//
//    expect($restDay)->not->toBeNull();
//    expect($restDay->net_quantity)->toBe(8.0);
//    expect($restDay->status)->toBe('approved');
//});


test('creates unscheduled timecard when clocking in on a rest day', function () {
    // Set up roles and permissions
    ($this->setupRolesAndPermissions)();

    // Mock Unleash feature toggle - enabled for new work types
    $mock = $this->getMockBuilder(Unleash::class)
        ->onlyMethods(['getNewWorkTypesFeatureFlag'])
        ->getMock();

    $mock
        ->expects($this->any())
        ->method('getNewWorkTypesFeatureFlag')
        ->willReturn(true);

    $this->app->instance(Unleash::class, $mock);

    // Create a worker with flexible working hours work type (rotational rest days)
    ($this->setWorkerUser)(WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value);

    // Create and attach work type policy
    $this->workTypePolicy->update([
        'work_days_type' => WorkTypesEnum::FLEXIBLE_WORKING_HOURS->value,
        'rest_days_type' => RestDaysTypesEnum::ROTATIONAL->value,
        'rest_days_count' => 2,
        'work_days' => 'sun,mon,tue,wed,thu',
        'any_location_type' => AnyLocationTypesEnum::FIXED_DAYS->value,
        'apply_any_location' => true
    ]);

    // Create rest day leave balance
    $restDayBalance = EmployeeLeaveBalance::factory()->create([
        'employee_id' => $this->workerEmployee->id,
        'company_leave_type_id' => $this->restDay->id,
        'company_leave_type_policy_id' => $this->restDay->companyLeaveTypePolicy->id,
        'start' => now()->startOfMonth(),
        'end' => now()->endOfMonth(),
        'balance' => (float)$this->workTypePolicy->rest_days_count * 8,
    ]);

    // Create regular timecard type
    $timecardType = TimecardType::factory()->create([
        'company_id' => $this->workerEmployee->company_id,
        'name' => 'regular',
    ]);

    // Create a rest day
    $restDayDate = now()->addDay();
    $restDayResponse = $this->actingAs($this->user, 'user-api')
        ->json('POST', 'api/leave/addRestDay', [
            'employee_id' => $this->workerEmployee->id,
            'date' => $restDayDate->format('Y-m-d'),
        ]);

    $restDayResponse->assertStatus(200);

    // Verify rest day was created
    $restDay = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDayDate->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($restDay)->not->toBeNull();
    expect($restDay->net_quantity)->toBe(8.0);

    // Set the test time to the rest day
    Carbon::setTestNow($restDayDate);

    // Get next slot first
    $nextSlotResponse = $this->actingAs($this->worker, 'user-api')
        ->json('GET', '/api/v1/work-type-next-slot', [
            'lat' => 3.0444,
            'long' => 1.2357,
        ]);

    dump($nextSlotResponse->getContent());

    $nextSlotResponse->assertStatus(200);
    $nextSlotData = json_decode($nextSlotResponse->getContent(), true)['payload']['data'];

    // Try to clock in on the rest day
    $clockInResponse = $this->actingAs($this->worker, 'user-api')
        ->json('POST', '/api/v1/attendance/clockIn', [
            'employee_id' => $this->workerEmployee->id,
            'branch_id' => $this->workerEmployee->branch_id,
            'slot_id' => $nextSlotData['data']['id'] ?? null,
            'lat' => '30.0444',
            'long' => '31.2357',
            'is_manual_action' => false,
            'notes' => 'Clocking in on rest day',
            'is_unscheduled' => true,
            'check_warning' => false,
            'timecard_type_id' => $timecardType->id
        ], ['Authorization' => 'Bearer ' . JWTAuth::fromUser($this->worker)]);

    dump($clockInResponse->getContent());

    // Verify clock in was successful
    $clockInResponse->assertStatus(200);

    // Verify an unscheduled timecard was created
    $timecard = Timecard::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDayDate->format('Y-m-d'))
        ->first();

    expect($timecard)->not->toBeNull();
    expect($timecard->timecard_type_id)->toBe(TimecardType::where('company_id', $this->workerEmployee->company_id)->first()->id);

    // Verify attendance was created
    $attendance = Attendance::where('employee_id', $this->workerEmployee->id)
        ->where('date', $restDayDate->format('Y-m-d'))
        ->first();

    expect($attendance)->not->toBeNull();

    // Verify rest day is still intact
    $restDayAfterClockIn = EmployeeLeaveRequest::where('employee_id', $this->workerEmployee->id)
        ->whereDate('from', $restDayDate->format('Y-m-d'))
        ->where('company_leave_type_id', $this->restDay->id)
        ->first();

    expect($restDayAfterClockIn)->toBeNull();
});
