<?php

namespace App\Http\Controllers\Payroll\PayrollHub;

use App\Http\Controllers\NewController;
use App\Http\Requests\V1\PayrollHub\CompanyPayrollEntriesRequest;
use App\Http\Resources\V1\PayrollHub\GetCompanyAnalyticsResource;
use App\Services\V1\PayrollHub\EmployeeChangesService;
use App\Util\HttpStatusCodeUtil;
use App\Util\ScopeUtil;
use App\Traits\QueriesHelper;

class EmployeeChangesController extends NewController
{

    use QueriesHelper;

    public function __construct(
        protected EmployeeChangesService $employeeChangesService,
    )
    {
    }

    public function getMonthlyAnalytics(CompanyPayrollEntriesRequest $request){
        $data = null;
        if($this->getUserHighestScopeKey(auth()->user()) == ScopeUtil::COMPANY_SCOPE)
        {
            $data = $this->employeeChangesService->getMonthlyAnalytics($request->validated());
        }
        return getResponseStructure(
            ['data' => $data ? GetCompanyAnalyticsResource::collection($data) : null],
            HttpStatusCodeUtil::OK,
            'Analytics fetched successfully'
        );
    }
}