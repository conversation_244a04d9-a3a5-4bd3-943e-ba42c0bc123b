<?php

namespace App\Handlers\MonthlyAttendanceTags;

use App\Models\EmployeeLeaveRequest;
use Carbon\Carbon;
use Exception;

class LeaveTagHandler implements TagHandlerInterface
{
    public function handleTagData($employeeAttendance): array
    {
        return [
            'name' => $employeeAttendance->companyLeaveType->name ?? null,
            'id' => $employeeAttendance->id,
            'date' => null,
            'from_date' => Carbon::parse($employeeAttendance->from)->format('Y-m-d'),
            'to_date' => Carbon::parse($employeeAttendance->to)->format('Y-m-d'),
        ];
    }

    /**
     * @throws Exception
     */
    public function handleTagCount($tags, $employeeId, $employeeAttendance = null)
    {
        return isset($tags[$employeeId]['tags']['leaves']) ?
            $tags[$employeeId]['tags']['leaves']['count'] +
            ($employeeAttendance->net_quantity / config('globals.WORKED_HOURS_PER_DAY')) :
            ($employeeAttendance->net_quantity / config('globals.WORKED_HOURS_PER_DAY'));
    }

    public function handleTagCheck($employeeAttendance, $tag = null): bool
    {
        return $employeeAttendance instanceof EmployeeLeaveRequest &&
            $employeeAttendance->company_leave_type_id != config('globals.rest_day_leave_id'); // that mean he in leave;
    }

    public function handleTagUnit(): string
    {
        return 'days';
    }
}
