<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Attendance;
use App\Models\EmployeeLeaveRequest;
use App\Models\EntityTag;
use App\Models\PublicHoliday;
use App\Services\V1\Holidays\PublicHolidaysAttendanceService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CompensateMissingHolidayAttendances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'compensate:holiday:attendances {company_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            // import PublicHolidayAttendanceService here to use function createPublicHolidayAttendance
            $companyId = $this->argument('company_id') ?? null;
            $this->compensateAttendance($companyId);
            $this->cancelLeaves($companyId);
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            echo $e->getMessage();
            DB::rollBack();
        }
    }

    public function compensateAttendance($companyId = null)
    {
        $publicHolidaysAttendanceService = app(PublicHolidaysAttendanceService::class);
        $countCompensated = 0;
        $employeeDateMap = [];
        $holidayTagData = ['tag' => 'public_holiday'];
        $publicHolidays = PublicHoliday::all();
        foreach ($publicHolidays as $publicHoliday) {
            $attendanceToCompensate = Attendance::when(isset($companyId), function ($q) use ($companyId) {
                $q->where('company_id', $companyId);
            })
                ->where('date', '>=', $publicHoliday->start)
                ->where('date', '<=', $publicHoliday->end)
                ->whereHas('employee.title.publicHolidaysPolicy')
                ->with('employee.user.company')
                ->get();

            $employeeDateMap = [];

            foreach ($attendanceToCompensate as $attendance) {
                if (! isset($employeeDateMap[$attendance->employee->id])) {
                    $employeeDateMap[$attendance->employee->id] = [];
                }
                if (! isset($employeeDateMap[$attendance->employee->id][$attendance->date])) {
                    $employeeDateMap[$attendance->employee->id][$attendance->date] = true;
                } else {
                    echo 'employee  : '.$attendance->employee->id.' already compensated for date '.$attendance->date."\n";

                    continue;
                }
                config(['globals.user' => $attendance->employee->user]);
                config(['globals.company' => $attendance->employee->user->company]);
                Auth::login($attendance->employee->user);
                try {
                    $publicHolidaysAttendanceService->createPublicHolidayAttendance($attendance->employee->id, $publicHoliday, $attendance->date);
                    $holidayTagData['company'] = $attendance->company_id;
                    $tag = EntityTag::create($holidayTagData);
                    $attendance->entityTags()->save($tag);
                    $countCompensated++;
                    echo 'employee  : '.$attendance->employee->id.' in company: '.$attendance->company_id.' has been compensated for date '.$attendance->date."\n";
                } catch (Exception $e) {
                    echo 'employee  : '.$attendance->employee->id.'in company: '.$attendance->company_id.' has error '.$e->getMessage()."\n";
                }
            }

        }
        echo 'Total employees compensated: '.$countCompensated." \n";
    }

    public function cancelLeaves($companyId = null)
    {
        $publicHolidays = PublicHoliday::all();
        $leavesDeleted = 0;
        $leavesSplitted = 0;
        foreach ($publicHolidays as $publicHoliday) {
            $leavesToCancel = EmployeeLeaveRequest::when(isset($companyId), function ($q) use ($companyId) {
                $q->whereHas('employee', function ($q) use ($companyId) {
                    $q->where('company_id', $companyId);
                });
            })
                ->where('status', config('globals.REQUEST_STATUSES.APPROVED'))
                ->where(function ($q) use ($publicHoliday) {
                    $q->where(function ($q) use ($publicHoliday) {
                        $q->whereDate('from', '>=', $publicHoliday->start)
                            ->whereDate('from', '<=', $publicHoliday->end);
                    })
                        ->orWhere(function ($q) use ($publicHoliday) {
                            $q->whereDate('to', '>=', $publicHoliday->start)
                                ->whereDate('to', '<=', $publicHoliday->end);
                        })
                        ->orWhere(function ($q) use ($publicHoliday) {
                            $q->whereDate('from', '<=', $publicHoliday->start)
                                ->whereDate('to', '>=', $publicHoliday->end);
                        })
                        ->orWhere(function ($q) use ($publicHoliday) {
                            $q->whereDate('from', '>=', $publicHoliday->start)
                                ->whereDate('to', '<=', $publicHoliday->end);
                        });
                })
                ->get();

            echo 'num of leaves to cancel: '.count($leavesToCancel)."\n";

            foreach ($leavesToCancel as $leave) {

                $leaveEnd = Carbon::parse($leave->to)->toDateString();
                $leaveStart = Carbon::parse($leave->from)->toDateString();
                $holidayStart = Carbon::parse($publicHoliday->start)->toDateString();
                $holidayEnd = Carbon::parse($publicHoliday->end)->toDateString();

                if ($leaveStart >= $holidayStart && $leaveEnd <= $holidayEnd) {
                    $leave->delete();
                    echo 'leave with id: '.$leave->id.' has been deleted'."\n";
                    $leavesDeleted++;
                } elseif ($leaveStart < $holidayStart && $leaveEnd > $holidayEnd) {
                    $newLeave = clone $leave;
                    $newLeave->from = Carbon::parse($holidayEnd)->addDay()->startOfDay()->toDateString();
                    $newLeave->net_quantity = Carbon::parse($newLeave->from)->diffInDays($newLeave->to) * 8;
                    $newLeave->save();
                    $leave->to = Carbon::parse($holidayStart)->subDay()->endOfDay()->toDateString();
                    $leave->net_quantity = Carbon::parse($leave->from)->diffInDays($leave->to) * 8;
                    $leave->save();
                    $leavesSplitted++;
                    echo 'leave with id: '.$leave->id.' has been splitted'."\n";
                } elseif ($leaveStart < $holidayStart && $leaveEnd >= $holidayStart && $leaveEnd <= $holidayEnd) {
                    $leave->to = Carbon::parse($holidayStart)->subDay()->endOfDay()->toDateString();
                    $leave->net_quantity = Carbon::parse($leave->from)->diffInDays($leave->to) * 8;
                    $leave->save();
                    echo 'leave with id: '.$leave->id.' has been edited'."\n";
                    $leavesSplitted++;
                } elseif ($leaveStart >= $holidayStart && $leaveStart <= $holidayEnd && $leaveEnd > $holidayEnd) {
                    $leave->from = Carbon::parse($holidayEnd)->addDay()->startOfDay()->toDateString();
                    $leave->net_quantity = Carbon::parse($leave->from)->diffInDays($leave->to) * 8;
                    $leave->save();
                    echo 'leave with id: '.$leave->id.' has been edited'."\n";
                    $leavesSplitted++;
                }
            }
            echo 'Total leaves deleted: '.$leavesDeleted." \n";
            echo 'Total leaves splitted: '.$leavesSplitted." \n";
        }
    }
}
