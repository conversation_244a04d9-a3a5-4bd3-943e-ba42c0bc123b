<?php

namespace App\Console\Commands\OneTimeScript;

use App\Repositories\Repository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RemoveAbsentTimecardsOnApprovedLeaves extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove-timecards-on-approved-leaves';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $timecardRepository = Repository::getRepository('Timecard');
        $query = "
        select * from timecards 
        where timecards.employee_id in (
            select employee_id from employee_leave_requests 
            where employee_leave_requests.from <= timecards.from 
            and employee_leave_requests.to >= timecards.from 
            and employee_leave_requests.status = 'approved' 
            and employee_leave_requests.net_quantity > 0
            and employee_leave_requests.deleted_at is null
        ) 
        and timecards.id in (
            select entity_id from entity_tags 
            where entity_type = 'time_card'
            and tag in ('absent', 'absent_without_permission', 'not_in_yet')
        )
        and 
        timecards.deleted_at is null
        and 
        timecards.from >= '2025-01-01 00:00:00'
    ";

        $ids = [];
        $timecardsOnLeaves = DB::select($query);
        foreach ($timecardsOnLeaves as $timecard) {
            $ids[] = $timecard->id;
        }
        $timecardRepository->delete($ids);
    }
}
