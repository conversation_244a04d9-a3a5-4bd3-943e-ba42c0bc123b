<?php

namespace App\Console\Commands;

use App\Models\Cico;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ClearAutomatedClockout extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clear:automated:clockout';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $automatedClockOutCicos = Cico::where('source', 'automated')->get();
            $bar = $this->output->createProgressBar(count($automatedClockOutCicos));
            $bar->start();

            foreach ($automatedClockOutCicos as $cico) {
                $cico->delete();
                $bar->advance();

            }

            $bar->finish();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
