<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\AttendanceDeduction;
use App\Models\EmployeeLeaveRequest;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ChangeDeductionsStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'change:deductions_status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $deductions = AttendanceDeduction::all();
            foreach ($deductions as $deduction) {
                if ($deduction->status == 'approved') {
                    $deduction->status = 'applied';
                } else {
                    $deduction->status = 'waived';
                }
                $deduction->save();

            }
            $leaves = EmployeeLeaveRequest::where('status', 'in_process')->get();
            foreach ($leaves as $leave) {
                $leave->status = 'pending';
                $leave->save();
            }

            DB::commit();
        } catch (Exception $e) {
            Log::error($e);
            \Sentry\captureException($e);
            DB::rollBack();
        }
    }
}
