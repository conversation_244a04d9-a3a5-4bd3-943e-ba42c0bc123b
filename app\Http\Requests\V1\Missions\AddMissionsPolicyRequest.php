<?php

namespace App\Http\Requests\V1\Missions;

use Illuminate\Foundation\Http\FormRequest;

class AddMissionsPolicyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // Check if all title_ids belong to the user's company and are not deleted
        if (! count($this->input('title_ids'))) {
            return true;
        }

        if ($this->has('title_ids')) {
            $titleIds = $this->input('title_ids');
            $countValidTitles = \DB::table('titles')
                ->whereIn('id', $titleIds)
                ->where('company_id', auth()->user()->company_id)
                ->whereNull('deleted_at')
                ->count();

            return count($titleIds) === $countValidTitles;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title_ids' => 'array',
            'title_ids.*' => 'required|integer',
        ];
    }
}
