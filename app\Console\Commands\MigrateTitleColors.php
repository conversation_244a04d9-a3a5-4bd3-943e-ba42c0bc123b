<?php

namespace App\Console\Commands;

use App\Services\CompanySetup\TitlesService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateTitleColors extends Command
{
    public function __construct(private TitlesService $titlesService)
    {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:title:colors';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate old title colors to the new list of colors';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $this->titlesService->migrateOldTitleColors();
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::info($e);
            DB::rollBack();
        }
    }
}
