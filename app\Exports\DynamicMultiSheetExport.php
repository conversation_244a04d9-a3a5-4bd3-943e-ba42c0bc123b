<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class DynamicMultiSheetExport implements FromCollection, WithMultipleSheets
{
    private $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return collect($this->data);
    }

    public function sheets(): array
    {
        return [
            new DynamicEmployeeExport(
                $this->data['counters'],
                $this->data['employee_code_type'],
                $this->data['selected_fields'],
                $this->data['operation_type'],
                $this->data['company_country_dial_code'] ?? '',
                $this->data['employees'] ?? null
            ),
            new DynamicCompanyDataExport($this->data['dropdown_data']),
        ];
    }
}
