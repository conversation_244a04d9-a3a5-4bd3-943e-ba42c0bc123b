name: Dev Deployment
on:
  push:
    branches: ["development_trunk"]
jobs:
  tests:
    name: Run tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Setup PHP with PECL extension
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.2"
      - uses: actions/checkout@v2
      - name: Copy .env
        run: cp .env.example .env
      - name: Install Dependencies
        run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress
      - name: Directory Permissions
        run: chmod -R 777 storage bootstrap/cache

#      - name: Wait for MySQL to be ready
#        run: |
#          until mysqladmin ping -h"${{ secrets.DEV_DB_HOST }}" --silent; do
#            echo 'waiting for mysql to be available...'
#            sleep 1
#          done
#
#      - name: Create JWT Secret key
#        env:
#          DB_CONNECTION: mysql
#          DB_HOST: ${{ secrets.DEV_DB_HOST }}
#          DB_PORT: 3306
#          DB_DATABASE: bluworks_qa
#          DB_USERNAME: bluworks_stg
#          DB_PASSWORD: ${{ secrets.MYSQL_DEV_ROOT_PASSWORD }}
#          CACHE_DRIVER: array
#          SESSION_DRIVER: array
#          QUEUE_DRIVER: sync
#        run: php artisan jwt:secret
#
#      - name: Create Application key
#        env:
#          DB_CONNECTION: mysql
#          DB_HOST: ${{ secrets.DEV_DB_HOST }}
#          DB_PORT: 3306
#          DB_DATABASE: bluworks_qa
#          DB_USERNAME: bluworks_stg
#          DB_PASSWORD: ${{ secrets.MYSQL_DEV_ROOT_PASSWORD }}
#          CACHE_DRIVER: array
#          SESSION_DRIVER: array
#          QUEUE_DRIVER: sync
#        run: php artisan key:generate
#
##      - name: Run migrations
##        env:
##          DB_CONNECTION: mysql
##          DB_HOST: ${{ secrets.DEV_DB_HOST }}
##          DB_PORT: 3306
##          DB_DATABASE: bluworks_qa
##          DB_USERNAME: bluworks_stg
##          DB_PASSWORD: ${{ secrets.MYSQL_DEV_ROOT_PASSWORD }}
##          CACHE_DRIVER: array
##          SESSION_DRIVER: array
##          QUEUE_DRIVER: sync
##        run: php artisan migrate:fresh --path=database/migrations
#
#      - name: Execute tests (Unit and Feature tests) via PHPUnit
#        env:
#          DB_CONNECTION: mysql
#          DB_HOST: ${{ secrets.DEV_DB_HOST }}
#          DB_PORT: 3306
#          DB_DATABASE: bluworks_qa
#          DB_USERNAME: bluworks_stg
#          DB_PASSWORD: ${{ secrets.MYSQL_DEV_ROOT_PASSWORD }}
#          CACHE_DRIVER: array
#          SESSION_DRIVER: array
#          QUEUE_DRIVER: sync
#        run: php artisan test  tests/Feature/V1/Running/Loans

  dev-deployment:
    needs: tests
    runs-on: ubuntu-latest
    steps:
      - name: Check test status
        id: test-status
        run: echo "::set-output name=status::${{ needs.tests.status }}"

      - name: Install SSH Key
        # if: steps.test-status.outputs.status == 'success'
        run: |
          install -m 600 -D /dev/null ~/.ssh/id_rsa
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.SSH_HOST }} > ~/.ssh/known_hosts

      - name: run deployment commands
        # if: steps.test-status.outputs.status == 'success'
        run: ssh bluworks@${{ secrets.SSH_HOST }} "cd ${{secrets.DEV_DIR}} && ${{vars.DEVELOPMENTS_DEPLOYMENT_COMMANDS}} && exit"
