<?php

namespace App\Console\Commands\OneTimeScript;

use App\Models\Timecard;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeleteWrongTimecards extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:wrong:timecards';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        DB::beginTransaction();
        try {
            $timecards = Timecard::withTrashed()->where('from', '>=', '2025-01-01 00:00:00')
                ->whereRaw('`from` = `to`')
                ->doesntHave('attendance')
                ->delete();
            $this->info('Deleted '.$timecards.' wrong timecards');
            DB::commit();
        } catch (Exception $e) {
            \Sentry\captureException($e);
            Log::error($e);
            DB::rollBack();
        }
    }
}
