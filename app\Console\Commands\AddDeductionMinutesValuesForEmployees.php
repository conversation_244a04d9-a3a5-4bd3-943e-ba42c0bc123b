<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddDeductionMinutesValuesForEmployees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:add-deduction-minutes-values-for-employees';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $this->info('Starting to update deduction minutes for employees...');
            $attendance = Attendance::where('date', '>=', '2024-10-01')
                ->where('date', '<=', '2025-01-06')
                ->withWherehas('attendanceDeductions')
                ->with('clockIn:id,date', 'slotable:id,from')
                ->get();
            $this->info('Total Attendance Records: '.count($attendance));
            $count = 0;
            foreach ($attendance as $attendanceRecord) {
                if ($attendanceRecord->clockIn == null || $attendanceRecord->slotable == null) {
                    continue;
                }
                $count++;
                $LateMinutes = Carbon::parse($attendanceRecord->slotable->from)->diffInMinutes(Carbon::parse($attendanceRecord->clockIn->date));
                $deduction = $attendanceRecord->attendanceDeductions()->first();
                $deduction->update([
                    'deduction_minutes' => $LateMinutes,
                ]);
                $this->info('Deduction Minutes for Attendance ID: '.$attendanceRecord->id.' is updated to '.$LateMinutes);
            }
            DB::commit();
            $this->info('Total Attendance Records Processed: '.$count);
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error: '.$e);
        }
    }
}
