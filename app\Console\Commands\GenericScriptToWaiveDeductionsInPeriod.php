<?php

namespace App\Console\Commands;

use App\Models\AttendanceDeduction;
use App\Models\EmployeeRequest;
use DB;
use Illuminate\Console\Command;

class GenericScriptToWaiveDeductionsInPeriod extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generic-script-to-waive-deductions-in-period {company_id} {date_from} {date_to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'waive deductions for a given company within a specified date range';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companyId = $this->argument('company_id');
        $dateFrom = $this->argument('date_from');
        $dateTo = $this->argument('date_to');

        try {
            DB::beginTransaction();
            $Deductions = AttendanceDeduction::with('employeeRequests')
                ->where([
                    'company_id' => $companyId,
                ]
                )
                ->whereBetween(
                    'date', [$dateFrom,  $dateTo]
                )->get();

            echo 'Total deductions: '.$Deductions->count().' For company ID: '.$companyId.' From: '.$dateFrom.' To: '.$dateTo."\n";

            foreach ($Deductions as $deduction) {
                $employeeRequests = $deduction->employeeRequests()->get();
                echo 'Deduction ID: '.$deduction->id.' Employee Requests: '.$employeeRequests->count()."\n";
                if ($employeeRequests->count() == 0) {
                    echo 'Creating new request for deduction ID: '.$deduction->id."\n";
                    EmployeeRequest::create([
                        'company_id' => $deduction->company_id,
                        'requestable_id' => $deduction->id,
                        'requestable_type' => 'attendance_deduction',
                        'request_name' => 'waive_deduction',
                        'status' => 'waived',
                    ]);
                } else {
                    echo 'Updating status for deduction ID: '.$deduction->id."\n";
                    foreach ($employeeRequests as $employeeRequest) {
                        $employeeRequest->status = 'waived';
                        $employeeRequest->save();
                    }
                }

                $deduction->status = 'waived';
                $deduction->updated_value = '0';

                $deduction->save();

                echo 'Updating status for deduction ID: '.$deduction->id."\n";

            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            // dd($e);
        }

    }
}
