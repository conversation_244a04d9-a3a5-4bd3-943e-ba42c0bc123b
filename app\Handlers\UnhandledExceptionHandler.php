<?php

namespace App\Handlers;

use App\Util\HttpStatusCodeUtil;
use Illuminate\Http\Request;
use Throwable;

class UnhandledExceptionHandler extends BaseExceptionHandler
{
    /**
     * {@inheritDoc}
     */
    public static function shouldLog(Request $request, Throwable $exception): bool
    {
        return true;
    }

    /**
     * {@inheritDoc}
     */
    public static function getResponseStatusCode(Throwable $exception): int
    {
        return HttpStatusCodeUtil::INTERNAL_SERVER_ERROR;
    }

    public static function getPayload(Request $request, Throwable $exception): array
    {
        $payload = parent::getPayload($request, $exception);
        if ($exception instanceof \PDOException) {
            $payload['errors']['message'] = 'Internal server error.';
        }

        return $payload;
    }
}
