<?php

namespace App\Http\Controllers\V1\Billing;

use App\Exports\V1\BillDetailsExport;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\Billing\AddCommentRequest;
use App\Http\Requests\V1\Billing\CancelContractRequest;
use App\Http\Requests\V1\Billing\EditBillingSettingsRequest;
use App\Http\Requests\V1\Billing\EmployeesConversionsRequest;
use App\Http\Requests\V1\Billing\GetBillingRequest;
use App\Http\Requests\V1\Billing\PayBillRequest;
use App\Http\Requests\V1\Billing\SetupBillingRequest;
use App\Http\Resources\V1\Billing\BillCollection;
use App\Http\Resources\V1\Billing\BillingSettingsResource;
use App\Http\Resources\V1\Billing\BillResource;
use App\Services\V1\Billing\BillingService;
use App\Traits\GetLastDraftedPayRollOrCreate;
use App\Traits\V1\Payroll\PayrollHelper;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class BillingController extends NewController
{
    use GetLastDraftedPayRollOrCreate, PayrollHelper;

    public function __construct(
        protected BillingService $billingService,
    ) {}

    public function listBills(GetBillingRequest $request)
    {
        $bills = $this->billingService->getBills($request->validated());
        $data = new BillCollection($bills);
        $paginatedData = $data->response()->getData();

        return getResponseStructure(
            ['data' => $data, 'pagination' => $paginatedData->meta],
            HttpStatusCodeUtil::OK,
            'Bills fetched successfully'
        );
    }

    public function setFirstPriceToGenerateBill(SetupBillingRequest $request)
    {
        return DB::transaction(function () use ($request) {
            $companyId = auth()->user()->company_id;
            $billingSettings = $this->billingService->getBillingSettings($companyId);

            if (! empty($billingSettings->price_per_employee)) {
                return getResponseStructure([], HttpStatusCodeUtil::UNPROCESSABLE_ENTITY, 'setup already done before');
            }

            $this->billingService->editBillingSettings($companyId, $request->validated());
            $this->billingService->generateFirstBillForCompany($companyId);
            $billingSettings = $this->billingService->getBillingSettings($companyId);

            return getResponseStructure(
                ['data' => new BillingSettingsResource($billingSettings)],
                HttpStatusCodeUtil::OK,
                'Company billing setup done successfully'
            );
        });
    }

    public function editBillingSettings(EditBillingSettingsRequest $request)
    {
        $billingSettings = $this->billingService->getBillingSettings(auth()->user()->company_id);
        if (empty($billingSettings->price_per_employee)) {
            return getResponseStructure([], HttpStatusCodeUtil::UNPROCESSABLE_ENTITY, 'need to setup billing first');
        }

        $this->billingService->editBillingSettings(auth()->user()->company_id, $request->validated());

        return getResponseStructure(
            ['data' => []],
            HttpStatusCodeUtil::OK,
            'Company billing settings updated successfully'
        );
    }

    public function getBillingSettings()
    {
        $companyId = auth()->user()->company_id;

        $companyBillingSettings = $this->billingService->getBillingSettings($companyId);
        if (empty($companyBillingSettings->contract_start_date)) {
            $this->billingService->startContractWithFirstClockIn($companyId);
            $companyBillingSettings = $this->billingService->getBillingSettings($companyId);
        }

        return getResponseStructure(
            ['data' => new BillingSettingsResource($companyBillingSettings)],
            HttpStatusCodeUtil::OK,
            'Bill fetched successfully'
        );
    }

    public function cancelContract(CancelContractRequest $request)
    {
        $request = $request->validated();
        $request['status'] = 'deactivated';
        $this->billingService->editBillingSettings(auth()->user()->company_id, $request);

        return getResponseStructure(
            ['data' => []],
            HttpStatusCodeUtil::OK,
            'Contract cancelled successfully'
        );
    }

    public function getBillDetails($id)
    {
        $bill = $this->billingService->find($id);
        $response = new BillResource($bill);

        return getResponseStructure(
            ['data' => $response],
            HttpStatusCodeUtil::OK,
            'Bill fetched successfully'
        );
    }

    public function payBill($id, PayBillRequest $request)
    {
        $data = $request->validated();

        if (! empty($data['discount'])) {
            $bill = $this->billingService->find($id);
            $discountAmount = ($data['discount'] * $bill->total / 100);
            $data['total'] = $bill->total - $discountAmount;
            $data['discount'] = $discountAmount;
        }
        $this->billingService->update($id, $data);

        return getResponseStructure(
            ['data' => []],
            HttpStatusCodeUtil::OK,
            'bill paid successfully'
        );
    }

    public function addComment($id, AddCommentRequest $request)
    {
        $this->billingService->update($id, $request->validated());

        return getResponseStructure(
            ['data' => []],
            HttpStatusCodeUtil::OK,
            'Comment added successfully'
        );
    }

    public function deleteComment($billId)
    {
        $this->billingService->update($billId, ['comment' => null]);

        return getResponseStructure(
            ['data' => []],
            HttpStatusCodeUtil::OK,
            'Comment deleted successfully'
        );
    }

    public function employeesConversionsBasedMonths(EmployeesConversionsRequest $request)
    {

        $response = $this->billingService->getEmployeeConversionsMonthly(auth()->user()->company_id, $request->validated('year'));

        return getResponseStructure(
            ['data' => $response],
            HttpStatusCodeUtil::OK,
            'employees conversions return successfully'
        );
    }

    public function exportDetails($billId)
    {
        $bill = $this->billingService->find($billId);
        $data = $this->billingService->getBillCalculationDetails($bill);

        return Excel::download(new BillDetailsExport($data), "$bill->from - $bill->to.xlsx");

    }
}
