<?php

namespace App\Console\Commands;

use App\Models\Branch;
use Illuminate\Console\Command;

class RevertBranchLocationLink extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:revert-branch-location-link';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Revert branch location from coordinates back to the original link for specific branch IDs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Mapping of branch IDs to their originally assigned location link.
        $originalLinks = [
            1145 => "https://maps.app.goo.gl/TM3Ukt113MTsWn979",
            1146 => "https://maps.app.goo.gl/TM3Ukt113MTsWn979",
            1147 => "https://maps.app.goo.gl/sbqbuiXavLS1FTu68",
            1148 => "https://goo.gl/maps/hQywKZ93LjwJpAFC7",
            1149 => "https://maps.app.goo.gl/sbqbuiXavLS1FTu68",
            1150 => "https://maps.app.goo.gl/gHdvVtusVRDaGxVA8",
            1151 => "https://maps.app.goo.gl/as9GqnLzQ97ogGVC7",
            1152 => "https://maps.app.goo.gl/Hko3ie3hviwmAmPw8",
            1153 => "https://maps.app.goo.gl/D7gTZ9hWYGffZ3F98",
            1154 => "https://maps.app.goo.gl/7qEjzbERnTSxqKTH7",
            1155 => "https://maps.app.goo.gl/ZcDTCgrRJGDGc5JC8",
            1156 => "https://maps.app.goo.gl/9HtRDJFQsqZFt5117",
            1157 => "https://maps.app.goo.gl/NMsY1KBK6kecgMK27",
            1158 => "https://maps.app.goo.gl/9HtRDJFQsqZFt5117",
            1159 => "https://maps.app.goo.gl/ZbtvTA9LNwt4M8bp7",
            1160 => "https://maps.app.goo.gl/yHyp351xSqWtvk4x5",
            1161 => "https://maps.app.goo.gl/yHyp351xSqWtvk4x5",
            1162 => "https://maps.app.goo.gl/yHyp351xSqWtvk4x5",
            1163 => "https://maps.app.goo.gl/yHyp351xSqWtvk4x5",
            1164 => "https://maps.app.goo.gl/fyBCvPNxbFzY9Kex6",
            1165 => "https://maps.app.goo.gl/fyBCvPNxbFzY9Kex6",
            1166 => "https://maps.app.goo.gl/ksfnrVxPw5JHiVwj7",
            1167 => "https://maps.app.goo.gl/ksfnrVxPw5JHiVwj7",
            1168 => "https://maps.app.goo.gl/bQDTjgcUcuCMo4Ch7",
            1169 => "https://maps.app.goo.gl/bQDTjgcUcuCMo4Ch7",
            1170 => "https://maps.app.goo.gl/bQDTjgcUcuCMo4Ch7",
            1171 => "https://maps.app.goo.gl/bQDTjgcUcuCMo4Ch7",
            1172 => "https://maps.app.goo.gl/kwR8w5WuZSb1a15j7",
            1173 => "https://maps.app.goo.gl/kwR8w5WuZSb1a15j7",
            1174 => "https://maps.app.goo.gl/FUAVE1AU883aH3qz7",
            1175 => "https://maps.app.goo.gl/E2LRrULKu695P8v88",
            1176 => "https://maps.app.goo.gl/HuwrjfeVx9foquGaA",
            1177 => "https://maps.app.goo.gl/vmys2foEorVVk3FW6",
            1178 => "https://maps.app.goo.gl/oCWvwLQiVrfpiTui9",
            1179 => "https://maps.app.goo.gl/2nhd4MBwaC9Wy49U9",
            1180 => "https://maps.app.goo.gl/zJU8sqzHB837eUcT7",
            1181 => "https://maps.app.goo.gl/zJU8sqzHB837eUcT7",
            1182 => "https://maps.app.goo.gl/yMyfpyZYDPfL9jaY8",
            1183 => "https://maps.app.goo.gl/WU5xfhcHpRqzCvH69",
            1184 => "https://maps.app.goo.gl/a1ue7ifj8EgSUssW7",
            1185 => "https://maps.app.goo.gl/1mj7wjVErrT3cKrW9",
            1186 => "https://maps.app.goo.gl/1mj7wjVErrT3cKrW9",
            1188 => "https://maps.app.goo.gl/89Ujp5pNGtC52zM98",
            1189 => "https://maps.app.goo.gl/XTHv2Q1H7VZ1yYN89",
            1190 => "https://maps.app.goo.gl/XTHv2Q1H7VZ1yYN89",
            1191 => "https://maps.app.goo.gl/XTHv2Q1H7VZ1yYN89",
            1192 => "https://maps.app.goo.gl/XTHv2Q1H7VZ1yYN89",
            1193 => "https://maps.app.goo.gl/XTHv2Q1H7VZ1yYN89",
            1194 => "https://maps.app.goo.gl/XTHv2Q1H7VZ1yYN89",
            1195 => "https://maps.app.goo.gl/XTHv2Q1H7VZ1yYN89",
            1196 => "https://maps.app.goo.gl/XTHv2Q1H7VZ1yYN89",
            1197 => "https://maps.app.goo.gl/a9WVrM88b2z6asX78",
            1198 => "https://maps.app.goo.gl/KB9CPJUP2REVK6N96",
            1199 => "https://maps.app.goo.gl/SyWZfjmMDzCK69TN9",
            1200 => "https://maps.app.goo.gl/SyWZfjmMDzCK69TN9",
            1201 => "https://maps.app.goo.gl/SyWZfjmMDzCK69TN9",
            1202 => "https://maps.app.goo.gl/TXYBmjUx8Z7KJwDd7",
            1203 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1204 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1205 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1206 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1207 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1208 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1209 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1210 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1211 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1212 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1213 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1214 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1215 => "https://maps.app.goo.gl/AHmx5bPJo4gsFVrn6",
            1216 => "https://maps.app.goo.gl/bfPnCagsXvVXww1S9",
            1217 => "https://maps.app.goo.gl/bfPnCagsXvVXww1S9",
            1218 => "https://maps.app.goo.gl/EVitey3fg7BVJPHx6",
            1219 => "https://maps.app.goo.gl/BAH3S9vdMv77gb33A",
            1220 => "https://maps.app.goo.gl/BAH3S9vdMv77gb33A",
            1221 => "https://maps.app.goo.gl/hVkGcj5HZYUWp4gj9",
            1222 => "https://maps.app.goo.gl/EVitey3fg7BVJPHx6",
            1223 => "https://maps.app.goo.gl/BAH3S9vdMv77gb33A",
            1224 => "https://maps.app.goo.gl/Xtpa9G6BHCY4CugC6",
            1225 => "https://maps.app.goo.gl/hVkGcj5HZYUWp4gj9",
            1226 => "https://maps.app.goo.gl/BAH3S9vdMv77gb33A",
            1227 => "https://maps.app.goo.gl/BAH3S9vdMv77gb33A",
            1228 => "https://maps.app.goo.gl/BAH3S9vdMv77gb33A",
            1229 => "https://maps.app.goo.gl/BAH3S9vdMv77gb33A",
            1230 => "https://maps.app.goo.gl/BAH3S9vdMv77gb33A",
            1231 => "https://maps.app.goo.gl/p9xTuUdCYPebcRcr5",
            1232 => "https://maps.app.goo.gl/p9xTuUdCYPebcRcr5",
            1233 => "https://maps.app.goo.gl/p9xTuUdCYPebcRcr5",
            1234 => "https://maps.app.goo.gl/GmW2Avb9r63QdPcZ7",
            1235 => "https://maps.app.goo.gl/GmW2Avb9r63QdPcZ7",
            1236 => "https://maps.app.goo.gl/fbsg23bWVCwtBpUD7",
            1237 => "https://maps.app.goo.gl/qbJ4SpPUSbvaa7uz6",
            1238 => "https://maps.app.goo.gl/qbJ4SpPUSbvaa7uz6",
            1239 => "https://maps.app.goo.gl/fbsg23bWVCwtBpUD7",
            1240 => "https://maps.app.goo.gl/fbsg23bWVCwtBpUD7",
            1241 => "https://maps.app.goo.gl/STEGnHeLs3hmFmZz9",
            1242 => "https://maps.app.goo.gl/STEGnHeLs3hmFmZz9",
            1243 => "https://maps.app.goo.gl/DjWAA1RHZexrizXS6",
            1244 => "https://maps.app.goo.gl/DjWAA1RHZexrizXS6",
            1245 => "https://maps.app.goo.gl/DjWAA1RHZexrizXS6",
            1246 => "https://maps.app.goo.gl/DjWAA1RHZexrizXS6",
            1247 => "https://maps.app.goo.gl/DjWAA1RHZexrizXS6",
            1248 => "https://maps.app.goo.gl/Cke7EA4f92n3GwrL7",
            1249 => "https://maps.app.goo.gl/DjWAA1RHZexrizXS6",
            1250 => "https://maps.app.goo.gl/DjWAA1RHZexrizXS6",
            1251 => "https://maps.app.goo.gl/H5NRcgLwhwAFxmdc8",
            1252 => "https://maps.app.goo.gl/uGAy2tbKLyWcL3R36",
            1253 => "https://maps.app.goo.gl/bU23fEV1DupHQDbm8",
            1254 => "https://maps.app.goo.gl/z1thcUFFPvvbL9ZQ8",
            1255 => "https://maps.app.goo.gl/z1thcUFFPvvbL9ZQ8",
            1256 => "https://maps.app.goo.gl/z1thcUFFPvvbL9ZQ8",
            1257 => "https://maps.app.goo.gl/uGAy2tbKLyWcL3R36",
            1258 => "https://maps.app.goo.gl/z1thcUFFPvvbL9ZQ8",
            1259 => "https://maps.app.goo.gl/z1thcUFFPvvbL9ZQ8",
            1260 => "https://maps.app.goo.gl/2wJuPMBEmpQDD5369",
            1261 => "https://maps.app.goo.gl/z1thcUFFPvvbL9ZQ8",
            1262 => "https://maps.app.goo.gl/uGAy2tbKLyWcL3R36",
            1263 => "https://maps.app.goo.gl/XBPrkFZd8wRBw4hC8",
            1264 => "https://maps.app.goo.gl/werCsootp8fhmcMZA",
            1265 => "https://maps.app.goo.gl/werCsootp8fhmcMZA",
            1266 => "https://maps.app.goo.gl/werCsootp8fhmcMZA",
            1268 => "https://maps.app.goo.gl/werCsootp8fhmcMZA",
            1269 => "https://maps.app.goo.gl/3cYRxEVxyLWyP7Cr6",
            1270 => "https://maps.app.goo.gl/zvrUePhyXKfXRjDUA",
            1271 => "https://maps.app.goo.gl/hz2TDNNWghTseG916",
            1272 => "https://maps.app.goo.gl/3EELo1TUAwGLz57K9",
            1273 => "https://maps.app.goo.gl/QquWm2L42nFmCRcv5",
            1274 => "https://maps.app.goo.gl/Ha5fpi8dMDMdCVpa7",
            1275 => "https://maps.app.goo.gl/R661WWMLREvhXYex8",
            1276 => "https://maps.app.goo.gl/v3fMiRQ9uCxwYvBw6",
            1277 => "https://maps.app.goo.gl/Xr42C5HE6MC46dQ6A",
            1278 => "https://maps.app.goo.gl/6w9qjSLEfRqtgcdx7",
            1279 => "https://maps.app.goo.gl/ESYdisneKbSeczfi6",
            1280 => "https://maps.app.goo.gl/QBGLtn1LPUUpvw6z5",
            1281 => "https://maps.app.goo.gl/2V98pqck3zTgBKgJ7",
            1282 => "https://maps.app.goo.gl/2V98pqck3zTgBKgJ7",
            1283 => "https://maps.app.goo.gl/nvvzkMKn3m8GGBr78",
            1284 => "https://maps.app.goo.gl/5NF3DRVwEbjTmPPU8",
            1285 => "https://maps.app.goo.gl/DmznFYPAMSWbgUb56",
            1286 => "https://maps.app.goo.gl/FVcSqZW3chJymuBR7",
            1287 => "https://maps.app.goo.gl/NGYqZVvUgmmyf8vY8",
            1288 => "https://maps.app.goo.gl/J7usc5fTAEGgcHQ7A",
            1289 => "https://maps.app.goo.gl/dmU7eSpcBEkdYMur7",
            1290 => "https://maps.app.goo.gl/uzQt4UD8WhAv7Fw57",
            1291 => "https://maps.app.goo.gl/ZtrJNmDpJQho7cvd7",
            1292 => "https://maps.app.goo.gl/AronCWvMFjLsb9Np6",
            1293 => "https://maps.app.goo.gl/gYSHhdQqqMsXDdzT6",
            1294 => "https://maps.app.goo.gl/Zby9n2RqwQGfjyMH7",
            1295 => "https://maps.app.goo.gl/XicyuWMv6gKtTqaG9",
            1296 => "https://maps.app.goo.gl/2MaqFUJUgNPWSoHQ7",
        ];

        $this->info("Reverting " . count($originalLinks) . " branches.");

        foreach ($originalLinks as $branchId => $originalLink) {
            $branch = Branch::find($branchId);
            if ($branch) {
                $branch->location = $originalLink;
                $branch->save();
                $this->info("Reverted branch ID {$branchId} to link: {$originalLink}");
            } else {
                $this->info("Branch ID {$branchId} not found.");
            }
        }

        $this->info("Completed reverting branches.");
    }
}
