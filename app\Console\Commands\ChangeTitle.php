<?php

namespace App\Console\Commands;

use App\Enums\Titles\EffectiveOnType;
use App\Repositories\NewEmployeeRepository;
use App\Repositories\V1\Employee\ChangeTitleRepository;
use App\Services\V1\Employee\ChangeTitleService;
use Illuminate\Console\Command;

class ChangeTitle extends Command
{
    private $newEmployeeRepository;

    private $changeTitleRepository;

    private $changeTitleService;

    public function __construct(
        NewEmployeeRepository $newEmployeeRepository,
        ChangeTitleRepository $changeTitleRepository,
        ChangeTitleService $changeTitleService
    ) {
        parent::__construct();
        $this->newEmployeeRepository = $newEmployeeRepository;
        $this->changeTitleRepository = $changeTitleRepository;
        $this->changeTitleService = $changeTitleService;
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:change-title';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Executes scheduled title changes for employees';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $scheduledChanges = $this->changeTitleRepository->getAllScheduledTitlesEmployees();
        foreach ($scheduledChanges as $change) {
            config(['globals.user' => $this->newEmployeeRepository->find($change->employee_id)->user]);

            $employeeId = $change->employee_id;

            $this->changeTitleService->changeTitleForEmployee($employeeId, [
                'title_id' => $change->to_title_id,
                'effective_on' => EffectiveOnType::CURRENT_MONTH->value,
                'type' => $change->type,
                'net_salary' => $change->net_salary,
                'by_employee_id' => $change->by_employee_id,
                'role_id' => $change->role_id,
            ]);

            $this->info("Processed scheduled title change for Employee ID: {$employeeId}");
        }

        $this->info('All scheduled title changes have been processed successfully.');
    }
}
