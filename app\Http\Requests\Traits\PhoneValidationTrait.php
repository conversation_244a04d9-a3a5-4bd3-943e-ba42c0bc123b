<?php

namespace App\Http\Requests\Traits;

use App\Rules\PhoneNumberByCountryRule;
use App\Rules\UniquePhoneWithCountryCodeRule;

trait PhoneValidationTrait
{
    /**
     * Validate a primary phone number with country code
     *
     * @param string $phoneField The name of the phone field
     * @param string $countryCodeField The name of the country code field
     * @param int|null $exceptId The employee ID to exclude from uniqueness check (for updates)
     * @return \Closure
     */
    protected function validatePrimaryPhone(string $phoneField, string $countryCodeField, ?int $exceptId = null): \Closure
    {
        return function($attribute, $value, $fail) use ($phoneField, $countryCodeField, $exceptId) {
            $countryCode = $this->input($countryCodeField);
            if ($countryCode) {
                $formatValidator = new PhoneNumberByCountryRule($countryCode);
                if (!$formatValidator->passes($attribute, $value)) {
                    $fail(__('validation.phone_number_format', ['country_code' => $countryCode]));
                    return;
                }

                $uniqueValidator = new UniquePhoneWithCountryCodeRule($countryCode, $exceptId);
                if (!$uniqueValidator->passes($attribute, $value)) {
                    $fail(__('validation.unique_phone'));
                }
            }
        };
    }

    /**
     * Validate a secondary phone number with country code
     *
     * @param string $phoneField The name of the phone field
     * @param string $countryCodeField The name of the country code field
     * @return \Closure
     */
    protected function validateSecondaryPhone(string $phoneField, string $countryCodeField): \Closure
    {
        return function($attribute, $value, $fail) use ($phoneField, $countryCodeField) {
            if ($value) {
                $countryCode = $this->input($countryCodeField);
                if ($countryCode) {
                    $validator = new PhoneNumberByCountryRule($countryCode);
                    if (!$validator->passes($attribute, $value)) {
                        $fail(__('validation.phone_number_format', ['country_code' => $countryCode]));
                    }
                }
            }
        };
    }

    /**
     * Validate an emergency contact phone number with country code
     *
     * @param string $baseField The base field name for emergency contacts (e.g., 'emergency_contacts')
     * @param string $indexKey The key used to extract the index from the attribute name
     * @return \Closure
     */
    protected function validateEmergencyContactPhone(string $baseField, string $indexKey = '1'): \Closure
    {
        return function($attribute, $value, $fail) use ($baseField, $indexKey) {
            $parts = explode('.', $attribute);
            $index = $parts[$indexKey];
            $countryCode = $this->input("{$baseField}.{$index}.phone_country_code");

            if ($countryCode) {
                $validator = new PhoneNumberByCountryRule($countryCode);
                if (!$validator->passes($attribute, $value)) {
                    $fail(__('validation.phone_number_format', ['country_code' => $countryCode]));
                }
            }
        };
    }

    /**
     * Validate emergency contacts for duplicate phone numbers
     *
     * @param string $baseField The base field name for emergency contacts
     * @return \Closure
     */
    protected function validateUniqueEmergencyContacts(string $baseField): \Closure
    {
        return function($attribute, $items, $fail) {
            if (!is_array($items)) {
                return;
            }

            $phoneNumbers = [];
            foreach ($items as $item) {
                if (!isset($item['phone']) || !isset($item['phone_country_code'])) {
                    continue;
                }

                $fullPhone = $item['phone_country_code'] . $item['phone'];

                if (in_array($fullPhone, $phoneNumbers)) {
                    $fail(__('validation.duplicate_emergency_contact', ['phone' => $fullPhone]));
                    return;
                }

                $phoneNumbers[] = $fullPhone;
            }
        };
    }
}
