<?php

namespace App\Http\Controllers\V1\CostCenter;

use App\Exports\V1\CostCenter\CustomCostCenterExport;
use App\Http\Controllers\NewController;
use App\Http\Requests\V1\CostCenter\GetCustomCostCenterResultRequest;
use App\Http\Resources\V1\CostCenter\CustomResultDetailsResource;
use App\Http\Resources\V1\CostCenter\GetCostCenterResultsResource;
use App\Models\CostCenterResult;
use App\Services\V1\CostCenter\CostCenterResultService;
use App\Services\V1\CostCenter\CustomCostCenterService;
use App\Traits\EmployeeChangesHelper;
use App\Util\HttpStatusCodeUtil;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class CostCenterResultController extends NewController
{
    use EmployeeChangesHelper;

    public function __construct(
        protected CostCenterResultService $costCenterResultService,
        protected CustomCostCenterService $customCostCenterService

    ) {}

    public function getCostCenterResults(GetCustomCostCenterResultRequest $request)
    {
        try {
            $results = $this->costCenterResultService->getCostCentersResults($request->validated('month'), $request->validated('year'));

            return getResponseStructure(
                ['data' => new GetCostCenterResultsResource($results)],
                HttpStatusCodeUtil::OK,
                'results retrieved successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw $e;
        }

    }

    public function exportCostCenterResults(GetCustomCostCenterResultRequest $request)
    {
        $results = $this->costCenterResultService->getCostCentersResults($request->validated('month'), $request->validated('year'));

        return Excel::download(new CustomCostCenterExport($results['details']->toArray()), 'export.xlsx');

    }

    public function getCostCenterResultDetails(int $resultId, $targetId)
    {
        try {
            $results = $this->costCenterResultService->getCostCentersResultDetails($resultId, $targetId);

            return getResponseStructure(
                ['data' => new CustomResultDetailsResource($results)],
                HttpStatusCodeUtil::OK,
                'setting retrieved successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw $e;
        }

    }

    public function reCalculateCostCenterResult(int $resultId)
    {
        try {
            $costCenterResult = CostCenterResult::where('id', $resultId)->first();
            $month = $costCenterResult->month;
            $year = $costCenterResult->year;

            $this->customCostCenterService->calculateCustomCostCenters($month, $year, $costCenterResult->costCenter);
            $costCenterResult->delete();

            $results = $this->costCenterResultService->getCostCentersResults($month, $year);

            return getResponseStructure(
                ['data' => new GetCostCenterResultsResource($results)],
                HttpStatusCodeUtil::OK,
                'results retrieved successfully');

        } catch (\Exception $e) {
            Log::error($e);
            throw $e;
        }

    }

    public function generateCostCenterTest(Request $request)
    {
        // Validate your request inputs as needed
        $request->validate([
            'month' => 'required|integer',
            'year' => 'required|integer',
        ]);

        $companyId = auth()->user()->company_id;
        $month = $request->input('month');
        $year = $request->input('year');

        try {
            // Call the console command synchronously
            Artisan::call('app:generate-cost-center', [
                'company_id' => $companyId,
                'month' => $month,
                'year' => $year,
            ]);

            return response()->json([
                'message' => 'Cost center generation triggered successfully.',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to trigger cost center generation: '.$e->getMessage(),
            ], 500);
        }
    }
}
