<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\EmployeeLeaveBalance;
use App\Models\EmployeeLeaveRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateOvertimeToAnnualLeave extends Command
{
    protected $signature = 'leaves:migrate-overtime-to-annual {company_id}';
    protected $description = 'Migrate overtime leave requests to annual leave type and update balances';

    public function handle()
    {
        $companyId = $this->argument('company_id');
        
        try {
            // Validate company exists
            $company = Company::find($companyId);
            if (!$company) {
                $this->error("Company with ID {$companyId} not found.");
                return 1;
            }

            // Validate company has overtime and annual leave types
            if (!$company->OvertimeLeaveType || !$company->annualLeaveType) {
                $this->error("Company must have both overtime and annual leave types configured.");
                return 1;
            }

            $this->info("Starting migration for company ID: {$companyId}");
            Log::info("Starting overtime to annual leave migration for company ID: {$companyId}");

            DB::beginTransaction();

            // Get all leave requests with overtime leave type
            $overtimeLeaveRequests = EmployeeLeaveRequest::where('company_id', $companyId)
                ->where('company_leave_type_policy_id', $company->OvertimeLeaveType->companyLeaveTypePolicy->id)
                ->get();

            $this->info("Found {$overtimeLeaveRequests->count()} overtime leave requests to migrate");
            Log::info("Found {$overtimeLeaveRequests->count()} overtime leave requests to migrate");

            // Update leave requests to annual leave type
            foreach ($overtimeLeaveRequests as $request) {
                $this->info("Processing leave request ID: {$request->id}");
                Log::info("Processing leave request ID: {$request->id}");

                // Update the leave type
                $request->company_leave_type_policy_id = $company->annualLeaveType->companyLeaveTypePolicy->id;
                $request->save();

                // Deduct from annual leave balance
                $annualBalance = EmployeeLeaveBalance::where('employee_id', $request->employee_id)
                    ->where('company_leave_type_policy_id', $company->annualLeaveType->companyLeaveTypePolicy->id)
                    ->first();

                if ($annualBalance) {
                    $oldBalance = $annualBalance->balance;
                    $annualBalance->balance -= $request->net_quantity;
                    $annualBalance->save();

                    $this->info("Updated annual balance for employee {$request->employee_id}: {$oldBalance} -> {$annualBalance->balance}");
                    Log::info("Updated annual balance for employee {$request->employee_id}: {$oldBalance} -> {$annualBalance->balance}");
                } else {
                    $this->warn("No annual balance found for employee {$request->employee_id}");
                    Log::warning("No annual balance found for employee {$request->employee_id}");
                }
            }

            // Reset overtime balances to zero
            $overtimeBalances = EmployeeLeaveBalance::where('company_leave_type_policy_id', $company->OvertimeLeaveType->companyLeaveTypePolicy->id)
                ->get();

            $this->info("Found {$overtimeBalances->count()} overtime balances to reset");
            Log::info("Found {$overtimeBalances->count()} overtime balances to reset");

            foreach ($overtimeBalances as $balance) {
                $oldBalance = $balance->balance;
                $balance->balance = 0;
                $balance->save();

                $this->info("Reset overtime balance for employee {$balance->employee_id}: {$oldBalance} -> 0");
                Log::info("Reset overtime balance for employee {$balance->employee_id}: {$oldBalance} -> 0");
            }

            DB::commit();
            $this->info("Migration completed successfully!");
            Log::info("Overtime to annual leave migration completed successfully for company ID: {$companyId}");

            return 0;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("An error occurred: " . $e->getMessage());
            Log::error("Error in overtime to annual leave migration: " . $e->getMessage());
            return 1;
        }
    }
} 